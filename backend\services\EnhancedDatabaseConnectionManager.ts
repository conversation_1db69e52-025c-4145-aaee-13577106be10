/**
 * Enhanced Database Connection Manager for MEV Arbitrage Bot
 * 
 * Implements comprehensive connection management with:
 * - Circuit breaker patterns for all database connections
 * - Connection pooling with performance targets (<1000ms service communication, <100ms database queries)
 * - Health monitoring and graceful degradation capabilities
 * - Performance metrics collection and monitoring
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { InfluxDB } from '@influxdata/influxdb-client';
import Redis from 'ioredis';
import { Pool, PoolClient } from 'pg';
import logger from '../utils/logger.js';
import config from '../config/index.js';

export interface ConnectionHealth {
  connected: boolean;
  latency: number;
  lastCheck: Date;
  errorCount: number;
  circuitBreakerOpen: boolean;
}

export interface ConnectionMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingConnections: number;
  averageLatency: number;
  errorRate: number;
  throughput: number;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
  halfOpenMaxCalls: number;
}

class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private halfOpenCalls = 0;

  constructor(private config: CircuitBreakerConfig) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.config.resetTimeout) {
        this.state = 'HALF_OPEN';
        this.halfOpenCalls = 0;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    if (this.state === 'HALF_OPEN' && this.halfOpenCalls >= this.config.halfOpenMaxCalls) {
      throw new Error('Circuit breaker is HALF_OPEN with max calls reached');
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
    }
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.state === 'HALF_OPEN') {
      this.state = 'OPEN';
    } else if (this.failures >= this.config.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getState(): string {
    return this.state;
  }

  isOpen(): boolean {
    return this.state === 'OPEN';
  }
}

export class EnhancedDatabaseConnectionManager {
  private supabaseClient: SupabaseClient | null = null;
  private influxDB: InfluxDB | null = null;
  private redisClient: Redis | null = null;
  private postgresPool: Pool | null = null;

  private circuitBreakers: Map<string, CircuitBreaker> = new Map();
  private connectionHealth: Map<string, ConnectionHealth> = new Map();
  private connectionMetrics: Map<string, ConnectionMetrics> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  private readonly circuitBreakerConfig: CircuitBreakerConfig = {
    failureThreshold: parseInt(config.CIRCUIT_BREAKER_FAILURE_THRESHOLD),
    resetTimeout: parseInt(config.CIRCUIT_BREAKER_TIMEOUT),
    monitoringPeriod: 60000, // 1 minute
    halfOpenMaxCalls: 5
  };

  constructor() {
    this.initializeCircuitBreakers();
    this.initializeHealthChecks();
  }

  private initializeCircuitBreakers(): void {
    const databases = ['supabase', 'influxdb', 'redis', 'postgres'];
    databases.forEach(db => {
      this.circuitBreakers.set(db, new CircuitBreaker(this.circuitBreakerConfig));
      this.connectionHealth.set(db, {
        connected: false,
        latency: 0,
        lastCheck: new Date(),
        errorCount: 0,
        circuitBreakerOpen: false
      });
      this.connectionMetrics.set(db, {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        waitingConnections: 0,
        averageLatency: 0,
        errorRate: 0,
        throughput: 0
      });
    });
  }

  private initializeHealthChecks(): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthChecks();
    }, 30000); // Check every 30 seconds
  }

  // Supabase Connection Management
  async initializeSupabase(): Promise<boolean> {
    const circuitBreaker = this.circuitBreakers.get('supabase')!;
    
    try {
      return await circuitBreaker.execute(async () => {
        if (!config.SUPABASE_URL || !config.SUPABASE_ANON_KEY) {
          throw new Error('Supabase configuration missing');
        }

        this.supabaseClient = createClient(config.SUPABASE_URL, config.SUPABASE_ANON_KEY, {
          auth: {
            persistSession: false
          },
          db: {
            schema: 'public'
          }
        });

        // Test connection
        const { error } = await this.supabaseClient.from('configuration').select('count').limit(1);
        if (error) throw error;

        this.updateConnectionHealth('supabase', true, 0);
        logger.info('Supabase connection initialized successfully');
        return true;
      });
    } catch (error) {
      this.updateConnectionHealth('supabase', false, 0);
      logger.error('Failed to initialize Supabase connection:', error);
      return false;
    }
  }

  // InfluxDB Connection Management
  async initializeInfluxDB(): Promise<boolean> {
    const circuitBreaker = this.circuitBreakers.get('influxdb')!;
    
    try {
      return await circuitBreaker.execute(async () => {
        if (!config.INFLUXDB_URL || !config.INFLUXDB_TOKEN) {
          throw new Error('InfluxDB configuration missing');
        }

        this.influxDB = new InfluxDB({
          url: config.INFLUXDB_URL,
          token: config.INFLUXDB_TOKEN,
          timeout: parseInt(config.CIRCUIT_BREAKER_TIMEOUT)
        });

        // Test connection
        const queryApi = this.influxDB.getQueryApi(config.INFLUXDB_ORG);
        const query = `from(bucket: "${config.INFLUXDB_BUCKET}") |> range(start: -1m) |> limit(n: 1)`;
        
        await new Promise((resolve, reject) => {
          queryApi.queryRows(query, {
            next: () => resolve(true),
            error: reject,
            complete: () => resolve(true)
          });
        });

        this.updateConnectionHealth('influxdb', true, 0);
        logger.info('InfluxDB connection initialized successfully');
        return true;
      });
    } catch (error) {
      this.updateConnectionHealth('influxdb', false, 0);
      logger.error('Failed to initialize InfluxDB connection:', error);
      return false;
    }
  }

  // Redis Connection Management
  async initializeRedis(): Promise<boolean> {
    const circuitBreaker = this.circuitBreakers.get('redis')!;
    
    try {
      return await circuitBreaker.execute(async () => {
        if (!config.REDIS_URL) {
          throw new Error('Redis configuration missing');
        }

        this.redisClient = new Redis(config.REDIS_URL, {
          retryDelayOnFailover: 100,
          enableReadyCheck: true,
          maxRetriesPerRequest: 3,
          connectTimeout: parseInt(config.CIRCUIT_BREAKER_TIMEOUT),
          commandTimeout: parseInt(config.CIRCUIT_BREAKER_TIMEOUT)
        });

        // Test connection
        await this.redisClient.ping();

        this.updateConnectionHealth('redis', true, 0);
        logger.info('Redis connection initialized successfully');
        return true;
      });
    } catch (error) {
      this.updateConnectionHealth('redis', false, 0);
      logger.error('Failed to initialize Redis connection:', error);
      return false;
    }
  }

  // PostgreSQL Connection Management
  async initializePostgres(): Promise<boolean> {
    const circuitBreaker = this.circuitBreakers.get('postgres')!;
    
    try {
      return await circuitBreaker.execute(async () => {
        if (!config.POSTGRES_URL) {
          throw new Error('PostgreSQL configuration missing');
        }

        this.postgresPool = new Pool({
          connectionString: config.POSTGRES_URL,
          max: parseInt(config.POSTGRES_MAX_CONNECTIONS),
          idleTimeoutMillis: parseInt(config.POSTGRES_IDLE_TIMEOUT),
          connectionTimeoutMillis: parseInt(config.CIRCUIT_BREAKER_TIMEOUT),
          statement_timeout: parseInt(config.CIRCUIT_BREAKER_TIMEOUT)
        });

        // Test connection
        const client = await this.postgresPool.connect();
        await client.query('SELECT 1');
        client.release();

        this.updateConnectionHealth('postgres', true, 0);
        logger.info('PostgreSQL connection initialized successfully');
        return true;
      });
    } catch (error) {
      this.updateConnectionHealth('postgres', false, 0);
      logger.error('Failed to initialize PostgreSQL connection:', error);
      return false;
    }
  }

  // Connection Getters with Circuit Breaker Protection
  async getSupabaseClient(): Promise<SupabaseClient | null> {
    const circuitBreaker = this.circuitBreakers.get('supabase')!;
    
    if (circuitBreaker.isOpen()) {
      logger.warn('Supabase circuit breaker is open');
      return null;
    }

    return this.supabaseClient;
  }

  async getInfluxDB(): Promise<InfluxDB | null> {
    const circuitBreaker = this.circuitBreakers.get('influxdb')!;
    
    if (circuitBreaker.isOpen()) {
      logger.warn('InfluxDB circuit breaker is open');
      return null;
    }

    return this.influxDB;
  }

  async getRedisClient(): Promise<Redis | null> {
    const circuitBreaker = this.circuitBreakers.get('redis')!;
    
    if (circuitBreaker.isOpen()) {
      logger.warn('Redis circuit breaker is open');
      return null;
    }

    return this.redisClient;
  }

  async getPostgresClient(): Promise<PoolClient | null> {
    const circuitBreaker = this.circuitBreakers.get('postgres')!;
    
    if (circuitBreaker.isOpen() || !this.postgresPool) {
      logger.warn('PostgreSQL circuit breaker is open or pool not initialized');
      return null;
    }

    try {
      return await circuitBreaker.execute(async () => {
        return await this.postgresPool!.connect();
      });
    } catch (error) {
      logger.error('Failed to get PostgreSQL client:', error);
      return null;
    }
  }

  // Health Monitoring
  private async performHealthChecks(): Promise<void> {
    const databases = ['supabase', 'influxdb', 'redis', 'postgres'];

    for (const db of databases) {
      await this.checkDatabaseHealth(db);
    }
  }

  private async checkDatabaseHealth(database: string): Promise<void> {
    const startTime = Date.now();
    let isHealthy = false;

    try {
      switch (database) {
        case 'supabase':
          if (this.supabaseClient) {
            const { error } = await this.supabaseClient.from('configuration').select('count').limit(1);
            isHealthy = !error;
          }
          break;
        case 'influxdb':
          if (this.influxDB) {
            const queryApi = this.influxDB.getQueryApi(config.INFLUXDB_ORG);
            await new Promise((resolve, reject) => {
              queryApi.queryRows('from(bucket: "test") |> range(start: -1m) |> limit(n: 1)', {
                next: () => resolve(true),
                error: reject,
                complete: () => resolve(true)
              });
            });
            isHealthy = true;
          }
          break;
        case 'redis':
          if (this.redisClient) {
            await this.redisClient.ping();
            isHealthy = true;
          }
          break;
        case 'postgres':
          if (this.postgresPool) {
            const client = await this.postgresPool.connect();
            await client.query('SELECT 1');
            client.release();
            isHealthy = true;
          }
          break;
      }
    } catch (error) {
      logger.warn(`Health check failed for ${database}:`, error);
      isHealthy = false;
    }

    const latency = Date.now() - startTime;
    this.updateConnectionHealth(database, isHealthy, latency);
  }

  private updateConnectionHealth(database: string, connected: boolean, latency: number): void {
    const health = this.connectionHealth.get(database);
    if (health) {
      health.connected = connected;
      health.latency = latency;
      health.lastCheck = new Date();
      health.circuitBreakerOpen = this.circuitBreakers.get(database)?.isOpen() || false;

      if (!connected) {
        health.errorCount++;
      } else {
        health.errorCount = Math.max(0, health.errorCount - 1);
      }
    }
  }

  // Performance Metrics
  updateConnectionMetrics(database: string, metrics: Partial<ConnectionMetrics>): void {
    const current = this.connectionMetrics.get(database);
    if (current) {
      Object.assign(current, metrics);
    }
  }

  getConnectionHealth(database?: string): Map<string, ConnectionHealth> | ConnectionHealth | null {
    if (database) {
      return this.connectionHealth.get(database) || null;
    }
    return this.connectionHealth;
  }

  getConnectionMetrics(database?: string): Map<string, ConnectionMetrics> | ConnectionMetrics | null {
    if (database) {
      return this.connectionMetrics.get(database) || null;
    }
    return this.connectionMetrics;
  }

  getAllConnectionStatus(): {
    health: Map<string, ConnectionHealth>;
    metrics: Map<string, ConnectionMetrics>;
    circuitBreakers: Map<string, string>;
  } {
    const circuitBreakerStates = new Map<string, string>();
    this.circuitBreakers.forEach((breaker, db) => {
      circuitBreakerStates.set(db, breaker.getState());
    });

    return {
      health: this.connectionHealth,
      metrics: this.connectionMetrics,
      circuitBreakers: circuitBreakerStates
    };
  }

  // Graceful Degradation
  async executeWithFallback<T>(
    primary: string,
    secondary: string,
    operation: (client: any) => Promise<T>
  ): Promise<T | null> {
    // Try primary database
    try {
      const primaryClient = await this.getClient(primary);
      if (primaryClient) {
        return await operation(primaryClient);
      }
    } catch (error) {
      logger.warn(`Primary database ${primary} failed, trying fallback:`, error);
    }

    // Try secondary database
    try {
      const secondaryClient = await this.getClient(secondary);
      if (secondaryClient) {
        logger.info(`Using fallback database: ${secondary}`);
        return await operation(secondaryClient);
      }
    } catch (error) {
      logger.error(`Fallback database ${secondary} also failed:`, error);
    }

    return null;
  }

  private async getClient(database: string): Promise<any> {
    switch (database) {
      case 'supabase':
        return await this.getSupabaseClient();
      case 'influxdb':
        return await this.getInfluxDB();
      case 'redis':
        return await this.getRedisClient();
      case 'postgres':
        return await this.getPostgresClient();
      default:
        throw new Error(`Unknown database: ${database}`);
    }
  }

  // Initialization and Cleanup
  async initializeAll(): Promise<boolean> {
    logger.info('Initializing all database connections...');

    const results = await Promise.allSettled([
      this.initializeSupabase(),
      this.initializeInfluxDB(),
      this.initializeRedis(),
      this.initializePostgres()
    ]);

    const successful = results.filter(result =>
      result.status === 'fulfilled' && result.value === true
    ).length;

    const total = results.length;
    logger.info(`Database initialization complete: ${successful}/${total} successful`);

    // At least one database must be available
    return successful > 0;
  }

  async closeAll(): Promise<void> {
    logger.info('Closing all database connections...');

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    const closePromises: Promise<void>[] = [];

    if (this.redisClient) {
      closePromises.push(this.redisClient.quit());
    }

    if (this.postgresPool) {
      closePromises.push(this.postgresPool.end());
    }

    await Promise.allSettled(closePromises);

    this.supabaseClient = null;
    this.influxDB = null;
    this.redisClient = null;
    this.postgresPool = null;

    logger.info('All database connections closed');
  }

  // Utility Methods
  isHealthy(): boolean {
    let healthyCount = 0;
    this.connectionHealth.forEach(health => {
      if (health.connected && !health.circuitBreakerOpen) {
        healthyCount++;
      }
    });

    // At least 50% of databases should be healthy
    return healthyCount >= Math.ceil(this.connectionHealth.size / 2);
  }

  getOverallLatency(): number {
    let totalLatency = 0;
    let connectedCount = 0;

    this.connectionHealth.forEach(health => {
      if (health.connected) {
        totalLatency += health.latency;
        connectedCount++;
      }
    });

    return connectedCount > 0 ? totalLatency / connectedCount : 0;
  }

  async testAllConnections(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    const databases = ['supabase', 'influxdb', 'redis', 'postgres'];

    for (const db of databases) {
      try {
        await this.checkDatabaseHealth(db);
        const health = this.connectionHealth.get(db);
        results.set(db, health?.connected || false);
      } catch (error) {
        results.set(db, false);
      }
    }

    return results;
  }
}

// Export singleton instance
export const enhancedDatabaseManager = new EnhancedDatabaseConnectionManager();
