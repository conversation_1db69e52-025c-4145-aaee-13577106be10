import axios from 'axios';
import logger from '../utils/logger.js';

export interface NetworkConfig {
  id: string;
  name: string;
  chainId: number;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrls: string[];
  blockExplorerUrls: string[];
  dexes: string[];
  bridgeProtocols: string[];
  avgGasPrice: number; // in gwei
  avgBlockTime: number; // in seconds
  bridgeFeePercentage: number; // typical bridge fee %
}

export interface TokenContract {
  address: string;
  decimals: number;
  verified: boolean;
  liquidity?: number;
  dexes?: string[];
}

export interface MultiChainToken {
  symbol: string;
  name: string;
  coingeckoId: string;
  contracts: { [networkId: string]: TokenContract };
  totalLiquidity: number;
  avgPrice: number;
  priceVariance: number;
  lastUpdated: string;
}

export interface CrossChainOpportunity {
  id: string;
  tokenSymbol: string;
  sourceNetwork: string;
  targetNetwork: string;
  sourceDex: string;
  targetDex: string;
  sourcePrice: number;
  targetPrice: number;
  priceDifference: number;
  priceDifferencePercentage: number;
  estimatedProfit: number;
  gasCosts: {
    source: number;
    target: number;
    bridge: number;
    total: number;
  };
  bridgeFee: number;
  slippageImpact: number;
  netProfit: number;
  netProfitPercentage: number;
  confidence: number;
  riskScore: number;
  executionTime: number; // estimated time in minutes
  minTradeSize: number;
  maxTradeSize: number;
  created_at: string;
}

export class MultiChainService {
  private networks: Map<string, NetworkConfig> = new Map();
  private multiChainTokens: Map<string, MultiChainToken> = new Map();
  private priceCache: Map<string, { price: number; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 30 * 1000; // 30 seconds
  private readonly COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

  constructor() {
    this.initializeNetworks();
    this.initializeTargetTokens();
    logger.info('MultiChainService initialized with 10 networks');
  }

  private initializeNetworks(): void {
    const networkConfigs: NetworkConfig[] = [
      {
        id: 'ethereum',
        name: 'Ethereum',
        chainId: 1,
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        rpcUrls: ['https://eth-mainnet.alchemyapi.io/v2/your-api-key'],
        blockExplorerUrls: ['https://etherscan.io'],
        dexes: ['Uniswap V3', 'Uniswap V2', 'SushiSwap', 'Curve', 'Balancer'],
        bridgeProtocols: ['Stargate', 'LayerZero', 'Multichain', 'Hop'],
        avgGasPrice: 30, // gwei
        avgBlockTime: 12,
        bridgeFeePercentage: 0.06 // 0.06%
      },
      {
        id: 'bsc',
        name: 'Binance Smart Chain',
        chainId: 56,
        nativeCurrency: { name: 'BNB', symbol: 'BNB', decimals: 18 },
        rpcUrls: ['https://bsc-dataseed.binance.org/'],
        blockExplorerUrls: ['https://bscscan.com'],
        dexes: ['PancakeSwap V3', 'PancakeSwap V2', 'BiSwap', 'ApeSwap'],
        bridgeProtocols: ['Stargate', 'Multichain', 'cBridge'],
        avgGasPrice: 5,
        avgBlockTime: 3,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'polygon',
        name: 'Polygon',
        chainId: 137,
        nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
        rpcUrls: ['https://polygon-rpc.com/'],
        blockExplorerUrls: ['https://polygonscan.com'],
        dexes: ['QuickSwap', 'SushiSwap', 'Curve', 'Balancer'],
        bridgeProtocols: ['Stargate', 'Hop', 'Multichain'],
        avgGasPrice: 30,
        avgBlockTime: 2,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'solana',
        name: 'Solana',
        chainId: 0, // Solana doesn't use EVM chain IDs
        nativeCurrency: { name: 'Solana', symbol: 'SOL', decimals: 9 },
        rpcUrls: ['https://api.mainnet-beta.solana.com'],
        blockExplorerUrls: ['https://solscan.io'],
        dexes: ['Jupiter', 'Raydium', 'Orca', 'Serum'],
        bridgeProtocols: ['Wormhole', 'Allbridge'],
        avgGasPrice: 0.000005, // SOL
        avgBlockTime: 0.4,
        bridgeFeePercentage: 0.1
      },
      {
        id: 'avalanche',
        name: 'Avalanche',
        chainId: 43114,
        nativeCurrency: { name: 'AVAX', symbol: 'AVAX', decimals: 18 },
        rpcUrls: ['https://api.avax.network/ext/bc/C/rpc'],
        blockExplorerUrls: ['https://snowtrace.io'],
        dexes: ['Trader Joe', 'Pangolin', 'SushiSwap'],
        bridgeProtocols: ['Stargate', 'Multichain', 'Avalanche Bridge'],
        avgGasPrice: 25,
        avgBlockTime: 2,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'arbitrum',
        name: 'Arbitrum One',
        chainId: 42161,
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        rpcUrls: ['https://arb1.arbitrum.io/rpc'],
        blockExplorerUrls: ['https://arbiscan.io'],
        dexes: ['Uniswap V3', 'SushiSwap', 'Curve', 'Balancer'],
        bridgeProtocols: ['Stargate', 'Hop', 'Arbitrum Bridge'],
        avgGasPrice: 0.1,
        avgBlockTime: 0.25,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'optimism',
        name: 'Optimism',
        chainId: 10,
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        rpcUrls: ['https://mainnet.optimism.io'],
        blockExplorerUrls: ['https://optimistic.etherscan.io'],
        dexes: ['Uniswap V3', 'Curve', 'Velodrome'],
        bridgeProtocols: ['Stargate', 'Hop', 'Optimism Bridge'],
        avgGasPrice: 0.001,
        avgBlockTime: 2,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'base',
        name: 'Base',
        chainId: 8453,
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        rpcUrls: ['https://mainnet.base.org'],
        blockExplorerUrls: ['https://basescan.org'],
        dexes: ['Uniswap V3', 'SushiSwap', 'Curve'],
        bridgeProtocols: ['Stargate', 'Base Bridge'],
        avgGasPrice: 0.001,
        avgBlockTime: 2,
        bridgeFeePercentage: 0.05
      },
      {
        id: 'fantom',
        name: 'Fantom',
        chainId: 250,
        nativeCurrency: { name: 'Fantom', symbol: 'FTM', decimals: 18 },
        rpcUrls: ['https://rpc.ftm.tools/'],
        blockExplorerUrls: ['https://ftmscan.com'],
        dexes: ['SpookySwap', 'SpiritSwap', 'Curve'],
        bridgeProtocols: ['Multichain', 'Stargate'],
        avgGasPrice: 20,
        avgBlockTime: 1,
        bridgeFeePercentage: 0.08
      },
      {
        id: 'sui',
        name: 'Sui',
        chainId: 0, // Sui doesn't use EVM chain IDs
        nativeCurrency: { name: 'Sui', symbol: 'SUI', decimals: 9 },
        rpcUrls: ['https://fullnode.mainnet.sui.io:443'],
        blockExplorerUrls: ['https://suiexplorer.com'],
        dexes: ['Cetus', 'Turbos', 'DeepBook'],
        bridgeProtocols: ['Wormhole', 'LayerZero'],
        avgGasPrice: 0.001, // SUI
        avgBlockTime: 0.4,
        bridgeFeePercentage: 0.1
      }
    ];

    networkConfigs.forEach(config => {
      this.networks.set(config.id, config);
    });
  }

  private initializeTargetTokens(): void {
    const targetTokens = [
      {
        symbol: 'BTC',
        name: 'Bitcoin',
        coingeckoId: 'bitcoin',
        variants: ['BTC', 'WBTC']
      },
      {
        symbol: 'ETH',
        name: 'Ethereum',
        coingeckoId: 'ethereum',
        variants: ['ETH', 'WETH', 'stETH', 'wstETH']
      },
      {
        symbol: 'USDT',
        name: 'Tether',
        coingeckoId: 'tether',
        variants: ['USDT']
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        coingeckoId: 'usd-coin',
        variants: ['USDC']
      },
      {
        symbol: 'BUSD',
        name: 'Binance USD',
        coingeckoId: 'binance-usd',
        variants: ['BUSD']
      },
      {
        symbol: 'BNB',
        name: 'BNB',
        coingeckoId: 'binancecoin',
        variants: ['BNB']
      },
      {
        symbol: 'SOL',
        name: 'Solana',
        coingeckoId: 'solana',
        variants: ['SOL', 'WSOL']
      },
      {
        symbol: 'MATIC',
        name: 'Polygon',
        coingeckoId: 'matic-network',
        variants: ['MATIC', 'WMATIC']
      },
      {
        symbol: 'ADA',
        name: 'Cardano',
        coingeckoId: 'cardano',
        variants: ['ADA']
      },
      {
        symbol: 'SUI',
        name: 'Sui',
        coingeckoId: 'sui',
        variants: ['SUI']
      },
      {
        symbol: 'AVAX',
        name: 'Avalanche',
        coingeckoId: 'avalanche-2',
        variants: ['AVAX']
      },
      {
        symbol: 'HYPE',
        name: 'Hyperliquid',
        coingeckoId: 'hyperliquid',
        variants: ['HYPE']
      }
    ];

    // Initialize empty multi-chain tokens
    targetTokens.forEach(token => {
      this.multiChainTokens.set(token.symbol, {
        symbol: token.symbol,
        name: token.name,
        coingeckoId: token.coingeckoId,
        contracts: {},
        totalLiquidity: 0,
        avgPrice: 0,
        priceVariance: 0,
        lastUpdated: new Date().toISOString()
      });
    });
  }

  /**
   * Get all supported networks
   */
  getSupportedNetworks(): NetworkConfig[] {
    return Array.from(this.networks.values());
  }

  /**
   * Get network configuration by ID
   */
  getNetwork(networkId: string): NetworkConfig | null {
    return this.networks.get(networkId) || null;
  }

  /**
   * Get all target tokens for cross-chain arbitrage
   */
  getTargetTokens(): MultiChainToken[] {
    return Array.from(this.multiChainTokens.values());
  }

  /**
   * Calculate gas costs for a network
   */
  calculateGasCosts(networkId: string, gasUnits: number = 150000): number {
    const network = this.networks.get(networkId);
    if (!network) return 0;

    // Convert gas price to USD (simplified calculation)
    const gasCostInNative = (gasUnits * network.avgGasPrice) / 1e9;
    
    // Rough USD conversion (would need real price feeds)
    const nativeToUsdRate = this.getNativeTokenPrice(networkId);
    return gasCostInNative * nativeToUsdRate;
  }

  /**
   * Get native token price in USD (simplified)
   */
  private getNativeTokenPrice(networkId: string): number {
    const priceMap: { [key: string]: number } = {
      'ethereum': 2500,
      'bsc': 600,
      'polygon': 0.8,
      'solana': 155,
      'avalanche': 35,
      'arbitrum': 2500,
      'optimism': 2500,
      'base': 2500,
      'fantom': 0.5,
      'sui': 3.3
    };
    return priceMap[networkId] || 1;
  }

  /**
   * Calculate bridge fees
   */
  calculateBridgeFee(networkId: string, amount: number): number {
    const network = this.networks.get(networkId);
    if (!network) return 0;
    
    return amount * (network.bridgeFeePercentage / 100);
  }

  /**
   * Estimate execution time for cross-chain trade
   */
  estimateExecutionTime(sourceNetwork: string, targetNetwork: string): number {
    const source = this.networks.get(sourceNetwork);
    const target = this.networks.get(targetNetwork);
    
    if (!source || !target) return 30; // Default 30 minutes
    
    // Base time + bridge time + confirmation time
    const baseTime = 2; // 2 minutes base
    const bridgeTime = 10; // 10 minutes average bridge time
    const confirmationTime = Math.max(source.avgBlockTime, target.avgBlockTime) / 60;
    
    return baseTime + bridgeTime + confirmationTime;
  }
}
