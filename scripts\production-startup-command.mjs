#!/usr/bin/env node

/**
 * MEV Arbitrage Bot - Production Startup Command
 * =============================================
 * 
 * Master command that orchestrates the complete system:
 * 1. Environment-specific configuration loading
 * 2. Database initialization and validation
 * 3. Service dependency management
 * 4. Complete arbitrage workflow validation
 * 5. Real-time monitoring and health checks
 * 6. Production-ready error handling and rollback
 * 7. Performance validation against system targets
 * 8. Graceful shutdown and cleanup
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';
import { config as dotenvConfig } from 'dotenv';
import DatabaseInitializationManager from './database-initialization-manager.mjs';
import ServiceDependencyManager from './service-dependency-manager.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Load environment configuration
dotenvConfig({ path: join(rootDir, '.env') });

// Environment-specific configurations
const ENVIRONMENT_CONFIGS = {
  development: {
    logLevel: 'debug',
    enableDetailedLogging: true,
    enablePerformanceMonitoring: true,
    enableHealthChecks: true,
    maxStartupTime: 300000, // 5 minutes
    performanceTargets: {
      maxLatency: 2000,
      targetUptime: 95,
      maxMemoryUsage: 1024 * 1024 * 1024 // 1GB
    }
  },
  
  production: {
    logLevel: 'info',
    enableDetailedLogging: false,
    enablePerformanceMonitoring: true,
    enableHealthChecks: true,
    maxStartupTime: 180000, // 3 minutes
    performanceTargets: {
      maxLatency: 1000,
      targetUptime: 99,
      maxMemoryUsage: 500 * 1024 * 1024 // 500MB
    }
  },
  
  testing: {
    logLevel: 'warn',
    enableDetailedLogging: false,
    enablePerformanceMonitoring: false,
    enableHealthChecks: false,
    maxStartupTime: 120000, // 2 minutes
    performanceTargets: {
      maxLatency: 5000,
      targetUptime: 90,
      maxMemoryUsage: 2048 * 1024 * 1024 // 2GB
    }
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class ProductionStartupCommand {
  constructor() {
    this.environment = process.env.NODE_ENV || 'development';
    this.config = ENVIRONMENT_CONFIGS[this.environment];
    this.startTime = Date.now();
    this.systemState = {
      phase: 'initialization',
      databases: {},
      services: {},
      errors: [],
      warnings: [],
      performance: {}
    };
    
    this.databaseManager = null;
    this.serviceManager = null;
    
    this.setupSignalHandlers();
  }
  
  setupSignalHandlers() {
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('uncaughtException', (error) => this.handleCriticalError('uncaughtException', error));
    process.on('unhandledRejection', (reason) => this.handleCriticalError('unhandledRejection', reason));
  }
  
  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const levelColors = {
      debug: colors.cyan,
      info: colors.blue,
      warn: colors.yellow,
      error: colors.red,
      success: colors.green
    };
    
    const color = levelColors[level] || colors.reset;
    console.log(`${color}[${timestamp}] [${level.toUpperCase()}] ${message}${colors.reset}`);
  }
  
  logPhase(phase) {
    this.systemState.phase = phase;
    this.log(`\n🔄 PHASE: ${phase}`, 'info');
    this.log('=' .repeat(60), 'info');
  }
  
  async startProductionSystem() {
    try {
      this.log(`${colors.bright}🚀 MEV ARBITRAGE BOT - PRODUCTION STARTUP${colors.reset}`);
      this.log('=' .repeat(70));
      this.log(`Environment: ${this.environment.toUpperCase()}`);
      this.log(`Max Startup Time: ${this.config.maxStartupTime / 1000}s`);
      this.log(`Performance Targets: <${this.config.performanceTargets.maxLatency}ms latency, >${this.config.performanceTargets.targetUptime}% uptime`);
      this.log('=' .repeat(70));
      
      // Phase 1: Environment Validation
      await this.validateEnvironment();
      
      // Phase 2: Database Initialization
      await this.initializeDatabases();
      
      // Phase 3: Service Startup
      await this.startServices();
      
      // Phase 4: System Validation
      await this.validateSystem();
      
      // Phase 5: Health Monitoring Setup
      await this.setupHealthMonitoring();
      
      // Phase 6: Final Validation
      await this.performFinalValidation();
      
      const totalTime = Date.now() - this.startTime;
      this.log(`\n🎉 PRODUCTION SYSTEM STARTUP COMPLETED IN ${totalTime}ms`, 'success');
      this.displaySystemStatus();
      
      // Keep the process running
      this.log('System is now operational and monitoring for opportunities...', 'info');
      
    } catch (error) {
      await this.handleStartupFailure(error);
    }
  }
  
  async validateEnvironment() {
    this.logPhase('Environment Validation');
    
    // Check required environment variables
    const requiredVars = [
      'SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY',
      'REDIS_URL',
      'DATABASE_URL',
      'INFLUXDB_URL'
    ];
    
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
    
    // Validate configuration files
    const configFiles = ['.env', 'package.json', 'docker-compose.yml'];
    for (const file of configFiles) {
      const filePath = join(rootDir, file);
      if (!existsSync(filePath)) {
        this.systemState.warnings.push(`Configuration file missing: ${file}`);
        this.log(`⚠️  Configuration file missing: ${file}`, 'warn');
      }
    }
    
    // Validate Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 18) {
      throw new Error(`Node.js version ${nodeVersion} is not supported. Minimum required: 18.x`);
    }
    
    this.log('✅ Environment validation completed', 'success');
  }
  
  async initializeDatabases() {
    this.logPhase('Database Initialization');
    
    this.databaseManager = new DatabaseInitializationManager();
    
    try {
      const initResult = await this.databaseManager.initializeAllDatabases();
      this.systemState.databases = initResult;
      
      if (!initResult.overallSuccess) {
        if (initResult.criticalFailures > 0) {
          throw new Error(`${initResult.criticalFailures} critical database(s) failed to initialize`);
        } else {
          this.log(`⚠️  ${initResult.warnings} database warning(s) - system may have reduced functionality`, 'warn');
        }
      }
      
      this.log('✅ Database initialization completed', 'success');
      
    } catch (error) {
      throw new Error(`Database initialization failed: ${error.message}`);
    }
  }
  
  async startServices() {
    this.logPhase('Service Startup');
    
    this.serviceManager = new ServiceDependencyManager();
    
    try {
      const serviceStatus = await this.serviceManager.startAllServices();
      this.systemState.services = serviceStatus;
      
      if (serviceStatus.failed > 0) {
        const criticalFailures = Object.values(serviceStatus.services)
          .filter(service => !service.status === 'running' && service.critical).length;
        
        if (criticalFailures > 0) {
          throw new Error(`${criticalFailures} critical service(s) failed to start`);
        } else {
          this.log(`⚠️  ${serviceStatus.failed} non-critical service(s) failed - system may have reduced functionality`, 'warn');
        }
      }
      
      this.log('✅ Service startup completed', 'success');
      
    } catch (error) {
      throw new Error(`Service startup failed: ${error.message}`);
    }
  }
  
  async validateSystem() {
    this.logPhase('System Validation');
    
    try {
      // Test API endpoints
      const apiTests = [
        { endpoint: '/api/health', name: 'Health Check' },
        { endpoint: '/api/opportunities', name: 'Opportunities' },
        { endpoint: '/api/analytics', name: 'Analytics' },
        { endpoint: '/api/system/status', name: 'System Status' }
      ];
      
      for (const test of apiTests) {
        try {
          const response = await fetch(`http://localhost:3001${test.endpoint}`);
          if (response.ok) {
            this.log(`✅ ${test.name} API endpoint operational`, 'success');
          } else {
            this.log(`⚠️  ${test.name} API endpoint returned ${response.status}`, 'warn');
          }
        } catch (error) {
          this.log(`❌ ${test.name} API endpoint failed: ${error.message}`, 'error');
        }
      }
      
      // Test WebSocket connection
      try {
        const ws = new WebSocket('ws://localhost:3001');
        await new Promise((resolve, reject) => {
          ws.on('open', () => {
            this.log('✅ WebSocket connection operational', 'success');
            ws.close();
            resolve();
          });
          ws.on('error', reject);
          setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
        });
      } catch (error) {
        this.log(`⚠️  WebSocket connection failed: ${error.message}`, 'warn');
      }
      
      this.log('✅ System validation completed', 'success');
      
    } catch (error) {
      throw new Error(`System validation failed: ${error.message}`);
    }
  }
  
  async setupHealthMonitoring() {
    this.logPhase('Health Monitoring Setup');
    
    if (this.config.enableHealthChecks) {
      // Setup periodic health checks
      setInterval(async () => {
        await this.performHealthCheck();
      }, 30000); // Every 30 seconds
      
      this.log('✅ Health monitoring enabled (30s intervals)', 'success');
    } else {
      this.log('⚠️  Health monitoring disabled for this environment', 'warn');
    }
    
    if (this.config.enablePerformanceMonitoring) {
      // Setup performance monitoring
      setInterval(async () => {
        await this.monitorPerformance();
      }, 60000); // Every minute
      
      this.log('✅ Performance monitoring enabled (60s intervals)', 'success');
    }
  }
  
  async performFinalValidation() {
    this.logPhase('Final System Validation');
    
    // Validate against performance targets
    const memoryUsage = process.memoryUsage();
    const heapUsed = memoryUsage.heapUsed;
    
    if (heapUsed > this.config.performanceTargets.maxMemoryUsage) {
      this.log(`⚠️  Memory usage ${Math.round(heapUsed / 1024 / 1024)}MB exceeds target ${Math.round(this.config.performanceTargets.maxMemoryUsage / 1024 / 1024)}MB`, 'warn');
    } else {
      this.log(`✅ Memory usage within target: ${Math.round(heapUsed / 1024 / 1024)}MB`, 'success');
    }
    
    // Validate startup time
    const totalStartupTime = Date.now() - this.startTime;
    if (totalStartupTime > this.config.maxStartupTime) {
      this.log(`⚠️  Startup time ${totalStartupTime}ms exceeds target ${this.config.maxStartupTime}ms`, 'warn');
    } else {
      this.log(`✅ Startup time within target: ${totalStartupTime}ms`, 'success');
    }
    
    this.log('✅ Final validation completed', 'success');
  }
  
  async performHealthCheck() {
    try {
      const response = await fetch('http://localhost:3001/api/health');
      if (!response.ok) {
        this.log(`⚠️  Health check failed: HTTP ${response.status}`, 'warn');
      }
    } catch (error) {
      this.log(`❌ Health check error: ${error.message}`, 'error');
    }
  }
  
  async monitorPerformance() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    this.systemState.performance = {
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: process.uptime()
    };
    
    // Log performance warnings
    if (memoryUsage.heapUsed > this.config.performanceTargets.maxMemoryUsage) {
      this.log(`⚠️  High memory usage: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`, 'warn');
    }
  }
  
  displaySystemStatus() {
    this.log('\n📊 SYSTEM STATUS SUMMARY:', 'info');
    this.log('=' .repeat(50), 'info');
    
    // Database status
    if (this.systemState.databases.overallSuccess) {
      this.log('🗄️  Databases: ✅ All operational', 'success');
    } else {
      this.log(`🗄️  Databases: ⚠️  ${this.systemState.databases.criticalFailures} critical failures`, 'warn');
    }
    
    // Service status
    if (this.systemState.services.running > 0) {
      this.log(`⚙️  Services: ✅ ${this.systemState.services.running}/${this.systemState.services.totalServices} running`, 'success');
    }
    
    // Performance status
    const memoryMB = Math.round(process.memoryUsage().heapUsed / 1024 / 1024);
    this.log(`📈 Performance: Memory ${memoryMB}MB, Uptime ${Math.round(process.uptime())}s`, 'info');
    
    // Access URLs
    this.log('\n🌐 ACCESS URLS:', 'info');
    this.log('Frontend: http://localhost:3000', 'info');
    this.log('Backend API: http://localhost:3001', 'info');
    this.log('Health Check: http://localhost:3001/api/health', 'info');
    
    this.log('\n🎯 SYSTEM IS READY FOR LIVE TRADING OPERATIONS!', 'success');
  }
  
  async handleStartupFailure(error) {
    this.log(`💥 STARTUP FAILURE: ${error.message}`, 'error');
    
    // Attempt graceful rollback
    await this.gracefulShutdown('startup_failure');
    
    process.exit(1);
  }
  
  async handleCriticalError(type, error) {
    this.log(`💥 CRITICAL ERROR (${type}): ${error.message || error}`, 'error');
    
    // Attempt graceful shutdown
    await this.gracefulShutdown('critical_error');
    
    process.exit(1);
  }
  
  async gracefulShutdown(reason) {
    this.log(`🛑 Initiating graceful shutdown (reason: ${reason})...`, 'warn');
    
    try {
      // Shutdown services
      if (this.serviceManager) {
        await this.serviceManager.shutdown();
      }
      
      // Stop database connections
      if (this.databaseManager) {
        // Add database cleanup if needed
      }
      
      this.log('✅ Graceful shutdown completed', 'success');
      
    } catch (error) {
      this.log(`❌ Shutdown error: ${error.message}`, 'error');
    }
  }
}

// Main execution
async function main() {
  const startupCommand = new ProductionStartupCommand();
  await startupCommand.startProductionSystem();
}

// Handle direct execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('💥 Production startup failed:', error);
    process.exit(1);
  });
}

export default ProductionStartupCommand;
