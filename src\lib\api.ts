import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, ArbitrageOpportunity, Trade, Token, NetworkStatus, PerformanceMetrics, MLStrategy, LearningEvent, SystemHealth, FlashLoanQuote, ExecutionQueue } from '@/types';

class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for performance monitoring
    this.client.interceptors.request.use(
      (config) => {
        config.metadata = { startTime: Date.now() };
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for performance monitoring and error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        const endTime = Date.now();
        const startTime = response.config.metadata?.startTime || endTime;
        const duration = endTime - startTime;
        
        // Log slow requests
        if (duration > 1000) {
          console.warn(`Slow API request: ${response.config.url} took ${duration}ms`);
        }
        
        return response;
      },
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Generic request method with error handling
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.client.request<ApiResponse<T>>(config);
      
      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'API request failed');
      }
    } catch (error: any) {
      if (error.response?.status === 503) {
        throw new Error('Backend services are starting up. Please wait...');
      }
      throw error;
    }
  }

  // Health check
  async getHealth(): Promise<{ status: string; timestamp: number }> {
    const response = await this.client.get('/health');
    return response.data;
  }

  // Opportunities
  async getOpportunities(params?: { limit?: number; offset?: number }): Promise<ArbitrageOpportunity[]> {
    return this.request<ArbitrageOpportunity[]>({
      method: 'GET',
      url: '/api/opportunities',
      params,
    });
  }

  async getOpportunityById(id: string): Promise<ArbitrageOpportunity> {
    return this.request<ArbitrageOpportunity>({
      method: 'GET',
      url: `/api/opportunities/${id}`,
    });
  }

  // Trades
  async getTrades(params?: { limit?: number; offset?: number }): Promise<Trade[]> {
    return this.request<Trade[]>({
      method: 'GET',
      url: '/api/trades',
      params,
    });
  }

  async getTradeById(id: string): Promise<Trade> {
    return this.request<Trade>({
      method: 'GET',
      url: `/api/trades/${id}`,
    });
  }

  // Tokens
  async getTokens(params?: { limit?: number; offset?: number; network?: string }): Promise<Token[]> {
    return this.request<Token[]>({
      method: 'GET',
      url: '/api/tokens',
      params,
    });
  }

  async getTokenById(id: string): Promise<Token> {
    return this.request<Token>({
      method: 'GET',
      url: `/api/tokens/${id}`,
    });
  }

  // Analytics
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    return this.request<PerformanceMetrics>({
      method: 'GET',
      url: '/api/analytics/performance',
    });
  }

  async getHistoricalData(params: { 
    metric: string; 
    timeframe: string; 
    network?: string 
  }): Promise<any[]> {
    return this.request<any[]>({
      method: 'GET',
      url: '/api/analytics/historical',
      params,
    });
  }

  // ML and Strategy
  async getMLStrategies(params?: { limit?: number; offset?: number }): Promise<MLStrategy[]> {
    return this.request<MLStrategy[]>({
      method: 'GET',
      url: '/api/ml/strategy-performance',
      params,
    });
  }

  async getLearningEvents(params?: { limit?: number; offset?: number }): Promise<LearningEvent[]> {
    return this.request<LearningEvent[]>({
      method: 'GET',
      url: '/api/ml/learning-events',
      params,
    });
  }

  async getMLStats(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/api/ml/learning-stats',
    });
  }

  // System Health
  async getSystemHealth(): Promise<SystemHealth> {
    return this.request<SystemHealth>({
      method: 'GET',
      url: '/api/system/health',
    });
  }

  async getNetworkStatus(): Promise<NetworkStatus[]> {
    return this.request<NetworkStatus[]>({
      method: 'GET',
      url: '/api/system/networks',
    });
  }

  // Flash Loans
  async getFlashLoanQuotes(params: { 
    asset: string; 
    amount: string; 
    network: string 
  }): Promise<FlashLoanQuote[]> {
    return this.request<FlashLoanQuote[]>({
      method: 'GET',
      url: '/api/flash-loans/quotes',
      params,
    });
  }

  // Execution Queue
  async getExecutionQueue(): Promise<ExecutionQueue[]> {
    return this.request<ExecutionQueue[]>({
      method: 'GET',
      url: '/api/execution/queue',
    });
  }

  // MEV Protection
  async getMEVProtectionStatus(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/api/mev/protection-status',
    });
  }

  // Real-time data endpoints
  async getRealtimeUpdate(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/api/realtime/update',
    });
  }

  // Database health
  async getDatabaseHealth(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/api/system/database-health',
    });
  }

  // Performance monitoring
  async getPerformanceStats(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/api/system/performance',
    });
  }

  // Cache statistics
  async getCacheStats(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/api/system/cache-stats',
    });
  }

  // Multi-chain data
  async getMultiChainData(): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/api/multi-chain/status',
    });
  }

  // Price data
  async getPriceData(params?: { 
    tokens?: string[]; 
    networks?: string[]; 
    timeframe?: string 
  }): Promise<any> {
    return this.request<any>({
      method: 'GET',
      url: '/api/prices',
      params,
    });
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export individual methods for easier use
export const {
  getHealth,
  getOpportunities,
  getOpportunityById,
  getTrades,
  getTradeById,
  getTokens,
  getTokenById,
  getPerformanceMetrics,
  getHistoricalData,
  getMLStrategies,
  getLearningEvents,
  getMLStats,
  getSystemHealth,
  getNetworkStatus,
  getFlashLoanQuotes,
  getExecutionQueue,
  getMEVProtectionStatus,
  getRealtimeUpdate,
  getDatabaseHealth,
  getPerformanceStats,
  getCacheStats,
  getMultiChainData,
  getPriceData,
} = apiClient;
