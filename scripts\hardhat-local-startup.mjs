#!/usr/bin/env node

/**
 * MEV Arbitrage Bot - Hardhat Local Testnet Startup Script
 * ========================================================
 * 
 * Complete system initialization for local development and testing:
 * 1. Enable mainnet forking in Hardhat configuration
 * 2. Start Hardhat local blockchain network with forking
 * 3. Initialize all database services (Redis, PostgreSQL, InfluxDB)
 * 4. Deploy smart contracts to local network
 * 5. Start all enhanced backend services
 * 6. Launch frontend dashboard with WebSocket connections
 * 7. Run comprehensive system health checks
 * 8. Provide testing environment for MEV arbitrage operations
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { readFile, writeFile } from 'fs/promises';
import { EventEmitter } from 'events';
import path from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Color utilities for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// System configuration for local development
const localConfig = {
  hardhat: {
    port: 8545,
    chainId: 1337,
    forkingEnabled: true,
    forkBlockNumber: 18500000,
    maxStartupTime: 60000
  },
  databases: {
    redis: { name: 'Redis', container: 'mev-redis', port: 6379, maxWait: 30000 },
    postgres: { name: 'PostgreSQL', container: 'mev-postgres', port: 5432, maxWait: 45000 },
    influxdb: { name: 'InfluxDB', container: 'mev-influxdb', port: 8086, maxWait: 60000 }
  },
  services: {
    backend: { port: 3001, maxStartupTime: 120000 },
    frontend: { port: 3000, maxStartupTime: 60000 },
    websocket: { port: 3002, maxStartupTime: 30000 }
  },
  contracts: {
    deploymentTimeout: 120000,
    gasLimit: 30000000,
    gasPrice: 1000000000 // 1 gwei
  },
  environment: 'development'
};

// Global state tracking
let startupState = {
  phase: null,
  startTime: Date.now(),
  processes: new Map(),
  services: {},
  databases: {},
  contracts: {},
  errors: [],
  warnings: [],
  hardhatReady: false,
  databasesReady: false,
  contractsDeployed: false,
  servicesStarted: false
};

// Event emitter for inter-process communication
const systemEvents = new EventEmitter();

// Logging utilities
function log(message) {
  const timestamp = new Date().toISOString();
  console.log(`${colors.cyan}[${timestamp}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  log(`${colors.red}❌ ${message}${colors.reset}`);
  startupState.errors.push(message);
}

function logWarning(message) {
  log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
  startupState.warnings.push(message);
}

function logInfo(message) {
  log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
}

function logPhase(phase) {
  startupState.phase = phase;
  log(`${colors.bright}${colors.magenta}🔄 Phase: ${phase}${colors.reset}`);
}

/**
 * Phase 1: Environment Validation
 */
async function validateEnvironment() {
  logPhase('Environment Validation');
  
  try {
    // Check Node.js version
    const nodeVersion = process.version;
    logInfo(`Node.js version: ${nodeVersion}`);
    
    // Check if required files exist
    const requiredFiles = [
      'hardhat.config.cjs',
      'package.json',
      'docker-compose.yml',
      '.env'
    ];
    
    for (const file of requiredFiles) {
      try {
        await readFile(path.join(rootDir, file));
        logSuccess(`Found required file: ${file}`);
      } catch (error) {
        if (file === '.env') {
          logWarning(`Missing ${file} - using default configuration`);
        } else {
          logError(`Missing required file: ${file}`);
          return false;
        }
      }
    }
    
    // Check environment variables
    const requiredEnvVars = ['MAINNET_RPC_URL'];
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missingEnvVars.length > 0) {
      logWarning(`Missing environment variables: ${missingEnvVars.join(', ')}`);
      logInfo('Using default RPC URL for mainnet forking');
    }
    
    logSuccess('Environment validation completed');
    return true;
  } catch (error) {
    logError(`Environment validation failed: ${error.message}`);
    return false;
  }
}

/**
 * Phase 2: Enable Mainnet Forking in Hardhat Config
 */
async function enableMainnetForking() {
  logPhase('Enabling Mainnet Forking');
  
  try {
    const configPath = path.join(rootDir, 'hardhat.config.cjs');
    const configContent = await readFile(configPath, 'utf-8');
    
    // Check if forking is already enabled
    if (configContent.includes('enabled: true')) {
      logInfo('Mainnet forking already enabled in hardhat.config.cjs');
      return true;
    }
    
    // Enable forking by replacing 'enabled: false' with 'enabled: true'
    const modifiedConfig = configContent.replace(
      /enabled:\s*false/g,
      'enabled: true'
    );
    
    // Backup original config
    await writeFile(`${configPath}.backup`, configContent);
    logInfo('Created backup of hardhat.config.cjs');
    
    // Write modified config
    await writeFile(configPath, modifiedConfig);
    logSuccess('Mainnet forking enabled in hardhat.config.cjs');
    
    return true;
  } catch (error) {
    logError(`Failed to enable mainnet forking: ${error.message}`);
    return false;
  }
}

/**
 * Phase 3: Start Database Services
 */
async function startDatabaseServices() {
  logPhase('Starting Database Services');
  
  try {
    // Check if Docker is available
    await execAsync('docker --version');
    logSuccess('Docker is available');
    
    // Start database containers
    logInfo('Starting database containers...');
    await execAsync('docker-compose up -d redis postgres influxdb', { cwd: rootDir });
    logSuccess('Database containers started');
    
    // Wait for services to be healthy
    for (const [dbId, dbConfig] of Object.entries(localConfig.databases)) {
      logInfo(`Waiting for ${dbConfig.name} to be ready...`);
      
      const startTime = Date.now();
      let isReady = false;
      
      while (!isReady && (Date.now() - startTime) < dbConfig.maxWait) {
        try {
          const { stdout } = await execAsync(`docker exec ${dbConfig.container} echo "ready"`);
          if (stdout.trim() === 'ready') {
            isReady = true;
            logSuccess(`${dbConfig.name} is ready`);
            startupState.databases[dbId] = { status: 'ready', port: dbConfig.port };
          }
        } catch (error) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
      
      if (!isReady) {
        logError(`${dbConfig.name} failed to start within ${dbConfig.maxWait}ms`);
        return false;
      }
    }
    
    startupState.databasesReady = true;
    logSuccess('All database services are ready');
    return true;
  } catch (error) {
    logError(`Failed to start database services: ${error.message}`);
    return false;
  }
}

/**
 * Phase 4: Start Hardhat Network
 */
async function startHardhatNetwork() {
  logPhase('Starting Hardhat Network with Mainnet Forking');
  
  try {
    const rpcUrl = process.env.MAINNET_RPC_URL || 'https://eth-mainnet.alchemyapi.io/v2/demo';
    
    logInfo(`Starting Hardhat node with forking from: ${rpcUrl}`);
    logInfo(`Fork block number: ${localConfig.hardhat.forkBlockNumber}`);
    
    // Start Hardhat node with forking
    const hardhatProcess = spawn('npx', [
      'hardhat', 'node',
      '--fork', rpcUrl,
      '--fork-block-number', localConfig.hardhat.forkBlockNumber.toString(),
      '--port', localConfig.hardhat.port.toString()
    ], {
      cwd: rootDir,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    startupState.processes.set('hardhat', hardhatProcess);
    
    let output = '';
    let networkReady = false;
    
    // Monitor Hardhat output
    hardhatProcess.stdout.on('data', (data) => {
      output += data.toString();
      
      // Check if network is ready
      if (output.includes('Started HTTP and WebSocket JSON-RPC server')) {
        networkReady = true;
        startupState.hardhatReady = true;
        logSuccess(`Hardhat network started on localhost:${localConfig.hardhat.port}`);
        logSuccess(`Chain ID: ${localConfig.hardhat.chainId}`);
        logSuccess('Mainnet forking enabled');
        systemEvents.emit('hardhat:ready');
      }
      
      // Log account information
      if (output.includes('Account #')) {
        const lines = output.split('\n');
        const accountLines = lines.filter(line => line.includes('Account #') || line.includes('Private Key:'));
        if (accountLines.length > 0) {
          logInfo('Test accounts created:');
          accountLines.slice(0, 6).forEach(line => logInfo(`  ${line.trim()}`));
        }
      }
    });
    
    hardhatProcess.stderr.on('data', (data) => {
      const errorOutput = data.toString();
      if (!errorOutput.includes('Warning')) {
        logError(`Hardhat error: ${errorOutput}`);
      }
    });
    
    hardhatProcess.on('exit', (code) => {
      if (code !== 0) {
        logError(`Hardhat process exited with code ${code}`);
        startupState.hardhatReady = false;
      }
    });
    
    // Wait for network to be ready
    const startTime = Date.now();
    while (!networkReady && (Date.now() - startTime) < localConfig.hardhat.maxStartupTime) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    if (!networkReady) {
      logError(`Hardhat network failed to start within ${localConfig.hardhat.maxStartupTime}ms`);
      return false;
    }
    
    return true;
  } catch (error) {
    logError(`Failed to start Hardhat network: ${error.message}`);
    return false;
  }
}

/**
 * Phase 5: Deploy Smart Contracts
 */
async function deploySmartContracts() {
  logPhase('Deploying Smart Contracts to Local Network');

  try {
    // Wait for Hardhat network to be ready
    if (!startupState.hardhatReady) {
      logError('Hardhat network is not ready for contract deployment');
      return false;
    }

    logInfo('Compiling smart contracts...');
    await execAsync('npx hardhat compile', { cwd: rootDir });
    logSuccess('Smart contracts compiled successfully');

    logInfo('Deploying contracts to local network...');
    const deployResult = await execAsync('npx hardhat run scripts/deploy.ts --network localhost', {
      cwd: rootDir,
      timeout: localConfig.contracts.deploymentTimeout
    });

    // Parse deployment output for contract addresses
    const deployOutput = deployResult.stdout;
    logInfo('Deployment output:');
    deployOutput.split('\n').forEach(line => {
      if (line.trim()) {
        logInfo(`  ${line.trim()}`);
      }
    });

    // Extract contract addresses (simplified parsing)
    const addressRegex = /deployed to:\s*(0x[a-fA-F0-9]{40})/g;
    const addresses = [];
    let match;
    while ((match = addressRegex.exec(deployOutput)) !== null) {
      addresses.push(match[1]);
    }

    if (addresses.length >= 3) {
      startupState.contracts = {
        TokenDiscovery: addresses[0],
        LiquidityChecker: addresses[1],
        ArbitrageExecutor: addresses[2],
        deploymentBlock: 'latest'
      };

      logSuccess(`TokenDiscovery deployed at: ${addresses[0]}`);
      logSuccess(`LiquidityChecker deployed at: ${addresses[1]}`);
      logSuccess(`ArbitrageExecutor deployed at: ${addresses[2]}`);

      startupState.contractsDeployed = true;
      systemEvents.emit('contracts:deployed', startupState.contracts);
      return true;
    } else {
      logError('Failed to extract contract addresses from deployment output');
      return false;
    }
  } catch (error) {
    logError(`Contract deployment failed: ${error.message}`);
    return false;
  }
}

/**
 * Phase 6: Start Backend Services
 */
async function startBackendServices() {
  logPhase('Starting Enhanced Backend Services');

  try {
    // Set environment variables for local development
    process.env.NODE_ENV = 'development';
    process.env.HARDHAT_NETWORK = 'localhost';
    process.env.HARDHAT_RPC_URL = `http://localhost:${localConfig.hardhat.port}`;
    process.env.REDIS_URL = 'redis://localhost:6379';
    process.env.DATABASE_URL = 'postgresql://mev_user:mev_password@localhost:5432/mev_arbitrage_bot';
    process.env.INFLUXDB_URL = 'http://localhost:8086';

    // Add contract addresses to environment
    if (startupState.contractsDeployed) {
      process.env.ARBITRAGE_EXECUTOR_ADDRESS = startupState.contracts.ArbitrageExecutor;
      process.env.TOKEN_DISCOVERY_ADDRESS = startupState.contracts.TokenDiscovery;
      process.env.LIQUIDITY_CHECKER_ADDRESS = startupState.contracts.LiquidityChecker;
    }

    logInfo('Starting backend services with local configuration...');

    // Start the enhanced backend
    const backendProcess = spawn('node', ['enhanced-backend.mjs'], {
      cwd: rootDir,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env }
    });

    startupState.processes.set('backend', backendProcess);

    let backendReady = false;
    let backendOutput = '';

    backendProcess.stdout.on('data', (data) => {
      backendOutput += data.toString();

      // Check if backend is ready
      if (backendOutput.includes('MEV Arbitrage System fully operational') ||
          backendOutput.includes('Server running on port')) {
        backendReady = true;
        startupState.servicesStarted = true;
        logSuccess(`Backend services started on port ${localConfig.services.backend.port}`);
        systemEvents.emit('backend:ready');
      }

      // Log important backend messages
      const lines = data.toString().split('\n');
      lines.forEach(line => {
        if (line.trim() && (line.includes('✅') || line.includes('🚀') || line.includes('Started'))) {
          logInfo(`Backend: ${line.trim()}`);
        }
      });
    });

    backendProcess.stderr.on('data', (data) => {
      const errorOutput = data.toString();
      if (!errorOutput.includes('Warning') && !errorOutput.includes('DeprecationWarning')) {
        logWarning(`Backend warning: ${errorOutput}`);
      }
    });

    backendProcess.on('exit', (code) => {
      if (code !== 0) {
        logError(`Backend process exited with code ${code}`);
        startupState.servicesStarted = false;
      }
    });

    // Wait for backend to be ready
    const startTime = Date.now();
    while (!backendReady && (Date.now() - startTime) < localConfig.services.backend.maxStartupTime) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    if (!backendReady) {
      logError(`Backend services failed to start within ${localConfig.services.backend.maxStartupTime}ms`);
      return false;
    }

    return true;
  } catch (error) {
    logError(`Failed to start backend services: ${error.message}`);
    return false;
  }
}

/**
 * Phase 7: Start Frontend Dashboard
 */
async function startFrontendDashboard() {
  logPhase('Starting Frontend Dashboard');

  try {
    logInfo('Starting Next.js frontend dashboard...');

    // Start the frontend
    const frontendProcess = spawn('npm', ['run', 'dev:frontend'], {
      cwd: rootDir,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env }
    });

    startupState.processes.set('frontend', frontendProcess);

    let frontendReady = false;
    let frontendOutput = '';

    frontendProcess.stdout.on('data', (data) => {
      frontendOutput += data.toString();

      // Check if frontend is ready
      if (frontendOutput.includes('Ready in') || frontendOutput.includes('Local:')) {
        frontendReady = true;
        logSuccess(`Frontend dashboard started on port ${localConfig.services.frontend.port}`);
        logSuccess(`Dashboard available at: http://localhost:${localConfig.services.frontend.port}`);
        systemEvents.emit('frontend:ready');
      }

      // Log important frontend messages
      const lines = data.toString().split('\n');
      lines.forEach(line => {
        if (line.trim() && (line.includes('Ready') || line.includes('Local:') || line.includes('compiled'))) {
          logInfo(`Frontend: ${line.trim()}`);
        }
      });
    });

    frontendProcess.stderr.on('data', (data) => {
      const errorOutput = data.toString();
      if (!errorOutput.includes('Warning') && !errorOutput.includes('Fast Refresh')) {
        logWarning(`Frontend warning: ${errorOutput}`);
      }
    });

    frontendProcess.on('exit', (code) => {
      if (code !== 0) {
        logError(`Frontend process exited with code ${code}`);
      }
    });

    // Wait for frontend to be ready
    const startTime = Date.now();
    while (!frontendReady && (Date.now() - startTime) < localConfig.services.frontend.maxStartupTime) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    if (!frontendReady) {
      logError(`Frontend failed to start within ${localConfig.services.frontend.maxStartupTime}ms`);
      return false;
    }

    return true;
  } catch (error) {
    logError(`Failed to start frontend dashboard: ${error.message}`);
    return false;
  }
}

/**
 * Phase 8: System Health Verification
 */
async function runSystemHealthChecks() {
  logPhase('Running System Health Checks');

  try {
    const healthChecks = [];

    // Check Hardhat network
    logInfo('Checking Hardhat network connectivity...');
    try {
      const response = await fetch(`http://localhost:${localConfig.hardhat.port}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'eth_blockNumber',
          params: [],
          id: 1
        })
      });
      const result = await response.json();
      if (result.result) {
        logSuccess(`Hardhat network responsive - Current block: ${parseInt(result.result, 16)}`);
        healthChecks.push({ service: 'Hardhat Network', status: 'healthy' });
      }
    } catch (error) {
      logError(`Hardhat network health check failed: ${error.message}`);
      healthChecks.push({ service: 'Hardhat Network', status: 'unhealthy', error: error.message });
    }

    // Check backend API
    logInfo('Checking backend API health...');
    try {
      const response = await fetch(`http://localhost:${localConfig.services.backend.port}/api/health`);
      if (response.ok) {
        const healthData = await response.json();
        logSuccess(`Backend API healthy - Status: ${healthData.status || 'ok'}`);
        healthChecks.push({ service: 'Backend API', status: 'healthy' });
      }
    } catch (error) {
      logWarning(`Backend API health check failed: ${error.message}`);
      healthChecks.push({ service: 'Backend API', status: 'unhealthy', error: error.message });
    }

    // Check database connections
    for (const [dbId, dbConfig] of Object.entries(localConfig.databases)) {
      logInfo(`Checking ${dbConfig.name} connectivity...`);
      try {
        const { stdout } = await execAsync(`docker exec ${dbConfig.container} echo "healthy"`);
        if (stdout.trim() === 'healthy') {
          logSuccess(`${dbConfig.name} is healthy`);
          healthChecks.push({ service: dbConfig.name, status: 'healthy' });
        }
      } catch (error) {
        logWarning(`${dbConfig.name} health check failed: ${error.message}`);
        healthChecks.push({ service: dbConfig.name, status: 'unhealthy', error: error.message });
      }
    }

    // Check contract deployment
    if (startupState.contractsDeployed) {
      logInfo('Verifying smart contract deployment...');
      try {
        const response = await fetch(`http://localhost:${localConfig.hardhat.port}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            method: 'eth_getCode',
            params: [startupState.contracts.ArbitrageExecutor, 'latest'],
            id: 1
          })
        });
        const result = await response.json();
        if (result.result && result.result !== '0x') {
          logSuccess('Smart contracts deployed and accessible');
          healthChecks.push({ service: 'Smart Contracts', status: 'healthy' });
        }
      } catch (error) {
        logWarning(`Contract verification failed: ${error.message}`);
        healthChecks.push({ service: 'Smart Contracts', status: 'unhealthy', error: error.message });
      }
    }

    // Summary
    const healthyServices = healthChecks.filter(check => check.status === 'healthy').length;
    const totalServices = healthChecks.length;

    logInfo(`Health Check Summary: ${healthyServices}/${totalServices} services healthy`);

    if (healthyServices === totalServices) {
      logSuccess('All system components are healthy and operational');
      return true;
    } else {
      logWarning(`${totalServices - healthyServices} services have health issues`);
      return false;
    }
  } catch (error) {
    logError(`System health check failed: ${error.message}`);
    return false;
  }
}

/**
 * Cleanup function for graceful shutdown
 */
async function cleanup() {
  logPhase('System Cleanup');

  try {
    // Kill all spawned processes
    for (const [name, process] of startupState.processes) {
      logInfo(`Stopping ${name} process...`);
      process.kill('SIGTERM');
    }

    // Restore original hardhat config
    const configPath = path.join(rootDir, 'hardhat.config.cjs');
    const backupPath = `${configPath}.backup`;

    try {
      const backupContent = await readFile(backupPath, 'utf-8');
      await writeFile(configPath, backupContent);
      logSuccess('Restored original hardhat.config.cjs');
    } catch (error) {
      logWarning('Could not restore hardhat config backup');
    }

    logSuccess('Cleanup completed');
  } catch (error) {
    logError(`Cleanup failed: ${error.message}`);
  }
}

/**
 * Main Hardhat Local Startup Orchestrator
 */
async function startHardhatLocalSystem() {
  try {
    log(`${colors.bright}🚀 MEV ARBITRAGE BOT - HARDHAT LOCAL TESTNET STARTUP${colors.reset}`);
    log('=' .repeat(80));
    log(`Environment: ${localConfig.environment.toUpperCase()}`);
    log(`Hardhat Network: localhost:${localConfig.hardhat.port} (Chain ID: ${localConfig.hardhat.chainId})`);
    log(`Mainnet Forking: Block ${localConfig.hardhat.forkBlockNumber}`);
    log('=' .repeat(80));

    // Phase 1: Environment Validation
    if (!(await validateEnvironment())) {
      process.exit(1);
    }

    // Phase 2: Enable Mainnet Forking
    if (!(await enableMainnetForking())) {
      process.exit(1);
    }

    // Phase 3: Start Database Services
    if (!(await startDatabaseServices())) {
      process.exit(1);
    }

    // Phase 4: Start Hardhat Network
    if (!(await startHardhatNetwork())) {
      process.exit(1);
    }

    // Phase 5: Deploy Smart Contracts
    if (!(await deploySmartContracts())) {
      process.exit(1);
    }

    // Phase 6: Start Backend Services
    if (!(await startBackendServices())) {
      process.exit(1);
    }

    // Phase 7: Start Frontend Dashboard
    if (!(await startFrontendDashboard())) {
      process.exit(1);
    }

    // Phase 8: System Health Verification
    const systemHealthy = await runSystemHealthChecks();

    // Final System Status
    const totalTime = Date.now() - startupState.startTime;
    const allSystemsOperational = startupState.hardhatReady &&
                                  startupState.databasesReady &&
                                  startupState.contractsDeployed &&
                                  startupState.servicesStarted;

    log('\n' + '=' .repeat(80));
    log(`${colors.bright}STARTUP COMPLETED IN ${totalTime}ms${colors.reset}`);
    log('=' .repeat(80));

    if (allSystemsOperational && systemHealthy) {
      logSuccess('\n🎉 MEV ARBITRAGE BOT LOCAL TESTNET FULLY OPERATIONAL!');
      logInfo('All core services are running and healthy');
      logInfo('System is ready for local development and testing');
      logInfo(`Frontend Dashboard: http://localhost:${localConfig.services.frontend.port}`);
      logInfo(`Backend API: http://localhost:${localConfig.services.backend.port}`);
      logInfo(`Hardhat Network: http://localhost:${localConfig.hardhat.port}`);
      logInfo(`Chain ID: ${localConfig.hardhat.chainId}`);

      if (startupState.contracts.ArbitrageExecutor) {
        logInfo(`ArbitrageExecutor: ${startupState.contracts.ArbitrageExecutor}`);
        logInfo(`TokenDiscovery: ${startupState.contracts.TokenDiscovery}`);
        logInfo(`LiquidityChecker: ${startupState.contracts.LiquidityChecker}`);
      }

      logInfo('\n📋 Available Testing Commands:');
      logInfo('  npm run test:local                 - Run local Hardhat tests');
      logInfo('  npm run test:mainnet-fork          - Run mainnet fork tests');
      logInfo('  npm run test:contracts             - Run contract tests');
      logInfo('  npm run test:integration           - Run integration tests');
      logInfo('  npm run benchmark                  - Run performance benchmarks');

      logInfo('\n🔧 System Management:');
      logInfo('  Press Ctrl+C to gracefully shutdown the system');
      logInfo('  All processes will be terminated and configs restored');

    } else {
      logWarning('\n⚠️  System started with some issues:');
      if (!startupState.hardhatReady) logError('- Hardhat network not ready');
      if (!startupState.databasesReady) logError('- Database services not ready');
      if (!startupState.contractsDeployed) logError('- Smart contracts not deployed');
      if (!startupState.servicesStarted) logError('- Backend services not started');
      if (!systemHealthy) logError('- System health checks failed');

      logInfo('\nCheck the logs above for specific error details');
      logInfo('You may need to restart individual components');
    }

    // Setup graceful shutdown
    process.on('SIGINT', async () => {
      logInfo('\n🛑 Received shutdown signal...');
      await cleanup();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logInfo('\n🛑 Received termination signal...');
      await cleanup();
      process.exit(0);
    });

    // Keep the process running
    process.stdin.resume();

  } catch (error) {
    logError(`\n💥 Startup failed: ${error.message}`);
    await cleanup();
    process.exit(1);
  }
}

// Handle direct execution
if (import.meta.url === `file://${process.argv[1]}`) {
  startHardhatLocalSystem().catch((error) => {
    console.error('💥 Hardhat local startup failed:', error);
    process.exit(1);
  });
}

// Export for use in other scripts
export {
  validateEnvironment,
  enableMainnetForking,
  startDatabaseServices,
  startHardhatNetwork,
  deploySmartContracts,
  startBackendServices,
  startFrontendDashboard,
  runSystemHealthChecks,
  startHardhatLocalSystem,
  cleanup,
  localConfig,
  startupState,
  systemEvents
};
