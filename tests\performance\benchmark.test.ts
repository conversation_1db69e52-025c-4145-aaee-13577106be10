import { jest, describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { performance } from 'perf_hooks';
import { PriceFeedService } from '../../backend/services/PriceFeedService.js';
import { OpportunityDetectionService } from '../../backend/services/OpportunityDetectionService.js';
import { TokenDiscoveryService } from '../../backend/services/TokenDiscoveryService.js';

describe('Performance Benchmarks', () => {
  let priceFeedService: PriceFeedService;
  let opportunityService: OpportunityDetectionService;
  let tokenService: TokenDiscoveryService;

  beforeAll(async () => {
    priceFeedService = new PriceFeedService();
    tokenService = new TokenDiscoveryService();
    opportunityService = new OpportunityDetectionService(priceFeedService, tokenService);

    await priceFeedService.start();
    await tokenService.start();
    await opportunityService.start();
  });

  afterAll(async () => {
    await opportunityService.stop();
    await tokenService.stop();
    await priceFeedService.stop();
  });

  describe('Price Feed Performance', () => {
    it('should handle high-frequency price updates', async () => {
      const updateCount = 1000;
      const startTime = performance.now();

      // Simulate high-frequency updates
      for (let i = 0; i < updateCount; i++) {
        const priceData = {
          ...global.testUtils.mockPriceData,
          symbol: `TOKEN${i % 10}`, // 10 different tokens
          price: 100 + Math.random() * 10,
          timestamp: Date.now() + i
        };
        
        priceFeedService.updatePriceData(priceData);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;
      const updatesPerSecond = (updateCount / duration) * 1000;

      console.log(`Price updates: ${updatesPerSecond.toFixed(2)} updates/second`);
      
      // Should handle at least 1000 updates per second
      expect(updatesPerSecond).toBeGreaterThan(1000);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should maintain low latency for price retrieval', () => {
      // Setup test data
      for (let i = 0; i < 100; i++) {
        priceFeedService.updatePriceData({
          ...global.testUtils.mockPriceData,
          symbol: `TOKEN${i}`,
          price: 100 + i
        });
      }

      const retrievalCount = 1000;
      const startTime = performance.now();

      // Perform many price retrievals
      for (let i = 0; i < retrievalCount; i++) {
        const symbol = `TOKEN${i % 100}`;
        const price = priceFeedService.getPrice(symbol);
        expect(price).toBeDefined();
      }

      const endTime = performance.now();
      const duration = endTime - startTime;
      const retrievalsPerSecond = (retrievalCount / duration) * 1000;

      console.log(`Price retrievals: ${retrievalsPerSecond.toFixed(2)} retrievals/second`);
      
      // Should handle at least 10,000 retrievals per second
      expect(retrievalsPerSecond).toBeGreaterThan(10000);
    });
  });

  describe('Opportunity Detection Performance', () => {
    it('should detect opportunities within acceptable time limits', async () => {
      // Setup price data for multiple tokens and exchanges
      const tokens = ['ETH', 'BTC', 'USDC', 'USDT', 'WBTC'];
      const exchanges = ['uniswap', 'sushiswap', 'balancer', 'curve'];

      const startTime = performance.now();

      // Create price differences across exchanges
      tokens.forEach((token, tokenIndex) => {
        exchanges.forEach((exchange, exchangeIndex) => {
          const basePrice = 1000 + tokenIndex * 1000;
          const priceVariation = 1 + (exchangeIndex * 0.01); // 0-3% variation
          
          priceFeedService.updatePriceData({
            ...global.testUtils.mockPriceData,
            symbol: token,
            price: basePrice * priceVariation,
            source: exchange,
            timestamp: Date.now()
          });
        });
      });

      // Wait for opportunity detection
      await global.testUtils.delay(100);

      const endTime = performance.now();
      const detectionTime = endTime - startTime;

      console.log(`Opportunity detection time: ${detectionTime.toFixed(2)}ms`);

      // Should detect opportunities within 200ms
      expect(detectionTime).toBeLessThan(200);

      const opportunities = opportunityService.getActiveOpportunities();
      expect(opportunities.length).toBeGreaterThan(0);
    });

    it('should handle concurrent opportunity processing', async () => {
      const concurrentOpportunities = 50;
      const startTime = performance.now();

      // Create multiple opportunities simultaneously
      const promises = Array.from({ length: concurrentOpportunities }, (_, i) => {
        return new Promise<void>((resolve) => {
          const opportunity = {
            ...global.testUtils.mockOpportunity,
            id: `concurrent-opp-${i}`,
            timestamp: Date.now() + i
          };
          
          opportunityService.addOpportunity(opportunity);
          resolve();
        });
      });

      await Promise.all(promises);

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      console.log(`Concurrent processing time: ${processingTime.toFixed(2)}ms for ${concurrentOpportunities} opportunities`);

      // Should process all opportunities within 500ms
      expect(processingTime).toBeLessThan(500);
    });
  });

  describe('Memory Usage', () => {
    it('should maintain reasonable memory usage under load', async () => {
      const initialMemory = process.memoryUsage();

      // Generate significant load
      for (let i = 0; i < 10000; i++) {
        // Add price data
        priceFeedService.updatePriceData({
          ...global.testUtils.mockPriceData,
          symbol: `LOAD_TOKEN_${i % 100}`,
          price: Math.random() * 1000,
          timestamp: Date.now() + i
        });

        // Add opportunities
        if (i % 10 === 0) {
          opportunityService.addOpportunity({
            ...global.testUtils.mockOpportunity,
            id: `load-opp-${i}`,
            timestamp: Date.now() + i
          });
        }
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreaseMB = memoryIncrease / (1024 * 1024);

      console.log(`Memory increase: ${memoryIncreaseMB.toFixed(2)}MB`);

      // Memory increase should be reasonable (less than 100MB for this test)
      expect(memoryIncreaseMB).toBeLessThan(100);
    });

    it('should clean up expired opportunities to prevent memory leaks', async () => {
      const initialOpportunities = opportunityService.getActiveOpportunities().length;

      // Add many opportunities with old timestamps
      for (let i = 0; i < 1000; i++) {
        opportunityService.addOpportunity({
          ...global.testUtils.mockOpportunity,
          id: `expired-opp-${i}`,
          timestamp: Date.now() - (60000 * (i + 1)) // 1+ minutes ago
        });
      }

      // Trigger cleanup
      opportunityService.cleanupExpiredOpportunities();

      const finalOpportunities = opportunityService.getActiveOpportunities().length;
      
      // Should have cleaned up expired opportunities
      expect(finalOpportunities).toBeLessThanOrEqual(initialOpportunities + 10);
    });
  });

  describe('Throughput Benchmarks', () => {
    it('should achieve target throughput for end-to-end processing', async () => {
      const testDuration = 5000; // 5 seconds
      const startTime = performance.now();
      let processedCount = 0;

      // Setup continuous processing
      const interval = setInterval(() => {
        // Add price update
        priceFeedService.updatePriceData({
          ...global.testUtils.mockPriceData,
          symbol: 'ETH',
          price: 2000 + Math.random() * 100,
          timestamp: Date.now()
        });

        processedCount++;
      }, 10); // Every 10ms

      // Wait for test duration
      await global.testUtils.delay(testDuration);
      clearInterval(interval);

      const endTime = performance.now();
      const actualDuration = endTime - startTime;
      const throughput = (processedCount / actualDuration) * 1000;

      console.log(`End-to-end throughput: ${throughput.toFixed(2)} operations/second`);

      // Should achieve at least 50 operations per second
      expect(throughput).toBeGreaterThan(50);
    });
  });

  describe('Stress Testing', () => {
    it('should remain stable under extreme load', async () => {
      const stressTestDuration = 3000; // 3 seconds
      const startTime = performance.now();
      let errorCount = 0;

      // Generate extreme load
      const stressInterval = setInterval(() => {
        try {
          // Rapid price updates
          for (let i = 0; i < 10; i++) {
            priceFeedService.updatePriceData({
              ...global.testUtils.mockPriceData,
              symbol: `STRESS_${i}`,
              price: Math.random() * 1000,
              timestamp: Date.now()
            });
          }

          // Rapid opportunity additions
          for (let i = 0; i < 5; i++) {
            opportunityService.addOpportunity({
              ...global.testUtils.mockOpportunity,
              id: `stress-opp-${Date.now()}-${i}`,
              timestamp: Date.now()
            });
          }
        } catch (error) {
          errorCount++;
        }
      }, 1); // Every 1ms

      await global.testUtils.delay(stressTestDuration);
      clearInterval(stressInterval);

      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`Stress test completed in ${duration.toFixed(2)}ms with ${errorCount} errors`);

      // Should handle stress with minimal errors
      expect(errorCount).toBeLessThan(10);
      
      // Services should still be responsive
      const price = priceFeedService.getPrice('ETH');
      expect(price).toBeDefined();
    });
  });
});
