'use client';

import { useState, useMemo } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  Target, 
  Zap,
  Brain,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

import { PerformanceMetrics, MLStrategy, LearningEvent } from '@/types';
import { 
  formatCurrency, 
  formatPercentage, 
  formatNumber,
  formatTimeAgo,
  getProfitColor,
  getTrendIcon
} from '@/lib/utils';
import { LoadingSpinner, SkeletonCard } from '@/components/ui/LoadingSpinner';

interface PerformanceAnalyticsProps {
  performance?: PerformanceMetrics;
  mlStrategies?: MLStrategy[];
  learningEvents?: LearningEvent[];
  isLoading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string;
  change?: number;
  changeLabel?: string;
  icon: React.ComponentType<any>;
  color: string;
  trend?: 'up' | 'down' | 'neutral';
}

function MetricCard({ title, value, change, changeLabel, icon: Icon, color, trend }: MetricCardProps) {
  const TrendIcon = getTrendIcon(trend);
  
  return (
    <div className="metric-card">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Icon className={`w-5 h-5 ${color}`} />
          <span className="text-sm font-medium text-dark-300">{title}</span>
        </div>
        {trend && (
          <TrendIcon className={`w-4 h-4 ${
            trend === 'up' ? 'text-success-400' : 
            trend === 'down' ? 'text-error-400' : 'text-dark-400'
          }`} />
        )}
      </div>
      
      <div className="text-2xl font-bold text-dark-100 mb-1">
        {value}
      </div>
      
      {change !== undefined && (
        <div className="flex items-center space-x-1">
          <span className={`text-sm font-medium ${getProfitColor(change)}`}>
            {change > 0 ? '+' : ''}{formatPercentage(change)}
          </span>
          {changeLabel && (
            <span className="text-xs text-dark-500">{changeLabel}</span>
          )}
        </div>
      )}
    </div>
  );
}

interface StrategyCardProps {
  strategy: MLStrategy;
}

function StrategyCard({ strategy }: StrategyCardProps) {
  const performance = strategy.performance || {};
  const winRate = performance.win_rate || performance.winRate || 0;
  const totalTrades = performance.total_trades || performance.totalTrades || 0;
  const netProfit = performance.net_profit || performance.netProfit || 0;

  return (
    <div className="glass-effect rounded-lg p-4 hover:bg-dark-700/30 transition-all duration-200">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Brain className="w-4 h-4 text-primary-400" />
          <span className="font-medium text-dark-100">{strategy.name}</span>
        </div>
        <div className={`
          px-2 py-1 rounded-full text-xs font-medium
          ${strategy.is_active || strategy.isActive 
            ? 'bg-success-500/20 text-success-400' 
            : 'bg-dark-600/20 text-dark-400'}
        `}>
          {strategy.is_active || strategy.isActive ? 'Active' : 'Inactive'}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <div className="text-xs text-dark-500 mb-1">Win Rate</div>
          <div className={`text-sm font-medium ${getProfitColor(winRate - 50)}`}>
            {formatPercentage(winRate)}
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Trades</div>
          <div className="text-sm font-medium text-dark-200">
            {formatNumber(totalTrades)}
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Profit</div>
          <div className={`text-sm font-medium ${getProfitColor(netProfit)}`}>
            {formatCurrency(netProfit)}
          </div>
        </div>
      </div>

      <div className="mt-3 pt-3 border-t border-dark-700">
        <div className="text-xs text-dark-500 mb-1">Confidence Score</div>
        <div className="flex items-center space-x-2">
          <div className="flex-1 bg-dark-700 rounded-full h-2">
            <div
              className="bg-primary-500 h-2 rounded-full transition-all duration-300"
              style={{ '--progress-width': `${(strategy.confidence_score || strategy.confidenceScore || 0) * 100}%` } as React.CSSProperties}
              data-progress-bar
            />
          </div>
          <span className="text-xs text-dark-300">
            {formatPercentage((strategy.confidence_score || strategy.confidenceScore || 0) * 100)}
          </span>
        </div>
      </div>
    </div>
  );
}

interface LearningEventItemProps {
  event: LearningEvent;
}

function LearningEventItem({ event }: LearningEventItemProps) {
  const eventTypeColors = {
    strategy_update: 'text-primary-400',
    performance_improvement: 'text-success-400',
    risk_adjustment: 'text-warning-400',
    error_correction: 'text-error-400',
    market_adaptation: 'text-purple-400',
  };

  const eventTypeColor = eventTypeColors[event.event_type || event.eventType] || 'text-dark-400';

  return (
    <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-dark-700/30 transition-colors">
      <div className={`w-2 h-2 rounded-full mt-2 ${eventTypeColor.replace('text-', 'bg-')}`} />
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <span className={`text-sm font-medium ${eventTypeColor}`}>
            {(event.event_type || event.eventType || '').replace('_', ' ').toUpperCase()}
          </span>
          <span className="text-xs text-dark-500">
            {formatTimeAgo(event.timestamp || event.createdAt || '')}
          </span>
        </div>
        
        <p className="text-sm text-dark-300 mb-2">
          {event.description}
        </p>
        
        {event.metrics && (
          <div className="text-xs text-dark-500">
            Impact: {formatPercentage((event.metrics.impact || 0) * 100)}
          </div>
        )}
      </div>
    </div>
  );
}

export function PerformanceAnalytics({ 
  performance, 
  mlStrategies = [], 
  learningEvents = [],
  isLoading = false 
}: PerformanceAnalyticsProps) {
  const [timeframe, setTimeframe] = useState('24h');
  const [activeTab, setActiveTab] = useState('overview');

  // Calculate performance metrics
  const metrics = useMemo(() => {
    if (!performance) return [];

    const totalProfit = performance.total_profit || performance.totalProfit || 0;
    const netProfit = performance.net_profit || performance.netProfit || 0;
    const winRate = performance.win_rate || performance.winRate || 0;
    const totalTrades = performance.total_trades || performance.totalTrades || 0;
    const dailyVolume = performance.daily_volume || performance.dailyVolume || 0;
    const avgProfit = performance.average_profit || performance.averageProfit || 0;

    return [
      {
        title: 'Total Profit',
        value: formatCurrency(totalProfit),
        change: 12.5, // Mock change - would come from historical data
        changeLabel: timeframe,
        icon: DollarSign,
        color: getProfitColor(totalProfit),
        trend: totalProfit > 0 ? 'up' : 'down',
      },
      {
        title: 'Net Profit',
        value: formatCurrency(netProfit),
        change: 8.3,
        changeLabel: timeframe,
        icon: TrendingUp,
        color: getProfitColor(netProfit),
        trend: netProfit > 0 ? 'up' : 'down',
      },
      {
        title: 'Win Rate',
        value: formatPercentage(winRate),
        change: 2.1,
        changeLabel: '7d',
        icon: Target,
        color: 'text-primary-400',
        trend: winRate > 50 ? 'up' : 'down',
      },
      {
        title: 'Total Trades',
        value: formatNumber(totalTrades),
        change: 15.7,
        changeLabel: timeframe,
        icon: Zap,
        color: 'text-purple-400',
        trend: 'up',
      },
      {
        title: 'Daily Volume',
        value: formatCurrency(dailyVolume),
        change: 5.2,
        changeLabel: timeframe,
        icon: BarChart3,
        color: 'text-warning-400',
        trend: 'up',
      },
      {
        title: 'Avg Profit/Trade',
        value: formatCurrency(avgProfit),
        change: -1.8,
        changeLabel: timeframe,
        icon: TrendingUp,
        color: getProfitColor(avgProfit),
        trend: avgProfit > 0 ? 'up' : 'down',
      },
    ];
  }, [performance, timeframe]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-dark-700 rounded w-1/3 animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'strategies', label: 'ML Strategies', icon: Brain },
    { id: 'learning', label: 'Learning Events', icon: TrendingUp },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-dark-100">Performance Analytics</h2>
          <p className="text-dark-400 mt-1">
            Comprehensive performance metrics and machine learning insights
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <label htmlFor="timeframe-select" className="sr-only">Select timeframe</label>
          <select
            id="timeframe-select"
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            className="input-field"
            aria-label="Select timeframe for analytics"
          >
            <option value="1h">1 Hour</option>
            <option value="24h">24 Hours</option>
            <option value="7d">7 Days</option>
            <option value="30d">30 Days</option>
          </select>

          <button
            type="button"
            className="btn-secondary"
            aria-label="Export analytics data"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
          
          <button
            type="button"
            aria-label="Refresh analytics data"
            title="Refresh"
            className="btn-secondary"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-dark-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 py-3 px-1 border-b-2 font-medium text-sm transition-colors
                  ${activeTab === tab.id
                    ? 'border-primary-500 text-primary-400'
                    : 'border-transparent text-dark-400 hover:text-dark-300 hover:border-dark-600'
                  }
                `}
                aria-label={`Switch to ${tab.label} tab`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Performance Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {metrics.map((metric, index) => (
              <MetricCard key={index} {...metric} />
            ))}
          </div>

          {/* Performance Charts Placeholder */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="glass-effect rounded-xl p-6">
              <h3 className="text-lg font-semibold text-dark-100 mb-4">Profit Over Time</h3>
              <div className="h-64 flex items-center justify-center text-dark-500">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2 text-dark-600" />
                  <p>Chart component will be integrated here</p>
                </div>
              </div>
            </div>

            <div className="glass-effect rounded-xl p-6">
              <h3 className="text-lg font-semibold text-dark-100 mb-4">Trade Distribution</h3>
              <div className="h-64 flex items-center justify-center text-dark-500">
                <div className="text-center">
                  <Target className="w-12 h-12 mx-auto mb-2 text-dark-600" />
                  <p>Chart component will be integrated here</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'strategies' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-dark-100">
              Machine Learning Strategies ({mlStrategies.length})
            </h3>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-dark-400">
                {mlStrategies.filter(s => s.is_active || s.isActive).length} active
              </span>
            </div>
          </div>

          {mlStrategies.length === 0 ? (
            <div className="text-center py-12">
              <Brain className="w-12 h-12 text-dark-600 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-dark-300 mb-2">
                No ML Strategies Found
              </h4>
              <p className="text-dark-500">
                Machine learning strategies will appear here once they are created.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {mlStrategies.map((strategy) => (
                <StrategyCard key={strategy.id} strategy={strategy} />
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'learning' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-dark-100">
              Learning Events ({learningEvents.length})
            </h3>
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-dark-400" />
              <label htmlFor="event-filter" className="sr-only">Filter learning events</label>
              <select
                id="event-filter"
                className="input-field text-sm"
                aria-label="Filter learning events by type"
              >
                <option value="all">All Events</option>
                <option value="strategy_update">Strategy Updates</option>
                <option value="performance_improvement">Performance Improvements</option>
                <option value="risk_adjustment">Risk Adjustments</option>
                <option value="error_correction">Error Corrections</option>
                <option value="market_adaptation">Market Adaptations</option>
              </select>
            </div>
          </div>

          {learningEvents.length === 0 ? (
            <div className="text-center py-12">
              <TrendingUp className="w-12 h-12 text-dark-600 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-dark-300 mb-2">
                No Learning Events Found
              </h4>
              <p className="text-dark-500">
                Machine learning events and adaptations will appear here.
              </p>
            </div>
          ) : (
            <div className="glass-effect rounded-xl p-6">
              <div className="space-y-2">
                {learningEvents.slice(0, 10).map((event) => (
                  <LearningEventItem key={event.id} event={event} />
                ))}
              </div>

              {learningEvents.length > 10 && (
                <div className="mt-4 pt-4 border-t border-dark-700 text-center">
                  <button
                    type="button"
                    className="btn-secondary text-sm"
                    aria-label="Load more learning events"
                  >
                    Load More Events
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
