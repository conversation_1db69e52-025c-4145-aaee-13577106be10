/**
 * Comprehensive Integration Testing Framework
 * 
 * Tests complete end-to-end workflows including:
 * - Multi-service communication and data flow
 * - Opportunity detection to execution pipeline
 * - Cross-chain arbitrage workflows
 * - Real-time data synchronization
 * - Performance validation against targets
 */

import { jest, describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { createServer, Server } from 'http';
import express from 'express';
import WebSocket from 'ws';
import { performance } from 'perf_hooks';

// Import services for integration testing
import { enhancedWebSocketService } from '../../backend/services/EnhancedWebSocketService.js';
import { profitValidationService } from '../../backend/services/ProfitValidationService.js';
import { enhancedTokenMonitoringService } from '../../backend/services/EnhancedTokenMonitoringService.js';
import { profitPrioritizedExecutionQueue } from '../../backend/services/ProfitPrioritizedExecutionQueue.js';
import { flashLoanService } from '../../backend/services/FlashLoanService.js';
import { mevProtectionService } from '../../backend/services/MEVProtectionService.js';
import { preExecutionValidationService } from '../../backend/services/PreExecutionValidationService.js';

interface TestEnvironment {
  server: Server;
  app: express.Application;
  wsClient: WebSocket;
  baseUrl: string;
  wsUrl: string;
}

interface WorkflowMetrics {
  totalDuration: number;
  detectionTime: number;
  validationTime: number;
  executionTime: number;
  success: boolean;
  errors: string[];
}

describe('Comprehensive Integration Testing', () => {
  let testEnv: TestEnvironment;
  const TEST_PORT = 3003;

  beforeAll(async () => {
    // Setup test environment
    testEnv = await setupTestEnvironment();
    
    // Initialize all services
    await initializeServices();
    
    // Wait for services to be ready
    await waitForServicesReady();
  }, 60000);

  afterAll(async () => {
    await cleanupTestEnvironment();
  }, 30000);

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Arbitrage Workflow', () => {
    it('should execute complete intra-chain arbitrage workflow within performance targets', async () => {
      const workflowStartTime = performance.now();
      
      // Step 1: Token price update triggers opportunity detection
      const mockPriceUpdate = {
        symbol: 'ETH',
        price: 2000,
        volume24h: 1000000,
        change24h: 5.2,
        timestamp: Date.now(),
        network: 'ethereum'
      };

      const detectionStartTime = performance.now();
      await enhancedTokenMonitoringService.processPriceUpdate(mockPriceUpdate);
      const detectionTime = performance.now() - detectionStartTime;

      // Step 2: Opportunity validation
      const mockOpportunity = {
        id: 'integration-test-1',
        type: 'intra-chain' as const,
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 100,
        profitPercentage: 2.5,
        timestamp: Date.now(),
        network: 'ethereum',
        route: {
          path: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          amounts: [1, 2000]
        },
        estimatedGas: 150000,
        slippage: 0.5,
        confidence: 85
      };

      const validationStartTime = performance.now();
      const validationResult = await profitValidationService.validateProfit(mockOpportunity);
      const validationTime = performance.now() - validationStartTime;

      expect(validationResult.isValid).toBe(true);
      expect(validationTime).toBeLessThan(3000); // 3 second target

      // Step 3: Pre-execution validation
      const preValidationResult = await preExecutionValidationService.validateExecution(mockOpportunity);
      expect(preValidationResult.isValid).toBe(true);

      // Step 4: Queue for execution
      const queueResult = await profitPrioritizedExecutionQueue.addOpportunity(mockOpportunity);
      expect(queueResult).toBe(true);

      // Step 5: MEV protection check
      const mevProtection = await mevProtectionService.protectTransaction(mockOpportunity);
      expect(mevProtection.isProtected).toBe(true);

      // Step 6: Flash loan optimization (if needed)
      const flashLoanOptimization = await flashLoanService.optimizeExecution(mockOpportunity);
      expect(flashLoanOptimization).toBeDefined();

      const totalWorkflowTime = performance.now() - workflowStartTime;
      
      // Validate performance targets
      expect(totalWorkflowTime).toBeLessThan(30000); // 30 second target for complete workflow
      expect(detectionTime).toBeLessThan(5000); // 5 second price update target
      expect(validationTime).toBeLessThan(3000); // 3 second validation target

      // Verify WebSocket notifications were sent
      await verifyWebSocketNotifications(['opportunity:detected', 'validation:complete']);

    }, 45000);

    it('should execute cross-chain arbitrage workflow with bridge optimization', async () => {
      const mockCrossChainOpportunity = {
        id: 'cross-chain-test-1',
        type: 'cross-chain' as const,
        assets: ['ETH', 'WETH'],
        exchanges: ['Uniswap', 'PancakeSwap'],
        potentialProfit: 200,
        profitPercentage: 3.0,
        timestamp: Date.now(),
        sourceNetwork: 'ethereum',
        targetNetwork: 'bsc',
        route: {
          path: ['ETH', 'WETH'],
          exchanges: ['Uniswap', 'PancakeSwap'],
          amounts: [1, 1]
        },
        estimatedGas: 300000,
        slippage: 1.0,
        confidence: 80,
        bridgeFees: 50,
        bridgeTime: 600000
      };

      const workflowStartTime = performance.now();

      // Cross-chain validation
      const validationResult = await profitValidationService.validateCrossChainProfit(mockCrossChainOpportunity);
      expect(validationResult.isValid).toBe(true);

      // Pre-execution validation with bridge considerations
      const preValidationResult = await preExecutionValidationService.validateCrossChainExecution(mockCrossChainOpportunity);
      expect(preValidationResult.isValid).toBe(true);

      // MEV protection for cross-chain
      const mevProtection = await mevProtectionService.protectCrossChainTransaction(mockCrossChainOpportunity);
      expect(mevProtection.isProtected).toBe(true);

      const totalTime = performance.now() - workflowStartTime;
      expect(totalTime).toBeLessThan(45000); // 45 second target for cross-chain workflow

    }, 60000);
  });

  describe('Multi-Service Communication', () => {
    it('should maintain data consistency across all services', async () => {
      const testOpportunity = {
        id: 'consistency-test-1',
        type: 'intra-chain' as const,
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 150,
        profitPercentage: 3.0,
        timestamp: Date.now(),
        network: 'ethereum',
        route: {
          path: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          amounts: [1, 2000]
        },
        estimatedGas: 150000,
        slippage: 0.5,
        confidence: 90
      };

      // Process through all services
      const validationResult = await profitValidationService.validateProfit(testOpportunity);
      const queueResult = await profitPrioritizedExecutionQueue.addOpportunity(testOpportunity);
      const preValidationResult = await preExecutionValidationService.validateExecution(testOpportunity);

      // Verify data consistency
      expect(validationResult.opportunityId).toBe(testOpportunity.id);
      expect(queueResult).toBe(true);
      expect(preValidationResult.opportunityId).toBe(testOpportunity.id);

      // Check that all services have consistent state
      const queueStats = profitPrioritizedExecutionQueue.getQueueStats();
      expect(queueStats.totalOpportunities).toBeGreaterThan(0);

    }, 30000);

    it('should handle service communication latency within targets', async () => {
      const communicationTests = [
        {
          name: 'Validation to Queue',
          test: async () => {
            const startTime = performance.now();
            const opportunity = global.testUtils.mockOpportunity;
            await profitValidationService.validateProfit(opportunity);
            await profitPrioritizedExecutionQueue.addOpportunity(opportunity);
            return performance.now() - startTime;
          }
        },
        {
          name: 'Queue to Execution',
          test: async () => {
            const startTime = performance.now();
            const opportunity = global.testUtils.mockOpportunity;
            await profitPrioritizedExecutionQueue.addOpportunity(opportunity);
            await preExecutionValidationService.validateExecution(opportunity);
            return performance.now() - startTime;
          }
        }
      ];

      for (const test of communicationTests) {
        const latency = await test.test();
        expect(latency).toBeLessThan(1000); // 1 second inter-service communication target
      }

    }, 20000);
  });

  describe('Real-Time Data Synchronization', () => {
    it('should synchronize price updates across all monitoring services', async () => {
      const priceUpdate = {
        symbol: 'BTC',
        price: 45000,
        volume24h: 2000000,
        change24h: 2.1,
        timestamp: Date.now(),
        network: 'ethereum'
      };

      const syncStartTime = performance.now();
      
      // Trigger price update
      await enhancedTokenMonitoringService.processPriceUpdate(priceUpdate);
      
      // Wait for synchronization
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const syncTime = performance.now() - syncStartTime;
      expect(syncTime).toBeLessThan(5000); // 5 second synchronization target

      // Verify WebSocket broadcast
      await verifyWebSocketNotifications(['price:update']);

    }, 15000);

    it('should maintain real-time dashboard updates', async () => {
      const wsMessages: any[] = [];
      
      // Listen for WebSocket messages
      testEnv.wsClient.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          wsMessages.push(message);
        } catch (error) {
          // Ignore parsing errors
        }
      });

      // Trigger various updates
      await enhancedTokenMonitoringService.updateTokenPrices();
      await profitPrioritizedExecutionQueue.processQueue();

      // Wait for messages
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Verify real-time updates
      expect(wsMessages.length).toBeGreaterThan(0);
      
      const updateTypes = wsMessages.map(msg => msg.type);
      expect(updateTypes).toContain('data:update');

    }, 20000);
  });

  describe('Performance Validation', () => {
    it('should meet all system performance targets', async () => {
      const performanceTests = [
        {
          name: 'Price Update Latency',
          target: 5000,
          test: async () => {
            const startTime = performance.now();
            await enhancedTokenMonitoringService.updateTokenPrices();
            return performance.now() - startTime;
          }
        },
        {
          name: 'Profit Validation Time',
          target: 3000,
          test: async () => {
            const startTime = performance.now();
            await profitValidationService.validateProfit(global.testUtils.mockOpportunity);
            return performance.now() - startTime;
          }
        },
        {
          name: 'Queue Operation Time',
          target: 1000,
          test: async () => {
            const startTime = performance.now();
            await profitPrioritizedExecutionQueue.addOpportunity(global.testUtils.mockOpportunity);
            return performance.now() - startTime;
          }
        }
      ];

      for (const test of performanceTests) {
        const duration = await test.test();
        expect(duration).toBeLessThan(test.target);
      }

    }, 30000);

    it('should maintain system uptime and stability', async () => {
      // Test system stability under load
      const stressTestPromises = [];
      
      for (let i = 0; i < 10; i++) {
        stressTestPromises.push(
          profitValidationService.validateProfit({
            ...global.testUtils.mockOpportunity,
            id: `stress-test-${i}`
          })
        );
      }

      const results = await Promise.allSettled(stressTestPromises);
      const successfulResults = results.filter(result => result.status === 'fulfilled');
      
      // Expect at least 90% success rate under stress
      expect(successfulResults.length / results.length).toBeGreaterThan(0.9);

    }, 25000);
  });

  // Helper functions
  async function setupTestEnvironment(): Promise<TestEnvironment> {
    const app = express();
    app.use(express.json());
    
    const server = createServer(app);
    
    return new Promise((resolve) => {
      server.listen(TEST_PORT, () => {
        const baseUrl = `http://localhost:${TEST_PORT}`;
        const wsUrl = `ws://localhost:${TEST_PORT}/ws`;
        
        const wsClient = new WebSocket(wsUrl);
        wsClient.on('open', () => {
          resolve({
            server,
            app,
            wsClient,
            baseUrl,
            wsUrl
          });
        });
      });
    });
  }

  async function initializeServices(): Promise<void> {
    // Initialize WebSocket service
    enhancedWebSocketService.initialize(testEnv.server);
    
    // Initialize other services
    await enhancedTokenMonitoringService.initialize();
    await profitPrioritizedExecutionQueue.initialize();
    await flashLoanService.initialize();
    await mevProtectionService.initialize();
    await preExecutionValidationService.initialize();
  }

  async function waitForServicesReady(): Promise<void> {
    const maxWaitTime = 30000; // 30 seconds
    const checkInterval = 1000; // 1 second
    let waitTime = 0;

    while (waitTime < maxWaitTime) {
      const allHealthy = [
        enhancedWebSocketService.isHealthy(),
        enhancedTokenMonitoringService.isHealthy(),
        profitPrioritizedExecutionQueue.isHealthy(),
        flashLoanService.isHealthy(),
        mevProtectionService.isHealthy(),
        preExecutionValidationService.isHealthy()
      ].every(healthy => healthy);

      if (allHealthy) {
        return;
      }

      await new Promise(resolve => setTimeout(resolve, checkInterval));
      waitTime += checkInterval;
    }

    throw new Error('Services failed to become ready within timeout');
  }

  async function verifyWebSocketNotifications(expectedTypes: string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      const receivedTypes: string[] = [];
      const timeout = setTimeout(() => {
        reject(new Error(`Timeout waiting for WebSocket notifications: ${expectedTypes.join(', ')}`));
      }, 10000);

      const messageHandler = (data: any) => {
        try {
          const message = JSON.parse(data.toString());
          receivedTypes.push(message.type);
          
          if (expectedTypes.every(type => receivedTypes.includes(type))) {
            clearTimeout(timeout);
            testEnv.wsClient.off('message', messageHandler);
            resolve();
          }
        } catch (error) {
          // Ignore parsing errors
        }
      };

      testEnv.wsClient.on('message', messageHandler);
    });
  }

  async function cleanupTestEnvironment(): Promise<void> {
    // Shutdown services
    await enhancedWebSocketService.shutdown();
    await enhancedTokenMonitoringService.shutdown();
    await profitPrioritizedExecutionQueue.shutdown();
    await flashLoanService.shutdown();
    await mevProtectionService.shutdown();
    await preExecutionValidationService.shutdown();

    // Close WebSocket client
    if (testEnv.wsClient) {
      testEnv.wsClient.close();
    }

    // Close server
    if (testEnv.server) {
      await new Promise<void>((resolve) => {
        testEnv.server.close(() => resolve());
      });
    }
  }
});
