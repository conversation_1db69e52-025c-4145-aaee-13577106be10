#!/usr/bin/env tsx

import logger from '../backend/utils/logger.js';
import config from '../backend/config/index.js';
import { ServiceIntegrator } from '../backend/services/ServiceIntegrator.js';
import { ArbitrageOpportunity, ArbitrageType } from '../backend/services/OpportunityDetectionService.js';

interface ValidationResult {
  component: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
  duration?: number;
}

interface SystemValidationReport {
  timestamp: string;
  overallStatus: 'PASS' | 'FAIL' | 'WARNING';
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  warningChecks: number;
  results: ValidationResult[];
  systemMetrics: {
    startupTime: number;
    memoryUsage: NodeJS.MemoryUsage;
    serviceCount: number;
    healthyServices: number;
  };
}

class SystemIntegrationValidator {
  private serviceIntegrator: ServiceIntegrator | null = null;
  private validationResults: ValidationResult[] = [];

  async validateSystem(): Promise<SystemValidationReport> {
    logger.info('🔍 Starting comprehensive system integration validation...');
    
    const startTime = Date.now();
    
    try {
      // 1. Configuration Validation
      await this.validateConfiguration();
      
      // 2. Service Initialization
      await this.validateServiceInitialization();
      
      // 3. Service Dependencies
      await this.validateServiceDependencies();
      
      // 4. Inter-Service Communication
      await this.validateInterServiceCommunication();
      
      // 5. End-to-End Workflow
      await this.validateEndToEndWorkflow();
      
      // 6. Performance Validation
      await this.validatePerformanceTargets();
      
      // 7. Health Checks
      await this.validateHealthChecks();
      
      const endTime = Date.now();
      const startupTime = endTime - startTime;
      
      // Generate report
      const report = this.generateValidationReport(startupTime);
      
      // Display results
      this.displayValidationResults(report);
      
      return report;
      
    } catch (error) {
      logger.error('System validation failed:', error);
      
      this.addResult('SYSTEM_VALIDATION', 'FAIL', `Critical error: ${error.message}`);
      
      return this.generateValidationReport(Date.now() - startTime);
    } finally {
      // Cleanup
      if (this.serviceIntegrator) {
        await this.serviceIntegrator.shutdown();
      }
    }
  }

  private async validateConfiguration(): Promise<void> {
    logger.info('📋 Validating configuration...');
    
    try {
      // Check required configuration parameters
      const requiredConfigs = [
        'ENABLE_PROFIT_VALIDATION',
        'ENABLE_ENHANCED_TOKEN_MONITORING',
        'ENABLE_FLASH_LOANS',
        'ENABLE_EXECUTION_QUEUE',
        'PROFIT_VALIDATION_THRESHOLD',
        'TOKEN_MONITORING_INTERVAL',
        'QUEUE_MAX_SIZE',
        'MAX_SERVICE_LATENCY'
      ];

      let missingConfigs = 0;
      for (const configKey of requiredConfigs) {
        if (!config[configKey as keyof typeof config]) {
          this.addResult('CONFIG_VALIDATION', 'WARNING', `Missing configuration: ${configKey}`);
          missingConfigs++;
        }
      }

      if (missingConfigs === 0) {
        this.addResult('CONFIG_VALIDATION', 'PASS', 'All required configurations present');
      } else {
        this.addResult('CONFIG_VALIDATION', 'WARNING', `${missingConfigs} configurations missing or using defaults`);
      }

      // Validate configuration values
      const profitThreshold = parseFloat(config.PROFIT_VALIDATION_THRESHOLD);
      if (profitThreshold < 0.001 || profitThreshold > 0.1) {
        this.addResult('CONFIG_VALUES', 'WARNING', `Profit validation threshold may be too ${profitThreshold < 0.001 ? 'low' : 'high'}: ${profitThreshold}`);
      } else {
        this.addResult('CONFIG_VALUES', 'PASS', 'Configuration values within acceptable ranges');
      }

    } catch (error) {
      this.addResult('CONFIG_VALIDATION', 'FAIL', `Configuration validation failed: ${error.message}`);
    }
  }

  private async validateServiceInitialization(): Promise<void> {
    logger.info('🚀 Validating service initialization...');
    
    const startTime = Date.now();
    
    try {
      this.serviceIntegrator = new ServiceIntegrator({
        enablePreExecutionValidation: true,
        enableMEVProtection: true,
        enableFlashLoans: true,
        enableProfitValidation: true,
        enableEnhancedTokenMonitoring: true,
        enableExecutionQueue: true,
        enableMLLearning: true,
        enableRiskManagement: true
      });

      await this.serviceIntegrator.initialize();
      
      const endTime = Date.now();
      const initTime = endTime - startTime;
      
      if (initTime > 60000) { // 60 seconds
        this.addResult('SERVICE_INIT', 'WARNING', `Service initialization took ${initTime}ms (>60s)`, { duration: initTime });
      } else {
        this.addResult('SERVICE_INIT', 'PASS', `Services initialized successfully in ${initTime}ms`, { duration: initTime });
      }

    } catch (error) {
      this.addResult('SERVICE_INIT', 'FAIL', `Service initialization failed: ${error.message}`);
      throw error;
    }
  }

  private async validateServiceDependencies(): Promise<void> {
    logger.info('🔗 Validating service dependencies...');
    
    try {
      if (!this.serviceIntegrator) {
        throw new Error('ServiceIntegrator not initialized');
      }

      const services = this.serviceIntegrator.getAllServices();
      const requiredServices = [
        'profitValidation',
        'enhancedTokenMonitoring',
        'flashLoan',
        'mevProtection',
        'preExecutionValidation',
        'executionQueue'
      ];

      let missingServices = 0;
      for (const serviceName of requiredServices) {
        if (!services.has(serviceName)) {
          this.addResult('SERVICE_DEPENDENCIES', 'FAIL', `Required service missing: ${serviceName}`);
          missingServices++;
        }
      }

      if (missingServices === 0) {
        this.addResult('SERVICE_DEPENDENCIES', 'PASS', `All ${requiredServices.length} required services present`);
      }

    } catch (error) {
      this.addResult('SERVICE_DEPENDENCIES', 'FAIL', `Service dependency validation failed: ${error.message}`);
    }
  }

  private async validateInterServiceCommunication(): Promise<void> {
    logger.info('📡 Validating inter-service communication...');
    
    try {
      if (!this.serviceIntegrator) {
        throw new Error('ServiceIntegrator not initialized');
      }

      const profitValidationService = this.serviceIntegrator.getService('profitValidation');
      const executionQueue = this.serviceIntegrator.getService('executionQueue');
      const flashLoanService = this.serviceIntegrator.getService('flashLoan');

      if (!profitValidationService || !executionQueue || !flashLoanService) {
        throw new Error('Required services not available');
      }

      // Test communication with mock opportunity
      const mockOpportunity: ArbitrageOpportunity = {
        id: 'validation-test',
        type: ArbitrageType.DEX_ARBITRAGE,
        assets: ['ETH', 'USDC'],
        buyExchange: 'uniswap',
        sellExchange: 'sushiswap',
        buyPrice: 2000,
        sellPrice: 2100,
        potentialProfit: 100,
        profitPercentage: 5,
        gasEstimate: 150000,
        confidence: 0.95,
        timestamp: Date.now(),
        network: 'ethereum',
        liquidityCheck: {
          sufficient: true,
          buyLiquidity: 1000000,
          sellLiquidity: 1000000
        }
      };

      // Test profit validation
      const startTime = Date.now();
      const profitValidation = await profitValidationService.validatePreExecution(mockOpportunity);
      const validationTime = Date.now() - startTime;

      if (profitValidation && validationTime < 5000) {
        this.addResult('INTER_SERVICE_COMM', 'PASS', `Service communication working (${validationTime}ms)`, { validationTime });
      } else {
        this.addResult('INTER_SERVICE_COMM', 'WARNING', `Service communication slow (${validationTime}ms)`, { validationTime });
      }

    } catch (error) {
      this.addResult('INTER_SERVICE_COMM', 'FAIL', `Inter-service communication failed: ${error.message}`);
    }
  }

  private async validateEndToEndWorkflow(): Promise<void> {
    logger.info('🔄 Validating end-to-end workflow...');
    
    try {
      if (!this.serviceIntegrator) {
        throw new Error('ServiceIntegrator not initialized');
      }

      const startTime = Date.now();
      
      // Simulate complete workflow
      const profitValidationService = this.serviceIntegrator.getService('profitValidation');
      const preExecutionValidationService = this.serviceIntegrator.getService('preExecutionValidation');
      const executionQueue = this.serviceIntegrator.getService('executionQueue');
      const flashLoanService = this.serviceIntegrator.getService('flashLoan');

      const mockOpportunity: ArbitrageOpportunity = {
        id: 'e2e-test',
        type: ArbitrageType.DEX_ARBITRAGE,
        assets: ['ETH', 'USDC'],
        buyExchange: 'uniswap',
        sellExchange: 'sushiswap',
        buyPrice: 2000,
        sellPrice: 2100,
        potentialProfit: 100,
        profitPercentage: 5,
        gasEstimate: 150000,
        confidence: 0.95,
        timestamp: Date.now(),
        network: 'ethereum',
        liquidityCheck: {
          sufficient: true,
          buyLiquidity: 1000000,
          sellLiquidity: 1000000
        }
      };

      // Execute workflow steps
      const profitValidation = await profitValidationService.validatePreExecution(mockOpportunity);
      const preValidation = await preExecutionValidationService.validateOpportunity(mockOpportunity);
      await executionQueue.addOpportunity(mockOpportunity);
      const flashLoanOptimization = await flashLoanService.optimizeFlashLoanStrategy(mockOpportunity, 10000);

      const endTime = Date.now();
      const workflowTime = endTime - startTime;

      if (profitValidation.isValid && preValidation.isValid && flashLoanOptimization && workflowTime < 30000) {
        this.addResult('E2E_WORKFLOW', 'PASS', `End-to-end workflow completed successfully (${workflowTime}ms)`, { workflowTime });
      } else {
        this.addResult('E2E_WORKFLOW', 'WARNING', `End-to-end workflow issues detected (${workflowTime}ms)`, { 
          workflowTime,
          profitValid: profitValidation.isValid,
          preValid: preValidation.isValid,
          flashLoanOpt: !!flashLoanOptimization
        });
      }

    } catch (error) {
      this.addResult('E2E_WORKFLOW', 'FAIL', `End-to-end workflow failed: ${error.message}`);
    }
  }

  private async validatePerformanceTargets(): Promise<void> {
    logger.info('⚡ Validating performance targets...');
    
    try {
      if (!this.serviceIntegrator) {
        throw new Error('ServiceIntegrator not initialized');
      }

      const profitValidationService = this.serviceIntegrator.getService('profitValidation');
      const executionQueue = this.serviceIntegrator.getService('executionQueue');

      // Test latency targets
      const iterations = 10;
      const latencies: number[] = [];

      const mockOpportunity: ArbitrageOpportunity = {
        id: 'perf-test',
        type: ArbitrageType.DEX_ARBITRAGE,
        assets: ['ETH', 'USDC'],
        buyExchange: 'uniswap',
        sellExchange: 'sushiswap',
        buyPrice: 2000,
        sellPrice: 2100,
        potentialProfit: 100,
        profitPercentage: 5,
        gasEstimate: 150000,
        confidence: 0.95,
        timestamp: Date.now(),
        network: 'ethereum',
        liquidityCheck: {
          sufficient: true,
          buyLiquidity: 1000000,
          sellLiquidity: 1000000
        }
      };

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        await profitValidationService.validatePreExecution(mockOpportunity);
        const endTime = Date.now();
        latencies.push(endTime - startTime);
      }

      const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
      const maxLatency = Math.max(...latencies);
      const targetLatency = parseInt(config.MAX_SERVICE_LATENCY);

      if (averageLatency < targetLatency && maxLatency < targetLatency * 2) {
        this.addResult('PERFORMANCE_TARGETS', 'PASS', `Performance targets met (avg: ${averageLatency.toFixed(2)}ms, max: ${maxLatency}ms)`, {
          averageLatency,
          maxLatency,
          targetLatency
        });
      } else {
        this.addResult('PERFORMANCE_TARGETS', 'WARNING', `Performance targets not met (avg: ${averageLatency.toFixed(2)}ms, max: ${maxLatency}ms)`, {
          averageLatency,
          maxLatency,
          targetLatency
        });
      }

    } catch (error) {
      this.addResult('PERFORMANCE_TARGETS', 'FAIL', `Performance validation failed: ${error.message}`);
    }
  }

  private async validateHealthChecks(): Promise<void> {
    logger.info('🏥 Validating health checks...');
    
    try {
      if (!this.serviceIntegrator) {
        throw new Error('ServiceIntegrator not initialized');
      }

      const healthChecks = await this.serviceIntegrator.performHealthChecks();
      
      if (healthChecks.overall) {
        const healthyServices = Object.values(healthChecks.services).filter(status => status).length;
        const totalServices = Object.keys(healthChecks.services).length;
        
        this.addResult('HEALTH_CHECKS', 'PASS', `All health checks passed (${healthyServices}/${totalServices} services healthy)`, {
          healthyServices,
          totalServices,
          healthChecks
        });
      } else {
        const unhealthyServices = Object.entries(healthChecks.services)
          .filter(([_, status]) => !status)
          .map(([name, _]) => name);
        
        this.addResult('HEALTH_CHECKS', 'FAIL', `Health checks failed for services: ${unhealthyServices.join(', ')}`, {
          unhealthyServices,
          healthChecks
        });
      }

    } catch (error) {
      this.addResult('HEALTH_CHECKS', 'FAIL', `Health check validation failed: ${error.message}`);
    }
  }

  private addResult(component: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any): void {
    this.validationResults.push({
      component,
      status,
      message,
      details
    });
  }

  private generateValidationReport(startupTime: number): SystemValidationReport {
    const passedChecks = this.validationResults.filter(r => r.status === 'PASS').length;
    const failedChecks = this.validationResults.filter(r => r.status === 'FAIL').length;
    const warningChecks = this.validationResults.filter(r => r.status === 'WARNING').length;

    let overallStatus: 'PASS' | 'FAIL' | 'WARNING' = 'PASS';
    if (failedChecks > 0) {
      overallStatus = 'FAIL';
    } else if (warningChecks > 0) {
      overallStatus = 'WARNING';
    }

    return {
      timestamp: new Date().toISOString(),
      overallStatus,
      totalChecks: this.validationResults.length,
      passedChecks,
      failedChecks,
      warningChecks,
      results: this.validationResults,
      systemMetrics: {
        startupTime,
        memoryUsage: process.memoryUsage(),
        serviceCount: this.serviceIntegrator?.getAllServices().size || 0,
        healthyServices: passedChecks
      }
    };
  }

  private displayValidationResults(report: SystemValidationReport): void {
    logger.info('\n' + '='.repeat(80));
    logger.info('🔍 SYSTEM INTEGRATION VALIDATION REPORT');
    logger.info('='.repeat(80));
    
    const statusIcon = report.overallStatus === 'PASS' ? '✅' : report.overallStatus === 'WARNING' ? '⚠️' : '❌';
    logger.info(`${statusIcon} Overall Status: ${report.overallStatus}`);
    logger.info(`📊 Total Checks: ${report.totalChecks}`);
    logger.info(`✅ Passed: ${report.passedChecks}`);
    logger.info(`⚠️  Warnings: ${report.warningChecks}`);
    logger.info(`❌ Failed: ${report.failedChecks}`);
    logger.info(`⏱️  Startup Time: ${(report.systemMetrics.startupTime / 1000).toFixed(2)}s`);
    logger.info(`🧠 Memory Usage: ${(report.systemMetrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    
    logger.info('\n📋 Detailed Results:');
    report.results.forEach((result, index) => {
      const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'WARNING' ? '⚠️' : '❌';
      logger.info(`${index + 1}. ${statusIcon} ${result.component}: ${result.message}`);
    });
    
    logger.info('\n' + '='.repeat(80));
    
    if (report.overallStatus === 'PASS') {
      logger.info('🎉 SYSTEM INTEGRATION VALIDATION PASSED! All components are working together correctly.');
    } else if (report.overallStatus === 'WARNING') {
      logger.warn('⚠️  SYSTEM INTEGRATION VALIDATION COMPLETED WITH WARNINGS. Review warnings before deployment.');
    } else {
      logger.error('❌ SYSTEM INTEGRATION VALIDATION FAILED. Critical issues must be resolved before deployment.');
    }
  }
}

// Main execution
async function main() {
  const validator = new SystemIntegrationValidator();
  
  try {
    const report = await validator.validateSystem();
    
    // Exit with appropriate code
    process.exit(report.overallStatus === 'FAIL' ? 1 : 0);
  } catch (error) {
    logger.error('System validation failed:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { SystemIntegrationValidator, ValidationResult, SystemValidationReport };
