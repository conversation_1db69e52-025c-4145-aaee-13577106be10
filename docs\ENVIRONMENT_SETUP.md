# Environment Variables Setup Guide

This document provides a comprehensive guide to setting up environment variables for the MEV Arbitrage Bot system.

## 📋 Overview

The MEV Arbitrage Bot uses environment variables to configure:
- **Server settings** (port, environment)
- **Database connections** (Redis, Supabase, InfluxDB)
- **Blockchain networks** (RPC URLs, private keys)
- **External APIs** (price feeds, blockchain explorers)
- **Trading parameters** (profit thresholds, risk limits)
- **Notifications** (Telegram, email alerts)
- **Security settings** (JWT secrets, CORS origins)

## 🚀 Quick Start

1. **Copy the example file:**
```bash
cp .env.example .env
```

2. **Install new dependencies:**
```bash
npm install @supabase/supabase-js @influxdata/influxdb-client
```

3. **Update your `.env` file** with your actual values (see sections below)

## 🔧 Environment Variables Reference

### Server Configuration
```env
PORT=3001                    # Backend server port
NODE_ENV=development         # Environment: development, production, test
```

### Database Configuration

#### Redis (Caching)
```env
REDIS_URL=redis://localhost:6379
```

#### Supabase (Primary Database)
```env
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_JWT_SECRET=your-jwt-secret-32-characters-long
SUPABASE_DATABASE_URL=postgresql://postgres:[PASSWORD]@db.your-project-ref.supabase.co:5432/postgres
```

#### InfluxDB (Time-Series Metrics)
```env
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token-here
INFLUXDB_ORG=mev-arbitrage-org
INFLUXDB_BUCKET=mev-arbitrage-metrics
INFLUXDB_USERNAME=admin
INFLUXDB_PASSWORD=your-secure-password
```

### Blockchain Configuration

#### Local Development (Hardhat)
```env
ETHEREUM_RPC_URL=http://127.0.0.1:8545
POLYGON_RPC_URL=http://127.0.0.1:8545
BSC_RPC_URL=http://127.0.0.1:8545
SOLANA_RPC_URL=http://127.0.0.1:8545
```

#### Production Networks
```env
ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/YOUR_API_KEY
POLYGON_RPC_URL=https://polygon-mainnet.alchemyapi.io/v2/YOUR_API_KEY
BSC_RPC_URL=https://bsc-dataseed.binance.org/
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
```

### Smart Contract Addresses
```env
TOKEN_DISCOVERY_ADDRESS=******************************************
LIQUIDITY_CHECKER_ADDRESS=******************************************
ARBITRAGE_EXECUTOR_ADDRESS=******************************************
GOVERNANCE_ADDRESS=******************************************
```

### Private Keys
```env
# Local development (Hardhat default account)
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80

# Production (use secure keys)
PRIVATE_KEY=0x1234567890abcdef...
FLASHBOTS_PRIVATE_KEY=0x1234567890abcdef...
```

### External API Keys
```env
COINGECKO_API_KEY=CG-your-api-key-here
ETHERSCAN_API_KEY=YourEtherscanApiKey
POLYGONSCAN_API_KEY=YourPolygonscanApiKey
BSCSCAN_API_KEY=YourBscscanApiKey
ALCHEMY_API_KEY=your-alchemy-api-key
INFURA_PROJECT_ID=your-infura-project-id
MORALIS_API_KEY=your-moralis-api-key
CHAINLINK_API_KEY=your-chainlink-api-key
PYTH_API_KEY=your-pyth-api-key
```

### Flashbots Configuration
```env
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
FLASHBOTS_BUILDER_URL=https://builder.flashbots.net
```

### Trading Configuration
```env
MIN_PROFIT_THRESHOLD=50      # Minimum profit in USD
MAX_POSITION_SIZE=10000      # Maximum position size in USD
MAX_SLIPPAGE=0.5            # Maximum slippage percentage
GAS_PRICE_MULTIPLIER=1.1    # Gas price multiplier
```

### Risk Management
```env
EMERGENCY_STOP=false         # Emergency stop flag
MAX_DAILY_LOSS=1000         # Maximum daily loss in USD
POSITION_SIZE_PERCENTAGE=2   # Position size as % of capital
```

### Notifications

#### Telegram Bot
```env
TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=-1001234567890
```

#### Email Alerts
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ALERT_EMAIL=<EMAIL>
```

### Security
```env
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
API_RATE_LIMIT_WINDOW=900000  # 15 minutes in milliseconds
API_RATE_LIMIT_MAX=100        # Max requests per window
CORS_ORIGIN=http://localhost:5173,http://localhost:3000
```

### Monitoring
```env
LOG_LEVEL=info              # error, warn, info, debug
ENABLE_METRICS=true         # Enable metrics collection
```

## 🔒 Security Best Practices

### 1. Never Commit Secrets
- Add `.env` to `.gitignore`
- Use `.env.example` for documentation
- Rotate keys regularly

### 2. Use Strong Passwords
- Database passwords: 16+ characters
- JWT secrets: 32+ characters
- API keys: Use official key generation

### 3. Limit Access
- Use read-only keys where possible
- Restrict API key permissions
- Use IP whitelisting when available

### 4. Environment Separation
```env
# Development
NODE_ENV=development
ETHEREUM_RPC_URL=http://127.0.0.1:8545

# Production
NODE_ENV=production
ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/YOUR_KEY
```

## 🧪 Testing Configuration

### Validate Environment Variables
```bash
# Test backend configuration
npm run dev:backend

# Check for initialization messages:
# ✅ "Supabase service initialized successfully"
# ✅ "InfluxDB service initialized successfully"
# ✅ "MEV Arbitrage Bot server started on port 3001"
```

### Test Database Connections
```bash
# Run integration test
node test-integration.js

# Expected output:
# ✅ Health Check: healthy
# ✅ Opportunities: X opportunities found
# ✅ Tokens: X tokens found
# ✅ Analytics: Total trades: X
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Supabase Connection Failed
```
Error: Invalid API key or URL
```
**Solution:** Check `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY`

#### 2. InfluxDB Connection Failed
```
Error: unauthorized access
```
**Solution:** Verify `INFLUXDB_TOKEN` and organization settings

#### 3. RPC Connection Failed
```
Error: could not detect network
```
**Solution:** Check RPC URL and API key validity

#### 4. Rate Limiting
```
Error: Too many requests
```
**Solution:** Adjust `API_RATE_LIMIT_MAX` or wait for reset

### Debug Mode
```env
LOG_LEVEL=debug
NODE_ENV=development
```

## 📚 Additional Resources

- [Supabase Setup Guide](./DATABASE_SETUP.md)
- [InfluxDB Setup Guide](./DATABASE_SETUP.md)
- [API Keys Setup Guide](./API_KEYS.md)
- [Security Best Practices](./SECURITY.md)

## 🔄 Environment Updates

When updating environment variables:

1. **Update `.env`** with new values
2. **Restart backend** to load new config
3. **Test connections** to verify changes
4. **Update documentation** if needed

```bash
# Restart backend
npm run dev:backend

# Verify health
curl http://localhost:3001/health
```
