/**
 * Enhanced MEV Arbitrage Bot Example
 * 
 * This example demonstrates the comprehensive pre-execution validation
 * and MEV-optimized transaction submission system.
 */

import { ServiceIntegrator } from '../backend/services/ServiceIntegrator.js';
import { PreExecutionValidationService } from '../backend/services/PreExecutionValidationService.js';
import { MEVProtectionService } from '../backend/services/MEVProtectionService.js';
import { OpportunityDetectionService } from '../backend/services/OpportunityDetectionService.js';
import { ExecutionService } from '../backend/services/ExecutionService.js';
import logger from '../backend/utils/logger.js';

async function runEnhancedArbitrageBot() {
  logger.info('Starting Enhanced MEV Arbitrage Bot...');

  // Initialize the service integrator with all features enabled
  const serviceIntegrator = new ServiceIntegrator({
    enablePreExecutionValidation: true,
    enableMEVProtection: true,
    enableMLLearning: true,
    enableRiskManagement: true
  });

  try {
    // Initialize all services
    await serviceIntegrator.initialize();

    // Get service instances
    const opportunityDetectionService = serviceIntegrator.getService<OpportunityDetectionService>('opportunityDetection');
    const executionService = serviceIntegrator.getService<ExecutionService>('execution');
    const preExecutionValidationService = serviceIntegrator.getService<PreExecutionValidationService>('preExecutionValidation');
    const mevProtectionService = serviceIntegrator.getService<MEVProtectionService>('mevProtection');

    // Set up event listeners for monitoring
    setupEventListeners(opportunityDetectionService, executionService, preExecutionValidationService, mevProtectionService);

    // Display system status
    await displaySystemStatus(serviceIntegrator);

    // Run the bot for demonstration
    logger.info('Bot is now running with enhanced features...');
    logger.info('- Pre-execution validation: ENABLED');
    logger.info('- MEV protection: ENABLED');
    logger.info('- ML learning: ENABLED');
    logger.info('- Risk management: ENABLED');

    // Keep the bot running
    await new Promise(resolve => {
      process.on('SIGINT', async () => {
        logger.info('Shutting down Enhanced MEV Arbitrage Bot...');
        await serviceIntegrator.shutdown();
        resolve(void 0);
      });
    });

  } catch (error) {
    logger.error('Failed to start Enhanced MEV Arbitrage Bot:', error);
    await serviceIntegrator.shutdown();
    process.exit(1);
  }
}

function setupEventListeners(
  opportunityDetectionService: OpportunityDetectionService,
  executionService: ExecutionService,
  preExecutionValidationService: PreExecutionValidationService,
  mevProtectionService: MEVProtectionService
) {
  // Opportunity Detection Events
  opportunityDetectionService.on('opportunity', (opportunity) => {
    logger.info(`🎯 New opportunity detected: ${opportunity.type} - ${opportunity.assets.join('/')} - $${opportunity.potentialProfit.toFixed(2)}`);
    
    if (opportunity.isValidated) {
      logger.info(`✅ Opportunity ${opportunity.id} passed pre-execution validation`);
      logger.info(`   Simulated profit: $${opportunity.validationResult?.simulatedProfit.toFixed(2)}`);
      logger.info(`   Profit margin: ${opportunity.validationResult?.profitMargin.toFixed(2)}%`);
      logger.info(`   Risk score: ${opportunity.validationResult?.riskScore}/100`);
    }
  });

  opportunityDetectionService.on('validationFailed', (event) => {
    logger.warn(`❌ Validation failed for opportunity ${event.opportunity.id}: ${event.validationResult.reason}`);
    logger.debug(`   Original profit: $${event.opportunity.potentialProfit.toFixed(2)}`);
    logger.debug(`   Simulated profit: $${event.validationResult.simulatedProfit.toFixed(2)}`);
    logger.debug(`   Total costs: $${event.validationResult.totalCosts.totalCosts.toFixed(2)}`);
  });

  opportunityDetectionService.on('validationError', (event) => {
    logger.error(`💥 Validation error for opportunity ${event.opportunity.id}: ${event.error}`);
  });

  // Pre-Execution Validation Events
  preExecutionValidationService.on('validationComplete', (event) => {
    const { opportunity, result } = event;
    
    if (result.isValid) {
      logger.info(`✅ Validation PASSED for ${opportunity.id}`);
      logger.info(`   Cost breakdown:`);
      logger.info(`     Gas costs: $${result.totalCosts.gasCosts.toFixed(2)}`);
      logger.info(`     DEX fees: $${result.totalCosts.dexFees.toFixed(2)}`);
      logger.info(`     Bridge fees: $${result.totalCosts.bridgeFees.toFixed(2)}`);
      logger.info(`     Slippage costs: $${result.totalCosts.slippageCosts.toFixed(2)}`);
      logger.info(`     MEV protection: $${result.totalCosts.mevProtectionCosts.toFixed(2)}`);
      logger.info(`     Total costs: $${result.totalCosts.totalCosts.toFixed(2)}`);
    } else {
      logger.warn(`❌ Validation FAILED for ${opportunity.id}: ${result.reason}`);
    }
  });

  // Execution Service Events
  executionService.on('trade', (trade) => {
    logger.info(`🚀 Trade initiated: ${trade.id} for ${trade.assets.join('/')}`);
  });

  executionService.on('tradeUpdate', (trade) => {
    switch (trade.status) {
      case 'executing':
        logger.info(`⏳ Trade ${trade.id} executing...`);
        break;
      case 'success':
        logger.info(`✅ Trade ${trade.id} completed successfully!`);
        logger.info(`   Executed profit: $${trade.executedProfit.toFixed(2)}`);
        logger.info(`   Gas fees: $${trade.gasFees.toFixed(2)}`);
        logger.info(`   Execution time: ${trade.executionTime}ms`);
        
        if (trade.mevProtectionResult) {
          logger.info(`   MEV protection: ${trade.mevProtectionResult.protectionMethod}`);
          logger.info(`   MEV execution time: ${trade.mevProtectionResult.executionTime}ms`);
        }
        break;
      case 'failed':
        logger.error(`❌ Trade ${trade.id} failed: ${trade.errorMessage}`);
        break;
    }
  });

  // MEV Protection Events
  mevProtectionService.on('mevProtectionResult', (event) => {
    const { opportunity, result } = event;
    
    if (result.success) {
      logger.info(`🛡️ MEV protection successful for ${opportunity.id}`);
      logger.info(`   Method: ${result.protectionMethod}`);
      logger.info(`   Transaction hash: ${result.transactionHash}`);
      logger.info(`   Execution time: ${result.executionTime}ms`);
      
      if (result.mevRevenue) {
        logger.info(`   MEV revenue: $${result.mevRevenue.toFixed(2)}`);
      }
    } else {
      logger.warn(`🛡️ MEV protection failed for ${opportunity.id}: ${result.error}`);
    }
  });
}

async function displaySystemStatus(serviceIntegrator: ServiceIntegrator) {
  logger.info('=== SYSTEM STATUS ===');
  
  // Integration stats
  const integrationStats = serviceIntegrator.getIntegrationStats();
  logger.info(`Services initialized: ${integrationStats.totalServices}`);
  logger.info(`Features enabled: ${Object.entries(integrationStats.enabledFeatures).filter(([_, enabled]) => enabled).map(([feature]) => feature).join(', ')}`);
  
  // Health check
  const healthStatus = await serviceIntegrator.healthCheck();
  const healthyServices = Object.entries(healthStatus).filter(([_, healthy]) => healthy).length;
  const totalServices = Object.keys(healthStatus).length;
  
  logger.info(`Service health: ${healthyServices}/${totalServices} healthy`);
  
  // Individual service status
  for (const [serviceName, isHealthy] of Object.entries(healthStatus)) {
    const status = isHealthy ? '✅' : '❌';
    logger.info(`  ${status} ${serviceName}`);
  }
  
  // Service-specific stats
  const preExecutionValidationService = serviceIntegrator.getService<PreExecutionValidationService>('preExecutionValidation');
  if (preExecutionValidationService) {
    const validationStats = preExecutionValidationService.getValidationStats();
    logger.info(`Pre-execution validation config:`);
    logger.info(`  Min profit margin buffer: ${validationStats.minProfitMarginBuffer}%`);
    logger.info(`  Max simulation time: ${validationStats.maxSimulationTime}ms`);
    logger.info(`  Bridge fee calculation: ${validationStats.enableBridgeFeeCalc ? 'enabled' : 'disabled'}`);
    logger.info(`  Network congestion check: ${validationStats.enableCongestionCheck ? 'enabled' : 'disabled'}`);
  }
  
  const mevProtectionService = serviceIntegrator.getService<MEVProtectionService>('mevProtection');
  if (mevProtectionService) {
    const mevStats = mevProtectionService.getMEVProtectionStats();
    logger.info(`MEV protection config:`);
    logger.info(`  Supported networks: ${mevStats.supportedNetworks.join(', ')}`);
    logger.info(`  Private mempool: ${mevStats.enablePrivateMempool ? 'enabled' : 'disabled'}`);
    logger.info(`  Dynamic gas pricing: ${mevStats.dynamicGasPricing ? 'enabled' : 'disabled'}`);
    logger.info(`  Fallback enabled: ${mevStats.mevProtectionFallback ? 'yes' : 'no'}`);
  }
  
  logger.info('===================');
}

// Example of manual validation testing
async function testValidationSystem(serviceIntegrator: ServiceIntegrator) {
  const preExecutionValidationService = serviceIntegrator.getService<PreExecutionValidationService>('preExecutionValidation');
  
  if (!preExecutionValidationService) {
    logger.error('Pre-execution validation service not available');
    return;
  }

  // Create a test opportunity
  const testOpportunity = {
    id: 'test_opportunity_1',
    type: 'intra-chain' as const,
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    potentialProfit: 150,
    profitPercentage: 2.5,
    timestamp: Date.now(),
    network: 'ethereum',
    route: {
      steps: [],
      totalGasCost: 0,
      expectedProfit: 150,
      priceImpact: 0.5
    },
    estimatedGas: 200000,
    slippage: 0.5,
    confidence: 85
  };

  logger.info('Testing validation system with sample opportunity...');
  
  const validationResult = await preExecutionValidationService.validateOpportunity(testOpportunity);
  
  logger.info(`Validation result: ${validationResult.isValid ? 'PASSED' : 'FAILED'}`);
  if (!validationResult.isValid) {
    logger.info(`Reason: ${validationResult.reason}`);
  }
  logger.info(`Simulated profit: $${validationResult.simulatedProfit.toFixed(2)}`);
  logger.info(`Profit margin: ${validationResult.profitMargin.toFixed(2)}%`);
  logger.info(`Risk score: ${validationResult.riskScore}/100`);
  logger.info(`Execution time: ${validationResult.executionTime}ms`);
}

// Run the enhanced arbitrage bot
if (require.main === module) {
  runEnhancedArbitrageBot().catch(console.error);
}
