import { jest } from '@jest/globals';

// Global test setup
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.PORT = '3001';
  process.env.REDIS_URL = 'redis://localhost:6379/1'; // Use test database
  process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests
});

afterAll(async () => {
  // Cleanup after all tests
  await new Promise(resolve => setTimeout(resolve, 1000));
});

// Mock external dependencies
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    disconnect: jest.fn(),
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    publish: jest.fn(),
    subscribe: jest.fn(),
    on: jest.fn(),
    off: jest.fn()
  }))
}));

jest.mock('ws', () => ({
  WebSocketServer: jest.fn(() => ({
    on: jest.fn(),
    close: jest.fn(),
    clients: new Set()
  }))
}));

// Global test utilities
global.testUtils = {
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  mockPriceData: {
    symbol: 'ETH',
    price: 2000,
    volume24h: 1000000,
    change24h: 5.2,
    timestamp: Date.now(),
    source: 'test',
    network: 'ethereum'
  },
  
  mockOpportunity: {
    id: 'test-opp-1',
    type: 'intra-chain' as const,
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    potentialProfit: 100,
    profitPercentage: 2.5,
    timestamp: Date.now(),
    network: 'ethereum',
    route: {
      path: ['ETH', 'USDC'],
      exchanges: ['Uniswap', 'SushiSwap'],
      amounts: [1, 2000]
    },
    estimatedGas: 150000,
    slippage: 0.5,
    confidence: 85
  },
  
  mockToken: {
    address: '******************************************',
    symbol: 'TEST',
    name: 'Test Token',
    decimals: 18,
    isWhitelisted: true,
    isBlacklisted: false,
    safetyScore: 85,
    liquidity: 1000000,
    volume24h: 500000,
    priceUSD: 1.0,
    lastUpdated: Date.now(),
    network: 'ethereum'
  }
};

// Extend Jest matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeWithinRange(floor: number, ceiling: number): R;
    }
  }
  
  var testUtils: {
    delay: (ms: number) => Promise<void>;
    mockPriceData: any;
    mockOpportunity: any;
    mockToken: any;
  };
}

expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
});
