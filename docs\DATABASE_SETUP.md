# Database Setup Guide

This guide will help you set up Supabase and InfluxDB for the MEV Arbitrage Bot.

## 🗄️ Supabase Setup

Supabase is used for storing structured data like trades, opportunities, and performance metrics.

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/login
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `mev-arbitrage-bot`
   - **Database Password**: Choose a strong password
   - **Region**: Choose closest to your deployment
5. Click "Create new project"

### 2. Get Your Supabase Credentials

Once your project is created:

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://your-project-ref.supabase.co`)
   - **anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)
   - **service_role secret key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)

3. Go to **Settings** → **Database**
4. Copy the **Connection string** (URI format)

### 3. Update Environment Variables

Add these to your `.env` file:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_JWT_SECRET=your-jwt-secret-from-settings
SUPABASE_DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project-ref.supabase.co:5432/postgres
```

### 4. Create Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Copy the contents of `database/supabase-schema.sql`
3. Paste and run the SQL script
4. This will create all necessary tables, indexes, and policies

### 5. Verify Setup

The schema creates these tables:

- `trades` - Trade execution records
- `opportunities` - Arbitrage opportunities
- `performance_metrics` - Daily performance data
- `tokens` - Token whitelist/blacklist
- `system_alerts` - System alerts and notifications
- `configuration` - System configuration

## 📊 InfluxDB Setup

InfluxDB is used for time-series data like price feeds, real-time metrics, and analytics.

### Option 1: Local InfluxDB Installation

#### Install InfluxDB

**Windows:**

```bash
# Download from https://portal.influxdata.com/downloads/
# Or use chocolatey
choco install influxdb
```

**macOS:**

```bash
brew install influxdb
```

**Linux (Ubuntu/Debian):**

```bash
wget -qO- https://repos.influxdata.com/influxdb.key | sudo apt-key add -
echo "deb https://repos.influxdata.com/ubuntu focal stable" | sudo tee /etc/apt/sources.list.d/influxdb.list
sudo apt update
sudo apt install influxdb2
```

#### Setup InfluxDB

1. Start InfluxDB:

```bash
influxd
```

2. Open InfluxDB UI: <http://localhost:8086>

3. Complete the initial setup:
   - **Username**: `admin`
   - **Password**: Choose a strong password
   - **Organization**: `mev-arbitrage-org`
   - **Bucket**: `mev-arbitrage-metrics`

4. Create an API token:
   - Go to **Data** → **API Tokens**
   - Click **Generate API Token** → **All Access API Token**
   - Copy the token

### Option 2: InfluxDB Cloud

1. Go to [cloud2.influxdata.com](https://cloud2.influxdata.com)
2. Sign up for a free account
3. Create a new organization: `mev-arbitrage-org`
4. Create a bucket: `mev-arbitrage-metrics`
5. Generate an API token with read/write access

### Update Environment Variables

Add these to your `.env` file:

```env
# InfluxDB Configuration
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token-here
INFLUXDB_ORG=mev-arbitrage-org
INFLUXDB_BUCKET=mev-arbitrage-metrics
INFLUXDB_USERNAME=admin
INFLUXDB_PASSWORD=your-influxdb-password
```

For InfluxDB Cloud, use your cloud URL:

```env
INFLUXDB_URL=https://us-west-2-1.aws.cloud2.influxdata.com
```

## 🧪 Testing the Setup

### Test Supabase Connection

```bash
# Run the backend with the new environment variables
npm run dev:backend

# Check logs for "Supabase service initialized successfully"
```

### Test InfluxDB Connection

```bash
# The backend will log "InfluxDB service initialized successfully"
# You can also check the InfluxDB UI for incoming data
```

### Verify Data Flow

1. Start the complete system:

```bash
# Terminal 1: Hardhat network
npx hardhat node --config hardhat.config.cjs

# Terminal 2: Backend
npm run dev:backend

# Terminal 3: Frontend
npm run dev:frontend
```

2. Check data in Supabase:
   - Go to **Table Editor**
   - Check `opportunities` table for new entries
   - Check `trades` table when trades are executed

3. Check data in InfluxDB:
   - Go to **Data Explorer**
   - Query the `mev-arbitrage-metrics` bucket
   - Look for measurements: `price_data`, `opportunities`, `trades`, `system_metrics`

## 📈 Data Retention and Cleanup

### Supabase

The system automatically cleans up old data:

- **Opportunities**: Kept for 90 days
- **Trades**: Kept indefinitely (for compliance)
- **Performance Metrics**: Kept for 1 year
- **System Alerts**: Kept for 30 days

### InfluxDB

Configure retention policies:

```bash
# Set retention policy for 30 days
influx bucket update \
  --name mev-arbitrage-metrics \
  --retention 720h \
  --org mev-arbitrage-org
```

## 🔒 Security Best Practices

### Supabase

1. **Row Level Security (RLS)**: Already enabled in the schema
2. **API Keys**: Never expose service role key in frontend
3. **Database Access**: Use connection pooling for production
4. **Backups**: Enable automatic backups in Supabase dashboard

### InfluxDB

1. **Authentication**: Always use tokens, never disable auth
2. **Network**: Restrict access to trusted IPs
3. **HTTPS**: Use HTTPS in production
4. **Token Rotation**: Regularly rotate API tokens

## 🚨 Monitoring and Alerts

### Health Checks

The system includes health checks for both databases:

```typescript
// Check in your application
const supabaseHealthy = supabaseService.isHealthy();
const influxHealthy = influxDBService.isHealthy();
```

### Alerts

Set up alerts for:

- Database connection failures
- High query latency
- Storage usage limits
- Failed writes

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [InfluxDB Documentation](https://docs.influxdata.com/)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [InfluxDB JavaScript Client](https://github.com/influxdata/influxdb-client-js)
