import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import rateLimit from 'express-rate-limit';
import logger from './utils/logger.js';
import config from './config/index.js';
import { ServiceIntegrator } from './services/ServiceIntegrator.js';
import { webSocketService } from './websocket/EnhancedWebSocketService.js';
import { performanceMonitoringService } from './services/PerformanceMonitoringService.js';
import { dataRoutingService } from './services/DataRoutingService.js';
import { enhancedCacheService } from './services/EnhancedCacheService.js';
import { databaseManager } from './services/DatabaseConnectionManager.js';

export class EnhancedMEVArbitrageServer {
  private app: express.Application;
  private server: any;
  private serviceIntegrator: ServiceIntegrator;
  private isShuttingDown = false;

  constructor() {
    this.app = express();
    this.serviceIntegrator = new ServiceIntegrator();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // CORS configuration
    this.app.use(cors({
      origin: config.CORS_ORIGIN.split(','),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(config.API_RATE_LIMIT_WINDOW),
      max: parseInt(config.API_RATE_LIMIT_MAX),
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        performanceMonitoringService.recordError('rate_limit');
        res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil(parseInt(config.API_RATE_LIMIT_WINDOW) / 1000)
        });
      }
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging and performance monitoring
    this.app.use((req, res, next) => {
      const operationId = `${req.method}_${req.path}_${Date.now()}`;
      performanceMonitoringService.startOperation(operationId);
      
      res.on('finish', () => {
        const latency = performanceMonitoringService.endOperation(operationId, 'api');
        
        if (latency > parseInt(config.MAX_API_RESPONSE_TIME)) {
          logger.warn(`Slow API response: ${req.method} ${req.path} took ${latency}ms`);
        }
        
        logger.info(`${req.method} ${req.path} - ${res.statusCode} - ${latency}ms`);
      });
      
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', async (req, res) => {
      try {
        const healthStatus = await this.getSystemHealth();
        res.json({
          success: true,
          status: healthStatus.status,
          timestamp: Date.now(),
          uptime: process.uptime(),
          data: healthStatus
        });
      } catch (error) {
        logger.error('Health check failed:', error);
        res.status(500).json({
          success: false,
          error: 'Health check failed',
          timestamp: Date.now()
        });
      }
    });

    // Enhanced system metrics endpoint
    this.app.get('/api/system/metrics', async (req, res) => {
      try {
        const metrics = {
          performance: performanceMonitoringService.getCurrentMetrics(),
          cache: enhancedCacheService.getMetrics(),
          dataRouting: dataRoutingService.getMetrics(),
          database: Object.fromEntries(databaseManager.getHealthStatus()),
          webSocket: webSocketService.getMetrics()
        };

        res.json({
          success: true,
          data: metrics,
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error fetching system metrics:', error);
        performanceMonitoringService.recordError('metrics_fetch');
        res.status(500).json({
          success: false,
          error: 'Failed to fetch system metrics'
        });
      }
    });

    // Performance alerts endpoint
    this.app.get('/api/system/alerts', async (req, res) => {
      try {
        const alerts = performanceMonitoringService.getActiveAlerts();
        res.json({
          success: true,
          data: alerts,
          count: alerts.length,
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error fetching alerts:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch alerts'
        });
      }
    });

    // Enhanced opportunities endpoint with caching
    this.app.get('/api/opportunities', async (req, res) => {
      try {
        const { limit = 20, offset = 0 } = req.query;
        const cacheKey = `opportunities_${limit}_${offset}`;
        
        // Try cache first
        let opportunities = await enhancedCacheService.get(cacheKey, 'opportunity');
        
        if (!opportunities) {
          // Fetch from data routing service
          opportunities = await dataRoutingService.retrieveData('opportunity.detected', {
            limit: parseInt(limit as string),
            offset: parseInt(offset as string),
            orderBy: { field: 'created_at', ascending: false }
          });
          
          // Cache the result
          if (opportunities) {
            await enhancedCacheService.set(cacheKey, opportunities, 60, 'opportunity');
          }
        }

        res.json({
          success: true,
          data: opportunities || [],
          pagination: {
            limit: parseInt(limit as string),
            offset: parseInt(offset as string),
            total: opportunities?.length || 0
          },
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error fetching opportunities:', error);
        performanceMonitoringService.recordError('opportunities_fetch');
        res.status(500).json({
          success: false,
          error: 'Failed to fetch opportunities'
        });
      }
    });

    // Enhanced trades endpoint with caching
    this.app.get('/api/trades', async (req, res) => {
      try {
        const { limit = 20, offset = 0 } = req.query;
        const cacheKey = `trades_${limit}_${offset}`;
        
        let trades = await enhancedCacheService.get(cacheKey, 'trade');
        
        if (!trades) {
          trades = await dataRoutingService.retrieveData('opportunity.execution', {
            limit: parseInt(limit as string),
            offset: parseInt(offset as string),
            orderBy: { field: 'created_at', ascending: false }
          });
          
          if (trades) {
            await enhancedCacheService.set(cacheKey, trades, 60, 'trade');
          }
        }

        res.json({
          success: true,
          data: trades || [],
          pagination: {
            limit: parseInt(limit as string),
            offset: parseInt(offset as string),
            total: trades?.length || 0
          },
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error fetching trades:', error);
        performanceMonitoringService.recordError('trades_fetch');
        res.status(500).json({
          success: false,
          error: 'Failed to fetch trades'
        });
      }
    });

    // Queue status endpoint
    this.app.get('/api/queue/status', async (req, res) => {
      try {
        const queueStatus = await enhancedCacheService.getQueueStatus();
        res.json({
          success: true,
          data: queueStatus || { length: 0, items: [] },
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error fetching queue status:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch queue status'
        });
      }
    });

    // Service integration stats
    this.app.get('/api/system/integration', (req, res) => {
      try {
        const stats = this.serviceIntegrator.getIntegrationStats();
        res.json({
          success: true,
          data: stats,
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error fetching integration stats:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to fetch integration stats'
        });
      }
    });

    // Cache management endpoints
    this.app.post('/api/cache/flush', async (req, res) => {
      try {
        await enhancedCacheService.flush();
        res.json({
          success: true,
          message: 'Cache flushed successfully',
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error flushing cache:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to flush cache'
        });
      }
    });

    // Fallback for undefined routes
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        path: req.originalUrl,
        timestamp: Date.now()
      });
    });
  }

  private setupErrorHandling(): void {
    // Global error handler
    this.app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error:', error);
      performanceMonitoringService.recordError('unhandled_error');
      
      if (res.headersSent) {
        return next(error);
      }
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: Date.now(),
        ...(config.NODE_ENV === 'development' && { details: error.message })
      });
    });

    // Graceful shutdown handling
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      this.gracefulShutdown('uncaughtException');
    });
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown('unhandledRejection');
    });
  }

  private async getSystemHealth(): Promise<any> {
    const serviceHealth = await this.serviceIntegrator.healthCheck();
    const performanceHealth = performanceMonitoringService.getSystemHealth();
    const databaseHealth = Object.fromEntries(databaseManager.getHealthStatus());
    
    const overallHealth = {
      status: performanceHealth.status,
      score: performanceHealth.score,
      services: serviceHealth,
      databases: databaseHealth,
      performance: {
        uptime: performanceHealth.score,
        issues: performanceHealth.issues
      },
      timestamp: Date.now()
    };

    return overallHealth;
  }

  public async start(): Promise<void> {
    try {
      logger.info('Starting Enhanced MEV Arbitrage Bot Server...');

      // Initialize all services
      await this.serviceIntegrator.initialize();

      // Create HTTP server
      this.server = createServer(this.app);

      // Initialize WebSocket service
      webSocketService.initialize(this.server);

      // Start listening
      const port = parseInt(config.PORT);
      this.server.listen(port, () => {
        logger.info(`Enhanced MEV Arbitrage Bot Server running on port ${port}`);
        logger.info(`WebSocket endpoint available at ws://localhost:${port}/ws`);
        logger.info('All enhanced services are operational');
      });

    } catch (error) {
      logger.error('Failed to start server:', error);
      throw error;
    }
  }

  private async gracefulShutdown(signal: string): Promise<void> {
    if (this.isShuttingDown) {
      logger.warn('Shutdown already in progress...');
      return;
    }

    this.isShuttingDown = true;
    logger.info(`Received ${signal}, starting graceful shutdown...`);

    try {
      // Stop accepting new connections
      if (this.server) {
        this.server.close();
      }

      // Shutdown WebSocket service
      webSocketService.shutdown();

      // Shutdown all services
      await this.serviceIntegrator.shutdown();

      // Shutdown database connections
      await databaseManager.shutdown();

      logger.info('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new EnhancedMEVArbitrageServer();
  server.start().catch((error) => {
    logger.error('Failed to start server:', error);
    process.exit(1);
  });
}
