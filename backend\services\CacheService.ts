import { createClient, RedisClientType } from 'redis';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/index.js';

export interface CacheOptions {
  ttl?: number;
  compress?: boolean;
  namespace?: string;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  hitRate: number;
  avgResponseTime: number;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  timeout: number;
  resetTimeout: number;
  monitoringPeriod: number;
}

export class CacheService extends EventEmitter {
  private client: RedisClientType | null = null;
  private isConnected = false;
  private connectionPool: RedisClientType[] = [];
  private poolSize = 15;
  private currentPoolIndex = 0;
  
  // Circuit breaker state
  private circuitBreaker = {
    state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
    failures: 0,
    lastFailureTime: 0,
    config: {
      failureThreshold: 5,
      timeout: 3000,
      resetTimeout: 30000,
      monitoringPeriod: 60000
    } as CircuitBreakerConfig
  };

  // Performance metrics
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
    hitRate: 0,
    avgResponseTime: 0
  };

  private responseTimes: number[] = [];
  private readonly maxResponseTimeHistory = 1000;

  // Cache key patterns for different data types
  private readonly keyPatterns = {
    opportunity: 'opportunity:{id}',
    prices: 'prices:{symbol}:{network}',
    queue: 'queue:execution',
    health: 'health:{service}',
    validation: 'validation:{id}',
    flashloan: 'flashloan:{provider}:{asset}',
    mev: 'mev:{method}:{network}',
    tokens: 'tokens:top50',
    analytics: 'analytics:{type}:{period}'
  };

  // TTL configurations for different data types
  private readonly ttlConfig = {
    opportunity: 60,      // 60 seconds
    prices: 30,           // 30 seconds
    queue: 120,           // 2 minutes
    health: 15,           // 15 seconds
    validation: 300,      // 5 minutes
    flashloan: 30,        // 30 seconds
    mev: 60,             // 60 seconds
    tokens: 300,         // 5 minutes
    analytics: 1800      // 30 minutes
  };

  constructor() {
    super();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      await this.createConnectionPool();
      this.startMetricsCollection();
      this.startCircuitBreakerMonitoring();
      logger.info('CacheService initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize CacheService:', error);
      this.handleCircuitBreakerFailure();
    }
  }

  private async createConnectionPool(): Promise<void> {
    const redisUrl = config.REDIS_URL || 'redis://localhost:6379';
    
    for (let i = 0; i < this.poolSize; i++) {
      const client = createClient({
        url: redisUrl,
        socket: {
          reconnectStrategy: (retries) => Math.min(retries * 50, 500),
          connectTimeout: 5000,
          lazyConnect: true
        },
        database: 0
      });

      client.on('error', (err) => {
        logger.error(`Redis pool client ${i} error:`, err);
        this.handleCircuitBreakerFailure();
      });

      client.on('connect', () => {
        logger.debug(`Redis pool client ${i} connected`);
      });

      client.on('ready', () => {
        this.isConnected = true;
        logger.debug(`Redis pool client ${i} ready`);
      });

      client.on('end', () => {
        logger.warn(`Redis pool client ${i} connection ended`);
      });

      try {
        await client.connect();
        this.connectionPool.push(client);
      } catch (error) {
        logger.error(`Failed to connect Redis pool client ${i}:`, error);
      }
    }

    if (this.connectionPool.length > 0) {
      this.client = this.connectionPool[0];
      this.isConnected = true;
      logger.info(`Redis connection pool created with ${this.connectionPool.length} connections`);
    } else {
      throw new Error('Failed to create any Redis connections');
    }
  }

  private getPooledClient(): RedisClientType | null {
    if (this.connectionPool.length === 0) return null;
    
    const client = this.connectionPool[this.currentPoolIndex];
    this.currentPoolIndex = (this.currentPoolIndex + 1) % this.connectionPool.length;
    
    return client;
  }

  private async executeWithCircuitBreaker<T>(
    operation: () => Promise<T>,
    fallback?: () => T
  ): Promise<T | null> {
    if (this.circuitBreaker.state === 'OPEN') {
      const timeSinceLastFailure = Date.now() - this.circuitBreaker.lastFailureTime;
      if (timeSinceLastFailure < this.circuitBreaker.config.resetTimeout) {
        logger.debug('Circuit breaker is OPEN, using fallback');
        return fallback ? fallback() : null;
      } else {
        this.circuitBreaker.state = 'HALF_OPEN';
        logger.info('Circuit breaker transitioning to HALF_OPEN');
      }
    }

    const startTime = Date.now();
    
    try {
      const result = await Promise.race([
        operation(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Operation timeout')), this.circuitBreaker.config.timeout)
        )
      ]);

      const responseTime = Date.now() - startTime;
      this.recordResponseTime(responseTime);

      if (this.circuitBreaker.state === 'HALF_OPEN') {
        this.circuitBreaker.state = 'CLOSED';
        this.circuitBreaker.failures = 0;
        logger.info('Circuit breaker reset to CLOSED');
      }

      return result;
    } catch (error) {
      this.handleCircuitBreakerFailure();
      logger.error('Cache operation failed:', error);
      return fallback ? fallback() : null;
    }
  }

  private handleCircuitBreakerFailure(): void {
    this.circuitBreaker.failures++;
    this.circuitBreaker.lastFailureTime = Date.now();
    this.metrics.errors++;

    if (this.circuitBreaker.failures >= this.circuitBreaker.config.failureThreshold) {
      this.circuitBreaker.state = 'OPEN';
      logger.warn(`Circuit breaker opened after ${this.circuitBreaker.failures} failures`);
      this.emit('circuitBreakerOpen');
    }
  }

  private recordResponseTime(responseTime: number): void {
    this.responseTimes.push(responseTime);
    if (this.responseTimes.length > this.maxResponseTimeHistory) {
      this.responseTimes.shift();
    }
    
    this.metrics.avgResponseTime = this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length;
  }

  private startMetricsCollection(): void {
    setInterval(() => {
      this.updateMetrics();
      this.emit('metricsUpdate', this.metrics);
    }, 60000); // Update every minute
  }

  private startCircuitBreakerMonitoring(): void {
    setInterval(() => {
      if (this.circuitBreaker.state === 'OPEN') {
        const timeSinceLastFailure = Date.now() - this.circuitBreaker.lastFailureTime;
        if (timeSinceLastFailure >= this.circuitBreaker.config.resetTimeout) {
          this.circuitBreaker.state = 'HALF_OPEN';
          logger.info('Circuit breaker transitioning to HALF_OPEN for testing');
        }
      }
    }, 10000); // Check every 10 seconds
  }

  private updateMetrics(): void {
    const total = this.metrics.hits + this.metrics.misses;
    this.metrics.hitRate = total > 0 ? (this.metrics.hits / total) * 100 : 0;
    
    if (this.metrics.hitRate < 80) {
      logger.warn(`Cache hit rate is low: ${this.metrics.hitRate.toFixed(2)}%`);
    }
  }

  private buildKey(pattern: string, params: Record<string, string>): string {
    let key = pattern;
    Object.entries(params).forEach(([param, value]) => {
      key = key.replace(`{${param}}`, value);
    });
    return key;
  }
