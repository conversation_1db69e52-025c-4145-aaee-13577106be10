import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import { ServiceManager } from './ServiceManager.js';
import { ArbitrageType } from '../services/OpportunityDetectionService.js';

export interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
  critical: boolean;
}

export interface SystemTestResult {
  allPassed: boolean;
  passedCount: number;
  totalCount: number;
  tests: TestResult[];
  criticalFailures: string[];
  warnings: string[];
  totalDuration: number;
}

export class SystemTester extends EventEmitter {
  private serviceManager: ServiceManager | null = null;
  private readonly TEST_TIMEOUT = 30000; // 30 seconds per test

  constructor(serviceManager?: ServiceManager) {
    super();
    this.serviceManager = serviceManager || null;
  }

  public setServiceManager(serviceManager: ServiceManager): void {
    this.serviceManager = serviceManager;
  }

  public async runStartupTests(): Promise<SystemTestResult> {
    logger.info('🧪 Running system integration tests...');
    const startTime = Date.now();

    const tests: TestResult[] = [];
    const criticalFailures: string[] = [];
    const warnings: string[] = [];

    // Define test suite
    const testSuite = [
      { name: 'Service Communication', test: this.testServiceCommunication.bind(this), critical: true },
      { name: 'Database Connectivity', test: this.testDatabaseConnectivity.bind(this), critical: true },
      { name: 'Blockchain Connectivity', test: this.testBlockchainConnectivity.bind(this), critical: true },
      { name: 'Price Feed Integration', test: this.testPriceFeedIntegration.bind(this), critical: true },
      { name: 'ML Learning System', test: this.testMLLearningSystem.bind(this), critical: false },
      { name: 'Strategy Selection', test: this.testStrategySelection.bind(this), critical: false },
      { name: 'Opportunity Detection Flow', test: this.testOpportunityDetectionFlow.bind(this), critical: true },
      { name: 'Risk Management Integration', test: this.testRiskManagementIntegration.bind(this), critical: true },
      { name: 'WebSocket Connectivity', test: this.testWebSocketConnectivity.bind(this), critical: false },
      { name: 'API Endpoints', test: this.testAPIEndpoints.bind(this), critical: false }
    ];

    // Run tests
    for (const testDef of testSuite) {
      const result = await this.runTest(testDef.name, testDef.test, testDef.critical);
      tests.push(result);

      if (!result.passed) {
        if (result.critical) {
          criticalFailures.push(`${result.name}: ${result.error}`);
        } else {
          warnings.push(`${result.name}: ${result.error}`);
        }
      }

      this.emit('testCompleted', result);
    }

    const totalDuration = Date.now() - startTime;
    const passedCount = tests.filter(t => t.passed).length;

    const systemResult: SystemTestResult = {
      allPassed: criticalFailures.length === 0,
      passedCount,
      totalCount: tests.length,
      tests,
      criticalFailures,
      warnings,
      totalDuration
    };

    this.logTestResults(systemResult);
    return systemResult;
  }

  private async runTest(name: string, testFn: () => Promise<any>, critical: boolean): Promise<TestResult> {
    const startTime = Date.now();
    logger.debug(`Running test: ${name}`);

    try {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Test timeout')), this.TEST_TIMEOUT);
      });

      const result = await Promise.race([testFn(), timeoutPromise]);
      const duration = Date.now() - startTime;

      logger.debug(`✅ Test passed: ${name} (${duration}ms)`);
      return {
        name,
        passed: true,
        duration,
        details: result,
        critical
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      logger.error(`❌ Test failed: ${name} - ${errorMessage}`);
      return {
        name,
        passed: false,
        duration,
        error: errorMessage,
        critical
      };
    }
  }

  private async testServiceCommunication(): Promise<any> {
    if (!this.serviceManager) {
      throw new Error('ServiceManager not available');
    }

    const services = this.serviceManager.getServiceStatus();
    const healthyServices = Object.values(services).filter(Boolean).length;
    const totalServices = Object.keys(services).length;

    if (healthyServices === 0) {
      throw new Error('No services are healthy');
    }

    return {
      healthyServices,
      totalServices,
      services
    };
  }

  private async testDatabaseConnectivity(): Promise<any> {
    if (!this.serviceManager) {
      throw new Error('ServiceManager not available');
    }

    const supabaseService = this.serviceManager.getService('SupabaseService');
    if (!supabaseService) {
      throw new Error('SupabaseService not available');
    }

    if (!supabaseService.isHealthy()) {
      throw new Error('Supabase connection is not healthy');
    }

    // Test basic database operation
    try {
      await supabaseService.getPerformanceMetrics(1);
      return { status: 'connected', database: 'supabase' };
    } catch (error) {
      throw new Error(`Database operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async testBlockchainConnectivity(): Promise<any> {
    if (!this.serviceManager) {
      throw new Error('ServiceManager not available');
    }

    const executionService = this.serviceManager.getService('ExecutionService');
    if (!executionService) {
      throw new Error('ExecutionService not available');
    }

    if (!executionService.isHealthy()) {
      throw new Error('ExecutionService is not healthy');
    }

    // Test would verify blockchain connectivity
    // For now, just check service health
    return { status: 'connected', networks: ['ethereum', 'polygon', 'bsc'] };
  }

  private async testPriceFeedIntegration(): Promise<any> {
    if (!this.serviceManager) {
      throw new Error('ServiceManager not available');
    }

    const priceFeedService = this.serviceManager.getService('PriceFeedService');
    if (!priceFeedService) {
      throw new Error('PriceFeedService not available');
    }

    if (!priceFeedService.isHealthy()) {
      throw new Error('PriceFeedService is not healthy');
    }

    // Test price feed functionality
    const prices = priceFeedService.getAllPrices();
    if (!prices || Object.keys(prices).length === 0) {
      throw new Error('No price data available');
    }

    return { priceCount: Object.keys(prices).length };
  }

  private async testMLLearningSystem(): Promise<any> {
    if (!this.serviceManager) {
      throw new Error('ServiceManager not available');
    }

    const mlLearningService = this.serviceManager.getService('MLLearningService');
    if (!mlLearningService) {
      throw new Error('MLLearningService not available');
    }

    // Test ML service basic functionality
    const currentRegime = mlLearningService.getCurrentMarketRegime();
    const weights = mlLearningService.getStrategyWeights();

    return {
      marketRegime: currentRegime,
      strategyCount: weights.size
    };
  }

  private async testStrategySelection(): Promise<any> {
    if (!this.serviceManager) {
      throw new Error('ServiceManager not available');
    }

    const strategyService = this.serviceManager.getService('StrategySelectionService');
    if (!strategyService) {
      throw new Error('StrategySelectionService not available');
    }

    // Test strategy selection with mock data
    const mockOpportunities = [
      {
        id: 'test-opp-1',
        type: ArbitrageType.INTRA_CHAIN,
        tokenPair: 'ETH/USDC',
        potentialProfit: 100,
        confidence: 80,
        network: 'ethereum'
      }
    ];

    const mockMarketConditions = {
      volatility: 15,
      liquidity: 2000000,
      networkCongestion: 30
    };

    const decision = await strategyService.selectStrategy(
      mockOpportunities,
      mockMarketConditions,
      'ethereum'
    );

    if (!decision) {
      throw new Error('Strategy selection returned null');
    }

    return {
      selectedStrategy: decision.selectedStrategy,
      confidence: decision.confidence
    };
  }

  private async testOpportunityDetectionFlow(): Promise<any> {
    if (!this.serviceManager) {
      throw new Error('ServiceManager not available');
    }

    const opportunityService = this.serviceManager.getService('OpportunityDetectionService');
    if (!opportunityService) {
      throw new Error('OpportunityDetectionService not available');
    }

    if (!opportunityService.isHealthy()) {
      throw new Error('OpportunityDetectionService is not healthy');
    }

    const opportunities = opportunityService.getOpportunities();
    
    return {
      opportunityCount: opportunities.length,
      status: 'active'
    };
  }

  private async testRiskManagementIntegration(): Promise<any> {
    if (!this.serviceManager) {
      throw new Error('ServiceManager not available');
    }

    const riskService = this.serviceManager.getService('RiskManagementService');
    if (!riskService) {
      throw new Error('RiskManagementService not available');
    }

    if (!riskService.isHealthy()) {
      throw new Error('RiskManagementService is not healthy');
    }

    return { status: 'active', riskLevel: 'normal' };
  }

  private async testWebSocketConnectivity(): Promise<any> {
    // Test WebSocket functionality
    // For now, just return success
    return { status: 'connected', clients: 0 };
  }

  private async testAPIEndpoints(): Promise<any> {
    // Test API endpoints
    // For now, just return success
    return { status: 'available', endpoints: 6 };
  }

  private logTestResults(result: SystemTestResult): void {
    logger.info(`🧪 System tests completed: ${result.passedCount}/${result.totalCount} passed (${result.totalDuration}ms)`);

    if (result.allPassed) {
      logger.info('✅ All critical tests passed');
    } else {
      logger.error('❌ Some tests failed');
    }

    if (result.criticalFailures.length > 0) {
      logger.error('Critical test failures:');
      result.criticalFailures.forEach(failure => logger.error(`  - ${failure}`));
    }

    if (result.warnings.length > 0) {
      logger.warn('Test warnings:');
      result.warnings.forEach(warning => logger.warn(`  - ${warning}`));
    }

    // Log individual test results
    result.tests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      const critical = test.critical ? '[CRITICAL]' : '[OPTIONAL]';
      logger.debug(`${status} ${test.name} ${critical} (${test.duration}ms)`);
    });
  }

  // Additional test methods for specific scenarios
  public async runPerformanceTests(): Promise<SystemTestResult> {
    logger.info('🚀 Running performance tests...');
    
    const tests: TestResult[] = [];
    const startTime = Date.now();

    // Add performance-specific tests here
    const performanceTests = [
      { name: 'Response Time Test', test: this.testResponseTime.bind(this), critical: false },
      { name: 'Memory Usage Test', test: this.testMemoryUsage.bind(this), critical: false },
      { name: 'Concurrent Operations Test', test: this.testConcurrentOperations.bind(this), critical: false }
    ];

    for (const testDef of performanceTests) {
      const result = await this.runTest(testDef.name, testDef.test, testDef.critical);
      tests.push(result);
    }

    const totalDuration = Date.now() - startTime;
    const passedCount = tests.filter(t => t.passed).length;

    return {
      allPassed: passedCount === tests.length,
      passedCount,
      totalCount: tests.length,
      tests,
      criticalFailures: [],
      warnings: [],
      totalDuration
    };
  }

  private async testResponseTime(): Promise<any> {
    const startTime = Date.now();
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const responseTime = Date.now() - startTime;
    
    if (responseTime > 1000) {
      throw new Error(`Response time too high: ${responseTime}ms`);
    }

    return { responseTime };
  }

  private async testMemoryUsage(): Promise<any> {
    const memUsage = process.memoryUsage();
    const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
    
    if (heapUsedMB > 500) { // 500MB threshold
      throw new Error(`Memory usage too high: ${heapUsedMB.toFixed(2)}MB`);
    }

    return { heapUsedMB: heapUsedMB.toFixed(2) };
  }

  private async testConcurrentOperations(): Promise<any> {
    const operations = Array.from({ length: 10 }, (_, i) => 
      new Promise(resolve => setTimeout(() => resolve(i), Math.random() * 100))
    );

    const results = await Promise.all(operations);
    
    return { operationsCompleted: results.length };
  }
}
