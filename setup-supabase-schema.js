// Automatic Supabase Schema Setup Script
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🗄️ Setting up Supabase Database Schema...\n');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// SQL Schema broken into smaller chunks for execution
const schemaQueries = [
  // Enable extensions
  `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
  
  // Create trades table
  `CREATE TABLE IF NOT EXISTS trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    assets TEXT[] NOT NULL,
    exchanges TEXT[] NOT NULL,
    executed_profit DECIMAL(18, 8) NOT NULL,
    gas_fees DECIMAL(18, 8) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failed', 'pending')),
    timestamp TIMESTAMPTZ NOT NULL,
    network VARCHAR(50) NOT NULL,
    tx_hash VARCHAR(66),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
  );`,
  
  // Create opportunities table
  `CREATE TABLE IF NOT EXISTS opportunities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL,
    assets TEXT[] NOT NULL,
    exchanges TEXT[] NOT NULL,
    potential_profit DECIMAL(18, 8) NOT NULL,
    profit_percentage DECIMAL(8, 4) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    network VARCHAR(50) NOT NULL,
    confidence DECIMAL(5, 2) NOT NULL CHECK (confidence >= 0 AND confidence <= 100),
    slippage DECIMAL(5, 4) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
  );`,
  
  // Create performance_metrics table
  `CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE UNIQUE NOT NULL,
    total_trades INTEGER NOT NULL DEFAULT 0,
    successful_trades INTEGER NOT NULL DEFAULT 0,
    failed_trades INTEGER NOT NULL DEFAULT 0,
    total_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    total_loss DECIMAL(18, 8) NOT NULL DEFAULT 0,
    net_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    win_rate DECIMAL(5, 2) NOT NULL DEFAULT 0,
    avg_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    avg_loss DECIMAL(18, 8) NOT NULL DEFAULT 0,
    profit_factor DECIMAL(10, 4) NOT NULL DEFAULT 0,
    sharpe_ratio DECIMAL(10, 4) NOT NULL DEFAULT 0,
    max_drawdown DECIMAL(10, 4) NOT NULL DEFAULT 0,
    roi DECIMAL(10, 4) NOT NULL DEFAULT 0,
    daily_volume DECIMAL(18, 8) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
  );`,
  
  // Create tokens table
  `CREATE TABLE IF NOT EXISTS tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    address VARCHAR(42) NOT NULL,
    name VARCHAR(255) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    decimals INTEGER NOT NULL DEFAULT 18,
    network VARCHAR(50) NOT NULL,
    is_whitelisted BOOLEAN NOT NULL DEFAULT false,
    is_blacklisted BOOLEAN NOT NULL DEFAULT false,
    safety_score INTEGER NOT NULL DEFAULT 0 CHECK (safety_score >= 0 AND safety_score <= 100),
    liquidity DECIMAL(18, 8) NOT NULL DEFAULT 0,
    total_supply VARCHAR(78),
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(address, network)
  );`,
  
  // Create system_alerts table
  `CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    is_resolved BOOLEAN NOT NULL DEFAULT false,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
  );`,
  
  // Create configuration table
  `CREATE TABLE IF NOT EXISTS configuration (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL DEFAULT 'string' CHECK (type IN ('string', 'number', 'boolean', 'json')),
    is_sensitive BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
  );`
];

const indexQueries = [
  // Trades indexes
  `CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp DESC);`,
  `CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status);`,
  `CREATE INDEX IF NOT EXISTS idx_trades_network ON trades(network);`,
  `CREATE INDEX IF NOT EXISTS idx_trades_opportunity_id ON trades(opportunity_id);`,
  `CREATE INDEX IF NOT EXISTS idx_trades_created_at ON trades(created_at DESC);`,
  
  // Opportunities indexes
  `CREATE INDEX IF NOT EXISTS idx_opportunities_timestamp ON opportunities(timestamp DESC);`,
  `CREATE INDEX IF NOT EXISTS idx_opportunities_network ON opportunities(network);`,
  `CREATE INDEX IF NOT EXISTS idx_opportunities_type ON opportunities(type);`,
  `CREATE INDEX IF NOT EXISTS idx_opportunities_profit ON opportunities(potential_profit DESC);`,
  `CREATE INDEX IF NOT EXISTS idx_opportunities_created_at ON opportunities(created_at DESC);`,
  
  // Performance metrics indexes
  `CREATE INDEX IF NOT EXISTS idx_performance_metrics_date ON performance_metrics(date DESC);`,
  
  // Tokens indexes
  `CREATE INDEX IF NOT EXISTS idx_tokens_address_network ON tokens(address, network);`,
  `CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON tokens(symbol);`,
  `CREATE INDEX IF NOT EXISTS idx_tokens_whitelisted ON tokens(is_whitelisted);`,
  `CREATE INDEX IF NOT EXISTS idx_tokens_safety_score ON tokens(safety_score DESC);`,
  
  // System alerts indexes
  `CREATE INDEX IF NOT EXISTS idx_system_alerts_type ON system_alerts(type);`,
  `CREATE INDEX IF NOT EXISTS idx_system_alerts_severity ON system_alerts(severity);`,
  `CREATE INDEX IF NOT EXISTS idx_system_alerts_resolved ON system_alerts(is_resolved);`,
  `CREATE INDEX IF NOT EXISTS idx_system_alerts_created_at ON system_alerts(created_at DESC);`,
  
  // Configuration indexes
  `CREATE INDEX IF NOT EXISTS idx_configuration_key ON configuration(key);`
];

const triggerQueries = [
  // Create trigger function
  `CREATE OR REPLACE FUNCTION update_updated_at_column()
   RETURNS TRIGGER AS $$
   BEGIN
       NEW.updated_at = NOW();
       RETURN NEW;
   END;
   $$ language 'plpgsql';`,
  
  // Apply triggers
  `CREATE TRIGGER update_trades_updated_at BEFORE UPDATE ON trades
   FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();`,
  
  `CREATE TRIGGER update_performance_metrics_updated_at BEFORE UPDATE ON performance_metrics
   FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();`,
  
  `CREATE TRIGGER update_configuration_updated_at BEFORE UPDATE ON configuration
   FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();`
];

const rlsQueries = [
  // Enable RLS
  `ALTER TABLE trades ENABLE ROW LEVEL SECURITY;`,
  `ALTER TABLE opportunities ENABLE ROW LEVEL SECURITY;`,
  `ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;`,
  `ALTER TABLE tokens ENABLE ROW LEVEL SECURITY;`,
  `ALTER TABLE system_alerts ENABLE ROW LEVEL SECURITY;`,
  `ALTER TABLE configuration ENABLE ROW LEVEL SECURITY;`,
  
  // Create policies
  `CREATE POLICY "Service role can manage trades" ON trades FOR ALL USING (auth.role() = 'service_role');`,
  `CREATE POLICY "Service role can manage opportunities" ON opportunities FOR ALL USING (auth.role() = 'service_role');`,
  `CREATE POLICY "Service role can manage performance_metrics" ON performance_metrics FOR ALL USING (auth.role() = 'service_role');`,
  `CREATE POLICY "Service role can manage tokens" ON tokens FOR ALL USING (auth.role() = 'service_role');`,
  `CREATE POLICY "Service role can manage system_alerts" ON system_alerts FOR ALL USING (auth.role() = 'service_role');`,
  `CREATE POLICY "Service role can manage configuration" ON configuration FOR ALL USING (auth.role() = 'service_role');`
];

async function executeQuery(query, description) {
  try {
    const { error } = await supabase.rpc('exec_sql', { sql: query });
    if (error) {
      console.log(`❌ ${description}: ${error.message}`);
      return false;
    } else {
      console.log(`✅ ${description}`);
      return true;
    }
  } catch (error) {
    console.log(`❌ ${description}: ${error.message}`);
    return false;
  }
}

async function setupSchema() {
  console.log('🚀 Starting Supabase schema setup...\n');
  
  let successCount = 0;
  let totalQueries = 0;
  
  // Execute schema queries
  console.log('📋 Creating tables...');
  for (const query of schemaQueries) {
    totalQueries++;
    const success = await executeQuery(query, `Table creation`);
    if (success) successCount++;
  }
  
  // Execute index queries
  console.log('\n📊 Creating indexes...');
  for (const query of indexQueries) {
    totalQueries++;
    const success = await executeQuery(query, `Index creation`);
    if (success) successCount++;
  }
  
  // Execute trigger queries
  console.log('\n⚡ Creating triggers...');
  for (const query of triggerQueries) {
    totalQueries++;
    const success = await executeQuery(query, `Trigger creation`);
    if (success) successCount++;
  }
  
  // Execute RLS queries
  console.log('\n🔒 Setting up Row Level Security...');
  for (const query of rlsQueries) {
    totalQueries++;
    const success = await executeQuery(query, `RLS setup`);
    if (success) successCount++;
  }
  
  console.log(`\n📊 Schema setup completed: ${successCount}/${totalQueries} queries successful`);
  
  if (successCount === totalQueries) {
    console.log('🎉 Supabase schema setup completed successfully!');
    return true;
  } else {
    console.log('⚠️  Some queries failed. The schema may be partially set up.');
    return false;
  }
}

setupSchema().then(success => {
  if (success) {
    console.log('\n✅ Your Supabase database is now ready!');
    console.log('🧪 Run the connection test: node test-database-connections.js');
  } else {
    console.log('\n❌ Schema setup encountered issues.');
    console.log('💡 Try running the SQL manually in Supabase SQL Editor.');
  }
});
