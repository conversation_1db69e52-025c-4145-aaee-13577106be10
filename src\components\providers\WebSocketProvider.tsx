'use client';

import { createContext, useContext, useEffect, useRef, useState } from 'react';
import useWebSocket, { ReadyState } from 'react-use-websocket';
import { toast } from 'react-hot-toast';
import { ComponentWithChildren, WebSocketMessage } from '@/types';

interface WebSocketContextType {
  lastMessage: WebSocketMessage | null;
  readyState: ReadyState;
  sendMessage: (message: any) => void;
  isConnected: boolean;
  connectionStatus: string;
  subscribe: (channel: string) => void;
  unsubscribe: (channel: string) => void;
  metrics: {
    messagesReceived: number;
    messagesSent: number;
    reconnectAttempts: number;
    lastReconnectTime: number | null;
  };
}

const WebSocketContext = createContext<WebSocketContextType | null>(null);

export function useWebSocketContext() {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocketContext must be used within a WebSocketProvider');
  }
  return context;
}

export function WebSocketProvider({ children }: ComponentWithChildren) {
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [subscriptions, setSubscriptions] = useState<Set<string>>(new Set());
  const [metrics, setMetrics] = useState({
    messagesReceived: 0,
    messagesSent: 0,
    reconnectAttempts: 0,
    lastReconnectTime: null as number | null,
  });

  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectInterval = useRef<NodeJS.Timeout | null>(null);

  const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8080/ws';

  const {
    sendMessage: wsSendMessage,
    lastMessage: wsLastMessage,
    readyState,
  } = useWebSocket(
    wsUrl,
    {
      onOpen: () => {
        console.log('WebSocket connected');
        reconnectAttemptsRef.current = 0;
        toast.success('Real-time connection established');
        
        // Resubscribe to channels after reconnection
        subscriptions.forEach(channel => {
          wsSendMessage(JSON.stringify({
            type: 'subscribe',
            data: { type: channel }
          }));
        });
      },
      onClose: (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        
        if (event.code !== 1000) { // Not a normal closure
          toast.error('Real-time connection lost');
          
          // Attempt to reconnect
          if (reconnectAttemptsRef.current < maxReconnectAttempts) {
            const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
            
            reconnectInterval.current = setTimeout(() => {
              reconnectAttemptsRef.current++;
              setMetrics(prev => ({
                ...prev,
                reconnectAttempts: prev.reconnectAttempts + 1,
                lastReconnectTime: Date.now(),
              }));
              
              toast.loading(`Reconnecting... (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);
            }, delay);
          } else {
            toast.error('Failed to reconnect. Please refresh the page.');
          }
        }
      },
      onError: (event) => {
        console.error('WebSocket error:', event);
        toast.error('WebSocket connection error');
      },
      onMessage: (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          setLastMessage(message);
          setMetrics(prev => ({
            ...prev,
            messagesReceived: prev.messagesReceived + 1,
          }));
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      },
      shouldReconnect: (closeEvent) => {
        // Reconnect unless it was a normal closure
        return closeEvent.code !== 1000 && reconnectAttemptsRef.current < maxReconnectAttempts;
      },
      reconnectAttempts: maxReconnectAttempts,
      reconnectInterval: (attemptNumber) => Math.min(1000 * Math.pow(2, attemptNumber), 30000),
    },
    // Only connect in browser environment
    typeof window !== 'undefined'
  );

  // Process incoming messages
  useEffect(() => {
    if (wsLastMessage) {
      try {
        const message: WebSocketMessage = JSON.parse(wsLastMessage.data);
        setLastMessage(message);
        setMetrics(prev => ({
          ...prev,
          messagesReceived: prev.messagesReceived + 1,
        }));
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    }
  }, [wsLastMessage]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectInterval.current) {
        clearTimeout(reconnectInterval.current);
      }
    };
  }, []);

  const sendMessage = (message: any) => {
    if (readyState === ReadyState.OPEN) {
      wsSendMessage(JSON.stringify(message));
      setMetrics(prev => ({
        ...prev,
        messagesSent: prev.messagesSent + 1,
      }));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
      toast.warning('Cannot send message: WebSocket not connected');
    }
  };

  const subscribe = (channel: string) => {
    setSubscriptions(prev => new Set([...prev, channel]));
    
    if (readyState === ReadyState.OPEN) {
      sendMessage({
        type: 'subscribe',
        data: { type: channel }
      });
    }
  };

  const unsubscribe = (channel: string) => {
    setSubscriptions(prev => {
      const newSet = new Set(prev);
      newSet.delete(channel);
      return newSet;
    });
    
    if (readyState === ReadyState.OPEN) {
      sendMessage({
        type: 'unsubscribe',
        data: { type: channel }
      });
    }
  };

  const isConnected = readyState === ReadyState.OPEN;
  
  const getConnectionStatus = () => {
    switch (readyState) {
      case ReadyState.CONNECTING:
        return 'Connecting...';
      case ReadyState.OPEN:
        return 'Connected';
      case ReadyState.CLOSING:
        return 'Disconnecting...';
      case ReadyState.CLOSED:
        return reconnectAttemptsRef.current > 0 ? 'Reconnecting...' : 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  const value: WebSocketContextType = {
    lastMessage,
    readyState,
    sendMessage,
    isConnected,
    connectionStatus: getConnectionStatus(),
    subscribe,
    unsubscribe,
    metrics,
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
}
