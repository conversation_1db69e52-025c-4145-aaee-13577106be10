'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { Activity, TrendingUp, Zap, Shield, Database, Network } from 'lucide-react';

import { useDashboardData } from '@/hooks/useApi';
import { useWebSocketContext } from '@/components/providers/WebSocketProvider';
import { LoadingSpinner, SkeletonCard } from '@/components/ui/LoadingSpinner';
import { ErrorFallback } from '@/components/ui/ErrorBoundary';

// Import dashboard components (to be created)
import { DashboardHeader } from '@/components/dashboard/DashboardHeader';
import { MetricsOverview } from '@/components/dashboard/MetricsOverview';
import { OpportunityMonitor } from '@/components/dashboard/OpportunityMonitor';
import { MultiChainPriceDashboard } from '@/components/dashboard/MultiChainPriceDashboard';
import { ExecutionQueueVisualization } from '@/components/dashboard/ExecutionQueueVisualization';
import { PerformanceAnalytics } from '@/components/dashboard/PerformanceAnalytics';
import { NetworkStatusPanel } from '@/components/dashboard/NetworkStatusPanel';
import { FlashLoanDisplay } from '@/components/dashboard/FlashLoanDisplay';
import { MEVProtectionStatus } from '@/components/dashboard/MEVProtectionStatus';
import { SystemHealthMonitor } from '@/components/dashboard/SystemHealthMonitor';

export function Dashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
  
  const {
    opportunities,
    trades,
    performance,
    systemHealth,
    networkStatus,
    executionQueue,
    mlStrategies,
    learningEvents,
    mlStats,
    isLoading,
    isError,
  } = useDashboardData();

  const { isConnected, connectionStatus, lastMessage } = useWebSocketContext();

  // Update timestamp when new data arrives
  useEffect(() => {
    if (lastMessage) {
      setLastUpdateTime(new Date());
    }
  }, [lastMessage]);

  // Show connection status notifications
  useEffect(() => {
    if (isConnected) {
      toast.success('Real-time connection established');
    }
  }, [isConnected]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="min-h-screen p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <div className="h-8 bg-dark-700 rounded w-1/3 mb-4 animate-pulse"></div>
            <div className="h-4 bg-dark-700 rounded w-1/2 animate-pulse"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {Array.from({ length: 4 }).map((_, i) => (
              <SkeletonCard key={i} />
            ))}
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <SkeletonCard key={i} className="h-96" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (isError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <ErrorFallback
          error={new Error('Failed to load dashboard data')}
          resetError={() => window.location.reload()}
        />
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Activity },
    { id: 'opportunities', label: 'Opportunities', icon: TrendingUp },
    { id: 'execution', label: 'Execution', icon: Zap },
    { id: 'analytics', label: 'Analytics', icon: Database },
    { id: 'networks', label: 'Networks', icon: Network },
    { id: 'protection', label: 'MEV Protection', icon: Shield },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
      {/* Header */}
      <DashboardHeader
        connectionStatus={connectionStatus}
        isConnected={isConnected}
        lastUpdateTime={lastUpdateTime}
        systemHealth={systemHealth.data}
      />

      {/* Navigation Tabs */}
      <div className="border-b border-dark-700 bg-dark-800/50 backdrop-blur-sm sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors
                    ${activeTab === tab.id
                      ? 'border-primary-500 text-primary-400'
                      : 'border-transparent text-dark-400 hover:text-dark-300 hover:border-dark-600'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto p-6">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            <MetricsOverview
              performance={performance.data}
              mlStats={mlStats.data}
              systemHealth={systemHealth.data}
            />
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <OpportunityMonitor
                opportunities={opportunities.data || []}
                isLoading={opportunities.isLoading}
              />
              
              <ExecutionQueueVisualization
                queue={executionQueue.data || []}
                isLoading={executionQueue.isLoading}
              />
            </div>
            
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              <div className="xl:col-span-2">
                <MultiChainPriceDashboard />
              </div>
              <SystemHealthMonitor
                systemHealth={systemHealth.data}
                networkStatus={networkStatus.data || []}
              />
            </div>
          </div>
        )}

        {activeTab === 'opportunities' && (
          <div className="space-y-6">
            <OpportunityMonitor
              opportunities={opportunities.data || []}
              isLoading={opportunities.isLoading}
              detailed={true}
            />
            <FlashLoanDisplay />
          </div>
        )}

        {activeTab === 'execution' && (
          <div className="space-y-6">
            <ExecutionQueueVisualization
              queue={executionQueue.data || []}
              isLoading={executionQueue.isLoading}
              detailed={true}
            />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <MEVProtectionStatus />
              <div className="glass-effect rounded-xl p-6">
                <h3 className="text-lg font-semibold text-dark-100 mb-4">Recent Trades</h3>
                {/* Trade history component will go here */}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <PerformanceAnalytics
            performance={performance.data}
            mlStrategies={mlStrategies.data || []}
            learningEvents={learningEvents.data || []}
          />
        )}

        {activeTab === 'networks' && (
          <div className="space-y-6">
            <NetworkStatusPanel
              networkStatus={networkStatus.data || []}
              isLoading={networkStatus.isLoading}
            />
            <MultiChainPriceDashboard detailed={true} />
          </div>
        )}

        {activeTab === 'protection' && (
          <div className="space-y-6">
            <MEVProtectionStatus detailed={true} />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="glass-effect rounded-xl p-6">
                <h3 className="text-lg font-semibold text-dark-100 mb-4">Protection Statistics</h3>
                {/* Protection stats component will go here */}
              </div>
              <div className="glass-effect rounded-xl p-6">
                <h3 className="text-lg font-semibold text-dark-100 mb-4">Fee Analysis</h3>
                {/* Fee analysis component will go here */}
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
