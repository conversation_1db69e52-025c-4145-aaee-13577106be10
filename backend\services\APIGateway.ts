import express, { Request, Response, NextFunction } from 'express';
import { EventEmitter } from 'events';
import rateLimit from 'express-rate-limit';
import cors from 'cors';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { enhancedCacheService } from './EnhancedCacheService.js';
import { performanceMonitoringService } from './PerformanceMonitoringService.js';

export interface APIRoute {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  handler: string; // Service method to call
  service: string; // Service name
  cache?: {
    enabled: boolean;
    ttl: number;
    key?: string;
  };
  rateLimit?: {
    windowMs: number;
    max: number;
  };
  auth?: boolean;
  validation?: any;
  priority: 'high' | 'medium' | 'low';
}

export interface APIMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  requestsPerSecond: number;
  cacheHitRatio: number;
  errorRate: number;
  lastReset: number;
}

export interface ServiceRegistry {
  name: string;
  instance: any;
  health: 'healthy' | 'degraded' | 'unhealthy';
  lastHealthCheck: number;
  responseTime: number;
  errorCount: number;
}

export class APIGateway extends EventEmitter {
  private app: express.Application;
  private routes: Map<string, APIRoute> = new Map();
  private services: Map<string, ServiceRegistry> = new Map();
  private metrics: APIMetrics;
  private isInitialized = false;

  constructor() {
    super();
    this.app = express();
    this.initializeMetrics();
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * Initialize API metrics
   */
  private initializeMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      requestsPerSecond: 0,
      cacheHitRatio: 0,
      errorRate: 0,
      lastReset: Date.now()
    };
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // CORS configuration
    this.app.use(cors({
      origin: config.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging and metrics
    this.app.use(this.requestLogger.bind(this));
    this.app.use(this.metricsMiddleware.bind(this));

    // Global rate limiting
    const globalRateLimit = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // Limit each IP to 1000 requests per windowMs
      message: 'Too many requests from this IP',
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use(globalRateLimit);

    // Health check endpoint
    this.app.get('/health', this.healthCheck.bind(this));
    this.app.get('/metrics', this.getMetrics.bind(this));
  }

  /**
   * Setup API routes with intelligent routing
   */
  private setupRoutes(): void {
    // Define all API routes with their configurations
    const routeConfigs: APIRoute[] = [
      // Opportunity routes
      {
        path: '/api/opportunities',
        method: 'GET',
        handler: 'getOpportunities',
        service: 'opportunityDetection',
        cache: { enabled: true, ttl: 30 },
        priority: 'high'
      },
      {
        path: '/api/opportunities/:id',
        method: 'GET',
        handler: 'getOpportunityById',
        service: 'opportunityDetection',
        cache: { enabled: true, ttl: 60 },
        priority: 'high'
      },
      
      // Trade routes
      {
        path: '/api/trades',
        method: 'GET',
        handler: 'getTrades',
        service: 'execution',
        cache: { enabled: true, ttl: 60 },
        priority: 'medium'
      },
      {
        path: '/api/trades',
        method: 'POST',
        handler: 'executeTrade',
        service: 'execution',
        auth: true,
        priority: 'high'
      },
      
      // Token routes
      {
        path: '/api/tokens',
        method: 'GET',
        handler: 'getTokens',
        service: 'tokenDiscovery',
        cache: { enabled: true, ttl: 300 },
        priority: 'medium'
      },
      {
        path: '/api/tokens/top50',
        method: 'GET',
        handler: 'getTop50Tokens',
        service: 'enhancedTokenMonitoring',
        cache: { enabled: true, ttl: 300 },
        priority: 'medium'
      },
      
      // Price routes
      {
        path: '/api/prices',
        method: 'GET',
        handler: 'getPrices',
        service: 'priceFeed',
        cache: { enabled: true, ttl: 30 },
        priority: 'high'
      },
      {
        path: '/api/prices/multi-chain',
        method: 'GET',
        handler: 'getMultiChainPrices',
        service: 'multiChain',
        cache: { enabled: true, ttl: 30 },
        priority: 'high'
      },
      
      // Analytics routes
      {
        path: '/api/analytics/performance',
        method: 'GET',
        handler: 'getPerformanceMetrics',
        service: 'analytics',
        cache: { enabled: true, ttl: 60 },
        priority: 'medium'
      },
      {
        path: '/api/analytics/profit',
        method: 'GET',
        handler: 'getProfitAnalytics',
        service: 'profitValidation',
        cache: { enabled: true, ttl: 120 },
        priority: 'medium'
      },
      
      // ML routes
      {
        path: '/api/ml/strategies',
        method: 'GET',
        handler: 'getMLStrategies',
        service: 'mlLearning',
        cache: { enabled: true, ttl: 300 },
        priority: 'low'
      },
      {
        path: '/api/ml/learning-events',
        method: 'GET',
        handler: 'getLearningEvents',
        service: 'mlLearning',
        cache: { enabled: true, ttl: 120 },
        priority: 'low'
      },
      
      // System routes
      {
        path: '/api/system/health',
        method: 'GET',
        handler: 'getSystemHealth',
        service: 'system',
        priority: 'high'
      },
      {
        path: '/api/system/networks',
        method: 'GET',
        handler: 'getNetworkStatus',
        service: 'multiChain',
        cache: { enabled: true, ttl: 60 },
        priority: 'medium'
      },
      
      // Flash loan routes
      {
        path: '/api/flash-loans/quotes',
        method: 'GET',
        handler: 'getFlashLoanQuotes',
        service: 'flashLoan',
        cache: { enabled: true, ttl: 30 },
        priority: 'high'
      },
      
      // Execution queue routes
      {
        path: '/api/execution/queue',
        method: 'GET',
        handler: 'getExecutionQueue',
        service: 'executionQueue',
        cache: { enabled: true, ttl: 15 },
        priority: 'high'
      },
      
      // MEV protection routes
      {
        path: '/api/mev/protection-status',
        method: 'GET',
        handler: 'getMEVProtectionStatus',
        service: 'mevProtection',
        cache: { enabled: true, ttl: 60 },
        priority: 'medium'
      }
    ];

    // Register all routes
    routeConfigs.forEach(route => {
      this.registerRoute(route);
    });

    logger.info(`Registered ${routeConfigs.length} API routes`);
  }

  /**
   * Register a new API route
   */
  public registerRoute(route: APIRoute): void {
    const routeKey = `${route.method}:${route.path}`;
    this.routes.set(routeKey, route);

    // Create Express route handler
    const handler = this.createRouteHandler(route);

    // Apply route-specific rate limiting if configured
    if (route.rateLimit) {
      const routeRateLimit = rateLimit({
        windowMs: route.rateLimit.windowMs,
        max: route.rateLimit.max,
        message: `Rate limit exceeded for ${route.path}`,
      });
      this.app.use(route.path, routeRateLimit);
    }

    // Register the route with Express
    switch (route.method) {
      case 'GET':
        this.app.get(route.path, handler);
        break;
      case 'POST':
        this.app.post(route.path, handler);
        break;
      case 'PUT':
        this.app.put(route.path, handler);
        break;
      case 'DELETE':
        this.app.delete(route.path, handler);
        break;
      case 'PATCH':
        this.app.patch(route.path, handler);
        break;
    }

    logger.debug(`Registered route: ${route.method} ${route.path} -> ${route.service}.${route.handler}`);
  }

  /**
   * Create route handler with caching, error handling, and metrics
   */
  private createRouteHandler(route: APIRoute) {
    return async (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      const requestId = this.generateRequestId();

      try {
        // Check cache first if enabled
        if (route.cache?.enabled) {
          const cacheKey = this.generateCacheKey(route, req);
          const cachedResponse = await enhancedCacheService.get(cacheKey);
          
          if (cachedResponse) {
            res.json(cachedResponse);
            this.recordMetrics(startTime, true, true);
            return;
          }
        }

        // Get service instance
        const serviceRegistry = this.services.get(route.service);
        if (!serviceRegistry || serviceRegistry.health === 'unhealthy') {
          throw new Error(`Service ${route.service} is not available`);
        }

        // Call service method
        const result = await this.callServiceMethod(
          serviceRegistry.instance,
          route.handler,
          req.params,
          req.query,
          req.body
        );

        // Cache the response if enabled
        if (route.cache?.enabled && result) {
          const cacheKey = this.generateCacheKey(route, req);
          await enhancedCacheService.set(cacheKey, result, route.cache.ttl);
        }

        // Send response
        res.json({
          success: true,
          data: result,
          requestId,
          timestamp: new Date().toISOString()
        });

        this.recordMetrics(startTime, true, false);

      } catch (error) {
        logger.error(`API Gateway error for ${route.method} ${route.path}:`, error);
        
        res.status(500).json({
          success: false,
          error: error.message,
          requestId,
          timestamp: new Date().toISOString()
        });

        this.recordMetrics(startTime, false, false);
      }
    };
  }

  /**
   * Register a service with the gateway
   */
  public registerService(name: string, instance: any): void {
    this.services.set(name, {
      name,
      instance,
      health: 'healthy',
      lastHealthCheck: Date.now(),
      responseTime: 0,
      errorCount: 0
    });

    logger.info(`Registered service: ${name}`);
  }

  /**
   * Call a service method with error handling
   */
  private async callServiceMethod(
    service: any,
    method: string,
    params: any,
    query: any,
    body: any
  ): Promise<any> {
    if (!service[method] || typeof service[method] !== 'function') {
      throw new Error(`Method ${method} not found on service`);
    }

    // Prepare arguments based on method signature
    const args = [];
    if (Object.keys(params).length > 0) args.push(params);
    if (Object.keys(query).length > 0) args.push(query);
    if (Object.keys(body).length > 0) args.push(body);

    return await service[method](...args);
  }

  /**
   * Generate cache key for request
   */
  private generateCacheKey(route: APIRoute, req: Request): string {
    const baseKey = `api:${route.service}:${route.handler}`;
    const paramsKey = Object.keys(req.params).length > 0 ? 
      `:params:${JSON.stringify(req.params)}` : '';
    const queryKey = Object.keys(req.query).length > 0 ? 
      `:query:${JSON.stringify(req.query)}` : '';
    
    return `${baseKey}${paramsKey}${queryKey}`;
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Request logging middleware
   */
  private requestLogger(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      logger.info(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
    });

    next();
  }

  /**
   * Metrics collection middleware
   */
  private metricsMiddleware(req: Request, res: Response, next: NextFunction): void {
    this.metrics.totalRequests++;
    
    res.on('finish', () => {
      if (res.statusCode >= 200 && res.statusCode < 400) {
        this.metrics.successfulRequests++;
      } else {
        this.metrics.failedRequests++;
      }
    });

    next();
  }

  /**
   * Record performance metrics
   */
  private recordMetrics(startTime: number, success: boolean, fromCache: boolean): void {
    const duration = Date.now() - startTime;
    
    // Update average response time
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime + duration) / 2;

    // Update cache hit ratio
    if (fromCache) {
      this.metrics.cacheHitRatio = 
        (this.metrics.cacheHitRatio * 0.9) + (1 * 0.1);
    } else {
      this.metrics.cacheHitRatio = 
        (this.metrics.cacheHitRatio * 0.9) + (0 * 0.1);
    }

    // Update error rate
    this.metrics.errorRate = 
      this.metrics.failedRequests / this.metrics.totalRequests;

    // Calculate requests per second
    const timeElapsed = (Date.now() - this.metrics.lastReset) / 1000;
    this.metrics.requestsPerSecond = this.metrics.totalRequests / timeElapsed;
  }

  /**
   * Health check endpoint
   */
  private async healthCheck(req: Request, res: Response): Promise<void> {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: Array.from(this.services.entries()).map(([name, registry]) => ({
        name,
        health: registry.health,
        lastCheck: registry.lastHealthCheck,
        responseTime: registry.responseTime
      })),
      metrics: this.metrics
    };

    res.json(health);
  }

  /**
   * Get API metrics
   */
  private async getMetrics(req: Request, res: Response): Promise<void> {
    res.json({
      success: true,
      data: this.metrics,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Initialize the API Gateway
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('API Gateway already initialized');
      return;
    }

    try {
      // Start health monitoring for services
      setInterval(() => {
        this.performServiceHealthChecks();
      }, 30000);

      this.isInitialized = true;
      logger.info('API Gateway initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize API Gateway:', error);
      throw error;
    }
  }

  /**
   * Perform health checks on registered services
   */
  private async performServiceHealthChecks(): Promise<void> {
    for (const [name, registry] of this.services.entries()) {
      try {
        const startTime = Date.now();
        
        // Call health check method if available
        if (registry.instance.healthCheck && 
            typeof registry.instance.healthCheck === 'function') {
          await registry.instance.healthCheck();
        }

        registry.health = 'healthy';
        registry.responseTime = Date.now() - startTime;
        registry.lastHealthCheck = Date.now();

      } catch (error) {
        logger.warn(`Service ${name} health check failed:`, error);
        registry.health = 'unhealthy';
        registry.errorCount++;
      }
    }
  }

  /**
   * Get Express app instance
   */
  public getApp(): express.Application {
    return this.app;
  }

  /**
   * Get current metrics
   */
  public getMetricsData(): APIMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  public resetMetrics(): void {
    this.initializeMetrics();
    logger.info('API Gateway metrics reset');
  }
}

// Export singleton instance
export const apiGateway = new APIGateway();
