import { jest, describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { performance } from 'perf_hooks';
import { EventEmitter } from 'events';
import logger from '../../backend/utils/logger.js';
import config from '../../backend/config/index.js';

// Import enhanced services for performance testing
import { ServiceIntegrator } from '../../backend/services/ServiceIntegrator.js';
import { ProfitValidationService } from '../../backend/services/ProfitValidationService.js';
import { EnhancedTokenMonitoringService } from '../../backend/services/EnhancedTokenMonitoringService.js';
import { ProfitPrioritizedExecutionQueue } from '../../backend/services/ProfitPrioritizedExecutionQueue.js';
import { FlashLoanService } from '../../backend/services/FlashLoanService.js';
import { ArbitrageOpportunity, ArbitrageType } from '../../backend/services/OpportunityDetectionService.js';

interface PerformanceMetrics {
  latency: {
    average: number;
    min: number;
    max: number;
    p95: number;
    p99: number;
  };
  throughput: {
    operationsPerSecond: number;
    totalOperations: number;
    duration: number;
  };
  memory: {
    initialHeapUsed: number;
    finalHeapUsed: number;
    peakHeapUsed: number;
    memoryIncrease: number;
  };
  errors: {
    count: number;
    rate: number;
  };
}

describe('Enhanced System Performance Benchmarks', () => {
  let serviceIntegrator: ServiceIntegrator;
  let profitValidationService: ProfitValidationService;
  let tokenMonitoringService: EnhancedTokenMonitoringService;
  let executionQueue: ProfitPrioritizedExecutionQueue;
  let flashLoanService: FlashLoanService;

  const mockOpportunity: ArbitrageOpportunity = {
    id: 'perf-test-opportunity',
    type: ArbitrageType.INTRA_CHAIN,
    assets: ['ETH', 'USDC'],
    exchanges: ['uniswap', 'sushiswap'],
    potentialProfit: 100,
    profitPercentage: 5,
    timestamp: Date.now(),
    network: 'ethereum',
    route: {
      steps: [],
      totalGasCost: 150000,
      expectedProfit: 100,
      priceImpact: 0.5
    },
    estimatedGas: 150000,
    slippage: 0.5,
    confidence: 95
  };

  beforeAll(async () => {
    // Initialize system for performance testing
    serviceIntegrator = new ServiceIntegrator({
      enablePreExecutionValidation: true,
      enableMEVProtection: true,
      enableFlashLoans: true,
      enableProfitValidation: true,
      enableEnhancedTokenMonitoring: true,
      enableExecutionQueue: true,
      enableMLLearning: true,
      enableRiskManagement: true
    });

    await serviceIntegrator.initialize();

    // Get service references
    profitValidationService = serviceIntegrator.getService('profitValidation') as ProfitValidationService;
    tokenMonitoringService = serviceIntegrator.getService('enhancedTokenMonitoring') as EnhancedTokenMonitoringService;
    executionQueue = serviceIntegrator.getService('executionQueue') as ProfitPrioritizedExecutionQueue;
    flashLoanService = serviceIntegrator.getService('flashLoan') as FlashLoanService;

    logger.info('Performance benchmark tests initialized');
  });

  afterAll(async () => {
    if (serviceIntegrator) {
      await serviceIntegrator.shutdown();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Latency Benchmarks', () => {
    it('should measure profit validation service latency', async () => {
      const iterations = 100;
      const latencies: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        await profitValidationService.validatePreExecution(mockOpportunity);
        const endTime = performance.now();
        latencies.push(endTime - startTime);
      }

      const metrics = calculateLatencyMetrics(latencies);
      
      expect(metrics.average).toBeLessThan(parseInt(config.PROFIT_VALIDATION_TIMEOUT));
      expect(metrics.p95).toBeLessThan(parseInt(config.PROFIT_VALIDATION_TIMEOUT) * 1.5);
      
      logger.info('Profit Validation Latency:', metrics);
    });

    it('should measure execution queue performance', async () => {
      const iterations = 1000;
      const latencies: number[] = [];

      // Pre-populate queue
      for (let i = 0; i < 50; i++) {
        await executionQueue.addOpportunity({
          ...mockOpportunity,
          id: `queue-prep-${i}`,
          potentialProfit: Math.random() * 1000
        });
      }

      // Measure queue operations
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        if (i % 2 === 0) {
          await executionQueue.addOpportunity({
            ...mockOpportunity,
            id: `queue-test-${i}`,
            potentialProfit: Math.random() * 1000
          });
        } else {
          await executionQueue.getNextOpportunity();
        }
        
        const endTime = performance.now();
        latencies.push(endTime - startTime);
      }

      const metrics = calculateLatencyMetrics(latencies);
      
      expect(metrics.average).toBeLessThan(parseInt(config.MAX_QUEUE_PROCESSING_TIME));
      expect(metrics.p99).toBeLessThan(parseInt(config.MAX_QUEUE_PROCESSING_TIME) * 2);
      
      logger.info('Execution Queue Latency:', metrics);
    });

    it('should measure flash loan optimization latency', async () => {
      const iterations = 50; // Fewer iterations due to complexity
      const latencies: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        await flashLoanService.optimizeFlashLoanStrategy(mockOpportunity, 10000);
        const endTime = performance.now();
        latencies.push(endTime - startTime);
      }

      const metrics = calculateLatencyMetrics(latencies);
      
      expect(metrics.average).toBeLessThan(5000); // 5 seconds max
      expect(metrics.p95).toBeLessThan(10000); // 10 seconds for 95th percentile
      
      logger.info('Flash Loan Optimization Latency:', metrics);
    });
  });

  describe('Throughput Benchmarks', () => {
    it('should measure system throughput under load', async () => {
      const duration = 30000; // 30 seconds
      const startTime = performance.now();
      let operationsCompleted = 0;
      let errors = 0;

      const endTime = startTime + duration;
      
      while (performance.now() < endTime) {
        try {
          // Simulate complete workflow
          const opportunity = {
            ...mockOpportunity,
            id: `throughput-test-${operationsCompleted}`,
            potentialProfit: 50 + Math.random() * 200
          };

          await profitValidationService.validatePreExecution(opportunity);
          await executionQueue.addOpportunity(opportunity);
          
          operationsCompleted++;
        } catch (error) {
          errors++;
        }
      }

      const actualDuration = performance.now() - startTime;
      const throughput = (operationsCompleted / actualDuration) * 1000; // ops per second
      const errorRate = (errors / (operationsCompleted + errors)) * 100;

      expect(throughput).toBeGreaterThan(10); // At least 10 operations per second
      expect(errorRate).toBeLessThan(5); // Less than 5% error rate

      logger.info(`Throughput: ${throughput.toFixed(2)} ops/sec, Error rate: ${errorRate.toFixed(2)}%`);
    });

    it('should handle concurrent operations efficiently', async () => {
      const concurrentOperations = 100;
      const startTime = performance.now();

      const promises = Array.from({ length: concurrentOperations }, async (_, i) => {
        const opportunity = {
          ...mockOpportunity,
          id: `concurrent-${i}`,
          potentialProfit: Math.random() * 1000
        };

        return Promise.all([
          profitValidationService.validatePreExecution(opportunity),
          executionQueue.addOpportunity(opportunity)
        ]);
      });

      const results = await Promise.allSettled(promises);
      const endTime = performance.now();

      const successfulOperations = results.filter(result => result.status === 'fulfilled').length;
      const duration = endTime - startTime;
      const concurrentThroughput = (successfulOperations / duration) * 1000;

      expect(successfulOperations).toBeGreaterThan(concurrentOperations * 0.8); // 80% success rate
      expect(duration).toBeLessThan(10000); // Complete within 10 seconds

      logger.info(`Concurrent throughput: ${concurrentThroughput.toFixed(2)} ops/sec`);
    });
  });

  describe('Memory Usage Benchmarks', () => {
    it('should maintain stable memory usage under sustained load', async () => {
      const initialMemory = process.memoryUsage();
      let peakMemory = initialMemory.heapUsed;

      // Sustained load for 60 seconds
      const loadDuration = 60000;
      const startTime = Date.now();
      let operationCount = 0;

      while (Date.now() - startTime < loadDuration) {
        try {
          const opportunity = {
            ...mockOpportunity,
            id: `memory-test-${operationCount}`,
            potentialProfit: Math.random() * 1000,
            route: {
              ...mockOpportunity.route,
              expectedProfit: Math.random() * 1000
            }
          };

          await profitValidationService.validatePreExecution(opportunity);
          await executionQueue.addOpportunity(opportunity);
          
          operationCount++;

          // Track peak memory usage
          const currentMemory = process.memoryUsage().heapUsed;
          if (currentMemory > peakMemory) {
            peakMemory = currentMemory;
          }

          // Periodic cleanup simulation
          if (operationCount % 100 === 0) {
            if (global.gc) {
              global.gc();
            }
          }
        } catch (error) {
          // Continue on errors
        }
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = (finalMemory.heapUsed - initialMemory.heapUsed) / (1024 * 1024); // MB
      const peakIncrease = (peakMemory - initialMemory.heapUsed) / (1024 * 1024); // MB

      expect(memoryIncrease).toBeLessThan(200); // Less than 200MB increase
      expect(peakIncrease).toBeLessThan(500); // Peak less than 500MB increase

      logger.info(`Memory usage - Final increase: ${memoryIncrease.toFixed(2)}MB, Peak: ${peakIncrease.toFixed(2)}MB`);
      logger.info(`Operations completed: ${operationCount}`);
    });
  });

  // Helper function to calculate latency metrics
  function calculateLatencyMetrics(latencies: number[]): PerformanceMetrics['latency'] {
    const sorted = latencies.sort((a, b) => a - b);
    const sum = latencies.reduce((acc, val) => acc + val, 0);
    
    return {
      average: sum / latencies.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }
});
