# MEV Arbitrage Bot - Startup Guide

## Overview

This guide provides comprehensive instructions for starting up the MEV Arbitrage Bot with its complete adaptive machine learning system and multi-chain infrastructure.

## Prerequisites

### System Requirements
- **Node.js**: Version 18.0 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended for production)
- **Storage**: At least 10GB free space
- **Network**: Stable internet connection for blockchain and API access

### Required Services
- **Supabase**: Database and real-time features
- **InfluxDB**: Time-series metrics (optional but recommended)
- **Redis**: Caching and pub/sub (optional but recommended)

## Quick Start

### 1. Environment Setup

```bash
# Clone and navigate to the project
cd mev-arbitrage-bot

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Edit .env with your actual configuration
nano .env
```

### 2. Database Setup

```bash
# Setup ML database tables
npm run setup:db
```

### 3. Start the System

```bash
# Development mode
npm start

# Production mode
npm run start:production
```

## Detailed Setup Instructions

### Environment Configuration

#### Critical Settings (Required)
```env
# Supabase (Required for data storage)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Blockchain RPC URLs (Required)
ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/your-api-key
POLYGON_RPC_URL=https://polygon-mainnet.alchemyapi.io/v2/your-api-key
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# Trading Wallet (Required)
PRIVATE_KEY=your-private-key-here
```

#### Recommended Settings
```env
# InfluxDB for metrics
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token

# Redis for performance
REDIS_URL=redis://localhost:6379

# CoinGecko for market data
COINGECKO_API_KEY=your-coingecko-api-key
```

### Service Dependencies

#### Supabase Setup
1. Create a new Supabase project
2. Copy the URL and service role key to your `.env`
3. Run the database setup script: `npm run setup:db`

#### InfluxDB Setup (Optional)
```bash
# Using Docker
docker run -d -p 8086:8086 \
  -v influxdb-storage:/var/lib/influxdb2 \
  influxdb:2.0

# Create organization and bucket
# Copy token to .env file
```

#### Redis Setup (Optional)
```bash
# Using Docker
docker run -d -p 6379:6379 redis:alpine

# Or install locally
# Ubuntu/Debian: sudo apt install redis-server
# macOS: brew install redis
```

## Startup Process

### Phase 1: Configuration Validation
The system validates all environment variables and configuration:
- ✅ Required settings present
- ✅ Valid URLs and API keys
- ✅ Trading parameters within safe ranges
- ✅ Directory structure exists

### Phase 2: Dependency Checking
Verifies external service connectivity:
- ✅ Supabase database connection
- ✅ Blockchain RPC endpoints
- ✅ InfluxDB (if configured)
- ✅ Redis (if configured)
- ✅ External APIs (CoinGecko, etc.)

### Phase 3: Service Initialization
Starts services in dependency order:
1. **SupabaseService** - Database connectivity
2. **TokenDiscoveryService** - Token management
3. **PriceFeedService** - Real-time price data
4. **OpportunityDetectionService** - Arbitrage detection
5. **RiskManagementService** - Risk controls
6. **AnalyticsService** - Performance tracking
7. **MLLearningService** - Machine learning
8. **StrategySelectionService** - Intelligent strategy selection
9. **ExecutionService** - Trade execution

### Phase 4: Integration Testing
Runs comprehensive system tests:
- ✅ Service communication
- ✅ Database operations
- ✅ Blockchain connectivity
- ✅ ML learning system
- ✅ Strategy selection
- ✅ WebSocket connections

### Phase 5: Health Monitoring
Starts continuous monitoring:
- ✅ Service health checks
- ✅ Performance metrics
- ✅ Error tracking
- ✅ Alert system

## Monitoring and Verification

### Dashboard Access
Once started, access the system at:
- **Main Dashboard**: http://localhost:3001
- **Health Check**: http://localhost:3001/health
- **Detailed Health**: http://localhost:3001/health/detailed
- **System Status**: http://localhost:3001/status

### Key Metrics to Monitor
- **Service Status**: All services should show as healthy
- **Market Regime**: Current ML-detected market conditions
- **Active Strategies**: Number of strategies being tracked
- **Learning Events**: ML system adaptation frequency
- **Memory Usage**: Should stay below 85%
- **Response Time**: Should be under 1000ms

### Log Monitoring
```bash
# View real-time logs
tail -f logs/app.log

# Filter for errors
grep "ERROR" logs/app.log

# Monitor ML learning events
grep "ML learning" logs/app.log
```

## Troubleshooting

### Common Issues

#### Configuration Errors
```
❌ Missing required configuration: SUPABASE_URL
```
**Solution**: Ensure all required environment variables are set in `.env`

#### Database Connection Failed
```
❌ Supabase connection failed: Invalid API key
```
**Solution**: Verify Supabase URL and service role key are correct

#### Blockchain RPC Issues
```
❌ Ethereum RPC failed: Network timeout
```
**Solution**: Check RPC URL and network connectivity

#### Service Startup Timeout
```
❌ Service PriceFeedService start timeout
```
**Solution**: Check external API connectivity and increase timeout if needed

### Health Check Commands
```bash
# Check system status
curl http://localhost:3001/health

# Get detailed metrics
curl http://localhost:3001/health/detailed

# Check ML learning status
curl http://localhost:3001/api/ml/learning-stats
```

### Recovery Procedures

#### Restart Individual Service
The system supports hot-restarting of individual services:
```bash
# This would be implemented in a management interface
# For now, restart the entire system
```

#### Emergency Stop
```bash
# Set emergency stop flag
export EMERGENCY_STOP=true

# Or update .env file and restart
```

#### Database Recovery
```bash
# Re-run database setup
npm run setup:db

# This will create missing tables without affecting existing data
```

## Production Deployment

### Environment Preparation
```env
NODE_ENV=production
LOG_LEVEL=warn
ENABLE_METRICS=true

# Use production database URLs
SUPABASE_URL=https://your-prod-project.supabase.co
REDIS_URL=redis://your-prod-redis:6379

# Use production RPC endpoints
ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/your-prod-key
```

### Security Considerations
- ✅ Use strong JWT secrets
- ✅ Secure private key storage
- ✅ Enable API rate limiting
- ✅ Configure proper CORS origins
- ✅ Set up monitoring and alerting

### Scaling Considerations
- **Memory**: Monitor heap usage, scale vertically if needed
- **CPU**: ML learning can be CPU intensive during market volatility
- **Network**: Ensure sufficient bandwidth for multi-chain operations
- **Database**: Monitor Supabase usage and upgrade plan if needed

## Advanced Configuration

### ML Learning Tuning
```env
# Adjust learning parameters for your trading style
ML_LEARNING_RATE=0.1          # How quickly to adapt (0.05-0.2)
ML_MIN_SAMPLES=10              # Minimum trades before learning (5-20)
ML_PERFORMANCE_WINDOW_HOURS=24 # Learning window (12-48)

# Strategy selection tuning
STRATEGY_CONFIDENCE_THRESHOLD=60  # Minimum confidence (50-80)
STRATEGY_RISK_TOLERANCE=70        # Maximum risk score (60-80)
```

### Trading Parameters
```env
# Conservative settings
MIN_PROFIT_THRESHOLD=100
MAX_POSITION_SIZE=5000
MAX_SLIPPAGE=0.3

# Aggressive settings
MIN_PROFIT_THRESHOLD=25
MAX_POSITION_SIZE=25000
MAX_SLIPPAGE=1.0
```

## Support and Maintenance

### Regular Maintenance
- **Daily**: Check system health and performance metrics
- **Weekly**: Review ML learning progress and strategy performance
- **Monthly**: Update dependencies and review configuration

### Performance Optimization
- Monitor memory usage and restart if needed
- Adjust ML parameters based on market conditions
- Update RPC endpoints for better performance
- Scale database resources as trading volume grows

### Backup and Recovery
- **Database**: Supabase handles automatic backups
- **Configuration**: Keep `.env` files in secure version control
- **Logs**: Implement log rotation and archival
- **ML Data**: Strategy weights and learning data are stored in database

## Getting Help

### Log Analysis
The system provides comprehensive logging for troubleshooting:
- **Startup logs**: Detailed initialization process
- **Service logs**: Individual service status and errors
- **ML logs**: Learning decisions and strategy updates
- **Trade logs**: Execution details and performance

### Community Support
- Check the documentation in the `docs/` directory
- Review the ML Learning System guide
- Examine the API documentation
- Test with the provided integration tests

The MEV Arbitrage Bot is designed to be robust and self-healing, with comprehensive monitoring and automatic recovery mechanisms. Follow this guide for a smooth startup experience and optimal performance.
