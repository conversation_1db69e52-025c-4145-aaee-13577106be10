#!/usr/bin/env node

/**
 * Service Dependency Manager for MEV Arbitrage Bot
 * ===============================================
 * 
 * Manages service startup order, dependencies, health checks, and retry logic:
 * 1. Dependency graph resolution
 * 2. Sequential service startup with validation
 * 3. Health monitoring and retry mechanisms
 * 4. Graceful failure handling and rollback
 * 5. Performance monitoring and validation
 */

import { EventEmitter } from 'events';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Service definitions with dependencies
const SERVICE_DEFINITIONS = {
  // Database Services (Level 0 - No dependencies)
  redis: {
    name: 'Redis Cache',
    type: 'database',
    dependencies: [],
    healthCheck: 'redis-cli ping',
    startCommand: 'docker-compose up -d redis',
    port: 6379,
    maxStartupTime: 30000,
    retryAttempts: 3,
    critical: true
  },
  
  postgres: {
    name: 'PostgreSQL Database',
    type: 'database',
    dependencies: ['redis'],
    healthCheck: 'pg_isready -h localhost -p 5432',
    startCommand: 'docker-compose up -d postgres',
    port: 5432,
    maxStartupTime: 45000,
    retryAttempts: 3,
    critical: true
  },
  
  influxdb: {
    name: 'InfluxDB Time Series',
    type: 'database',
    dependencies: ['postgres'],
    healthCheck: 'influx ping',
    startCommand: 'docker-compose up -d influxdb',
    port: 8086,
    maxStartupTime: 60000,
    retryAttempts: 3,
    critical: true
  },
  
  // Core Services (Level 1 - Database dependencies)
  databaseManager: {
    name: 'Database Manager',
    type: 'core',
    dependencies: ['redis', 'postgres', 'influxdb'],
    healthCheck: 'http://localhost:3001/api/health/database',
    startCommand: null, // Started as part of backend
    maxStartupTime: 15000,
    retryAttempts: 2,
    critical: true
  },
  
  enhancedCache: {
    name: 'Enhanced Cache Service',
    type: 'core',
    dependencies: ['redis'],
    healthCheck: 'http://localhost:3001/api/health/cache',
    startCommand: null,
    maxStartupTime: 10000,
    retryAttempts: 2,
    critical: true
  },
  
  // Network Services (Level 2 - Core dependencies)
  multiChain: {
    name: 'Multi-Chain Manager',
    type: 'network',
    dependencies: ['databaseManager', 'enhancedCache'],
    healthCheck: 'http://localhost:3001/api/health/multichain',
    startCommand: null,
    maxStartupTime: 30000,
    retryAttempts: 2,
    critical: true
  },
  
  priceFeed: {
    name: 'Price Feed Service',
    type: 'network',
    dependencies: ['multiChain'],
    healthCheck: 'http://localhost:3001/api/health/pricefeed',
    startCommand: null,
    maxStartupTime: 20000,
    retryAttempts: 2,
    critical: true
  },
  
  tokenDiscovery: {
    name: 'Token Discovery Service',
    type: 'network',
    dependencies: ['multiChain'],
    healthCheck: 'http://localhost:3001/api/health/tokendiscovery',
    startCommand: null,
    maxStartupTime: 25000,
    retryAttempts: 2,
    critical: true
  },
  
  // Trading Services (Level 3 - Network dependencies)
  opportunityDetection: {
    name: 'Opportunity Detection',
    type: 'trading',
    dependencies: ['priceFeed', 'tokenDiscovery'],
    healthCheck: 'http://localhost:3001/api/health/opportunities',
    startCommand: null,
    maxStartupTime: 20000,
    retryAttempts: 2,
    critical: true
  },
  
  preExecutionValidation: {
    name: 'Pre-Execution Validation',
    type: 'trading',
    dependencies: ['opportunityDetection'],
    healthCheck: 'http://localhost:3001/api/health/validation',
    startCommand: null,
    maxStartupTime: 15000,
    retryAttempts: 2,
    critical: true
  },
  
  mevProtection: {
    name: 'MEV Protection Service',
    type: 'trading',
    dependencies: ['preExecutionValidation'],
    healthCheck: 'http://localhost:3001/api/health/mev',
    startCommand: null,
    maxStartupTime: 20000,
    retryAttempts: 2,
    critical: true
  },
  
  flashLoan: {
    name: 'Flash Loan Service',
    type: 'trading',
    dependencies: ['mevProtection'],
    healthCheck: 'http://localhost:3001/api/health/flashloan',
    startCommand: null,
    maxStartupTime: 25000,
    retryAttempts: 2,
    critical: false
  },
  
  execution: {
    name: 'Execution Service',
    type: 'trading',
    dependencies: ['mevProtection', 'flashLoan'],
    healthCheck: 'http://localhost:3001/api/health/execution',
    startCommand: null,
    maxStartupTime: 30000,
    retryAttempts: 2,
    critical: true
  },
  
  // ML Services (Level 4 - Trading dependencies)
  mlLearning: {
    name: 'ML Learning Service',
    type: 'ml',
    dependencies: ['execution'],
    healthCheck: 'http://localhost:3001/api/health/ml',
    startCommand: null,
    maxStartupTime: 20000,
    retryAttempts: 1,
    critical: false
  },
  
  strategySelection: {
    name: 'Strategy Selection',
    type: 'ml',
    dependencies: ['mlLearning'],
    healthCheck: 'http://localhost:3001/api/health/strategy',
    startCommand: null,
    maxStartupTime: 15000,
    retryAttempts: 1,
    critical: false
  },
  
  // Monitoring Services (Level 5 - All dependencies)
  riskManagement: {
    name: 'Risk Management',
    type: 'monitoring',
    dependencies: ['execution', 'strategySelection'],
    healthCheck: 'http://localhost:3001/api/health/risk',
    startCommand: null,
    maxStartupTime: 15000,
    retryAttempts: 2,
    critical: true
  },
  
  analytics: {
    name: 'Analytics Service',
    type: 'monitoring',
    dependencies: ['riskManagement'],
    healthCheck: 'http://localhost:3001/api/health/analytics',
    startCommand: null,
    maxStartupTime: 10000,
    retryAttempts: 1,
    critical: false
  }
};

class ServiceDependencyManager extends EventEmitter {
  constructor() {
    super();
    this.services = new Map();
    this.startupOrder = [];
    this.serviceStates = new Map();
    this.retryCounters = new Map();
    this.startTime = Date.now();
    
    this.initializeServices();
    this.calculateStartupOrder();
  }
  
  initializeServices() {
    for (const [serviceId, definition] of Object.entries(SERVICE_DEFINITIONS)) {
      this.services.set(serviceId, {
        ...definition,
        id: serviceId,
        status: 'pending',
        startTime: null,
        endTime: null,
        error: null,
        retryCount: 0
      });
      
      this.serviceStates.set(serviceId, 'pending');
      this.retryCounters.set(serviceId, 0);
    }
  }
  
  calculateStartupOrder() {
    const visited = new Set();
    const visiting = new Set();
    const order = [];
    
    const visit = (serviceId) => {
      if (visiting.has(serviceId)) {
        throw new Error(`Circular dependency detected involving ${serviceId}`);
      }
      
      if (visited.has(serviceId)) {
        return;
      }
      
      visiting.add(serviceId);
      
      const service = this.services.get(serviceId);
      if (service) {
        for (const dependency of service.dependencies) {
          visit(dependency);
        }
      }
      
      visiting.delete(serviceId);
      visited.add(serviceId);
      order.push(serviceId);
    };
    
    for (const serviceId of this.services.keys()) {
      visit(serviceId);
    }
    
    this.startupOrder = order;
  }
  
  async startAllServices() {
    console.log('🚀 Starting Service Dependency Manager...');
    console.log(`Startup Order: ${this.startupOrder.join(' → ')}`);
    console.log('=' .repeat(60));
    
    for (const serviceId of this.startupOrder) {
      const success = await this.startService(serviceId);
      
      if (!success) {
        const service = this.services.get(serviceId);
        if (service.critical) {
          throw new Error(`Critical service ${service.name} failed to start`);
        } else {
          console.log(`⚠️  Non-critical service ${service.name} failed - continuing...`);
        }
      }
    }
    
    const totalTime = Date.now() - this.startTime;
    console.log(`\n✅ All services started in ${totalTime}ms`);
    
    return this.getServiceStatus();
  }
  
  async startService(serviceId) {
    const service = this.services.get(serviceId);
    if (!service) {
      throw new Error(`Service ${serviceId} not found`);
    }
    
    console.log(`\n🔄 Starting ${service.name}...`);
    service.startTime = Date.now();
    service.status = 'starting';
    this.serviceStates.set(serviceId, 'starting');
    
    // Check dependencies first
    for (const depId of service.dependencies) {
      const depStatus = this.serviceStates.get(depId);
      if (depStatus !== 'running') {
        throw new Error(`Dependency ${depId} is not running (status: ${depStatus})`);
      }
    }
    
    // Start the service
    let success = false;
    let attempts = 0;
    
    while (attempts <= service.retryAttempts && !success) {
      attempts++;
      
      try {
        if (service.startCommand) {
          await this.executeStartCommand(service);
        }
        
        success = await this.waitForServiceHealth(service);
        
        if (success) {
          service.status = 'running';
          service.endTime = Date.now();
          this.serviceStates.set(serviceId, 'running');
          
          const duration = service.endTime - service.startTime;
          console.log(`✅ ${service.name} started successfully (${duration}ms)`);
          
          this.emit('serviceStarted', { serviceId, service, duration });
        } else {
          if (attempts <= service.retryAttempts) {
            console.log(`⚠️  ${service.name} failed, retrying... (${attempts}/${service.retryAttempts})`);
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
        
      } catch (error) {
        service.error = error.message;
        console.log(`❌ ${service.name} error: ${error.message}`);
        
        if (attempts <= service.retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
    
    if (!success) {
      service.status = 'failed';
      this.serviceStates.set(serviceId, 'failed');
      console.log(`💥 ${service.name} failed to start after ${attempts} attempts`);
      
      this.emit('serviceFailed', { serviceId, service, attempts });
    }
    
    return success;
  }
  
  async executeStartCommand(service) {
    if (!service.startCommand) return true;
    
    try {
      await execAsync(service.startCommand);
      return true;
    } catch (error) {
      throw new Error(`Start command failed: ${error.message}`);
    }
  }
  
  async waitForServiceHealth(service) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < service.maxStartupTime) {
      try {
        if (service.healthCheck.startsWith('http')) {
          const response = await fetch(service.healthCheck);
          if (response.ok) {
            return true;
          }
        } else {
          await execAsync(service.healthCheck);
          return true;
        }
      } catch (error) {
        // Continue waiting
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return false;
  }
  
  getServiceStatus() {
    const status = {
      totalServices: this.services.size,
      running: 0,
      failed: 0,
      pending: 0,
      services: {},
      totalStartupTime: Date.now() - this.startTime
    };
    
    for (const [serviceId, service] of this.services) {
      status.services[serviceId] = {
        name: service.name,
        status: service.status,
        type: service.type,
        critical: service.critical,
        startTime: service.startTime,
        duration: service.endTime ? service.endTime - service.startTime : null,
        error: service.error
      };
      
      status[service.status]++;
    }
    
    return status;
  }
  
  async shutdown() {
    console.log('🛑 Shutting down services...');
    
    // Shutdown in reverse order
    const shutdownOrder = [...this.startupOrder].reverse();
    
    for (const serviceId of shutdownOrder) {
      const service = this.services.get(serviceId);
      if (service.status === 'running') {
        console.log(`Stopping ${service.name}...`);
        // Add shutdown logic here if needed
        this.serviceStates.set(serviceId, 'stopped');
      }
    }
    
    console.log('✅ All services stopped');
  }
}

export default ServiceDependencyManager;
