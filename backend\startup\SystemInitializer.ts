import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import { ConfigValidator } from './ConfigValidator.js';
import { DependencyChecker } from './DependencyChecker.js';
import { ServiceManager } from './ServiceManager.js';
import { HealthMonitor } from './HealthMonitor.js';
import { SystemTester } from './SystemTester.js';

export interface InitializationResult {
  success: boolean;
  startupTime: number;
  servicesStarted: string[];
  servicesFailed: string[];
  warnings: string[];
  errors: string[];
}

export interface SystemStatus {
  status: 'initializing' | 'healthy' | 'degraded' | 'critical' | 'failed';
  uptime: number;
  services: { [key: string]: boolean };
  lastHealthCheck: Date;
  warnings: string[];
  errors: string[];
}

export class SystemInitializer extends EventEmitter {
  private configValidator: ConfigValidator;
  private dependencyChecker: DependencyChecker;
  private serviceManager: ServiceManager;
  private healthMonitor: HealthMonitor;
  private systemTester: SystemTester;
  
  private startupTime: number = 0;
  private isInitialized = false;
  private systemStatus: SystemStatus;

  constructor() {
    super();
    
    this.configValidator = new ConfigValidator();
    this.dependencyChecker = new DependencyChecker();
    this.serviceManager = new ServiceManager();
    this.healthMonitor = new HealthMonitor();
    this.systemTester = new SystemTester();

    this.systemStatus = {
      status: 'initializing',
      uptime: 0,
      services: {},
      lastHealthCheck: new Date(),
      warnings: [],
      errors: []
    };

    this.setupEventHandlers();
  }

  public async initialize(): Promise<InitializationResult> {
    const startTime = Date.now();
    this.startupTime = startTime;
    
    logger.info('🚀 Starting MEV Arbitrage Bot System Initialization...');
    this.emit('initializationStarted');

    const result: InitializationResult = {
      success: false,
      startupTime: 0,
      servicesStarted: [],
      servicesFailed: [],
      warnings: [],
      errors: []
    };

    try {
      // Phase 1: Configuration Validation
      logger.info('📋 Phase 1: Validating Configuration...');
      const configResult = await this.configValidator.validate();
      if (!configResult.isValid) {
        result.errors.push(...configResult.errors);
        result.warnings.push(...configResult.warnings);
        
        if (configResult.criticalErrors.length > 0) {
          logger.error('❌ Critical configuration errors found. Cannot proceed.');
          result.success = false;
          return result;
        }
      }
      logger.info('✅ Configuration validation completed');

      // Phase 2: Dependency Checking
      logger.info('🔍 Phase 2: Checking Dependencies...');
      const dependencyResult = await this.dependencyChecker.checkAll();
      if (!dependencyResult.allHealthy) {
        result.warnings.push(...dependencyResult.warnings);
        
        if (dependencyResult.criticalFailures.length > 0) {
          logger.error('❌ Critical dependencies unavailable. Cannot proceed.');
          result.errors.push(...dependencyResult.criticalFailures);
          result.success = false;
          return result;
        }
      }
      logger.info('✅ Dependency checking completed');

      // Phase 3: Service Initialization
      logger.info('⚙️ Phase 3: Initializing Services...');
      const serviceResult = await this.serviceManager.initializeAll();
      result.servicesStarted = serviceResult.started;
      result.servicesFailed = serviceResult.failed;
      result.warnings.push(...serviceResult.warnings);
      
      if (serviceResult.criticalFailures.length > 0) {
        logger.error('❌ Critical services failed to start. Cannot proceed.');
        result.errors.push(...serviceResult.criticalFailures);
        result.success = false;
        return result;
      }
      logger.info('✅ Service initialization completed');

      // Phase 4: System Integration Testing
      logger.info('🧪 Phase 4: Running Integration Tests...');
      this.systemTester.setServiceManager(this.serviceManager);
      const testResult = await this.systemTester.runStartupTests();
      if (!testResult.allPassed) {
        result.warnings.push(...testResult.warnings);
        
        if (testResult.criticalFailures.length > 0) {
          logger.error('❌ Critical integration tests failed. Cannot proceed.');
          result.errors.push(...testResult.criticalFailures);
          result.success = false;
          return result;
        }
      }
      logger.info('✅ Integration testing completed');

      // Phase 5: Health Monitoring Setup
      logger.info('💓 Phase 5: Starting Health Monitoring...');
      await this.healthMonitor.start();
      logger.info('✅ Health monitoring started');

      // Finalization
      const endTime = Date.now();
      result.startupTime = endTime - startTime;
      result.success = true;
      this.isInitialized = true;
      
      this.systemStatus.status = 'healthy';
      this.systemStatus.uptime = 0;
      this.systemStatus.services = this.serviceManager.getServiceStatus();
      
      logger.info(`🎉 System initialization completed successfully in ${result.startupTime}ms`);
      logger.info(`📊 Services started: ${result.servicesStarted.length}`);
      logger.info(`⚠️ Warnings: ${result.warnings.length}`);
      
      this.emit('initializationCompleted', result);
      
      // Start periodic health checks
      this.startPeriodicHealthChecks();
      
      return result;

    } catch (error) {
      const endTime = Date.now();
      result.startupTime = endTime - startTime;
      result.success = false;
      result.errors.push(`Initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      logger.error('❌ System initialization failed:', error);
      this.emit('initializationFailed', result);
      
      return result;
    }
  }

  public async shutdown(): Promise<void> {
    logger.info('🛑 Starting system shutdown...');
    this.emit('shutdownStarted');

    try {
      // Stop health monitoring
      await this.healthMonitor.stop();
      
      // Shutdown services in reverse order
      await this.serviceManager.shutdownAll();
      
      this.isInitialized = false;
      this.systemStatus.status = 'failed';
      
      logger.info('✅ System shutdown completed');
      this.emit('shutdownCompleted');
      
    } catch (error) {
      logger.error('❌ Error during system shutdown:', error);
      this.emit('shutdownFailed', error);
    }
  }

  public getSystemStatus(): SystemStatus {
    if (this.isInitialized) {
      this.systemStatus.uptime = Date.now() - this.startupTime;
    }
    return { ...this.systemStatus };
  }

  public isSystemHealthy(): boolean {
    return this.isInitialized && this.systemStatus.status === 'healthy';
  }

  public getServiceManager(): ServiceManager {
    return this.serviceManager;
  }

  public getHealthMonitor(): HealthMonitor {
    return this.healthMonitor;
  }

  private setupEventHandlers(): void {
    // Service Manager Events
    this.serviceManager.on('serviceStarted', (serviceName: string) => {
      logger.info(`✅ Service started: ${serviceName}`);
      this.systemStatus.services[serviceName] = true;
      this.emit('serviceStarted', serviceName);
    });

    this.serviceManager.on('serviceFailed', (serviceName: string, error: Error) => {
      logger.error(`❌ Service failed: ${serviceName}`, error);
      this.systemStatus.services[serviceName] = false;
      this.systemStatus.errors.push(`Service ${serviceName} failed: ${error.message}`);
      this.emit('serviceFailed', serviceName, error);
    });

    // Health Monitor Events
    this.healthMonitor.on('healthCheckCompleted', (status: any) => {
      this.systemStatus.lastHealthCheck = new Date();
      this.systemStatus.services = { ...this.systemStatus.services, ...status.services };
      
      // Update overall system status
      const healthyServices = Object.values(status.services).filter(Boolean).length;
      const totalServices = Object.keys(status.services).length;
      
      if (healthyServices === totalServices) {
        this.systemStatus.status = 'healthy';
      } else if (healthyServices >= totalServices * 0.8) {
        this.systemStatus.status = 'degraded';
      } else {
        this.systemStatus.status = 'critical';
      }
      
      this.emit('healthCheckCompleted', status);
    });

    this.healthMonitor.on('serviceUnhealthy', (serviceName: string) => {
      logger.warn(`⚠️ Service unhealthy: ${serviceName}`);
      this.systemStatus.warnings.push(`Service ${serviceName} is unhealthy`);
      this.emit('serviceUnhealthy', serviceName);
    });

    // Dependency Checker Events
    this.dependencyChecker.on('dependencyFailed', (dependency: string, error: Error) => {
      logger.error(`❌ Dependency failed: ${dependency}`, error);
      this.systemStatus.errors.push(`Dependency ${dependency} failed: ${error.message}`);
      this.emit('dependencyFailed', dependency, error);
    });
  }

  private startPeriodicHealthChecks(): void {
    // Run health checks every 30 seconds
    setInterval(async () => {
      try {
        await this.healthMonitor.runHealthCheck();
      } catch (error) {
        logger.error('Error during periodic health check:', error);
      }
    }, 30000);
  }

  // Graceful shutdown handling
  public setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      await this.shutdown();
      process.exit(0);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart

    process.on('uncaughtException', async (error) => {
      logger.error('Uncaught Exception:', error);
      await this.shutdown();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      await this.shutdown();
      process.exit(1);
    });
  }
}
