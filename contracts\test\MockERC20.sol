// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title MockERC20
 * @dev Mock ERC20 token for testing with realistic behavior
 */
contract MockERC20 is ERC20, Ownable {
    uint8 private _decimals;
    bool public transfersEnabled = true;
    bool public transferTaxEnabled = false;
    uint256 public transferTaxRate = 0; // Basis points (100 = 1%)
    
    mapping(address => bool) public blacklisted;
    mapping(address => bool) public whitelisted;
    
    event TransfersToggled(bool enabled);
    event TransferTaxUpdated(uint256 rate);
    event AddressBlacklisted(address indexed account);
    event AddressWhitelisted(address indexed account);

    constructor(
        string memory name,
        string memory symbol,
        uint8 decimals_,
        uint256 initialSupply,
        address owner
    ) ERC20(name, symbol) Ownable(owner) {
        _decimals = decimals_;
        _mint(owner, initialSupply);
    }

    function decimals() public view virtual override returns (uint8) {
        return _decimals;
    }

    /**
     * @dev Mint tokens to specified address (only owner)
     */
    function mint(address to, uint256 amount) external onlyOwner {
        _mint(to, amount);
    }

    /**
     * @dev Burn tokens from specified address (only owner)
     */
    function burn(address from, uint256 amount) external onlyOwner {
        _burn(from, amount);
    }

    /**
     * @dev Toggle transfers on/off for testing emergency scenarios
     */
    function toggleTransfers() external onlyOwner {
        transfersEnabled = !transfersEnabled;
        emit TransfersToggled(transfersEnabled);
    }

    /**
     * @dev Set transfer tax for testing fee-on-transfer tokens
     */
    function setTransferTax(uint256 rate) external onlyOwner {
        require(rate <= 1000, "Tax rate too high"); // Max 10%
        transferTaxRate = rate;
        transferTaxEnabled = rate > 0;
        emit TransferTaxUpdated(rate);
    }

    /**
     * @dev Blacklist address for testing security scenarios
     */
    function blacklistAddress(address account) external onlyOwner {
        blacklisted[account] = true;
        emit AddressBlacklisted(account);
    }

    /**
     * @dev Whitelist address for testing
     */
    function whitelistAddress(address account) external onlyOwner {
        whitelisted[account] = true;
        emit AddressWhitelisted(account);
    }

    /**
     * @dev Override transfer to include testing features
     */
    function _update(address from, address to, uint256 value) internal virtual override {
        require(transfersEnabled, "Transfers disabled");
        require(!blacklisted[from] && !blacklisted[to], "Address blacklisted");
        
        if (transferTaxEnabled && from != address(0) && to != address(0)) {
            uint256 tax = (value * transferTaxRate) / 10000;
            if (tax > 0) {
                super._update(from, owner(), tax);
                value -= tax;
            }
        }
        
        super._update(from, to, value);
    }

    /**
     * @dev Simulate realistic token behavior for different scenarios
     */
    function simulateRealisticBehavior(string memory scenario) external onlyOwner {
        if (keccak256(bytes(scenario)) == keccak256(bytes("fee_token"))) {
            transferTaxRate = 300; // 3% fee
            transferTaxEnabled = true;
            emit TransferTaxUpdated(300);
        } else if (keccak256(bytes(scenario)) == keccak256(bytes("deflationary"))) {
            transferTaxRate = 100; // 1% burn
            transferTaxEnabled = true;
            emit TransferTaxUpdated(100);
        } else if (keccak256(bytes(scenario)) == keccak256(bytes("paused"))) {
            transfersEnabled = false;
        } else if (keccak256(bytes(scenario)) == keccak256(bytes("normal"))) {
            transferTaxRate = 0;
            transferTaxEnabled = false;
            transfersEnabled = true;
            emit TransferTaxUpdated(0);
        }
    }

    /**
     * @dev Get effective balance after potential taxes
     */
    function getEffectiveBalance(address account) external view returns (uint256) {
        uint256 balance = balanceOf(account);
        if (transferTaxEnabled) {
            uint256 tax = (balance * transferTaxRate) / 10000;
            return balance - tax;
        }
        return balance;
    }

    /**
     * @dev Simulate price volatility by adjusting total supply
     */
    function simulateVolatility(int256 percentageChange) external onlyOwner {
        require(percentageChange >= -50 && percentageChange <= 100, "Invalid percentage");
        
        uint256 currentSupply = totalSupply();
        
        if (percentageChange > 0) {
            uint256 increaseAmount = (currentSupply * uint256(percentageChange)) / 100;
            _mint(owner(), increaseAmount);
        } else if (percentageChange < 0) {
            uint256 decreaseAmount = (currentSupply * uint256(-percentageChange)) / 100;
            uint256 ownerBalance = balanceOf(owner());
            uint256 burnAmount = decreaseAmount > ownerBalance ? ownerBalance : decreaseAmount;
            _burn(owner(), burnAmount);
        }
    }
}

/**
 * @title MockWETH
 * @dev Mock WETH contract for testing
 */
contract MockWETH is MockERC20 {
    event Deposit(address indexed dst, uint256 wad);
    event Withdrawal(address indexed src, uint256 wad);

    constructor() MockERC20("Wrapped Ether", "WETH", 18, 0, msg.sender) {}

    receive() external payable {
        deposit();
    }

    function deposit() public payable {
        _mint(msg.sender, msg.value);
        emit Deposit(msg.sender, msg.value);
    }

    function withdraw(uint256 wad) public {
        require(balanceOf(msg.sender) >= wad, "Insufficient balance");
        _burn(msg.sender, wad);
        payable(msg.sender).transfer(wad);
        emit Withdrawal(msg.sender, wad);
    }
}

/**
 * @title MockPriceOracle
 * @dev Mock price oracle for testing price feeds with advanced features
 */
contract MockPriceOracle {
    mapping(address => uint256) public prices;
    mapping(address => uint256) public lastUpdated;
    mapping(address => uint256[]) public priceHistory;
    mapping(address => bool) public isStale;
    mapping(address => uint256) public stalenessThreshold;

    uint256 public constant PRICE_PRECISION = 1e8; // 8 decimals like Chainlink
    uint256 public constant DEFAULT_STALENESS_THRESHOLD = 3600; // 1 hour
    uint256 public constant MAX_PRICE_DEVIATION = 2000; // 20% in basis points

    bool public circuitBreakerEnabled = true;
    mapping(address => bool) public circuitBreakerTriggered;

    event PriceUpdated(address indexed token, uint256 price, uint256 timestamp);
    event CircuitBreakerTriggered(address indexed token, uint256 oldPrice, uint256 newPrice);
    event PriceValidationFailed(address indexed token, uint256 attemptedPrice, string reason);

    function setPrice(address token, uint256 price) external {
        require(price > 0, "Invalid price");

        // Circuit breaker check
        if (circuitBreakerEnabled && prices[token] > 0) {
            uint256 oldPrice = prices[token];
            uint256 deviation = price > oldPrice
                ? ((price - oldPrice) * 10000) / oldPrice
                : ((oldPrice - price) * 10000) / oldPrice;

            if (deviation > MAX_PRICE_DEVIATION) {
                circuitBreakerTriggered[token] = true;
                emit CircuitBreakerTriggered(token, oldPrice, price);
                emit PriceValidationFailed(token, price, "Circuit breaker triggered");
                return;
            }
        }

        prices[token] = price;
        lastUpdated[token] = block.timestamp;
        priceHistory[token].push(price);
        isStale[token] = false;
        circuitBreakerTriggered[token] = false;

        // Keep only last 100 prices in history
        if (priceHistory[token].length > 100) {
            for (uint i = 0; i < priceHistory[token].length - 1; i++) {
                priceHistory[token][i] = priceHistory[token][i + 1];
            }
            priceHistory[token].pop();
        }

        emit PriceUpdated(token, price, block.timestamp);
    }

    function getPrice(address token) external view returns (uint256) {
        require(prices[token] > 0, "Price not set");
        require(!isStale[token], "Price is stale");
        require(!circuitBreakerTriggered[token], "Circuit breaker triggered");
        return prices[token];
    }

    function getPriceUnsafe(address token) external view returns (uint256) {
        return prices[token];
    }

    function latestRoundData(address token) external view returns (
        uint80 roundId,
        int256 answer,
        uint256 startedAt,
        uint256 updatedAt,
        uint80 answeredInRound
    ) {
        require(prices[token] > 0, "Price not set");
        return (
            1,
            int256(prices[token]),
            lastUpdated[token],
            lastUpdated[token],
            1
        );
    }

    function getPriceHistory(address token) external view returns (uint256[] memory) {
        return priceHistory[token];
    }

    function getTWAP(address token, uint256 period) external view returns (uint256) {
        require(priceHistory[token].length > 0, "No price history");

        uint256 sum = 0;
        uint256 count = 0;
        uint256 cutoff = block.timestamp - period;

        // Simple TWAP calculation (in real implementation, would use time-weighted)
        for (uint i = 0; i < priceHistory[token].length; i++) {
            sum += priceHistory[token][i];
            count++;
        }

        return count > 0 ? sum / count : 0;
    }

    function checkStaleness(address token) external {
        uint256 threshold = stalenessThreshold[token] > 0
            ? stalenessThreshold[token]
            : DEFAULT_STALENESS_THRESHOLD;

        if (block.timestamp - lastUpdated[token] > threshold) {
            isStale[token] = true;
        }
    }

    function setStalenessThreshold(address token, uint256 threshold) external {
        stalenessThreshold[token] = threshold;
    }

    function resetCircuitBreaker(address token) external {
        circuitBreakerTriggered[token] = false;
    }

    function setCircuitBreakerEnabled(bool enabled) external {
        circuitBreakerEnabled = enabled;
    }




}
