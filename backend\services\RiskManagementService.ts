import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/index.js';

export interface RiskMetrics {
  totalExposure: number;
  dailyPnL: number;
  maxDrawdown: number;
  volatility: number;
  sharpeRatio: number;
  winRate: number;
  avgProfit: number;
  avgLoss: number;
  consecutiveLosses: number;
  lastUpdated: number;
}

export interface RiskLimits {
  maxDailyLoss: number;
  maxPositionSize: number;
  maxTotalExposure: number;
  maxConsecutiveLosses: number;
  minWinRate: number;
  maxVolatility: number;
}

export interface RiskAlert {
  id: string;
  type: 'warning' | 'critical';
  message: string;
  metric: string;
  value: number;
  threshold: number;
  timestamp: number;
}

export class RiskManagementService extends EventEmitter {
  private riskMetrics: RiskMetrics;
  private riskLimits: RiskLimits;
  private alerts: Map<string, RiskAlert> = new Map();
  private emergencyStop = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // Historical data for calculations
  private dailyReturns: number[] = [];
  private tradeHistory: Array<{ profit: number; timestamp: number }> = [];

  constructor() {
    super();
    
    this.riskLimits = {
      maxDailyLoss: parseFloat(config.MAX_DAILY_LOSS),
      maxPositionSize: parseFloat(config.MAX_POSITION_SIZE),
      maxTotalExposure: parseFloat(config.MAX_POSITION_SIZE) * 5,
      maxConsecutiveLosses: 5,
      minWinRate: 60, // 60%
      maxVolatility: 30 // 30%
    };

    this.riskMetrics = {
      totalExposure: 0,
      dailyPnL: 0,
      maxDrawdown: 0,
      volatility: 0,
      sharpeRatio: 0,
      winRate: 0,
      avgProfit: 0,
      avgLoss: 0,
      consecutiveLosses: 0,
      lastUpdated: Date.now()
    };

    this.emergencyStop = config.EMERGENCY_STOP === 'true';
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Risk Management Service...');
    this.isRunning = true;

    // Start monitoring loop
    this.monitoringInterval = setInterval(() => {
      this.monitorRisk();
    }, 5000); // Monitor every 5 seconds

    // Initial risk assessment
    await this.monitorRisk();

    logger.info('Risk Management Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Risk Management Service...');
    this.isRunning = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    logger.info('Risk Management Service stopped');
  }

  private async monitorRisk() {
    try {
      // Update risk metrics
      this.updateRiskMetrics();

      // Check risk limits
      this.checkRiskLimits();

      // Clean up old alerts
      this.cleanupOldAlerts();

    } catch (error) {
      logger.error('Error monitoring risk:', error);
    }
  }

  private updateRiskMetrics() {
    const now = Date.now();
    const oneDayAgo = now - 24 * 60 * 60 * 1000;

    // Calculate daily P&L
    const todaysTrades = this.tradeHistory.filter(trade => trade.timestamp >= oneDayAgo);
    this.riskMetrics.dailyPnL = todaysTrades.reduce((sum, trade) => sum + trade.profit, 0);

    // Calculate win rate
    if (this.tradeHistory.length > 0) {
      const winningTrades = this.tradeHistory.filter(trade => trade.profit > 0).length;
      this.riskMetrics.winRate = (winningTrades / this.tradeHistory.length) * 100;
    }

    // Calculate average profit and loss
    const winningTrades = this.tradeHistory.filter(trade => trade.profit > 0);
    const losingTrades = this.tradeHistory.filter(trade => trade.profit < 0);

    if (winningTrades.length > 0) {
      this.riskMetrics.avgProfit = winningTrades.reduce((sum, trade) => sum + trade.profit, 0) / winningTrades.length;
    }

    if (losingTrades.length > 0) {
      this.riskMetrics.avgLoss = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0) / losingTrades.length);
    }

    // Calculate consecutive losses
    this.riskMetrics.consecutiveLosses = this.calculateConsecutiveLosses();

    // Calculate volatility
    this.riskMetrics.volatility = this.calculateVolatility();

    // Calculate Sharpe ratio
    this.riskMetrics.sharpeRatio = this.calculateSharpeRatio();

    // Calculate max drawdown
    this.riskMetrics.maxDrawdown = this.calculateMaxDrawdown();

    this.riskMetrics.lastUpdated = now;
  }

  private calculateConsecutiveLosses(): number {
    let consecutiveLosses = 0;
    
    // Check from most recent trades backwards
    for (let i = this.tradeHistory.length - 1; i >= 0; i--) {
      if (this.tradeHistory[i].profit < 0) {
        consecutiveLosses++;
      } else {
        break;
      }
    }

    return consecutiveLosses;
  }

  private calculateVolatility(): number {
    if (this.dailyReturns.length < 2) return 0;

    const mean = this.dailyReturns.reduce((sum, ret) => sum + ret, 0) / this.dailyReturns.length;
    const variance = this.dailyReturns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / this.dailyReturns.length;
    
    return Math.sqrt(variance) * 100; // Convert to percentage
  }

  private calculateSharpeRatio(): number {
    if (this.dailyReturns.length < 2) return 0;

    const mean = this.dailyReturns.reduce((sum, ret) => sum + ret, 0) / this.dailyReturns.length;
    const volatility = this.calculateVolatility() / 100; // Convert back to decimal
    
    if (volatility === 0) return 0;
    
    const riskFreeRate = 0.02 / 365; // Assume 2% annual risk-free rate
    return (mean - riskFreeRate) / volatility;
  }

  private calculateMaxDrawdown(): number {
    if (this.tradeHistory.length === 0) return 0;

    let peak = 0;
    let maxDrawdown = 0;
    let runningTotal = 0;

    for (const trade of this.tradeHistory) {
      runningTotal += trade.profit;
      
      if (runningTotal > peak) {
        peak = runningTotal;
      }
      
      const drawdown = peak - runningTotal;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    return maxDrawdown;
  }

  private checkRiskLimits() {
    // Check daily loss limit
    if (this.riskMetrics.dailyPnL < -this.riskLimits.maxDailyLoss) {
      this.createAlert('critical', 'Daily loss limit exceeded', 'dailyPnL', 
        Math.abs(this.riskMetrics.dailyPnL), this.riskLimits.maxDailyLoss);
      this.triggerEmergencyStop('Daily loss limit exceeded');
    }

    // Check consecutive losses
    if (this.riskMetrics.consecutiveLosses >= this.riskLimits.maxConsecutiveLosses) {
      this.createAlert('critical', 'Too many consecutive losses', 'consecutiveLosses',
        this.riskMetrics.consecutiveLosses, this.riskLimits.maxConsecutiveLosses);
      this.triggerEmergencyStop('Too many consecutive losses');
    }

    // Check win rate
    if (this.tradeHistory.length >= 10 && this.riskMetrics.winRate < this.riskLimits.minWinRate) {
      this.createAlert('warning', 'Win rate below threshold', 'winRate',
        this.riskMetrics.winRate, this.riskLimits.minWinRate);
    }

    // Check volatility
    if (this.riskMetrics.volatility > this.riskLimits.maxVolatility) {
      this.createAlert('warning', 'High volatility detected', 'volatility',
        this.riskMetrics.volatility, this.riskLimits.maxVolatility);
    }

    // Check total exposure
    if (this.riskMetrics.totalExposure > this.riskLimits.maxTotalExposure) {
      this.createAlert('warning', 'Total exposure limit exceeded', 'totalExposure',
        this.riskMetrics.totalExposure, this.riskLimits.maxTotalExposure);
    }
  }

  private createAlert(type: 'warning' | 'critical', message: string, metric: string, value: number, threshold: number) {
    const alertId = `${metric}_${Date.now()}`;
    
    const alert: RiskAlert = {
      id: alertId,
      type,
      message,
      metric,
      value,
      threshold,
      timestamp: Date.now()
    };

    this.alerts.set(alertId, alert);
    this.emit('riskAlert', alert);
    
    logger.warn(`Risk alert (${type}): ${message} - ${metric}: ${value} (threshold: ${threshold})`);
  }

  private triggerEmergencyStop(reason: string) {
    if (!this.emergencyStop) {
      this.emergencyStop = true;
      this.emit('emergencyStop', { reason, timestamp: Date.now() });
      logger.error(`Emergency stop triggered: ${reason}`);
    }
  }

  private cleanupOldAlerts() {
    const now = Date.now();
    const maxAge = 60 * 60 * 1000; // 1 hour

    for (const [id, alert] of this.alerts) {
      if (now - alert.timestamp > maxAge) {
        this.alerts.delete(id);
      }
    }
  }

  public recordTrade(profit: number) {
    const trade = {
      profit,
      timestamp: Date.now()
    };

    this.tradeHistory.push(trade);
    
    // Keep only last 1000 trades
    if (this.tradeHistory.length > 1000) {
      this.tradeHistory = this.tradeHistory.slice(-1000);
    }

    // Update daily returns
    this.updateDailyReturns(profit);

    logger.debug(`Trade recorded: profit=${profit.toFixed(2)}`);
  }

  private updateDailyReturns(profit: number) {
    const today = new Date().toDateString();
    
    // This is simplified - in production you'd want more sophisticated daily return calculation
    const dailyReturn = profit / 10000; // Assume base capital of $10,000
    this.dailyReturns.push(dailyReturn);

    // Keep only last 30 days
    if (this.dailyReturns.length > 30) {
      this.dailyReturns = this.dailyReturns.slice(-30);
    }
  }

  public updateExposure(exposure: number) {
    this.riskMetrics.totalExposure = exposure;
  }

  public toggleEmergencyStop(): boolean {
    this.emergencyStop = !this.emergencyStop;
    
    if (this.emergencyStop) {
      this.emit('emergencyStop', { reason: 'Manual trigger', timestamp: Date.now() });
      logger.warn('Emergency stop activated manually');
    } else {
      this.emit('emergencyStopDisabled', { timestamp: Date.now() });
      logger.info('Emergency stop disabled');
    }

    return this.emergencyStop;
  }

  public isEmergencyStopActive(): boolean {
    return this.emergencyStop;
  }

  public getRiskMetrics(): RiskMetrics {
    return { ...this.riskMetrics };
  }

  public getRiskLimits(): RiskLimits {
    return { ...this.riskLimits };
  }

  public updateRiskLimits(limits: Partial<RiskLimits>) {
    this.riskLimits = { ...this.riskLimits, ...limits };
    logger.info('Risk limits updated:', limits);
  }

  public getAlerts(): RiskAlert[] {
    return Array.from(this.alerts.values())
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  public getActiveAlerts(): RiskAlert[] {
    const now = Date.now();
    const activeThreshold = 5 * 60 * 1000; // 5 minutes

    return this.getAlerts().filter(alert => 
      now - alert.timestamp < activeThreshold
    );
  }

  public clearAlert(alertId: string): boolean {
    return this.alerts.delete(alertId);
  }

  public isHealthy(): boolean {
    return this.isRunning && 
           !this.emergencyStop && 
           this.getActiveAlerts().filter(a => a.type === 'critical').length === 0;
  }

  public getStats() {
    const alertStats: Record<string, number> = {};
    this.alerts.forEach(alert => {
      alertStats[alert.type] = (alertStats[alert.type] || 0) + 1;
    });

    return {
      ...this.riskMetrics,
      emergencyStop: this.emergencyStop,
      totalAlerts: this.alerts.size,
      activeAlerts: this.getActiveAlerts().length,
      alertStats,
      isRunning: this.isRunning,
      tradeCount: this.tradeHistory.length
    };
  }
}
