const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
  console.log("🚀 Starting deployment of MEV Arbitrage Bot contracts to local network...");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Deploying contracts with account:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(balance), "ETH");

  // Deploy TokenDiscovery contract
  console.log("\n🔍 Deploying TokenDiscovery contract...");
  const TokenDiscovery = await ethers.getContractFactory("TokenDiscovery");
  const tokenDiscovery = await TokenDiscovery.deploy();
  await tokenDiscovery.waitForDeployment();
  const tokenDiscoveryAddress = await tokenDiscovery.getAddress();
  console.log("✅ TokenDiscovery deployed to:", tokenDiscoveryAddress);

  // Deploy LiquidityChecker contract
  console.log("\n💧 Deploying LiquidityChecker contract...");
  const LiquidityChecker = await ethers.getContractFactory("LiquidityChecker");
  const liquidityChecker = await LiquidityChecker.deploy();
  await liquidityChecker.waitForDeployment();
  const liquidityCheckerAddress = await liquidityChecker.getAddress();
  console.log("✅ LiquidityChecker deployed to:", liquidityCheckerAddress);

  // Deploy ArbitrageExecutor contract
  console.log("\n⚡ Deploying ArbitrageExecutor contract...");
  const ArbitrageExecutor = await ethers.getContractFactory("ArbitrageExecutor");
  const arbitrageExecutor = await ArbitrageExecutor.deploy(
    tokenDiscoveryAddress,
    liquidityCheckerAddress
  );
  await arbitrageExecutor.waitForDeployment();
  const arbitrageExecutorAddress = await arbitrageExecutor.getAddress();
  console.log("✅ ArbitrageExecutor deployed to:", arbitrageExecutorAddress);

  // Deploy Governance contract
  console.log("\n🏛️ Deploying Governance contract...");
  const Governance = await ethers.getContractFactory("Governance");
  const governance = await Governance.deploy();
  await governance.waitForDeployment();
  const governanceAddress = await governance.getAddress();
  console.log("✅ Governance deployed to:", governanceAddress);

  // Set up contract relationships
  console.log("\n🔗 Setting up contract relationships...");
  
  try {
    // Set ArbitrageExecutor in TokenDiscovery
    await tokenDiscovery.setArbitrageExecutor(arbitrageExecutorAddress);
    console.log("✅ ArbitrageExecutor set in TokenDiscovery");

    // Set ArbitrageExecutor in LiquidityChecker
    await liquidityChecker.setArbitrageExecutor(arbitrageExecutorAddress);
    console.log("✅ ArbitrageExecutor set in LiquidityChecker");

    // Set Governance in ArbitrageExecutor
    await arbitrageExecutor.setGovernance(governanceAddress);
    console.log("✅ Governance set in ArbitrageExecutor");
  } catch (error) {
    console.log("⚠️ Some contract relationships could not be set:", error.message);
  }

  // Save deployment addresses to file
  const deploymentInfo = {
    network: "localhost",
    chainId: 1337,
    timestamp: new Date().toISOString(),
    deployer: deployer.address,
    contracts: {
      TokenDiscovery: tokenDiscoveryAddress,
      LiquidityChecker: liquidityCheckerAddress,
      ArbitrageExecutor: arbitrageExecutorAddress,
      Governance: governanceAddress
    },
    gasUsed: {
      // These would be calculated from actual deployment receipts
      total: "estimated"
    }
  };

  const deploymentPath = path.join(__dirname, '..', 'deployments', 'localhost.json');
  
  // Create deployments directory if it doesn't exist
  const deploymentsDir = path.dirname(deploymentPath);
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
  }

  fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
  console.log("📄 Deployment info saved to:", deploymentPath);

  // Display summary
  console.log("\n" + "=".repeat(80));
  console.log("🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!");
  console.log("=".repeat(80));
  console.log("📍 Network: localhost (Chain ID: 1337)");
  console.log("👤 Deployer:", deployer.address);
  console.log("\n📋 Contract Addresses:");
  console.log("  🔍 TokenDiscovery:     ", tokenDiscoveryAddress);
  console.log("  💧 LiquidityChecker:   ", liquidityCheckerAddress);
  console.log("  ⚡ ArbitrageExecutor:  ", arbitrageExecutorAddress);
  console.log("  🏛️ Governance:         ", governanceAddress);
  
  console.log("\n🔧 Next Steps:");
  console.log("  1. Start the backend services with these contract addresses");
  console.log("  2. Configure the frontend to connect to localhost:8545");
  console.log("  3. Run integration tests to verify functionality");
  console.log("  4. Begin MEV arbitrage testing on the local network");
  
  console.log("\n💡 Environment Variables to Set:");
  console.log(`  ARBITRAGE_EXECUTOR_ADDRESS=${arbitrageExecutorAddress}`);
  console.log(`  TOKEN_DISCOVERY_ADDRESS=${tokenDiscoveryAddress}`);
  console.log(`  LIQUIDITY_CHECKER_ADDRESS=${liquidityCheckerAddress}`);
  console.log(`  GOVERNANCE_ADDRESS=${governanceAddress}`);
  
  return {
    TokenDiscovery: tokenDiscoveryAddress,
    LiquidityChecker: liquidityCheckerAddress,
    ArbitrageExecutor: arbitrageExecutorAddress,
    Governance: governanceAddress
  };
}

main()
  .then((addresses) => {
    console.log("\n✅ Deployment script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("\n❌ Deployment failed:", error);
    process.exit(1);
  });
