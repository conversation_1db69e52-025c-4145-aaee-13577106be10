import { EventEmitter } from 'events';
import { createClient } from '@supabase/supabase-js';
import { InfluxDB } from '@influxdata/influxdb-client';
import { createClient as createRedisClient } from 'redis';
import { ethers } from 'ethers';
import axios from 'axios';
import logger from '../utils/logger.js';

export interface DependencyStatus {
  name: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  responseTime?: number;
  error?: string;
  critical: boolean;
  retryCount: number;
  lastChecked: Date;
}

export interface DependencyCheckResult {
  allHealthy: boolean;
  healthyCount: number;
  totalCount: number;
  dependencies: DependencyStatus[];
  criticalFailures: string[];
  warnings: string[];
}

export class DependencyChecker extends EventEmitter {
  private dependencies: Map<string, DependencyStatus> = new Map();
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 2000; // 2 seconds
  private readonly TIMEOUT = 10000; // 10 seconds

  constructor() {
    super();
    this.initializeDependencies();
  }

  private initializeDependencies(): void {
    const deps: Omit<DependencyStatus, 'lastChecked'>[] = [
      {
        name: 'Supabase',
        status: 'unknown',
        critical: true,
        retryCount: 0
      },
      {
        name: 'InfluxDB',
        status: 'unknown',
        critical: false,
        retryCount: 0
      },
      {
        name: 'Redis',
        status: 'unknown',
        critical: false,
        retryCount: 0
      },
      {
        name: 'Ethereum RPC',
        status: 'unknown',
        critical: true,
        retryCount: 0
      },
      {
        name: 'Polygon RPC',
        status: 'unknown',
        critical: true,
        retryCount: 0
      },
      {
        name: 'BSC RPC',
        status: 'unknown',
        critical: true,
        retryCount: 0
      },
      {
        name: 'CoinGecko API',
        status: 'unknown',
        critical: false,
        retryCount: 0
      }
    ];

    deps.forEach(dep => {
      this.dependencies.set(dep.name, {
        ...dep,
        lastChecked: new Date()
      });
    });
  }

  public async checkAll(): Promise<DependencyCheckResult> {
    logger.info('🔍 Checking system dependencies...');

    const checkPromises = Array.from(this.dependencies.keys()).map(name => 
      this.checkDependency(name)
    );

    await Promise.allSettled(checkPromises);

    return this.generateResult();
  }

  public async checkDependency(name: string): Promise<DependencyStatus> {
    const dependency = this.dependencies.get(name);
    if (!dependency) {
      throw new Error(`Unknown dependency: ${name}`);
    }

    const startTime = Date.now();
    let status: DependencyStatus['status'] = 'unknown';
    let error: string | undefined;

    try {
      switch (name) {
        case 'Supabase':
          await this.checkSupabase();
          break;
        case 'InfluxDB':
          await this.checkInfluxDB();
          break;
        case 'Redis':
          await this.checkRedis();
          break;
        case 'Ethereum RPC':
          await this.checkEthereumRPC();
          break;
        case 'Polygon RPC':
          await this.checkPolygonRPC();
          break;
        case 'BSC RPC':
          await this.checkBSCRPC();
          break;
        case 'CoinGecko API':
          await this.checkCoinGeckoAPI();
          break;
        default:
          throw new Error(`No check method for dependency: ${name}`);
      }
      
      status = 'healthy';
      dependency.retryCount = 0;
      
    } catch (err) {
      status = 'unhealthy';
      error = err instanceof Error ? err.message : 'Unknown error';
      dependency.retryCount++;

      // Retry for critical dependencies
      if (dependency.critical && dependency.retryCount < this.MAX_RETRIES) {
        logger.warn(`Retrying ${name} (attempt ${dependency.retryCount}/${this.MAX_RETRIES})...`);
        await this.delay(this.RETRY_DELAY);
        return this.checkDependency(name);
      }

      this.emit('dependencyFailed', name, err);
    }

    const responseTime = Date.now() - startTime;
    
    const updatedDependency: DependencyStatus = {
      ...dependency,
      status,
      responseTime,
      error,
      lastChecked: new Date()
    };

    this.dependencies.set(name, updatedDependency);
    
    if (status === 'healthy') {
      logger.info(`✅ ${name}: ${responseTime}ms`);
    } else {
      logger.error(`❌ ${name}: ${error}`);
    }

    return updatedDependency;
  }

  private async checkSupabase(): Promise<void> {
    if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('Supabase configuration missing');
    }

    const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
    
    // Test connection with a simple query
    const { error } = await supabase
      .from('trades')
      .select('count', { count: 'exact', head: true });

    if (error && !error.message.includes('relation "trades" does not exist')) {
      throw new Error(`Supabase connection failed: ${error.message}`);
    }
  }

  private async checkInfluxDB(): Promise<void> {
    if (!process.env.INFLUXDB_URL || !process.env.INFLUXDB_TOKEN) {
      throw new Error('InfluxDB configuration missing');
    }

    const influxDB = new InfluxDB({
      url: process.env.INFLUXDB_URL,
      token: process.env.INFLUXDB_TOKEN,
    });

    const queryApi = influxDB.getQueryApi(process.env.INFLUXDB_ORG || 'mev-arbitrage-org');

    // Test with a simple query
    const query = `from(bucket: "${process.env.INFLUXDB_BUCKET || 'mev-arbitrage-metrics'}") |> range(start: -1m) |> limit(n: 1)`;
    
    try {
      const result = await queryApi.collectRows(query);
      // Connection successful even if no data
    } catch (error) {
      if (error instanceof Error && error.message.includes('bucket not found')) {
        // Bucket doesn't exist yet, but connection is working
        return;
      }
      throw error;
    }
  }

  private async checkRedis(): Promise<void> {
    if (!process.env.REDIS_URL) {
      throw new Error('Redis configuration missing');
    }

    const client = createRedisClient({ url: process.env.REDIS_URL });
    
    try {
      await client.connect();
      await client.ping();
      await client.quit();
    } catch (error) {
      throw new Error(`Redis connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async checkEthereumRPC(): Promise<void> {
    const provider = new ethers.JsonRpcProvider(process.env.ETHEREUM_RPC_URL || 'https://eth.llamarpc.com');
    
    try {
      const blockNumber = await provider.getBlockNumber();
      if (blockNumber <= 0) {
        throw new Error('Invalid block number received');
      }
    } catch (error) {
      throw new Error(`Ethereum RPC failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async checkPolygonRPC(): Promise<void> {
    const provider = new ethers.JsonRpcProvider(process.env.POLYGON_RPC_URL || 'https://polygon.llamarpc.com');
    
    try {
      const blockNumber = await provider.getBlockNumber();
      if (blockNumber <= 0) {
        throw new Error('Invalid block number received');
      }
    } catch (error) {
      throw new Error(`Polygon RPC failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async checkBSCRPC(): Promise<void> {
    const provider = new ethers.JsonRpcProvider(process.env.BSC_RPC_URL || 'https://bsc-dataseed.binance.org/');
    
    try {
      const blockNumber = await provider.getBlockNumber();
      if (blockNumber <= 0) {
        throw new Error('Invalid block number received');
      }
    } catch (error) {
      throw new Error(`BSC RPC failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async checkCoinGeckoAPI(): Promise<void> {
    try {
      const response = await axios.get('https://api.coingecko.com/api/v3/ping', {
        timeout: this.TIMEOUT,
        headers: process.env.COINGECKO_API_KEY ? {
          'X-CG-Pro-API-Key': process.env.COINGECKO_API_KEY
        } : {}
      });

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(`CoinGecko API failed: ${error.message}`);
      }
      throw error;
    }
  }

  private generateResult(): DependencyCheckResult {
    const dependencies = Array.from(this.dependencies.values());
    const healthyCount = dependencies.filter(dep => dep.status === 'healthy').length;
    const criticalFailures: string[] = [];
    const warnings: string[] = [];

    dependencies.forEach(dep => {
      if (dep.status === 'unhealthy') {
        if (dep.critical) {
          criticalFailures.push(`Critical dependency ${dep.name} is unavailable: ${dep.error}`);
        } else {
          warnings.push(`Optional dependency ${dep.name} is unavailable: ${dep.error}`);
        }
      }
    });

    return {
      allHealthy: healthyCount === dependencies.length,
      healthyCount,
      totalCount: dependencies.length,
      dependencies,
      criticalFailures,
      warnings
    };
  }

  public getDependencyStatus(name: string): DependencyStatus | undefined {
    return this.dependencies.get(name);
  }

  public getAllDependencies(): DependencyStatus[] {
    return Array.from(this.dependencies.values());
  }

  public async recheckDependency(name: string): Promise<DependencyStatus> {
    const dependency = this.dependencies.get(name);
    if (dependency) {
      dependency.retryCount = 0; // Reset retry count
    }
    return this.checkDependency(name);
  }

  public async recheckCriticalDependencies(): Promise<DependencyCheckResult> {
    const criticalDeps = Array.from(this.dependencies.entries())
      .filter(([_, dep]) => dep.critical)
      .map(([name, _]) => name);

    const checkPromises = criticalDeps.map(name => this.checkDependency(name));
    await Promise.allSettled(checkPromises);

    return this.generateResult();
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Continuous monitoring
  public startContinuousMonitoring(intervalMs: number = 60000): void {
    setInterval(async () => {
      try {
        const result = await this.recheckCriticalDependencies();
        if (!result.allHealthy) {
          logger.warn(`Dependency health check: ${result.healthyCount}/${result.totalCount} healthy`);
        }
      } catch (error) {
        logger.error('Error during continuous dependency monitoring:', error);
      }
    }, intervalMs);
  }
}
