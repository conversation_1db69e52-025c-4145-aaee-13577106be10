# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Production builds
dist/
dist-ssr/
build/
.next/
out/

# Environment variables and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.pem
*.key
*.p12
*.pfx

# Logs
logs/
*.log
combined.log
error.log
health-monitor.log
health-report-*.json

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*~

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Blockchain and crypto specific
artifacts/
cache/
typechain-types/
deployments/localhost/
.openzeppelin/

# Database files
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore

# Local development files
quick-start.mjs
test-*.mjs
test-*.js
run-*.mjs

# Hardhat files
artifacts/
cache/
coverage/
coverage.json
typechain/
typechain-types/

# Foundry files
out/
cache_forge/

# MEV Bot specific
contract-addresses.json
metadata.json
system-status.js
