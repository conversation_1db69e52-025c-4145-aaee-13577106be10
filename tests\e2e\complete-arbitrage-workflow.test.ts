import { jest, describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { EventEmitter } from 'events';
import logger from '../../backend/utils/logger.js';
import config from '../../backend/config/index.js';

// Import all services for complete workflow testing
import { ServiceIntegrator } from '../../backend/services/ServiceIntegrator.js';
import { ArbitrageOpportunity, ArbitrageType } from '../../backend/services/OpportunityDetectionService.js';
import { Trade, TradeStatus } from '../../backend/services/ExecutionService.js';

describe('Complete Arbitrage Workflow E2E Tests', () => {
  let serviceIntegrator: ServiceIntegrator;
  let workflowMetrics: {
    totalOpportunities: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    totalProfit: number;
    averageLatency: number;
  };

  beforeAll(async () => {
    // Initialize complete system with all enhancements
    serviceIntegrator = new ServiceIntegrator({
      enablePreExecutionValidation: true,
      enableMEVProtection: true,
      enableFlashLoans: true,
      enableProfitValidation: true,
      enableEnhancedTokenMonitoring: true,
      enableExecutionQueue: true,
      enableMLLearning: true,
      enableRiskManagement: true
    });

    await serviceIntegrator.initialize();

    // Initialize metrics tracking
    workflowMetrics = {
      totalOpportunities: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0,
      totalProfit: 0,
      averageLatency: 0
    };

    logger.info('Complete Arbitrage Workflow E2E Tests initialized');
  });

  afterAll(async () => {
    if (serviceIntegrator) {
      await serviceIntegrator.shutdown();
    }

    // Log final metrics
    logger.info('Final Workflow Metrics:', workflowMetrics);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Arbitrage Pipeline', () => {
    it('should execute full arbitrage workflow from detection to completion', async () => {
      const workflowStart = Date.now();
      
      // Step 1: Simulate price discovery across multiple exchanges
      const tokenMonitoringService = serviceIntegrator.getService('enhancedTokenMonitoring');
      
      // Emit price updates that create arbitrage opportunity
      tokenMonitoringService.emit('priceUpdate', {
        symbol: 'ETH',
        price: 2000,
        exchange: 'uniswap',
        network: 'ethereum',
        timestamp: Date.now(),
        volume24h: 1000000,
        liquidity: 5000000
      });

      tokenMonitoringService.emit('priceUpdate', {
        symbol: 'ETH',
        price: 2120, // 6% price difference
        exchange: 'sushiswap',
        network: 'ethereum',
        timestamp: Date.now(),
        volume24h: 800000,
        liquidity: 3000000
      });

      // Wait for opportunity detection
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 2: Get detected opportunity
      const opportunityDetectionService = serviceIntegrator.getService('opportunityDetection');
      const detectedOpportunities = opportunityDetectionService.getActiveOpportunities();
      
      expect(detectedOpportunities.length).toBeGreaterThan(0);
      
      const opportunity = detectedOpportunities.find(opp => 
        opp.assets.includes('ETH') && opp.profitPercentage > 5
      );
      
      expect(opportunity).toBeDefined();
      workflowMetrics.totalOpportunities++;

      // Step 3: Pre-execution validation
      const preExecutionValidationService = serviceIntegrator.getService('preExecutionValidation');
      const validationResult = await preExecutionValidationService.validateOpportunity(opportunity);
      
      expect(validationResult.isValid).toBe(true);
      expect(validationResult.simulatedProfit).toBeGreaterThan(0);
      expect(validationResult.profitMargin).toBeGreaterThan(parseInt(config.MIN_PROFIT_MARGIN_BUFFER));

      // Step 4: Profit validation
      const profitValidationService = serviceIntegrator.getService('profitValidation');
      const profitValidation = await profitValidationService.validatePreExecution(opportunity);
      
      expect(profitValidation.isValid).toBe(true);
      expect(profitValidation.predictedProfit).toBeGreaterThan(parseFloat(config.PROFIT_VALIDATION_THRESHOLD));

      // Step 5: Flash loan optimization
      const flashLoanService = serviceIntegrator.getService('flashLoan');
      const flashLoanOptimization = await flashLoanService.optimizeFlashLoanStrategy(opportunity, 10000);
      
      expect(flashLoanOptimization).toBeDefined();
      expect(flashLoanOptimization.expectedProfit).toBeGreaterThan(0);

      // Step 6: Queue prioritization
      const executionQueue = serviceIntegrator.getService('executionQueue');
      await executionQueue.addOpportunity(opportunity);
      
      const queuedOpportunity = await executionQueue.getNextOpportunity();
      expect(queuedOpportunity).toBeDefined();
      expect(queuedOpportunity.opportunity.id).toBe(opportunity.id);

      // Step 7: MEV protection setup
      const mevProtectionService = serviceIntegrator.getService('mevProtection');
      const protectionMethod = mevProtectionService.selectOptimalProtectionMethod(opportunity);
      
      expect(protectionMethod).toBeDefined();

      // Step 8: Risk management check
      const riskManagementService = serviceIntegrator.getService('riskManagement');
      const riskAssessment = riskManagementService.assessOpportunity(opportunity);
      
      expect(riskAssessment.approved).toBe(true);
      expect(riskAssessment.riskScore).toBeLessThan(80); // Acceptable risk level

      // Step 9: Execution simulation (mock execution for testing)
      const executionService = serviceIntegrator.getService('execution');
      
      // Mock successful execution
      const mockTrade: Trade = {
        id: `trade-${Date.now()}`,
        opportunityId: opportunity.id,
        status: TradeStatus.COMPLETED,
        executedAt: Date.now(),
        gasUsed: 150000,
        gasPrice: 20000000000,
        actualProfit: profitValidation.predictedProfit * 0.95, // 95% of predicted
        slippage: 0.1,
        network: opportunity.network
      };

      // Step 10: Post-execution validation
      const postValidation = await profitValidationService.validatePostExecution(mockTrade, opportunity);
      
      expect(postValidation.validationPassed).toBe(true);
      expect(postValidation.profitAccuracy).toBeGreaterThan(parseInt(config.PROFIT_ACCURACY_THRESHOLD));

      const workflowEnd = Date.now();
      const totalWorkflowTime = workflowEnd - workflowStart;

      // Update metrics
      workflowMetrics.successfulExecutions++;
      workflowMetrics.averageExecutionTime = 
        (workflowMetrics.averageExecutionTime + totalWorkflowTime) / workflowMetrics.successfulExecutions;
      workflowMetrics.totalProfit += mockTrade.actualProfit;
      workflowMetrics.averageLatency = 
        (workflowMetrics.averageLatency + totalWorkflowTime) / workflowMetrics.totalOpportunities;

      // Verify performance targets
      expect(totalWorkflowTime).toBeLessThan(30000); // Complete workflow within 30 seconds
      
      logger.info(`Complete arbitrage workflow executed in ${totalWorkflowTime}ms`);
      logger.info(`Profit: $${mockTrade.actualProfit}, Accuracy: ${postValidation.profitAccuracy}%`);
    });

    it('should handle multiple concurrent arbitrage opportunities', async () => {
      const concurrentOpportunities = 5;
      const workflowPromises: Promise<any>[] = [];

      // Create multiple opportunities with different characteristics
      const opportunities = [
        {
          symbol: 'BTC',
          buyPrice: 45000,
          sellPrice: 46800,
          profitPercentage: 4,
          network: 'ethereum'
        },
        {
          symbol: 'USDC',
          buyPrice: 0.998,
          sellPrice: 1.002,
          profitPercentage: 0.4,
          network: 'polygon'
        },
        {
          symbol: 'LINK',
          buyPrice: 15.5,
          sellPrice: 16.2,
          profitPercentage: 4.5,
          network: 'arbitrum'
        },
        {
          symbol: 'UNI',
          buyPrice: 8.2,
          sellPrice: 8.7,
          profitPercentage: 6.1,
          network: 'optimism'
        },
        {
          symbol: 'MATIC',
          buyPrice: 1.1,
          sellPrice: 1.18,
          profitPercentage: 7.3,
          network: 'polygon'
        }
      ];

      // Process all opportunities concurrently
      for (let i = 0; i < concurrentOpportunities; i++) {
        const oppData = opportunities[i];
        
        workflowPromises.push(
          this.processOpportunityWorkflow(oppData, i)
        );
      }

      const results = await Promise.allSettled(workflowPromises);
      const successfulWorkflows = results.filter(result => result.status === 'fulfilled').length;
      
      expect(successfulWorkflows).toBeGreaterThan(concurrentOpportunities * 0.6); // 60% success rate
      
      logger.info(`Concurrent processing: ${successfulWorkflows}/${concurrentOpportunities} workflows completed`);
    });

    it('should maintain system stability under continuous operation', async () => {
      const operationDuration = 30000; // 30 seconds
      const operationStart = Date.now();
      let processedOpportunities = 0;
      let systemErrors = 0;

      // Continuous operation simulation
      const operationInterval = setInterval(async () => {
        try {
          // Simulate opportunity detection and processing
          const mockOpportunity: ArbitrageOpportunity = {
            id: `continuous-op-${Date.now()}`,
            type: ArbitrageType.INTRA_CHAIN,
            assets: ['ETH', 'USDC'],
            exchanges: ['uniswap', 'sushiswap'],
            potentialProfit: 50 + Math.random() * 200,
            profitPercentage: 2 + Math.random() * 8,
            timestamp: Date.now(),
            network: 'ethereum',
            route: {
              steps: [],
              totalGasCost: 150000,
              expectedProfit: 50 + Math.random() * 200,
              priceImpact: 0.5
            },
            estimatedGas: 150000,
            slippage: 0.5,
            confidence: 80 + Math.random() * 20
          };

          // Quick validation and processing
          const profitValidationService = serviceIntegrator.getService('profitValidation');
          await profitValidationService.validatePreExecution(mockOpportunity);
          
          const executionQueue = serviceIntegrator.getService('executionQueue');
          await executionQueue.addOpportunity(mockOpportunity);
          
          processedOpportunities++;
        } catch (error) {
          systemErrors++;
          logger.error('System error during continuous operation:', error);
        }
      }, 2000); // Process every 2 seconds

      // Run continuous operation
      await new Promise(resolve => setTimeout(resolve, operationDuration));
      clearInterval(operationInterval);

      const operationEnd = Date.now();
      const totalOperationTime = operationEnd - operationStart;
      
      // Verify system stability
      const errorRate = (systemErrors / processedOpportunities) * 100;
      expect(errorRate).toBeLessThan(10); // Less than 10% error rate
      
      // Verify system health after continuous operation
      const healthChecks = await serviceIntegrator.healthCheck();
      expect(typeof healthChecks).toBe('object');
      
      logger.info(`Continuous operation: ${processedOpportunities} opportunities processed over ${totalOperationTime}ms`);
      logger.info(`Error rate: ${errorRate.toFixed(2)}%`);
    });
  });

  // Helper method for processing individual opportunities
  private async processOpportunityWorkflow(oppData: any, index: number): Promise<void> {
    const opportunity: ArbitrageOpportunity = {
      id: `concurrent-opportunity-${index}`,
      type: ArbitrageType.INTRA_CHAIN,
      assets: [oppData.symbol, 'USDC'],
      exchanges: ['uniswap', 'sushiswap'],
      potentialProfit: (oppData.sellPrice - oppData.buyPrice) * 100,
      profitPercentage: oppData.profitPercentage,
      timestamp: Date.now(),
      network: oppData.network,
      route: {
        steps: [],
        totalGasCost: 150000,
        expectedProfit: (oppData.sellPrice - oppData.buyPrice) * 100,
        priceImpact: 0.5
      },
      estimatedGas: 150000,
      slippage: 0.5,
      confidence: 90
    };

    // Execute validation chain
    const profitValidationService = serviceIntegrator.getService('profitValidation');
    const preExecutionValidationService = serviceIntegrator.getService('preExecutionValidation');
    const executionQueue = serviceIntegrator.getService('executionQueue');

    const profitValidation = await profitValidationService.validatePreExecution(opportunity);
    const preValidation = await preExecutionValidationService.validateOpportunity(opportunity);
    await executionQueue.addOpportunity(opportunity);

    if (!profitValidation.isValid || !preValidation.isValid) {
      throw new Error(`Validation failed for opportunity ${opportunity.id}`);
    }
  }
});
