import { RedisClientType } from 'redis';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { databaseManager } from './DatabaseConnectionManager.js';

export interface CacheEntry {
  value: any;
  expiry: number;
  hits: number;
  created: number;
  size: number;
  tier: 'memory' | 'redis' | 'browser';
  priority: 'low' | 'medium' | 'high';
}

export interface CacheMetrics {
  totalRequests: number;
  hits: number;
  misses: number;
  hitRatio: number;
  memoryUsage: number;
  redisConnected: boolean;
  evictions: number;
  averageLatency: number;
  tierDistribution: Record<string, number>;
}

export interface CacheConfig {
  key: string;
  ttl: number;
  tier: 'memory' | 'redis' | 'both';
  compress?: boolean;
  priority?: 'low' | 'medium' | 'high';
  maxSize?: number;
}

export class EnhancedCacheService {
  private redis: RedisClientType | null = null;
  private memoryCache: Map<string, CacheEntry> = new Map();
  private maxMemoryCacheSize = 500 * 1024 * 1024; // 500MB
  private currentMemoryUsage = 0;
  private metrics: CacheMetrics = {
    totalRequests: 0,
    hits: 0,
    misses: 0,
    hitRatio: 0,
    memoryUsage: 0,
    redisConnected: false,
    evictions: 0,
    averageLatency: 0,
    tierDistribution: { memory: 0, redis: 0, browser: 0 }
  };
  private latencyHistory: number[] = [];

  // Cache configuration for different data types
  private cacheConfigs: Map<string, CacheConfig> = new Map([
    ['opportunity', { 
      key: 'opportunity', 
      ttl: parseInt(config.OPPORTUNITY_CACHE_TTL), 
      tier: 'both', 
      priority: 'high',
      maxSize: 1024 * 1024 // 1MB
    }],
    ['price', { 
      key: 'price', 
      ttl: parseInt(config.PRICE_CACHE_TTL), 
      tier: 'both', 
      priority: 'high',
      maxSize: 512 * 1024 // 512KB
    }],
    ['queue', { 
      key: 'queue', 
      ttl: parseInt(config.QUEUE_CACHE_TTL), 
      tier: 'redis', 
      priority: 'high',
      maxSize: 2 * 1024 * 1024 // 2MB
    }],
    ['health', { 
      key: 'health', 
      ttl: parseInt(config.HEALTH_CACHE_TTL), 
      tier: 'memory', 
      priority: 'medium',
      maxSize: 256 * 1024 // 256KB
    }],
    ['validation', { 
      key: 'validation', 
      ttl: 60, 
      tier: 'both', 
      priority: 'high',
      maxSize: 1024 * 1024 // 1MB
    }],
    ['flash_loan', { 
      key: 'flash_loan', 
      ttl: 30, 
      tier: 'redis', 
      priority: 'high',
      maxSize: 512 * 1024 // 512KB
    }],
    ['mev_protection', { 
      key: 'mev_protection', 
      ttl: 60, 
      tier: 'redis', 
      priority: 'medium',
      maxSize: 256 * 1024 // 256KB
    }],
    ['metrics', { 
      key: 'metrics', 
      ttl: 30, 
      tier: 'redis', 
      priority: 'medium',
      maxSize: 1024 * 1024 // 1MB
    }]
  ]);

  constructor() {
    this.initialize();
    this.startCleanupInterval();
    this.startMetricsCollection();
  }

  private async initialize(): Promise<void> {
    try {
      this.redis = databaseManager.getRedis();
      if (this.redis) {
        this.metrics.redisConnected = true;
        logger.info('Enhanced cache service initialized with Redis');
      } else {
        logger.warn('Redis not available, using memory cache only');
        this.metrics.redisConnected = false;
      }
    } catch (error) {
      logger.error('Failed to initialize enhanced cache service:', error);
      this.metrics.redisConnected = false;
    }
  }

  async get<T>(key: string, type?: string): Promise<T | null> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const config = type ? this.cacheConfigs.get(type) : null;
      
      // Try memory cache first for 'both' or 'memory' tier
      if (!config || config.tier === 'memory' || config.tier === 'both') {
        const memoryResult = this.getFromMemory<T>(key);
        if (memoryResult !== null) {
          this.recordHit('memory', Date.now() - startTime);
          return memoryResult;
        }
      }

      // Try Redis for 'redis' or 'both' tier
      if (this.redis && (!config || config.tier === 'redis' || config.tier === 'both')) {
        const redisResult = await this.getFromRedis<T>(key);
        if (redisResult !== null) {
          // Store in memory cache if tier is 'both'
          if (config && config.tier === 'both') {
            this.setInMemory(key, redisResult, config.ttl, config.priority || 'medium');
          }
          this.recordHit('redis', Date.now() - startTime);
          return redisResult;
        }
      }

      this.recordMiss(Date.now() - startTime);
      return null;
    } catch (error) {
      logger.error(`Cache get error for key ${key}:`, error);
      this.recordMiss(Date.now() - startTime);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number, type?: string): Promise<boolean> {
    const startTime = Date.now();
    
    try {
      const config = type ? this.cacheConfigs.get(type) : null;
      const finalTtl = ttl || config?.ttl || parseInt(config.REDIS_DEFAULT_TTL);
      const priority = config?.priority || 'medium';
      
      let success = false;

      // Set in memory cache for 'memory' or 'both' tier
      if (!config || config.tier === 'memory' || config.tier === 'both') {
        success = this.setInMemory(key, value, finalTtl, priority) || success;
      }

      // Set in Redis for 'redis' or 'both' tier
      if (this.redis && (!config || config.tier === 'redis' || config.tier === 'both')) {
        success = await this.setInRedis(key, value, finalTtl) || success;
      }

      const latency = Date.now() - startTime;
      this.recordLatency(latency);
      
      return success;
    } catch (error) {
      logger.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  async delete(key: string, type?: string): Promise<boolean> {
    try {
      const config = type ? this.cacheConfigs.get(type) : null;
      let success = false;

      // Delete from memory cache
      if (!config || config.tier === 'memory' || config.tier === 'both') {
        const entry = this.memoryCache.get(key);
        if (entry) {
          this.currentMemoryUsage -= entry.size;
          this.memoryCache.delete(key);
          success = true;
        }
      }

      // Delete from Redis
      if (this.redis && (!config || config.tier === 'redis' || config.tier === 'both')) {
        const result = await this.redis.del(key);
        success = result > 0 || success;
      }

      return success;
    } catch (error) {
      logger.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiry) {
      this.currentMemoryUsage -= entry.size;
      this.memoryCache.delete(key);
      return null;
    }

    entry.hits++;
    return entry.value as T;
  }

  private async getFromRedis<T>(key: string): Promise<T | null> {
    if (!this.redis) return null;

    try {
      const value = await this.redis.get(key);
      if (!value) return null;

      return JSON.parse(value) as T;
    } catch (error) {
      logger.error(`Redis get error for key ${key}:`, error);
      return null;
    }
  }

  private setInMemory<T>(key: string, value: T, ttl: number, priority: string): boolean {
    try {
      const serialized = JSON.stringify(value);
      const size = Buffer.byteLength(serialized, 'utf8');
      
      // Check if we need to evict entries
      while (this.currentMemoryUsage + size > this.maxMemoryCacheSize && this.memoryCache.size > 0) {
        this.evictLeastUsed();
      }

      const entry: CacheEntry = {
        value,
        expiry: Date.now() + (ttl * 1000),
        hits: 0,
        created: Date.now(),
        size,
        tier: 'memory',
        priority: priority as 'low' | 'medium' | 'high'
      };

      // Remove existing entry if it exists
      const existingEntry = this.memoryCache.get(key);
      if (existingEntry) {
        this.currentMemoryUsage -= existingEntry.size;
      }

      this.memoryCache.set(key, entry);
      this.currentMemoryUsage += size;
      this.metrics.tierDistribution.memory++;

      return true;
    } catch (error) {
      logger.error(`Memory cache set error for key ${key}:`, error);
      return false;
    }
  }

  private async setInRedis<T>(key: string, value: T, ttl: number): Promise<boolean> {
    if (!this.redis) return false;

    try {
      const serialized = JSON.stringify(value);
      await this.redis.setEx(key, ttl, serialized);
      this.metrics.tierDistribution.redis++;
      return true;
    } catch (error) {
      logger.error(`Redis set error for key ${key}:`, error);
      return false;
    }
  }

  private evictLeastUsed(): void {
    let leastUsedKey: string | null = null;
    let leastUsedEntry: CacheEntry | null = null;
    let lowestScore = Infinity;

    // Calculate eviction score based on hits, age, and priority
    this.memoryCache.forEach((entry, key) => {
      const age = Date.now() - entry.created;
      const priorityWeight = entry.priority === 'high' ? 3 : entry.priority === 'medium' ? 2 : 1;
      const score = (entry.hits + 1) * priorityWeight / (age + 1);

      if (score < lowestScore) {
        lowestScore = score;
        leastUsedKey = key;
        leastUsedEntry = entry;
      }
    });

    if (leastUsedKey && leastUsedEntry) {
      this.currentMemoryUsage -= leastUsedEntry.size;
      this.memoryCache.delete(leastUsedKey);
      this.metrics.evictions++;
    }
  }

  private recordHit(tier: string, latency: number): void {
    this.metrics.hits++;
    this.recordLatency(latency);
  }

  private recordMiss(latency: number): void {
    this.metrics.misses++;
    this.recordLatency(latency);
  }

  private recordLatency(latency: number): void {
    this.latencyHistory.push(latency);
    if (this.latencyHistory.length > 1000) {
      this.latencyHistory.shift();
    }
    
    this.metrics.averageLatency = this.latencyHistory.reduce((a, b) => a + b, 0) / this.latencyHistory.length;
  }

  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, 60000); // Cleanup every minute
  }

  private startMetricsCollection(): void {
    setInterval(() => {
      this.updateMetrics();
    }, 30000); // Update metrics every 30 seconds
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let cleanedCount = 0;

    this.memoryCache.forEach((entry, key) => {
      if (now > entry.expiry) {
        this.currentMemoryUsage -= entry.size;
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      logger.debug(`Cleaned up ${cleanedCount} expired cache entries`);
    }
  }

  private updateMetrics(): void {
    const total = this.metrics.hits + this.metrics.misses;
    this.metrics.hitRatio = total > 0 ? (this.metrics.hits / total) * 100 : 0;
    this.metrics.memoryUsage = this.currentMemoryUsage;

    // Alert if hit ratio is below threshold
    const threshold = parseInt(config.CACHE_HIT_RATIO_THRESHOLD);
    if (this.metrics.hitRatio < threshold) {
      logger.warn(`Cache hit ratio below threshold: ${this.metrics.hitRatio.toFixed(2)}% < ${threshold}%`);
    }
  }

  // Specialized cache methods for different data types
  async cacheOpportunity(id: string, opportunity: any): Promise<boolean> {
    return this.set(`opportunity:${id}`, opportunity, undefined, 'opportunity');
  }

  async getOpportunity(id: string): Promise<any> {
    return this.get(`opportunity:${id}`, 'opportunity');
  }

  async cachePrice(symbol: string, network: string, price: any): Promise<boolean> {
    return this.set(`price:${symbol}:${network}`, price, undefined, 'price');
  }

  async getPrice(symbol: string, network: string): Promise<any> {
    return this.get(`price:${symbol}:${network}`, 'price');
  }

  async cacheQueueStatus(status: any): Promise<boolean> {
    return this.set('queue:status', status, undefined, 'queue');
  }

  async getQueueStatus(): Promise<any> {
    return this.get('queue:status', 'queue');
  }

  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  async flush(): Promise<void> {
    this.memoryCache.clear();
    this.currentMemoryUsage = 0;
    
    if (this.redis) {
      try {
        await this.redis.flushDb();
      } catch (error) {
        logger.error('Error flushing Redis cache:', error);
      }
    }
    
    logger.info('Cache flushed');
  }
}

export const enhancedCacheService = new EnhancedCacheService();
