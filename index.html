<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEV Arbitrage Bot - Professional Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        :root {
            --primary-bg: #0F172A;
            --secondary-bg: #1E293B;
            --card-bg: #334155;
            --accent-bg: #475569;
            --success: #10B981;
            --error: #EF4444;
            --warning: #F59E0B;
            --info: #3B82F6;
            --text-primary: #F8FAFC;
            --text-secondary: #CBD5E1;
            --text-muted: #64748B;
            --border: #475569;
            --glass-bg: rgba(51, 65, 85, 0.8);
            --glass-border: rgba(148, 163, 184, 0.2);
        }

        body {
            background: linear-gradient(135deg, var(--primary-bg) 0%, #1E293B 100%);
            color: var(--text-primary);
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .status-success {
            background: linear-gradient(135deg, var(--success), #059669);
            color: #ECFDF5;
            border: 1px solid #10B981;
        }
        .status-error {
            background: linear-gradient(135deg, var(--error), #DC2626);
            color: #FEF2F2;
            border: 1px solid #EF4444;
        }
        .status-warning {
            background: linear-gradient(135deg, var(--warning), #D97706);
            color: #FFFBEB;
            border: 1px solid #F59E0B;
        }
        .status-info {
            background: linear-gradient(135deg, var(--info), #2563EB);
            color: #EFF6FF;
            border: 1px solid #3B82F6;
        }
        .glass-card {
            background: var(--glass-bg);
            -webkit-backdrop-filter: blur(12px);
            backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .metric-card {
            background: linear-gradient(135deg, var(--card-bg), var(--accent-bg));
            border: 1px solid var(--border);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--success), var(--info), var(--warning));
        }

        .opportunity-item {
            background: var(--secondary-bg);
            border: 1px solid var(--border);
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 0.75rem;
            transition: all 0.2s ease;
            position: relative;
        }

        .opportunity-item:hover {
            background: var(--card-bg);
            transform: translateX(4px);
            border-color: var(--info);
        }

        .profit-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .network-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .network-ethereum { background: #627EEA; color: white; }
        .network-polygon { background: #8247E5; color: white; }
        .network-bsc { background: #F3BA2F; color: black; }
        .network-arbitrum { background: #28A0F0; color: white; }

        .loading-skeleton {
            background: linear-gradient(90deg, var(--secondary-bg) 25%, var(--card-bg) 50%, var(--secondary-bg) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .performance-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .performance-excellent { background: rgba(16, 185, 129, 0.1); color: var(--success); }
        .performance-good { background: rgba(59, 130, 246, 0.1); color: var(--info); }
        .performance-warning { background: rgba(245, 158, 11, 0.1); color: var(--warning); }
        .performance-poor { background: rgba(239, 68, 68, 0.1); color: var(--error); }

        .connection-status {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .scrollbar-thin {
            scrollbar-width: thin;
            scrollbar-color: var(--border) transparent;
        }

        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
            background: transparent;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: var(--border);
            border-radius: 3px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        .grid { display: grid; gap: 1.5rem; }
        .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        .grid-cols-5 { grid-template-columns: repeat(5, 1fr); }

        @media (max-width: 1024px) {
            .grid-cols-4 { grid-template-columns: repeat(2, 1fr); }
            .grid-cols-5 { grid-template-columns: repeat(3, 1fr); }
        }

        @media (max-width: 768px) {
            .grid-cols-2, .grid-cols-3, .grid-cols-4, .grid-cols-5 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Connection Status Indicator -->
    <div id="connection-status" class="connection-status status-info">
        <div class="w-2 h-2 rounded-full bg-current animate-pulse"></div>
        <span>Connecting...</span>
    </div>

    <div class="min-h-screen p-6">
        <div class="max-w-7xl mx-auto">
            <!-- Header Section -->
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                        MEV Arbitrage Bot
                    </h1>
                    <p class="text-lg text-gray-400 mt-2">Professional Trading Dashboard</p>
                </div>
                <div class="flex items-center gap-4">
                    <div id="system-health" class="performance-indicator performance-good">
                        <i data-lucide="activity" class="w-4 h-4"></i>
                        <span>System Healthy</span>
                    </div>
                    <div id="last-update" class="text-sm text-gray-400">
                        Last update: <span id="update-time">--:--</span>
                    </div>
                </div>
            </div>

            <!-- Primary KPIs -->
            <div class="grid grid-cols-5 mb-8">
                <div class="metric-card">
                    <div class="profit-indicator"></div>
                    <h3 class="text-sm font-medium text-gray-400 mb-3 uppercase tracking-wide">Total Profit</h3>
                    <p class="text-3xl font-bold text-green-400 mb-2" id="total-profit">$0.00</p>
                    <div class="text-xs text-gray-500" id="profit-change">+0.0% (24h)</div>
                </div>
                <div class="metric-card">
                    <h3 class="text-sm font-medium text-gray-400 mb-3 uppercase tracking-wide">Win Rate</h3>
                    <p class="text-3xl font-bold text-blue-400 mb-2" id="win-rate">0.0%</p>
                    <div class="text-xs text-gray-500" id="win-rate-trend">Stable</div>
                </div>
                <div class="metric-card">
                    <h3 class="text-sm font-medium text-gray-400 mb-3 uppercase tracking-wide">Active Opportunities</h3>
                    <p class="text-3xl font-bold text-yellow-400 mb-2" id="active-opportunities">0</p>
                    <div class="text-xs text-gray-500" id="opportunities-trend">Scanning...</div>
                </div>
                <div class="metric-card">
                    <h3 class="text-sm font-medium text-gray-400 mb-3 uppercase tracking-wide">Execution Queue</h3>
                    <p class="text-3xl font-bold text-purple-400 mb-2" id="queue-size">0</p>
                    <div class="text-xs text-gray-500" id="queue-status">Ready</div>
                </div>
                <div class="metric-card">
                    <h3 class="text-sm font-medium text-gray-400 mb-3 uppercase tracking-wide">Daily Volume</h3>
                    <p class="text-3xl font-bold text-indigo-400 mb-2" id="daily-volume">$0</p>
                    <div class="text-xs text-gray-500" id="volume-change">+0.0% vs avg</div>
                </div>
            </div>

            <!-- Advanced Metrics & ML Status -->
            <div class="grid grid-cols-4 mb-8">
                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">MEV Protection</h3>
                        <i data-lucide="shield-check" class="w-5 h-5 text-green-400"></i>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Status</span>
                            <span class="text-sm font-medium text-green-400" id="mev-status">Active</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Protected Txs</span>
                            <span class="text-sm font-medium" id="mev-protected">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Success Rate</span>
                            <span class="text-sm font-medium text-blue-400" id="mev-success-rate">0%</span>
                        </div>
                    </div>
                </div>

                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">Flash Loans</h3>
                        <i data-lucide="zap" class="w-5 h-5 text-yellow-400"></i>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Available</span>
                            <span class="text-sm font-medium text-green-400" id="flash-loan-available">Yes</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Best Rate</span>
                            <span class="text-sm font-medium" id="flash-loan-rate">0.05%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Provider</span>
                            <span class="text-sm font-medium text-purple-400" id="flash-loan-provider">Aave V3</span>
                        </div>
                    </div>
                </div>

                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">ML Learning</h3>
                        <i data-lucide="brain" class="w-5 h-5 text-purple-400"></i>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Market Regime</span>
                            <span class="text-sm font-medium text-purple-400" id="market-regime">Normal</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Active Strategies</span>
                            <span class="text-sm font-medium" id="active-strategies">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Adaptation Rate</span>
                            <span class="text-sm font-medium text-green-400" id="adaptation-rate">0.0/hr</span>
                        </div>
                    </div>
                </div>

                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">Network Status</h3>
                        <i data-lucide="globe" class="w-5 h-5 text-blue-400"></i>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Networks</span>
                            <span class="text-sm font-medium text-green-400" id="networks-active">10/10</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Avg Latency</span>
                            <span class="text-sm font-medium" id="network-latency">--ms</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-400">Gas Prices</span>
                            <span class="text-sm font-medium text-yellow-400" id="gas-prices">Optimal</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Charts Section -->
            <div class="grid grid-cols-2 mb-8">
                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-semibold">Profit Performance</h3>
                        <div class="flex gap-2">
                            <button class="text-xs px-2 py-1 rounded bg-blue-600 text-white" onclick="updateChart('1h')">1H</button>
                            <button class="text-xs px-2 py-1 rounded bg-gray-600" onclick="updateChart('24h')">24H</button>
                            <button class="text-xs px-2 py-1 rounded bg-gray-600" onclick="updateChart('7d')">7D</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="profit-chart"></canvas>
                    </div>
                </div>

                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-semibold">Network Distribution</h3>
                        <i data-lucide="pie-chart" class="w-5 h-5 text-gray-400"></i>
                    </div>
                    <div class="chart-container">
                        <canvas id="network-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Main Data Sections -->
            <div class="grid grid-cols-4 gap-6">
                <!-- Live Opportunities -->
                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-bold" id="opportunities-title">
                            Live Opportunities (0)
                        </h2>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                            <span class="text-xs text-gray-400">Real-time</span>
                        </div>
                    </div>
                    <div class="max-h-96 overflow-y-auto scrollbar-thin" id="opportunities-list">
                        <div class="loading-skeleton h-20 rounded mb-3"></div>
                        <div class="loading-skeleton h-20 rounded mb-3"></div>
                        <div class="loading-skeleton h-20 rounded"></div>
                    </div>
                </div>

                <!-- Execution Queue -->
                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-bold" id="queue-title">
                            Execution Queue (0)
                        </h2>
                        <i data-lucide="list-ordered" class="w-5 h-5 text-purple-400"></i>
                    </div>
                    <div class="max-h-96 overflow-y-auto scrollbar-thin" id="queue-list">
                        <p class="text-gray-400 text-center py-8">Queue empty</p>
                    </div>
                </div>

                <!-- Recent Trades -->
                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-bold" id="trades-title">
                            Recent Trades (0)
                        </h2>
                        <i data-lucide="trending-up" class="w-5 h-5 text-green-400"></i>
                    </div>
                    <div class="max-h-96 overflow-y-auto scrollbar-thin" id="trades-list">
                        <div class="loading-skeleton h-16 rounded mb-2"></div>
                        <div class="loading-skeleton h-16 rounded mb-2"></div>
                        <div class="loading-skeleton h-16 rounded"></div>
                    </div>
                </div>

                <!-- Strategy Performance -->
                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-bold" id="strategy-title">
                            ML Strategies (0)
                        </h2>
                        <i data-lucide="brain" class="w-5 h-5 text-purple-400"></i>
                    </div>
                    <div class="max-h-96 overflow-y-auto scrollbar-thin" id="strategy-list">
                        <div class="loading-skeleton h-14 rounded mb-2"></div>
                        <div class="loading-skeleton h-14 rounded mb-2"></div>
                        <div class="loading-skeleton h-14 rounded"></div>
                    </div>
                </div>
            </div>

            <!-- System Monitoring Section -->
            <div class="grid grid-cols-3 mt-8">
                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">System Health</h3>
                        <i data-lucide="activity" class="w-5 h-5 text-green-400"></i>
                    </div>
                    <div class="space-y-3" id="system-health-details">
                        <div class="loading-skeleton h-4 rounded"></div>
                        <div class="loading-skeleton h-4 rounded"></div>
                        <div class="loading-skeleton h-4 rounded"></div>
                    </div>
                </div>

                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">Performance Metrics</h3>
                        <i data-lucide="gauge" class="w-5 h-5 text-blue-400"></i>
                    </div>
                    <div class="space-y-3" id="performance-metrics">
                        <div class="loading-skeleton h-4 rounded"></div>
                        <div class="loading-skeleton h-4 rounded"></div>
                        <div class="loading-skeleton h-4 rounded"></div>
                    </div>
                </div>

                <div class="glass-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">Debug Console</h3>
                        <button type="button" onclick="clearDebugLog()" class="text-xs px-2 py-1 rounded bg-gray-600 hover:bg-gray-500">
                            Clear
                        </button>
                    </div>
                    <div class="max-h-48 overflow-y-auto scrollbar-thin">
                        <pre id="debug-info" class="text-xs text-gray-300 whitespace-pre-wrap"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        // ===== ENHANCED CONFIGURATION =====
        const CONFIG = {
            API_BASE: window.location.protocol === 'file:' ? 'http://localhost:8080' : '',
            WS_URL: window.location.protocol === 'file:' ? 'ws://localhost:8080/ws' :
                   `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`,

            // Performance targets from your system requirements
            PERFORMANCE_TARGETS: {
                LATENCY_TARGET: 5000,        // <5s price updates
                QUEUE_LATENCY: 1000,         // <1s queue operations
                UPTIME_TARGET: 99,           // >99% uptime
                DB_QUERY_TARGET: 100,        // <100ms database queries
                API_RESPONSE_TARGET: 200,    // <200ms API response
                MEMORY_TARGET: 100 * 1024 * 1024, // <100MB memory
                THROUGHPUT_TARGET: 500000    // >500K ops/second
            },

            // WebSocket configuration
            WS_CONFIG: {
                maxReconnectAttempts: 10,
                reconnectDelay: 1000,
                heartbeatInterval: 30000,
                messageQueueSize: 1000,
                compressionThreshold: 1024
            },

            // Cache configuration
            CACHE_CONFIG: {
                defaultTTL: 300000,          // 5 minutes
                criticalDataTTL: 60000,      // 1 minute for critical data
                maxCacheSize: 1000,
                cleanupInterval: 600000      // 10 minutes
            },

            // Update intervals based on data criticality
            UPDATE_INTERVALS: {
                critical: 5000,              // Opportunities, trades, queue
                important: 15000,            // System health, performance
                standard: 30000,             // Analytics, historical data
                background: 60000            // ML stats, network status
            }
        };

        // ===== GLOBAL STATE MANAGEMENT =====
        const AppState = {
            // Connection state
            websocket: null,
            isConnected: false,
            reconnectAttempts: 0,
            lastHeartbeat: 0,

            // Data cache with TTL
            dataCache: new Map(),
            cacheMetrics: {
                hits: 0,
                misses: 0,
                size: 0
            },

            // Performance monitoring
            performanceMetrics: {
                latency: 0,
                uptime: Date.now(),
                errorRate: 0,
                throughput: 0,
                memoryUsage: 0,
                cacheHitRatio: 0,
                lastUpdate: Date.now()
            },

            // UI state
            currentChartTimeframe: '1h',
            activeFilters: {},
            sortPreferences: {},

            // Debug and logging
            debugLog: [],
            maxDebugEntries: 100,

            // Subscription management
            subscriptions: new Set(),
            subscriptionCallbacks: new Map()
        };

        // ===== UTILITY FUNCTIONS =====
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                level,
                message,
                time: Date.now()
            };

            AppState.debugLog.push(logEntry);

            // Keep only recent entries
            if (AppState.debugLog.length > AppState.maxDebugEntries) {
                AppState.debugLog = AppState.debugLog.slice(-AppState.maxDebugEntries);
            }

            // Update debug display
            updateDebugDisplay();

            // Console logging with appropriate level
            const consoleMethod = console[level] || console.log;
            consoleMethod(`[${timestamp}] ${message}`);
        }

        function updateDebugDisplay() {
            const debugEl = document.getElementById('debug-info');
            if (debugEl) {
                const recentLogs = AppState.debugLog.slice(-20);
                debugEl.textContent = recentLogs.map(entry =>
                    `[${entry.timestamp}] ${entry.level.toUpperCase()}: ${entry.message}`
                ).join('\n');
                debugEl.scrollTop = debugEl.scrollHeight;
            }
        }

        function clearDebugLog() {
            AppState.debugLog = [];
            updateDebugDisplay();
            log('Debug log cleared');
        }

        function updateConnectionStatus(status, message) {
            const statusEl = document.getElementById('connection-status');
            if (statusEl) {
                statusEl.className = `connection-status status-${status}`;
                statusEl.innerHTML = `
                    <div class="w-2 h-2 rounded-full bg-current ${status === 'success' ? '' : 'animate-pulse'}"></div>
                    <span>${message}</span>
                `;
            }
        }

        function updateSystemHealth(healthData) {
            const healthEl = document.getElementById('system-health');
            if (healthEl && healthData) {
                const isHealthy = healthData.isHealthy !== false;
                const statusClass = isHealthy ? 'performance-excellent' : 'performance-poor';
                const statusText = isHealthy ? 'System Healthy' : 'System Issues';

                healthEl.className = `performance-indicator ${statusClass}`;
                healthEl.innerHTML = `
                    <i data-lucide="activity" class="w-4 h-4"></i>
                    <span>${statusText}</span>
                `;
                lucide.createIcons();
            }
        }

        function updateLastUpdateTime() {
            const timeEl = document.getElementById('update-time');
            if (timeEl) {
                timeEl.textContent = new Date().toLocaleTimeString();
            }
        }

        function formatNumber(num, decimals = 2) {
            if (num === null || num === undefined || isNaN(num)) return '--';
            return Number(num).toLocaleString(undefined, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }

        function formatCurrency(amount, currency = 'USD') {
            if (amount === null || amount === undefined || isNaN(amount)) return '--';
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }

        function formatPercentage(value, decimals = 1) {
            if (value === null || value === undefined || isNaN(value)) return '--';
            return `${Number(value).toFixed(decimals)}%`;
        }

        function getTimeAgo(timestamp) {
            const now = Date.now();
            const diff = now - (typeof timestamp === 'string' ? new Date(timestamp).getTime() : timestamp);

            const seconds = Math.floor(diff / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (seconds < 60) return 'just now';
            if (minutes < 60) return `${minutes}m ago`;
            if (hours < 24) return `${hours}h ago`;
            return `${days}d ago`;
        }

        // ===== ENHANCED WEBSOCKET MANAGEMENT =====
        class EnhancedWebSocketManager {
            constructor() {
                this.ws = null;
                this.messageQueue = [];
                this.heartbeatTimer = null;
                this.reconnectTimer = null;
                this.subscriptions = new Map();
                this.messageHandlers = new Map();

                this.setupMessageHandlers();
            }

            setupMessageHandlers() {
                // Map message types to handler functions
                this.messageHandlers.set('connection.established', this.handleConnectionEstablished.bind(this));
                this.messageHandlers.set('opportunity.updated', this.handleOpportunityUpdate.bind(this));
                this.messageHandlers.set('trade.updated', this.handleTradeUpdate.bind(this));
                this.messageHandlers.set('queue.updated', this.handleQueueUpdate.bind(this));
                this.messageHandlers.set('system.health', this.handleSystemHealth.bind(this));
                this.messageHandlers.set('metrics.updated', this.handleMetricsUpdate.bind(this));
                this.messageHandlers.set('price.updated', this.handlePriceUpdate.bind(this));
                this.messageHandlers.set('mev.protected', this.handleMEVUpdate.bind(this));
                this.messageHandlers.set('flash_loan.quoted', this.handleFlashLoanUpdate.bind(this));
                this.messageHandlers.set('ml.learning', this.handleMLUpdate.bind(this));
            }

            async connect() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    log('WebSocket already connected', 'warn');
                    return;
                }

                try {
                    log('Initializing enhanced WebSocket connection...');
                    this.ws = new WebSocket(CONFIG.WS_URL);

                    this.ws.onopen = this.handleOpen.bind(this);
                    this.ws.onmessage = this.handleMessage.bind(this);
                    this.ws.onclose = this.handleClose.bind(this);
                    this.ws.onerror = this.handleError.bind(this);

                } catch (error) {
                    log(`Failed to initialize WebSocket: ${error.message}`, 'error');
                    this.startPollingFallback();
                }
            }

            handleOpen(event) {
                AppState.isConnected = true;
                AppState.reconnectAttempts = 0;
                AppState.lastHeartbeat = Date.now();

                log('Enhanced WebSocket connected successfully');
                updateConnectionStatus('success', 'Real-time Connected');

                // Process queued messages
                this.processMessageQueue();

                // Start heartbeat
                this.startHeartbeat();

                // Subscribe to all critical channels
                this.subscribeToChannels();
            }

            handleMessage(event) {
                try {
                    const message = JSON.parse(event.data);
                    AppState.lastHeartbeat = Date.now();

                    // Update performance metrics
                    AppState.performanceMetrics.throughput++;

                    // Route message to appropriate handler
                    const handler = this.messageHandlers.get(message.type);
                    if (handler) {
                        handler(message.data, message);
                    } else {
                        log(`Unknown message type: ${message.type}`, 'warn');
                    }

                } catch (error) {
                    log(`Error parsing WebSocket message: ${error.message}`, 'error');
                    AppState.performanceMetrics.errorRate++;
                }
            }

            handleClose(event) {
                AppState.isConnected = false;
                this.stopHeartbeat();

                log(`WebSocket connection closed: ${event.code} - ${event.reason}`);
                updateConnectionStatus('warning', 'Reconnecting...');

                // Attempt reconnection with exponential backoff
                if (AppState.reconnectAttempts < CONFIG.WS_CONFIG.maxReconnectAttempts) {
                    const delay = CONFIG.WS_CONFIG.reconnectDelay * Math.pow(2, AppState.reconnectAttempts);
                    AppState.reconnectAttempts++;

                    log(`Reconnection attempt ${AppState.reconnectAttempts}/${CONFIG.WS_CONFIG.maxReconnectAttempts} in ${delay}ms`);

                    this.reconnectTimer = setTimeout(() => {
                        this.connect();
                    }, delay);
                } else {
                    log('Max reconnection attempts reached, switching to polling mode', 'error');
                    updateConnectionStatus('error', 'Connection Failed - Polling Mode');
                    this.startPollingFallback();
                }
            }

            handleError(error) {
                log(`WebSocket error: ${error}`, 'error');
                AppState.performanceMetrics.errorRate++;
            }

            subscribeToChannels() {
                const criticalChannels = [
                    'opportunities:active',
                    'trades:execution',
                    'prices:current',
                    'system:health',
                    'performance:metrics',
                    'queue:status',
                    'mev:protection',
                    'flash_loan:quotes',
                    'ml:events'
                ];

                criticalChannels.forEach(channel => {
                    this.subscribe(channel);
                });
            }

            subscribe(channel, filter = {}) {
                const subscription = {
                    type: 'subscribe',
                    data: { channel, filter },
                    timestamp: Date.now()
                };

                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify(subscription));
                    this.subscriptions.set(channel, filter);
                    log(`Subscribed to channel: ${channel}`);
                } else {
                    this.messageQueue.push(subscription);
                    log(`Queued subscription for channel: ${channel}`);
                }
            }

            processMessageQueue() {
                while (this.messageQueue.length > 0) {
                    const message = this.messageQueue.shift();
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify(message));
                    }
                }
            }

            startHeartbeat() {
                this.heartbeatTimer = setInterval(() => {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'ping',
                            timestamp: Date.now()
                        }));
                    }
                }, CONFIG.WS_CONFIG.heartbeatInterval);
            }

            stopHeartbeat() {
                if (this.heartbeatTimer) {
                    clearInterval(this.heartbeatTimer);
                    this.heartbeatTimer = null;
                }
            }

            startPollingFallback() {
                log('Starting polling fallback mode');
                // Implement polling for critical data
                setInterval(() => {
                    if (!AppState.isConnected) {
                        loadCriticalData();
                    }
                }, CONFIG.UPDATE_INTERVALS.critical);
            }

            // Message Handlers
            handleConnectionEstablished(data) {
                log(`Connected with client ID: ${data.clientId}`);
                updateLastUpdateTime();
            }

            handleOpportunityUpdate(data) {
                if (Array.isArray(data)) {
                    updateOpportunitiesDisplay(data);
                    updateMetric('active-opportunities', data.length);
                    log(`Updated ${data.length} opportunities via WebSocket`);
                }
            }

            handleTradeUpdate(data) {
                if (Array.isArray(data)) {
                    updateTradesDisplay(data);
                    log(`Updated ${data.length} trades via WebSocket`);
                }
            }

            handleQueueUpdate(data) {
                if (data) {
                    updateQueueDisplay(data);
                    updateMetric('queue-size', data.length || 0);
                    log('Updated execution queue via WebSocket');
                }
            }

            handleSystemHealth(data) {
                if (data) {
                    updateSystemHealth(data);
                    updateSystemHealthDetails(data);
                    log('Updated system health via WebSocket');
                }
            }

            handleMetricsUpdate(data) {
                if (data) {
                    updatePerformanceMetricsDisplay(data);
                    log('Updated performance metrics via WebSocket');
                }
            }

            handlePriceUpdate(data) {
                if (data) {
                    // Update price displays and charts
                    log(`Price update received for ${data.symbol || 'multiple tokens'}`);
                }
            }

            handleMEVUpdate(data) {
                if (data) {
                    updateMEVProtectionDisplay(data);
                    log('Updated MEV protection status via WebSocket');
                }
            }

            handleFlashLoanUpdate(data) {
                if (data) {
                    updateFlashLoanDisplay(data);
                    log('Updated flash loan quotes via WebSocket');
                }
            }

            handleMLUpdate(data) {
                if (data) {
                    updateMLLearningDisplay(data);
                    log('Updated ML learning events via WebSocket');
                }
            }

            disconnect() {
                if (this.reconnectTimer) {
                    clearTimeout(this.reconnectTimer);
                }
                this.stopHeartbeat();

                if (this.ws) {
                    this.ws.close();
                    this.ws = null;
                }

                AppState.isConnected = false;
                log('WebSocket disconnected');
            }
        }

        // Initialize WebSocket manager
        const wsManager = new EnhancedWebSocketManager();

        // ===== ENHANCED API CLIENT =====
        class EnhancedAPIClient {
            constructor() {
                this.baseURL = CONFIG.API_BASE;
                this.requestQueue = [];
                this.rateLimiters = new Map();
                this.circuitBreakers = new Map();
            }

            async request(endpoint, options = {}) {
                const startTime = Date.now();
                const url = `${this.baseURL}${endpoint}`;

                try {
                    // Check cache first
                    const cacheKey = this.getCacheKey(endpoint, options);
                    const cachedData = this.getFromCache(cacheKey);

                    if (cachedData && !options.skipCache) {
                        AppState.cacheMetrics.hits++;
                        log(`Cache hit for ${endpoint}`);
                        return cachedData;
                    }

                    AppState.cacheMetrics.misses++;

                    // Check circuit breaker
                    if (this.isCircuitOpen(endpoint)) {
                        throw new Error(`Circuit breaker open for ${endpoint}`);
                    }

                    // Make request with timeout
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), CONFIG.PERFORMANCE_TARGETS.API_RESPONSE_TARGET * 5);

                    const response = await fetch(url, {
                        ...options,
                        signal: controller.signal,
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers
                        }
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    const responseTime = Date.now() - startTime;

                    // Update performance metrics
                    AppState.performanceMetrics.latency = (AppState.performanceMetrics.latency + responseTime) / 2;

                    // Cache successful responses
                    if (data.success !== false) {
                        this.setCache(cacheKey, data, this.getCacheTTL(endpoint));
                    }

                    // Reset circuit breaker on success
                    this.resetCircuitBreaker(endpoint);

                    log(`API ${endpoint}: ${responseTime}ms`);
                    return data;

                } catch (error) {
                    const responseTime = Date.now() - startTime;
                    AppState.performanceMetrics.errorRate++;

                    // Update circuit breaker
                    this.recordFailure(endpoint);

                    log(`API error ${endpoint}: ${error.message} (${responseTime}ms)`, 'error');
                    throw error;
                }
            }

            getCacheKey(endpoint, options) {
                const params = options.params ? JSON.stringify(options.params) : '';
                return `api:${endpoint}:${params}`;
            }

            getFromCache(key) {
                const cached = AppState.dataCache.get(key);
                if (cached && cached.expires > Date.now()) {
                    return cached.data;
                }
                if (cached) {
                    AppState.dataCache.delete(key);
                }
                return null;
            }

            setCache(key, data, ttl) {
                if (AppState.dataCache.size >= CONFIG.CACHE_CONFIG.maxCacheSize) {
                    this.cleanupCache();
                }

                AppState.dataCache.set(key, {
                    data,
                    expires: Date.now() + ttl,
                    created: Date.now()
                });

                AppState.cacheMetrics.size = AppState.dataCache.size;
            }

            getCacheTTL(endpoint) {
                // Different TTL based on endpoint criticality
                if (endpoint.includes('opportunities') || endpoint.includes('trades')) {
                    return CONFIG.CACHE_CONFIG.criticalDataTTL;
                }
                return CONFIG.CACHE_CONFIG.defaultTTL;
            }

            cleanupCache() {
                const now = Date.now();
                for (const [key, value] of AppState.dataCache.entries()) {
                    if (value.expires <= now) {
                        AppState.dataCache.delete(key);
                    }
                }

                // If still too large, remove oldest entries
                if (AppState.dataCache.size >= CONFIG.CACHE_CONFIG.maxCacheSize) {
                    const entries = Array.from(AppState.dataCache.entries())
                        .sort((a, b) => a[1].created - b[1].created);

                    const toRemove = entries.slice(0, Math.floor(CONFIG.CACHE_CONFIG.maxCacheSize * 0.2));
                    toRemove.forEach(([key]) => AppState.dataCache.delete(key));
                }

                AppState.cacheMetrics.size = AppState.dataCache.size;
            }

            // Circuit breaker implementation
            isCircuitOpen(endpoint) {
                const breaker = this.circuitBreakers.get(endpoint);
                if (!breaker) return false;

                if (breaker.state === 'open') {
                    if (Date.now() - breaker.lastFailure > 60000) { // 1 minute timeout
                        breaker.state = 'half-open';
                        return false;
                    }
                    return true;
                }
                return false;
            }

            recordFailure(endpoint) {
                let breaker = this.circuitBreakers.get(endpoint);
                if (!breaker) {
                    breaker = { failures: 0, state: 'closed', lastFailure: 0 };
                    this.circuitBreakers.set(endpoint, breaker);
                }

                breaker.failures++;
                breaker.lastFailure = Date.now();

                if (breaker.failures >= 5) { // Open circuit after 5 failures
                    breaker.state = 'open';
                    log(`Circuit breaker opened for ${endpoint}`, 'warn');
                }
            }

            resetCircuitBreaker(endpoint) {
                const breaker = this.circuitBreakers.get(endpoint);
                if (breaker) {
                    breaker.failures = 0;
                    breaker.state = 'closed';
                }
            }

            // Specific API methods
            async getOpportunities(params = {}) {
                return this.request('/api/opportunities', { params });
            }

            async getTrades(params = {}) {
                return this.request('/api/trades', { params });
            }

            async getSystemHealth() {
                return this.request('/health');
            }

            async getAnalytics() {
                return this.request('/api/analytics/performance');
            }

            async getMLStats() {
                return this.request('/api/ml/learning-stats');
            }

            async getMLStrategies() {
                return this.request('/api/ml/strategy-performance');
            }

            async getMLEvents() {
                return this.request('/api/ml/learning-events');
            }

            async getNetworkStatus() {
                return this.request('/api/networks/status');
            }

            async getExecutionQueue() {
                return this.request('/api/system/execution-queue');
            }

            async getMEVProtectionStatus() {
                return this.request('/api/system/mev-protection');
            }

            async getFlashLoanQuotes() {
                return this.request('/api/system/flash-loan-quotes');
            }

            async getPerformanceMetrics() {
                return this.request('/api/analytics/performance-metrics');
            }

            async getDatabaseHealth() {
                return this.request('/api/system/database-health');
            }
        }

        // Initialize API client
        const apiClient = new EnhancedAPIClient();

        // ===== DISPLAY UPDATE FUNCTIONS =====
        function updateMetric(elementId, value, formatter = null) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = formatter ? formatter(value) : value;
            }
        }

        function updateOpportunitiesDisplay(opportunities) {
            const container = document.getElementById('opportunities-list');
            const titleEl = document.getElementById('opportunities-title');

            if (titleEl) {
                titleEl.textContent = `Live Opportunities (${opportunities.length})`;
            }

            if (!container) return;

            if (opportunities.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-400">
                        <i data-lucide="search" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                        <p>No opportunities detected</p>
                        <p class="text-xs mt-1">Scanning markets...</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            container.innerHTML = opportunities.map(opp => {
                const profitColor = (opp.potential_profit || opp.potentialProfit || 0) > 100 ? 'text-green-400' : 'text-green-300';
                const confidenceColor = (opp.confidence || 0) > 80 ? 'text-green-400' :
                                       (opp.confidence || 0) > 60 ? 'text-yellow-400' : 'text-red-400';

                return `
                    <div class="opportunity-item">
                        <div class="profit-indicator"></div>
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex items-center gap-2">
                                <span class="text-sm font-medium text-blue-400">${opp.type || 'Unknown'}</span>
                                <span class="network-badge network-${(opp.network || 'ethereum').toLowerCase()}">
                                    ${opp.network || 'ETH'}
                                </span>
                            </div>
                            <span class="text-xs text-gray-400">${getTimeAgo(opp.timestamp)}</span>
                        </div>
                        <div class="mb-2">
                            <p class="text-sm text-gray-300 mb-1">${(opp.assets || []).join(' → ')}</p>
                            <p class="text-xs text-gray-400">${(opp.exchanges || []).join(' & ')}</p>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-lg font-bold ${profitColor}">
                                    ${formatCurrency(opp.potential_profit || opp.potentialProfit || 0)}
                                </p>
                                <p class="text-xs text-gray-400">
                                    ${formatPercentage(opp.profit_percentage || opp.profitPercentage || 0)} margin
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm ${confidenceColor}">
                                    ${formatPercentage(opp.confidence || 0, 0)} confidence
                                </p>
                                <p class="text-xs text-gray-400">
                                    ${formatPercentage(opp.slippage || 0)} slippage
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            lucide.createIcons();
        }

        function updateTradesDisplay(trades) {
            const container = document.getElementById('trades-list');
            const titleEl = document.getElementById('trades-title');

            if (titleEl) {
                titleEl.textContent = `Recent Trades (${trades.length})`;
            }

            if (!container) return;

            if (trades.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-400">
                        <i data-lucide="trending-up" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                        <p>No trades executed</p>
                        <p class="text-xs mt-1">Waiting for opportunities...</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            container.innerHTML = trades.map(trade => {
                const statusColors = {
                    success: 'text-green-400',
                    completed: 'text-green-400',
                    failed: 'text-red-400',
                    pending: 'text-yellow-400',
                    executing: 'text-blue-400'
                };

                const statusColor = statusColors[trade.status] || 'text-gray-400';
                const profit = trade.executed_profit || trade.executedProfit || trade.actual_profit || 0;
                const gasFees = trade.gas_fees || trade.gasFees || 0;

                return `
                    <div class="opportunity-item">
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex items-center gap-2">
                                <span class="text-sm font-medium text-purple-400">${trade.type || 'Trade'}</span>
                                <span class="text-xs px-2 py-1 rounded ${statusColor} bg-opacity-20">
                                    ${(trade.status || 'unknown').toUpperCase()}
                                </span>
                            </div>
                            <span class="text-xs text-gray-400">${getTimeAgo(trade.timestamp)}</span>
                        </div>
                        <div class="mb-2">
                            <p class="text-sm text-gray-300">${(trade.assets || []).join(' → ')}</p>
                            ${trade.tx_hash || trade.txHash ?
                                `<p class="text-xs text-gray-400 font-mono">
                                    ${(trade.tx_hash || trade.txHash).substring(0, 10)}...
                                </p>` : ''
                            }
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-lg font-bold text-green-400">
                                    +${formatCurrency(profit)}
                                </p>
                                <p class="text-xs text-gray-400">
                                    Gas: ${formatCurrency(gasFees)}
                                </p>
                            </div>
                            <div class="text-right">
                                ${trade.execution_time ?
                                    `<p class="text-sm text-blue-400">${trade.execution_time.toFixed(1)}s</p>` : ''
                                }
                                ${trade.gas_used ?
                                    `<p class="text-xs text-gray-400">${formatNumber(trade.gas_used, 0)} gas</p>` : ''
                                }
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            lucide.createIcons();
        }

        function updateQueueDisplay(queueData) {
            const container = document.getElementById('queue-list');
            const titleEl = document.getElementById('queue-title');

            const queueItems = Array.isArray(queueData) ? queueData : queueData.items || [];

            if (titleEl) {
                titleEl.textContent = `Execution Queue (${queueItems.length})`;
            }

            if (!container) return;

            if (queueItems.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-400">
                        <i data-lucide="list-ordered" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                        <p>Queue empty</p>
                        <p class="text-xs mt-1">Ready for execution</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            container.innerHTML = queueItems.map((item, index) => {
                const priorityColors = {
                    high: 'text-red-400',
                    medium: 'text-yellow-400',
                    low: 'text-green-400'
                };

                const priorityColor = priorityColors[item.priority] || 'text-gray-400';

                return `
                    <div class="opportunity-item">
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex items-center gap-2">
                                <span class="text-sm font-bold text-purple-400">#${index + 1}</span>
                                <span class="text-xs px-2 py-1 rounded ${priorityColor} bg-opacity-20">
                                    ${(item.priority || 'medium').toUpperCase()}
                                </span>
                            </div>
                            <span class="text-xs text-gray-400">${getTimeAgo(item.queued_at || item.timestamp)}</span>
                        </div>
                        <div class="mb-2">
                            <p class="text-sm text-gray-300">${item.type || 'Arbitrage'}</p>
                            <p class="text-xs text-gray-400">${(item.assets || []).join(' → ')}</p>
                        </div>
                        <div class="flex justify-between items-center">
                            <p class="text-sm font-bold text-green-400">
                                ${formatCurrency(item.expected_profit || item.potential_profit || 0)}
                            </p>
                            <p class="text-xs text-gray-400">
                                Score: ${formatNumber(item.priority_score || 0, 1)}
                            </p>
                        </div>
                    </div>
                `;
            }).join('');

            lucide.createIcons();
        }

        function updateSystemHealthDetails(healthData) {
            const container = document.getElementById('system-health-details');
            if (!container || !healthData) return;

            const services = healthData.services || {};
            const databases = healthData.databases || {};

            container.innerHTML = `
                <div class="space-y-2">
                    ${Object.entries(services).map(([service, status]) => `
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-400">${service}</span>
                            <span class="text-xs px-2 py-1 rounded ${status ? 'text-green-400 bg-green-400' : 'text-red-400 bg-red-400'} bg-opacity-20">
                                ${status ? 'Healthy' : 'Down'}
                            </span>
                        </div>
                    `).join('')}
                    ${Object.entries(databases).map(([db, info]) => `
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-400">${db}</span>
                            <span class="text-xs text-gray-300">${info.latency || '--'}ms</span>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function updatePerformanceMetricsDisplay(metricsData) {
            const container = document.getElementById('performance-metrics');
            if (!container || !metricsData) return;

            container.innerHTML = `
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">Latency</span>
                        <span class="text-sm text-blue-400">${formatNumber(metricsData.latency || 0, 0)}ms</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">Throughput</span>
                        <span class="text-sm text-green-400">${formatNumber(metricsData.throughput || 0, 0)}/s</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">Cache Hit</span>
                        <span class="text-sm text-purple-400">${formatPercentage(metricsData.cacheHitRatio || 0)}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">Error Rate</span>
                        <span class="text-sm ${(metricsData.errorRate || 0) > 5 ? 'text-red-400' : 'text-green-400'}">
                            ${formatPercentage(metricsData.errorRate || 0)}
                        </span>
                    </div>
                </div>
            `;
        }

        function updateMEVProtectionDisplay(mevData) {
            updateMetric('mev-status', mevData.status || 'Unknown');
            updateMetric('mev-protected', mevData.protectedTransactions || 0);
            updateMetric('mev-success-rate', formatPercentage(mevData.successRate || 0));
        }

        function updateFlashLoanDisplay(flashLoanData) {
            updateMetric('flash-loan-available', flashLoanData.available ? 'Yes' : 'No');
            updateMetric('flash-loan-rate', formatPercentage(flashLoanData.bestRate || 0));
            updateMetric('flash-loan-provider', flashLoanData.bestProvider || 'N/A');
        }

        function updateMLLearningDisplay(mlData) {
            updateMetric('market-regime', mlData.currentRegime || 'Normal');
            updateMetric('active-strategies', mlData.activeStrategies || 0);
            updateMetric('adaptation-rate', `${formatNumber(mlData.adaptationRate || 0, 1)}/hr`);
        }

        function updateNetworkStatusDisplay(networkData) {
            const activeNetworks = networkData.networks ?
                networkData.networks.filter(n => n.status === 'active').length : 0;
            const totalNetworks = networkData.networks ? networkData.networks.length : 10;

            updateMetric('networks-active', `${activeNetworks}/${totalNetworks}`);
            updateMetric('network-latency', `${formatNumber(networkData.averageLatency || 0, 0)}ms`);
            updateMetric('gas-prices', networkData.gasPriceStatus || 'Unknown');
        }

        // ===== CHART MANAGEMENT =====
        let profitChart = null;
        let networkChart = null;

        function initializeCharts() {
            initializeProfitChart();
            initializeNetworkChart();
        }

        function initializeProfitChart() {
            const ctx = document.getElementById('profit-chart');
            if (!ctx) return;

            profitChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Profit',
                        data: [],
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            },
                            ticks: {
                                color: '#64748B'
                            }
                        },
                        y: {
                            display: true,
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            },
                            ticks: {
                                color: '#64748B',
                                callback: function(value) {
                                    return '$' + value.toFixed(0);
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 0,
                            hoverRadius: 6
                        }
                    }
                }
            });
        }

        function initializeNetworkChart() {
            const ctx = document.getElementById('network-chart');
            if (!ctx) return;

            networkChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Ethereum', 'BSC', 'Polygon', 'Arbitrum', 'Others'],
                    datasets: [{
                        data: [40, 25, 20, 10, 5],
                        backgroundColor: [
                            '#627EEA',
                            '#F3BA2F',
                            '#8247E5',
                            '#28A0F0',
                            '#64748B'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#CBD5E1',
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    }
                }
            });
        }

        function updateChart(timeframe) {
            AppState.currentChartTimeframe = timeframe;

            // Update button states
            document.querySelectorAll('[onclick*="updateChart"]').forEach(btn => {
                btn.className = btn.className.replace('bg-blue-600 text-white', 'bg-gray-600');
            });
            event.target.className = event.target.className.replace('bg-gray-600', 'bg-blue-600 text-white');

            // Load new chart data
            loadChartData(timeframe);
        }

        async function loadChartData(timeframe) {
            try {
                const response = await apiClient.request(`/api/analytics/historical?timeframe=${timeframe}`);
                if (response.success && profitChart) {
                    const data = response.data;
                    profitChart.data.labels = data.labels || [];
                    profitChart.data.datasets[0].data = data.profits || [];
                    profitChart.update();
                }
            } catch (error) {
                log(`Error loading chart data: ${error.message}`, 'error');
            }
        }

        // ===== MAIN DATA LOADING FUNCTIONS =====
        async function loadCriticalData() {
            try {
                log('Loading critical data...');

                const startTime = Date.now();

                // Load critical data in parallel
                const [
                    opportunitiesRes,
                    tradesRes,
                    queueRes,
                    healthRes
                ] = await Promise.allSettled([
                    apiClient.getOpportunities({ limit: 50 }),
                    apiClient.getTrades({ limit: 30 }),
                    apiClient.getExecutionQueue(),
                    apiClient.getSystemHealth()
                ]);

                // Process results
                if (opportunitiesRes.status === 'fulfilled' && opportunitiesRes.value.success) {
                    updateOpportunitiesDisplay(opportunitiesRes.value.data || []);
                }

                if (tradesRes.status === 'fulfilled' && tradesRes.value.success) {
                    updateTradesDisplay(tradesRes.value.data || []);
                }

                if (queueRes.status === 'fulfilled' && queueRes.value.success) {
                    updateQueueDisplay(queueRes.value.data || []);
                }

                if (healthRes.status === 'fulfilled' && healthRes.value.status) {
                    updateSystemHealth(healthRes.value);
                    updateSystemHealthDetails(healthRes.value);
                }

                const loadTime = Date.now() - startTime;
                log(`Critical data loaded in ${loadTime}ms`);

                return true;
            } catch (error) {
                log(`Error loading critical data: ${error.message}`, 'error');
                return false;
            }
        }

        async function loadAnalyticsData() {
            try {
                log('Loading analytics data...');

                const [
                    analyticsRes,
                    mlStatsRes,
                    mlStrategiesRes,
                    networkRes,
                    performanceRes
                ] = await Promise.allSettled([
                    apiClient.getAnalytics(),
                    apiClient.getMLStats(),
                    apiClient.getMLStrategies(),
                    apiClient.getNetworkStatus(),
                    apiClient.getPerformanceMetrics()
                ]);

                // Update analytics metrics
                if (analyticsRes.status === 'fulfilled' && analyticsRes.value.success) {
                    const data = analyticsRes.value.data;
                    updateMetric('total-profit', formatCurrency(data.netProfit || 0));
                    updateMetric('win-rate', formatPercentage(data.winRate || 0));
                    updateMetric('daily-volume', formatCurrency(data.dailyVolume || 0));

                    // Update trend indicators
                    updateMetric('profit-change', `${data.profitChange >= 0 ? '+' : ''}${formatPercentage(data.profitChange || 0)} (24h)`);
                    updateMetric('win-rate-trend', data.winRateTrend || 'Stable');
                    updateMetric('volume-change', `${data.volumeChange >= 0 ? '+' : ''}${formatPercentage(data.volumeChange || 0)} vs avg`);
                }

                // Update ML stats
                if (mlStatsRes.status === 'fulfilled' && mlStatsRes.value.success) {
                    const data = mlStatsRes.value.data;
                    updateMLLearningDisplay(data);
                }

                // Update ML strategies
                if (mlStrategiesRes.status === 'fulfilled' && mlStrategiesRes.value.success) {
                    updateMLStrategiesDisplay(mlStrategiesRes.value.data || []);
                }

                // Update network status
                if (networkRes.status === 'fulfilled' && networkRes.value.success) {
                    updateNetworkStatusDisplay(networkRes.value.data);
                }

                // Update performance metrics
                if (performanceRes.status === 'fulfilled' && performanceRes.value.success) {
                    updatePerformanceMetricsDisplay(performanceRes.value.data);
                }

                log('Analytics data loaded successfully');
                return true;
            } catch (error) {
                log(`Error loading analytics data: ${error.message}`, 'error');
                return false;
            }
        }

        function updateMLStrategiesDisplay(strategies) {
            const container = document.getElementById('strategy-list');
            const titleEl = document.getElementById('strategy-title');

            if (titleEl) {
                titleEl.textContent = `ML Strategies (${strategies.length})`;
            }

            if (!container) return;

            if (strategies.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-400">
                        <i data-lucide="brain" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                        <p>No strategy data</p>
                        <p class="text-xs mt-1">Learning in progress...</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            container.innerHTML = strategies.map(strategy => {
                const successRate = strategy.success_rate || strategy.successRate || 0;
                const weight = strategy.current_weight || strategy.currentWeight || 1.0;
                const executions = strategy.total_executions || strategy.totalExecutions || 0;

                const performanceClass = successRate > 80 ? 'performance-excellent' :
                                       successRate > 60 ? 'performance-good' :
                                       successRate > 40 ? 'performance-warning' : 'performance-poor';

                return `
                    <div class="opportunity-item">
                        <div class="flex justify-between items-start mb-2">
                            <span class="text-sm font-medium text-purple-400">
                                ${strategy.strategy_type || strategy.strategyType || 'Unknown'}
                            </span>
                            <span class="network-badge network-${(strategy.network || 'ethereum').toLowerCase()}">
                                ${strategy.network || 'ETH'}
                            </span>
                        </div>
                        <div class="space-y-1 mb-2">
                            <div class="flex justify-between">
                                <span class="text-xs text-gray-400">Success Rate</span>
                                <span class="text-xs ${performanceClass.includes('excellent') ? 'text-green-400' :
                                                      performanceClass.includes('good') ? 'text-blue-400' :
                                                      performanceClass.includes('warning') ? 'text-yellow-400' : 'text-red-400'}">
                                    ${formatPercentage(successRate)}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-xs text-gray-400">Weight</span>
                                <span class="text-xs text-gray-300">${formatNumber(weight, 3)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-xs text-gray-400">Executions</span>
                                <span class="text-xs text-gray-300">${formatNumber(executions, 0)}</span>
                            </div>
                        </div>
                        <div class="performance-indicator ${performanceClass} text-xs">
                            <i data-lucide="trending-up" class="w-3 h-3"></i>
                            <span>${performanceClass.split('-')[1].charAt(0).toUpperCase() + performanceClass.split('-')[1].slice(1)}</span>
                        </div>
                    </div>
                `;
            }).join('');

            lucide.createIcons();
        }

        async function loadAllData() {
            try {
                updateConnectionStatus('info', 'Loading data...');
                updateLastUpdateTime();

                // Load critical data first
                const criticalSuccess = await loadCriticalData();

                // Load analytics data
                const analyticsSuccess = await loadAnalyticsData();

                // Update cache metrics
                AppState.cacheMetrics.cacheHitRatio = AppState.cacheMetrics.hits /
                    (AppState.cacheMetrics.hits + AppState.cacheMetrics.misses) * 100;

                // Update connection status
                if (criticalSuccess && analyticsSuccess) {
                    updateConnectionStatus('success',
                        AppState.isConnected ? 'Real-time Connected' : 'Data Loaded');
                } else {
                    updateConnectionStatus('warning', 'Partial data loaded');
                }

                log('All data loading completed');
                return true;

            } catch (error) {
                log(`Error in loadAllData: ${error.message}`, 'error');
                updateConnectionStatus('error', 'Data loading failed');
                return false;
            }
        }

        // ===== INITIALIZATION AND STARTUP =====
        async function initializeApplication() {
            try {
                log('Initializing Enhanced MEV Arbitrage Dashboard...');

                // Initialize charts
                initializeCharts();

                // Start performance monitoring
                startPerformanceMonitoring();

                // Initialize WebSocket connection
                await wsManager.connect();

                // Load initial data
                await loadAllData();

                // Start update intervals
                startUpdateIntervals();

                // Start cache cleanup
                startCacheCleanup();

                log('Dashboard initialization completed successfully');
                return true;

            } catch (error) {
                log(`Initialization error: ${error.message}`, 'error');
                updateConnectionStatus('error', 'Initialization failed');
                return false;
            }
        }

        function startPerformanceMonitoring() {
            setInterval(() => {
                // Update performance metrics
                AppState.performanceMetrics.uptime = Date.now() - AppState.performanceMetrics.uptime;
                AppState.performanceMetrics.lastUpdate = Date.now();

                // Memory usage estimation
                AppState.performanceMetrics.memoryUsage = AppState.dataCache.size * 1024; // Rough estimate

                // Check performance targets
                const latencyOK = AppState.performanceMetrics.latency < CONFIG.PERFORMANCE_TARGETS.LATENCY_TARGET;
                const errorRateOK = AppState.performanceMetrics.errorRate < 5; // 5% threshold
                const memoryOK = AppState.performanceMetrics.memoryUsage < CONFIG.PERFORMANCE_TARGETS.MEMORY_TARGET;

                if (!latencyOK || !errorRateOK || !memoryOK) {
                    log(`Performance warning - Latency: ${AppState.performanceMetrics.latency}ms, ` +
                        `Errors: ${AppState.performanceMetrics.errorRate}%, ` +
                        `Memory: ${(AppState.performanceMetrics.memoryUsage / 1024 / 1024).toFixed(1)}MB`, 'warn');
                }

            }, 30000); // Every 30 seconds
        }

        function startUpdateIntervals() {
            // Critical data updates (opportunities, trades, queue)
            setInterval(async () => {
                if (!AppState.isConnected) {
                    await loadCriticalData();
                }
            }, CONFIG.UPDATE_INTERVALS.critical);

            // Important data updates (system health, performance)
            setInterval(async () => {
                try {
                    const [healthRes, performanceRes] = await Promise.allSettled([
                        apiClient.getSystemHealth(),
                        apiClient.getPerformanceMetrics()
                    ]);

                    if (healthRes.status === 'fulfilled' && healthRes.value.status) {
                        updateSystemHealth(healthRes.value);
                        updateSystemHealthDetails(healthRes.value);
                    }

                    if (performanceRes.status === 'fulfilled' && performanceRes.value.success) {
                        updatePerformanceMetricsDisplay(performanceRes.value.data);
                    }
                } catch (error) {
                    log(`Error updating important data: ${error.message}`, 'error');
                }
            }, CONFIG.UPDATE_INTERVALS.important);

            // Standard data updates (analytics, historical)
            setInterval(async () => {
                try {
                    const analyticsRes = await apiClient.getAnalytics();
                    if (analyticsRes.success) {
                        const data = analyticsRes.data;
                        updateMetric('total-profit', formatCurrency(data.netProfit || 0));
                        updateMetric('win-rate', formatPercentage(data.winRate || 0));
                        updateMetric('daily-volume', formatCurrency(data.dailyVolume || 0));
                    }
                } catch (error) {
                    log(`Error updating analytics: ${error.message}`, 'error');
                }
            }, CONFIG.UPDATE_INTERVALS.standard);

            // Background data updates (ML stats, network status)
            setInterval(async () => {
                try {
                    const [mlRes, networkRes] = await Promise.allSettled([
                        apiClient.getMLStats(),
                        apiClient.getNetworkStatus()
                    ]);

                    if (mlRes.status === 'fulfilled' && mlRes.value.success) {
                        updateMLLearningDisplay(mlRes.value.data);
                    }

                    if (networkRes.status === 'fulfilled' && networkRes.value.success) {
                        updateNetworkStatusDisplay(networkRes.value.data);
                    }
                } catch (error) {
                    log(`Error updating background data: ${error.message}`, 'error');
                }
            }, CONFIG.UPDATE_INTERVALS.background);
        }

        function startCacheCleanup() {
            setInterval(() => {
                apiClient.cleanupCache();

                // Log cache statistics
                const hitRatio = AppState.cacheMetrics.hits /
                    (AppState.cacheMetrics.hits + AppState.cacheMetrics.misses) * 100;

                log(`Cache stats - Size: ${AppState.cacheMetrics.size}, ` +
                    `Hit ratio: ${hitRatio.toFixed(1)}%, ` +
                    `Hits: ${AppState.cacheMetrics.hits}, ` +
                    `Misses: ${AppState.cacheMetrics.misses}`);

            }, CONFIG.CACHE_CONFIG.cleanupInterval);
        }

        // ===== EVENT HANDLERS =====
        window.addEventListener('beforeunload', () => {
            wsManager.disconnect();
        });

        window.addEventListener('online', () => {
            log('Network connection restored');
            if (!AppState.isConnected) {
                wsManager.connect();
            }
        });

        window.addEventListener('offline', () => {
            log('Network connection lost', 'warn');
            updateConnectionStatus('error', 'Offline - Cached data only');
        });

        // ===== APPLICATION STARTUP =====
        document.addEventListener('DOMContentLoaded', async () => {
            log('DOM loaded, starting application...');

            // Show loading state
            updateConnectionStatus('info', 'Initializing...');

            // Initialize application
            const success = await initializeApplication();

            if (!success) {
                updateConnectionStatus('error', 'Initialization failed - Check console');
                log('Application failed to initialize properly', 'error');
            }
        });

        // ===== UTILITY FUNCTIONS FOR BACKWARDS COMPATIBILITY =====
        // These functions maintain compatibility with any existing code
        function renderOpportunities(opportunities) {
            updateOpportunitiesDisplay(opportunities);
        }

        function renderTrades(trades) {
            updateTradesDisplay(trades);
        }

        function renderStrategyPerformance(strategies) {
            updateMLStrategiesDisplay(strategies);
        }

        function renderLearningEvents(events) {
            // This would be handled by ML learning display updates
            log(`Learning events update: ${events.length} events`);
        }

        // ===== GLOBAL FUNCTIONS FOR EXTERNAL ACCESS =====
        window.MEVDashboard = {
            // Expose key functions for external access or debugging
            loadData: loadAllData,
            updateChart: updateChart,
            clearDebugLog: clearDebugLog,
            getAppState: () => AppState,
            getConfig: () => CONFIG,

            // Performance monitoring
            getPerformanceMetrics: () => AppState.performanceMetrics,
            getCacheStats: () => AppState.cacheMetrics,

            // Connection management
            reconnectWebSocket: () => wsManager.connect(),
            disconnectWebSocket: () => wsManager.disconnect(),

            // Data refresh
            refreshCriticalData: loadCriticalData,
            refreshAnalytics: loadAnalyticsData
        };

        log('Enhanced MEV Arbitrage Dashboard loaded successfully');
    </script>
</body>
</html>
