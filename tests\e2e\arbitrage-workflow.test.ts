/**
 * End-to-End Arbitrage Workflow Testing
 * 
 * Comprehensive testing of complete arbitrage workflows:
 * - Opportunity detection to execution pipeline
 * - Cross-chain arbitrage workflows
 * - Performance validation against all targets
 * - Real-time data flow validation
 * - Error handling and recovery testing
 */

import { jest, describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { performance } from 'perf_hooks';
import WebSocket from 'ws';

// Import all services for E2E testing
import { enhancedTokenMonitoringService } from '../../backend/services/EnhancedTokenMonitoringService.js';
import { profitValidationService } from '../../backend/services/ProfitValidationService.js';
import { profitPrioritizedExecutionQueue } from '../../backend/services/ProfitPrioritizedExecutionQueue.js';
import { preExecutionValidationService } from '../../backend/services/PreExecutionValidationService.js';
import { flashLoanService } from '../../backend/services/FlashLoanService.js';
import { mevProtectionService } from '../../backend/services/MEVProtectionService.js';
import { enhancedWebSocketService } from '../../backend/services/EnhancedWebSocketService.js';
import { performanceValidationService } from '../../backend/services/PerformanceValidationService.js';

interface WorkflowStep {
  name: string;
  startTime: number;
  endTime: number;
  duration: number;
  success: boolean;
  data?: any;
  error?: string;
}

interface WorkflowResult {
  workflowId: string;
  type: 'intra-chain' | 'cross-chain';
  totalDuration: number;
  steps: WorkflowStep[];
  success: boolean;
  performanceTargetsMet: boolean;
  errors: string[];
}

class ArbitrageWorkflowTester {
  private workflowResults: WorkflowResult[] = [];
  private wsClient: WebSocket | null = null;
  private receivedNotifications: any[] = [];

  async setupE2EEnvironment(): Promise<void> {
    // Initialize all services
    await enhancedTokenMonitoringService.initialize();
    await profitPrioritizedExecutionQueue.initialize();
    await flashLoanService.initialize();
    await mevProtectionService.initialize();
    await preExecutionValidationService.initialize();
    await performanceValidationService.initialize();

    // Setup WebSocket client for real-time monitoring
    this.wsClient = new WebSocket('ws://localhost:8080/ws');
    
    return new Promise((resolve, reject) => {
      if (!this.wsClient) {
        reject(new Error('Failed to create WebSocket client'));
        return;
      }

      this.wsClient.on('open', () => {
        // Subscribe to all relevant channels
        const subscriptions = [
          'opportunities:active',
          'trades:execution',
          'prices:current',
          'system:health',
          'performance:metrics'
        ];

        subscriptions.forEach(channel => {
          this.wsClient!.send(JSON.stringify({
            type: 'subscribe',
            data: { channel }
          }));
        });

        resolve();
      });

      this.wsClient.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.receivedNotifications.push(message);
        } catch (error) {
          // Ignore parsing errors
        }
      });

      this.wsClient.on('error', reject);
    });
  }

  async teardownE2EEnvironment(): Promise<void> {
    if (this.wsClient) {
      this.wsClient.close();
    }

    // Shutdown services
    await enhancedTokenMonitoringService.shutdown();
    await profitPrioritizedExecutionQueue.shutdown();
    await flashLoanService.shutdown();
    await mevProtectionService.shutdown();
    await preExecutionValidationService.shutdown();
    await performanceValidationService.shutdown();
  }

  async executeIntraChainWorkflow(opportunityData: any): Promise<WorkflowResult> {
    const workflowId = `intra-chain-${Date.now()}`;
    const workflowStartTime = performance.now();
    const steps: WorkflowStep[] = [];
    const errors: string[] = [];

    console.log(`🔄 Executing intra-chain workflow: ${workflowId}`);

    try {
      // Step 1: Price Update and Opportunity Detection
      const step1 = await this.executeStep('Price Update & Detection', async () => {
        const priceUpdate = {
          symbol: opportunityData.assets[0],
          price: 2000 + Math.random() * 100,
          volume24h: 1000000,
          change24h: Math.random() * 10 - 5,
          timestamp: Date.now(),
          network: opportunityData.network
        };

        await enhancedTokenMonitoringService.processPriceUpdate(priceUpdate);
        return priceUpdate;
      });
      steps.push(step1);

      // Step 2: Profit Validation
      const step2 = await this.executeStep('Profit Validation', async () => {
        const validationResult = await profitValidationService.validateProfit(opportunityData);
        if (!validationResult.isValid) {
          throw new Error(`Profit validation failed: ${validationResult.rejectionReason}`);
        }
        return validationResult;
      });
      steps.push(step2);

      // Step 3: Pre-Execution Validation
      const step3 = await this.executeStep('Pre-Execution Validation', async () => {
        const preValidationResult = await preExecutionValidationService.validateExecution(opportunityData);
        if (!preValidationResult.isValid) {
          throw new Error(`Pre-execution validation failed: ${preValidationResult.rejectionReason}`);
        }
        return preValidationResult;
      });
      steps.push(step3);

      // Step 4: Flash Loan Optimization
      const step4 = await this.executeStep('Flash Loan Optimization', async () => {
        const flashLoanResult = await flashLoanService.optimizeExecution(opportunityData);
        return flashLoanResult;
      });
      steps.push(step4);

      // Step 5: MEV Protection
      const step5 = await this.executeStep('MEV Protection', async () => {
        const mevResult = await mevProtectionService.protectTransaction(opportunityData);
        if (!mevResult.isProtected) {
          throw new Error('MEV protection failed');
        }
        return mevResult;
      });
      steps.push(step5);

      // Step 6: Queue for Execution
      const step6 = await this.executeStep('Queue for Execution', async () => {
        const queueResult = await profitPrioritizedExecutionQueue.addOpportunity(opportunityData);
        if (!queueResult) {
          throw new Error('Failed to queue opportunity');
        }
        return { queued: true };
      });
      steps.push(step6);

      // Step 7: Validate Real-time Notifications
      const step7 = await this.executeStep('Real-time Notifications', async () => {
        await this.waitForNotifications(['opportunity:detected', 'validation:complete'], 10000);
        return { notificationsReceived: this.receivedNotifications.length };
      });
      steps.push(step7);

    } catch (error) {
      errors.push(error.message);
    }

    const totalDuration = performance.now() - workflowStartTime;
    const success = steps.every(step => step.success) && errors.length === 0;
    const performanceTargetsMet = await this.validatePerformanceTargets(steps);

    const result: WorkflowResult = {
      workflowId,
      type: 'intra-chain',
      totalDuration,
      steps,
      success,
      performanceTargetsMet,
      errors
    };

    this.workflowResults.push(result);
    return result;
  }

  async executeCrossChainWorkflow(opportunityData: any): Promise<WorkflowResult> {
    const workflowId = `cross-chain-${Date.now()}`;
    const workflowStartTime = performance.now();
    const steps: WorkflowStep[] = [];
    const errors: string[] = [];

    console.log(`🌉 Executing cross-chain workflow: ${workflowId}`);

    try {
      // Step 1: Multi-Network Price Detection
      const step1 = await this.executeStep('Multi-Network Price Detection', async () => {
        const sourcePrice = {
          symbol: opportunityData.assets[0],
          price: 2000,
          network: opportunityData.sourceNetwork,
          timestamp: Date.now()
        };
        
        const targetPrice = {
          symbol: opportunityData.assets[1],
          price: 2050, // Price difference for arbitrage
          network: opportunityData.targetNetwork,
          timestamp: Date.now()
        };

        await enhancedTokenMonitoringService.processPriceUpdate(sourcePrice);
        await enhancedTokenMonitoringService.processPriceUpdate(targetPrice);
        
        return { sourcePrice, targetPrice };
      });
      steps.push(step1);

      // Step 2: Cross-Chain Profit Validation
      const step2 = await this.executeStep('Cross-Chain Profit Validation', async () => {
        const validationResult = await profitValidationService.validateCrossChainProfit(opportunityData);
        if (!validationResult.isValid) {
          throw new Error(`Cross-chain validation failed: ${validationResult.rejectionReason}`);
        }
        return validationResult;
      });
      steps.push(step2);

      // Step 3: Bridge Cost Analysis
      const step3 = await this.executeStep('Bridge Cost Analysis', async () => {
        // Simulate bridge cost analysis
        const bridgeCosts = {
          fees: opportunityData.bridgeFees || 50,
          time: opportunityData.bridgeTime || 600000,
          slippage: 0.5
        };
        return bridgeCosts;
      });
      steps.push(step3);

      // Step 4: Cross-Chain MEV Protection
      const step4 = await this.executeStep('Cross-Chain MEV Protection', async () => {
        const mevResult = await mevProtectionService.protectCrossChainTransaction(opportunityData);
        if (!mevResult.isProtected) {
          throw new Error('Cross-chain MEV protection failed');
        }
        return mevResult;
      });
      steps.push(step4);

      // Step 5: Cross-Chain Execution Validation
      const step5 = await this.executeStep('Cross-Chain Execution Validation', async () => {
        const preValidationResult = await preExecutionValidationService.validateCrossChainExecution(opportunityData);
        if (!preValidationResult.isValid) {
          throw new Error(`Cross-chain execution validation failed: ${preValidationResult.rejectionReason}`);
        }
        return preValidationResult;
      });
      steps.push(step5);

      // Step 6: Queue Cross-Chain Opportunity
      const step6 = await this.executeStep('Queue Cross-Chain Opportunity', async () => {
        const queueResult = await profitPrioritizedExecutionQueue.addOpportunity(opportunityData);
        if (!queueResult) {
          throw new Error('Failed to queue cross-chain opportunity');
        }
        return { queued: true };
      });
      steps.push(step6);

    } catch (error) {
      errors.push(error.message);
    }

    const totalDuration = performance.now() - workflowStartTime;
    const success = steps.every(step => step.success) && errors.length === 0;
    const performanceTargetsMet = await this.validatePerformanceTargets(steps);

    const result: WorkflowResult = {
      workflowId,
      type: 'cross-chain',
      totalDuration,
      steps,
      success,
      performanceTargetsMet,
      errors
    };

    this.workflowResults.push(result);
    return result;
  }

  private async executeStep(stepName: string, stepFunction: () => Promise<any>): Promise<WorkflowStep> {
    const startTime = performance.now();
    let success = false;
    let data: any = null;
    let error: string | undefined;

    try {
      data = await stepFunction();
      success = true;
    } catch (err) {
      error = err.message;
      success = false;
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    return {
      name: stepName,
      startTime,
      endTime,
      duration,
      success,
      data,
      error
    };
  }

  private async validatePerformanceTargets(steps: WorkflowStep[]): Promise<boolean> {
    const targets = [
      { name: 'Price Update & Detection', maxDuration: 5000 },
      { name: 'Profit Validation', maxDuration: 3000 },
      { name: 'Pre-Execution Validation', maxDuration: 2000 },
      { name: 'Flash Loan Optimization', maxDuration: 1000 },
      { name: 'MEV Protection', maxDuration: 1000 },
      { name: 'Queue for Execution', maxDuration: 1000 }
    ];

    for (const target of targets) {
      const step = steps.find(s => s.name === target.name);
      if (step && step.duration > target.maxDuration) {
        console.warn(`⚠️  Performance target missed: ${step.name} took ${step.duration.toFixed(2)}ms (target: ${target.maxDuration}ms)`);
        return false;
      }
    }

    return true;
  }

  private async waitForNotifications(expectedTypes: string[], timeoutMs: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const checkInterval = setInterval(() => {
        const receivedTypes = this.receivedNotifications.map(n => n.type);
        const hasAllTypes = expectedTypes.every(type => receivedTypes.includes(type));

        if (hasAllTypes) {
          clearInterval(checkInterval);
          resolve();
        } else if (Date.now() - startTime > timeoutMs) {
          clearInterval(checkInterval);
          reject(new Error(`Timeout waiting for notifications: ${expectedTypes.join(', ')}`));
        }
      }, 100);
    });
  }

  getWorkflowResults(): WorkflowResult[] {
    return [...this.workflowResults];
  }

  generateWorkflowReport(): any {
    const totalWorkflows = this.workflowResults.length;
    const successfulWorkflows = this.workflowResults.filter(w => w.success).length;
    const performanceCompliantWorkflows = this.workflowResults.filter(w => w.performanceTargetsMet).length;

    const averageDuration = totalWorkflows > 0
      ? this.workflowResults.reduce((sum, w) => sum + w.totalDuration, 0) / totalWorkflows
      : 0;

    return {
      summary: {
        totalWorkflows,
        successfulWorkflows,
        performanceCompliantWorkflows,
        successRate: (successfulWorkflows / totalWorkflows) * 100,
        performanceComplianceRate: (performanceCompliantWorkflows / totalWorkflows) * 100,
        averageDuration
      },
      workflows: this.workflowResults,
      timestamp: new Date().toISOString()
    };
  }
}

describe('End-to-End Arbitrage Workflow Testing', () => {
  let workflowTester: ArbitrageWorkflowTester;

  beforeAll(async () => {
    workflowTester = new ArbitrageWorkflowTester();
    await workflowTester.setupE2EEnvironment();
  }, 60000);

  afterAll(async () => {
    await workflowTester.teardownE2EEnvironment();
  }, 30000);

  beforeEach(() => {
    // Clear received notifications before each test
    workflowTester['receivedNotifications'] = [];
  });

  describe('Intra-Chain Arbitrage Workflows', () => {
    it('should execute complete intra-chain arbitrage workflow within performance targets', async () => {
      const opportunityData = {
        id: 'e2e-intra-1',
        type: 'intra-chain' as const,
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 150,
        profitPercentage: 3.0,
        timestamp: Date.now(),
        network: 'ethereum',
        route: {
          path: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          amounts: [1, 2000]
        },
        estimatedGas: 150000,
        slippage: 0.5,
        confidence: 90
      };

      const result = await workflowTester.executeIntraChainWorkflow(opportunityData);

      expect(result.success).toBe(true);
      expect(result.performanceTargetsMet).toBe(true);
      expect(result.totalDuration).toBeLessThan(30000); // 30 second total workflow target
      expect(result.errors).toHaveLength(0);
      expect(result.steps).toHaveLength(7);

      // Validate individual step performance
      const profitValidationStep = result.steps.find(s => s.name === 'Profit Validation');
      expect(profitValidationStep?.duration).toBeLessThan(3000);

      const queueStep = result.steps.find(s => s.name === 'Queue for Execution');
      expect(queueStep?.duration).toBeLessThan(1000);

    }, 45000);

    it('should handle multiple concurrent intra-chain workflows', async () => {
      const workflows = [];
      
      for (let i = 0; i < 3; i++) {
        const opportunityData = {
          id: `e2e-concurrent-${i}`,
          type: 'intra-chain' as const,
          assets: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          potentialProfit: 100 + i * 25,
          profitPercentage: 2.0 + i * 0.5,
          timestamp: Date.now(),
          network: 'ethereum',
          route: {
            path: ['ETH', 'USDC'],
            exchanges: ['Uniswap', 'SushiSwap'],
            amounts: [1, 2000]
          },
          estimatedGas: 150000,
          slippage: 0.5,
          confidence: 85 + i * 2
        };

        workflows.push(workflowTester.executeIntraChainWorkflow(opportunityData));
      }

      const results = await Promise.allSettled(workflows);
      const successfulResults = results.filter(r => r.status === 'fulfilled');

      expect(successfulResults.length).toBeGreaterThanOrEqual(2); // At least 2/3 should succeed
      
    }, 60000);
  });

  describe('Cross-Chain Arbitrage Workflows', () => {
    it('should execute complete cross-chain arbitrage workflow', async () => {
      const crossChainOpportunity = {
        id: 'e2e-cross-1',
        type: 'cross-chain' as const,
        assets: ['ETH', 'WETH'],
        exchanges: ['Uniswap', 'PancakeSwap'],
        potentialProfit: 200,
        profitPercentage: 3.5,
        timestamp: Date.now(),
        sourceNetwork: 'ethereum',
        targetNetwork: 'bsc',
        route: {
          path: ['ETH', 'WETH'],
          exchanges: ['Uniswap', 'PancakeSwap'],
          amounts: [1, 1]
        },
        estimatedGas: 300000,
        slippage: 1.0,
        confidence: 80,
        bridgeFees: 50,
        bridgeTime: 600000
      };

      const result = await workflowTester.executeCrossChainWorkflow(crossChainOpportunity);

      expect(result.success).toBe(true);
      expect(result.performanceTargetsMet).toBe(true);
      expect(result.totalDuration).toBeLessThan(45000); // 45 second target for cross-chain
      expect(result.errors).toHaveLength(0);
      expect(result.steps).toHaveLength(6);

    }, 60000);
  });

  describe('Error Handling and Recovery', () => {
    it('should handle profit validation failures gracefully', async () => {
      const unprofitableOpportunity = {
        id: 'e2e-unprofitable',
        type: 'intra-chain' as const,
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 5, // Very low profit
        profitPercentage: 0.1,
        timestamp: Date.now(),
        network: 'ethereum',
        route: {
          path: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          amounts: [1, 2000]
        },
        estimatedGas: 500000, // High gas cost
        slippage: 0.5,
        confidence: 85
      };

      const result = await workflowTester.executeIntraChainWorkflow(unprofitableOpportunity);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('Profit validation failed');

    }, 30000);
  });

  describe('Performance Validation', () => {
    it('should generate comprehensive workflow performance report', async () => {
      // Execute a few workflows to generate data
      const opportunities = [
        {
          id: 'perf-test-1',
          type: 'intra-chain' as const,
          assets: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          potentialProfit: 120,
          profitPercentage: 2.5,
          timestamp: Date.now(),
          network: 'ethereum',
          route: { path: ['ETH', 'USDC'], exchanges: ['Uniswap', 'SushiSwap'], amounts: [1, 2000] },
          estimatedGas: 150000,
          slippage: 0.5,
          confidence: 88
        }
      ];

      for (const opportunity of opportunities) {
        await workflowTester.executeIntraChainWorkflow(opportunity);
      }

      const report = workflowTester.generateWorkflowReport();

      expect(report.summary.totalWorkflows).toBeGreaterThan(0);
      expect(report.summary.successRate).toBeGreaterThanOrEqual(0);
      expect(report.summary.performanceComplianceRate).toBeGreaterThanOrEqual(0);
      expect(report.summary.averageDuration).toBeGreaterThan(0);
      expect(report.workflows).toHaveLength(report.summary.totalWorkflows);

    }, 45000);
  });
});
