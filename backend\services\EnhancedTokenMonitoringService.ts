import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import logger from '../utils/logger.js';
import config from '../config/index.js';

export interface TokenInfo {
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  marketCap: number;
  volume24h: number;
  rank: number;
  isVerified: boolean;
  networks: string[];
}

export interface TokenPrice {
  symbol: string;
  network: string;
  price: number;
  timestamp: number;
  source: string;
  volume: number;
  liquidity: number;
  priceChange24h: number;
}

export interface LiquidityInfo {
  symbol: string;
  network: string;
  exchange: string;
  pair: string;
  liquidity: number;
  volume24h: number;
  fee: number;
  timestamp: number;
}

export interface NetworkConfig {
  name: string;
  rpcUrl: string;
  blockTime: number; // in seconds
  monitoringInterval: number; // in milliseconds
  gasThreshold: number; // Gwei
  majorDexes: string[];
  nativeToken: string;
  isHighVolume: boolean;
}

export interface MonitoringStats {
  totalTokens: number;
  activeNetworks: number;
  priceUpdatesPerSecond: number;
  averageLatency: number;
  lastUpdateTime: number;
  networkStats: Map<string, NetworkStats>;
}

export interface NetworkStats {
  network: string;
  tokensMonitored: number;
  priceUpdates: number;
  averageLatency: number;
  lastUpdate: number;
  isHealthy: boolean;
}

export class EnhancedTokenMonitoringService extends EventEmitter {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private networkConfigs: Map<string, NetworkConfig> = new Map();
  private monitoredTokens: Map<string, TokenInfo> = new Map();
  private tokenPrices: Map<string, Map<string, TokenPrice>> = new Map(); // symbol -> network -> price
  private liquidityData: Map<string, Map<string, LiquidityInfo[]>> = new Map(); // symbol -> network -> liquidity[]
  private monitoringIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isRunning = false;
  
  // Monitoring parameters
  private readonly maxPriceUpdateLatency = 5000; // 5 seconds
  private readonly priceDataSources = ['coingecko', 'coinmarketcap', 'dex_aggregator'];
  private readonly top50Tokens = this.getTop50VerifiedTokens();
  
  // Performance tracking
  private monitoringStats: MonitoringStats = {
    totalTokens: 0,
    activeNetworks: 0,
    priceUpdatesPerSecond: 0,
    averageLatency: 0,
    lastUpdateTime: 0,
    networkStats: new Map()
  };
  private performanceMetrics: Map<string, number[]> = new Map();

  constructor() {
    super();
    this.initializeNetworkConfigs();
    this.initializeProviders();
    this.initializeMonitoringStats();
  }

  private initializeNetworkConfigs() {
    // High-volume networks with faster monitoring
    this.networkConfigs.set('ethereum', {
      name: 'Ethereum',
      rpcUrl: config.ETHEREUM_RPC_URL,
      blockTime: 12,
      monitoringInterval: 2000, // 2 seconds
      gasThreshold: 50,
      majorDexes: ['Uniswap V3', 'Uniswap V2', 'SushiSwap', 'Balancer', 'Curve'],
      nativeToken: 'ETH',
      isHighVolume: true
    });

    this.networkConfigs.set('bsc', {
      name: 'Binance Smart Chain',
      rpcUrl: config.BSC_RPC_URL,
      blockTime: 3,
      monitoringInterval: 1500, // 1.5 seconds
      gasThreshold: 10,
      majorDexes: ['PancakeSwap V3', 'PancakeSwap V2', 'Biswap', 'ApeSwap'],
      nativeToken: 'BNB',
      isHighVolume: true
    });

    this.networkConfigs.set('polygon', {
      name: 'Polygon',
      rpcUrl: config.POLYGON_RPC_URL,
      blockTime: 2,
      monitoringInterval: 1000, // 1 second
      gasThreshold: 50,
      majorDexes: ['QuickSwap', 'SushiSwap', 'Uniswap V3', 'Balancer'],
      nativeToken: 'MATIC',
      isHighVolume: true
    });

    // Medium-volume networks
    this.networkConfigs.set('arbitrum', {
      name: 'Arbitrum',
      rpcUrl: '', // Would be configured in production
      blockTime: 1,
      monitoringInterval: 3000, // 3 seconds
      gasThreshold: 5,
      majorDexes: ['Uniswap V3', 'SushiSwap', 'Balancer', 'Curve'],
      nativeToken: 'ETH',
      isHighVolume: false
    });

    this.networkConfigs.set('optimism', {
      name: 'Optimism',
      rpcUrl: '', // Would be configured in production
      blockTime: 2,
      monitoringInterval: 3000,
      gasThreshold: 5,
      majorDexes: ['Uniswap V3', 'Velodrome', 'SushiSwap'],
      nativeToken: 'ETH',
      isHighVolume: false
    });

    this.networkConfigs.set('avalanche', {
      name: 'Avalanche',
      rpcUrl: '', // Would be configured in production
      blockTime: 2,
      monitoringInterval: 4000,
      gasThreshold: 25,
      majorDexes: ['Trader Joe', 'Pangolin', 'SushiSwap'],
      nativeToken: 'AVAX',
      isHighVolume: false
    });

    // Lower-volume networks with slower monitoring
    this.networkConfigs.set('base', {
      name: 'Base',
      rpcUrl: '', // Would be configured in production
      blockTime: 2,
      monitoringInterval: 5000,
      gasThreshold: 5,
      majorDexes: ['Uniswap V3', 'BaseSwap'],
      nativeToken: 'ETH',
      isHighVolume: false
    });

    this.networkConfigs.set('fantom', {
      name: 'Fantom',
      rpcUrl: '', // Would be configured in production
      blockTime: 1,
      monitoringInterval: 6000,
      gasThreshold: 100,
      majorDexes: ['SpookySwap', 'SpiritSwap', 'SushiSwap'],
      nativeToken: 'FTM',
      isHighVolume: false
    });

    // Note: Solana and Sui would require different providers (not ethers.js)
    // They would be implemented separately with their respective SDKs
  }

  private initializeProviders() {
    for (const [network, config] of this.networkConfigs) {
      if (config.rpcUrl) {
        try {
          const provider = new ethers.JsonRpcProvider(config.rpcUrl);
          this.providers.set(network, provider);
          logger.debug(`Provider initialized for ${network}`);
        } catch (error) {
          logger.error(`Failed to initialize provider for ${network}:`, error);
        }
      }
    }
  }

  private initializeMonitoringStats() {
    this.monitoringStats = {
      totalTokens: 0,
      activeNetworks: 0,
      priceUpdatesPerSecond: 0,
      averageLatency: 0,
      lastUpdateTime: 0,
      networkStats: new Map()
    };

    // Initialize network stats
    for (const network of this.networkConfigs.keys()) {
      this.monitoringStats.networkStats.set(network, {
        network,
        tokensMonitored: 0,
        priceUpdates: 0,
        averageLatency: 0,
        lastUpdate: 0,
        isHealthy: false
      });
    }
  }

  public async start() {
    if (this.isRunning) return;
    
    logger.info('Starting Enhanced Token Monitoring Service...');
    this.isRunning = true;

    // Load top 50 verified tokens
    await this.loadTop50Tokens();

    // Start monitoring for each network
    for (const [network, config] of this.networkConfigs) {
      if (this.providers.has(network)) {
        await this.startNetworkMonitoring(network, config);
      }
    }

    // Start performance monitoring
    this.startPerformanceMonitoring();

    logger.info(`Enhanced Token Monitoring Service started - monitoring ${this.monitoredTokens.size} tokens across ${this.providers.size} networks`);
  }

  public async stop() {
    if (!this.isRunning) return;
    
    logger.info('Stopping Enhanced Token Monitoring Service...');
    this.isRunning = false;

    // Stop all monitoring intervals
    for (const [network, interval] of this.monitoringIntervals) {
      clearInterval(interval);
      logger.debug(`Stopped monitoring for ${network}`);
    }
    this.monitoringIntervals.clear();

    logger.info('Enhanced Token Monitoring Service stopped');
  }

  private async loadTop50Tokens() {
    logger.info('Loading top 50 verified tokens...');
    
    // Initialize token data structures
    for (const token of this.top50Tokens) {
      this.monitoredTokens.set(token.symbol, token);
      this.tokenPrices.set(token.symbol, new Map());
      this.liquidityData.set(token.symbol, new Map());
    }

    this.monitoringStats.totalTokens = this.top50Tokens.length;
    logger.info(`Loaded ${this.top50Tokens.length} tokens for monitoring`);
  }

  private async startNetworkMonitoring(network: string, config: NetworkConfig) {
    logger.info(`Starting monitoring for ${network} (interval: ${config.monitoringInterval}ms)`);

    const interval = setInterval(async () => {
      await this.monitorNetworkTokens(network, config);
    }, config.monitoringInterval);

    this.monitoringIntervals.set(network, interval);

    // Initial monitoring run
    await this.monitorNetworkTokens(network, config);
  }

  private async monitorNetworkTokens(network: string, config: NetworkConfig) {
    const startTime = Date.now();
    
    try {
      // Monitor prices for all tokens on this network
      const pricePromises = Array.from(this.monitoredTokens.values())
        .filter(token => token.networks.includes(network))
        .map(token => this.updateTokenPrice(token.symbol, network));

      await Promise.allSettled(pricePromises);

      // Monitor liquidity for major pairs
      const liquidityPromises = Array.from(this.monitoredTokens.values())
        .filter(token => token.networks.includes(network))
        .map(token => this.updateTokenLiquidity(token.symbol, network, config));

      await Promise.allSettled(liquidityPromises);

      // Update network stats
      const latency = Date.now() - startTime;
      this.updateNetworkStats(network, latency);

    } catch (error) {
      logger.error(`Error monitoring ${network}:`, error);
      this.markNetworkUnhealthy(network);
    }
  }

  private async updateTokenPrice(symbol: string, network: string): Promise<void> {
    try {
      // In production, this would fetch from multiple price sources
      // For demo, simulate price data
      const basePrice = this.getBaseTokenPrice(symbol);
      const priceVariation = (Math.random() - 0.5) * 0.02; // ±1% variation
      const price = basePrice * (1 + priceVariation);

      const tokenPrice: TokenPrice = {
        symbol,
        network,
        price,
        timestamp: Date.now(),
        source: 'aggregated',
        volume: Math.random() * 10000000, // Random volume
        liquidity: Math.random() * 50000000, // Random liquidity
        priceChange24h: (Math.random() - 0.5) * 0.1 // ±5% change
      };

      // Store price data
      const networkPrices = this.tokenPrices.get(symbol);
      if (networkPrices) {
        networkPrices.set(network, tokenPrice);
      }

      // Emit price update event
      this.emit('priceUpdate', tokenPrice);

    } catch (error) {
      logger.error(`Failed to update price for ${symbol} on ${network}:`, error);
    }
  }

  private async updateTokenLiquidity(symbol: string, network: string, config: NetworkConfig): Promise<void> {
    try {
      const liquidityInfos: LiquidityInfo[] = [];

      // Monitor liquidity on major DEXes for this network
      for (const exchange of config.majorDexes) {
        const liquidityInfo: LiquidityInfo = {
          symbol,
          network,
          exchange,
          pair: `${symbol}/${config.nativeToken}`,
          liquidity: Math.random() * 5000000, // Random liquidity
          volume24h: Math.random() * 1000000, // Random volume
          fee: 0.003, // 0.3% standard fee
          timestamp: Date.now()
        };

        liquidityInfos.push(liquidityInfo);
      }

      // Store liquidity data
      const networkLiquidity = this.liquidityData.get(symbol);
      if (networkLiquidity) {
        networkLiquidity.set(network, liquidityInfos);
      }

      // Emit liquidity update event
      this.emit('liquidityUpdate', { symbol, network, liquidityInfos });

    } catch (error) {
      logger.error(`Failed to update liquidity for ${symbol} on ${network}:`, error);
    }
  }

  private updateNetworkStats(network: string, latency: number) {
    const stats = this.monitoringStats.networkStats.get(network);
    if (stats) {
      stats.priceUpdates++;
      stats.lastUpdate = Date.now();
      stats.isHealthy = latency < this.maxPriceUpdateLatency;
      
      // Update rolling average latency
      const latencies = this.performanceMetrics.get(network) || [];
      latencies.push(latency);
      if (latencies.length > 100) latencies.shift(); // Keep last 100 measurements
      this.performanceMetrics.set(network, latencies);
      
      stats.averageLatency = latencies.reduce((sum, l) => sum + l, 0) / latencies.length;
      stats.tokensMonitored = Array.from(this.monitoredTokens.values())
        .filter(token => token.networks.includes(network)).length;
    }
  }

  private markNetworkUnhealthy(network: string) {
    const stats = this.monitoringStats.networkStats.get(network);
    if (stats) {
      stats.isHealthy = false;
    }
  }

  private startPerformanceMonitoring() {
    setInterval(() => {
      this.updateGlobalStats();
    }, 10000); // Update every 10 seconds
  }

  private updateGlobalStats() {
    const now = Date.now();
    const activeNetworks = Array.from(this.monitoringStats.networkStats.values())
      .filter(stats => stats.isHealthy).length;
    
    const totalUpdates = Array.from(this.monitoringStats.networkStats.values())
      .reduce((sum, stats) => sum + stats.priceUpdates, 0);
    
    const totalLatencies = Array.from(this.performanceMetrics.values())
      .flat();
    const averageLatency = totalLatencies.length > 0 ? 
      totalLatencies.reduce((sum, l) => sum + l, 0) / totalLatencies.length : 0;

    this.monitoringStats.activeNetworks = activeNetworks;
    this.monitoringStats.averageLatency = averageLatency;
    this.monitoringStats.lastUpdateTime = now;
    
    // Calculate updates per second (rough estimate)
    this.monitoringStats.priceUpdatesPerSecond = totalUpdates / ((now - (now - 60000)) / 1000);
  }

  /**
   * Get current price for a token on a specific network
   */
  public getTokenPrice(symbol: string, network: string): TokenPrice | undefined {
    const networkPrices = this.tokenPrices.get(symbol);
    return networkPrices?.get(network);
  }

  /**
   * Get liquidity information for a token on a specific network
   */
  public getTokenLiquidity(symbol: string, network: string): LiquidityInfo[] | undefined {
    const networkLiquidity = this.liquidityData.get(symbol);
    return networkLiquidity?.get(network);
  }

  /**
   * Get monitoring statistics
   */
  public getMonitoringStats(): MonitoringStats {
    return { ...this.monitoringStats };
  }

  /**
   * Get network health status
   */
  public getNetworkHealth(): Map<string, boolean> {
    const health = new Map<string, boolean>();
    for (const [network, stats] of this.monitoringStats.networkStats) {
      health.set(network, stats.isHealthy);
    }
    return health;
  }

  /**
   * Get top 50 verified tokens (simplified list)
   */
  private getTop50VerifiedTokens(): TokenInfo[] {
    // This would be loaded from a real data source in production
    return [
      { symbol: 'BTC', name: 'Bitcoin', address: '0x...', decimals: 8, marketCap: ************, volume24h: 20000000000, rank: 1, isVerified: true, networks: ['ethereum', 'bsc', 'polygon'] },
      { symbol: 'ETH', name: 'Ethereum', address: '0x...', decimals: 18, marketCap: ************, volume24h: 15000000000, rank: 2, isVerified: true, networks: ['ethereum', 'polygon', 'arbitrum', 'optimism'] },
      { symbol: 'USDT', name: 'Tether', address: '0x...', decimals: 6, marketCap: 90000000000, volume24h: 30000000000, rank: 3, isVerified: true, networks: ['ethereum', 'bsc', 'polygon', 'arbitrum'] },
      { symbol: 'USDC', name: 'USD Coin', address: '0x...', decimals: 6, marketCap: 80000000000, volume24h: 25000000000, rank: 4, isVerified: true, networks: ['ethereum', 'polygon', 'arbitrum', 'optimism'] },
      { symbol: 'BNB', name: 'Binance Coin', address: '0x...', decimals: 18, marketCap: 70000000000, volume24h: 5000000000, rank: 5, isVerified: true, networks: ['bsc', 'ethereum'] },
      // ... would include all 50 tokens
    ];
  }

  private getBaseTokenPrice(symbol: string): number {
    // Simplified price mapping - would use real price feeds in production
    const prices: { [key: string]: number } = {
      'BTC': 45000,
      'ETH': 2500,
      'USDT': 1,
      'USDC': 1,
      'BNB': 300,
      'ADA': 0.5,
      'SOL': 100,
      'DOT': 7,
      'MATIC': 0.8,
      'AVAX': 35
    };
    return prices[symbol] || 1;
  }
}
