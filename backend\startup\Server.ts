import express, { Application, Request, Response, NextFunction } from 'express';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import cors from 'cors';
import rateLimit from 'express-rate-limit';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ServiceManager } from './ServiceManager.js';
import { HealthMonitor } from './HealthMonitor.js';
import { WebSocketManager } from '../websocket/WebSocketManager.js';

// Import routes
import tokenRoutes from '../routes/tokens.js';
import opportunityRoutes from '../routes/opportunities.js';
import tradeRoutes from '../routes/trades.js';
import systemRoutes from '../routes/system.js';
import analyticsRoutes from '../routes/analytics.js';
import mlRoutes, { initializeMLRoutes } from '../routes/ml.js';

export class Server {
  private app: Application;
  private server: any;
  private wss: WebSocketServer;
  private wsManager: WebSocketManager;
  private serviceManager: ServiceManager | null = null;
  private healthMonitor: HealthMonitor | null = null;
  private isRunning = false;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.wss = new WebSocketServer({ server: this.server });
    this.wsManager = new WebSocketManager(this.wss);

    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
  }

  public setServiceManager(serviceManager: ServiceManager): void {
    this.serviceManager = serviceManager;
    this.setupServiceIntegration();
  }

  public setHealthMonitor(healthMonitor: HealthMonitor): void {
    this.healthMonitor = healthMonitor;
  }

  private setupMiddleware(): void {
    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(config.API_RATE_LIMIT_WINDOW),
      max: parseInt(config.API_RATE_LIMIT_MAX),
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });

    this.app.use(limiter);

    // CORS
    const corsOrigins = config.CORS_ORIGIN.split(',').map(origin => origin.trim());
    this.app.use(cors({
      origin: corsOrigins,
      credentials: true
    }));

    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Request logging middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        logger.debug(`${req.method} ${req.path} ${res.statusCode} ${duration}ms`, {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          duration
        });

        // Record metrics for health monitoring
        if (this.healthMonitor) {
          this.healthMonitor.recordRequest(duration, res.statusCode >= 400);
        }
      });

      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req: Request, res: Response) => {
      const healthData = this.getHealthData();
      res.json(healthData);
    });

    // Detailed health endpoint
    this.app.get('/health/detailed', async (req: Request, res: Response) => {
      try {
        const detailedHealth = await this.getDetailedHealthData();
        res.json(detailedHealth);
      } catch (error) {
        logger.error('Error getting detailed health data:', error);
        res.status(500).json({ error: 'Failed to get health data' });
      }
    });

    // System status endpoint
    this.app.get('/status', (req: Request, res: Response) => {
      const status = this.getSystemStatus();
      res.json(status);
    });

    // API routes (will be set up after service manager is available)
    this.setupAPIRoutes();

    // Static file serving for frontend
    this.app.use(express.static('public'));

    // Serve index.html for SPA routes
    this.app.get('/', (req: Request, res: Response) => {
      res.sendFile('index.html', { root: '.' });
    });

    // Error handling middleware
    this.app.use((err: any, req: Request, res: Response, next: NextFunction) => {
      logger.error('Unhandled error:', err);
      
      // Record error for health monitoring
      if (this.healthMonitor) {
        this.healthMonitor.recordRequest(0, true);
      }

      res.status(500).json({
        error: 'Internal server error',
        message: config.NODE_ENV === 'development' ? err.message : 'Something went wrong',
        timestamp: new Date().toISOString()
      });
    });

    // 404 handler
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({ 
        error: 'Route not found',
        path: req.originalUrl,
        timestamp: new Date().toISOString()
      });
    });
  }

  private setupAPIRoutes(): void {
    // These will be properly initialized when service manager is set
    this.app.use('/api/tokens', (req, res, next) => {
      if (!this.serviceManager) {
        return res.status(503).json({ error: 'Services not initialized' });
      }
      const tokenService = this.serviceManager.getService('TokenDiscoveryService');
      return tokenRoutes(tokenService)(req, res, next);
    });

    this.app.use('/api/opportunities', (req, res, next) => {
      if (!this.serviceManager) {
        return res.status(503).json({ error: 'Services not initialized' });
      }
      const opportunityService = this.serviceManager.getService('OpportunityDetectionService');
      return opportunityRoutes(opportunityService)(req, res, next);
    });

    this.app.use('/api/trades', (req, res, next) => {
      if (!this.serviceManager) {
        return res.status(503).json({ error: 'Services not initialized' });
      }
      const executionService = this.serviceManager.getService('ExecutionService');
      return tradeRoutes(executionService)(req, res, next);
    });

    this.app.use('/api/system', (req, res, next) => {
      if (!this.serviceManager) {
        return res.status(503).json({ error: 'Services not initialized' });
      }
      const riskService = this.serviceManager.getService('RiskManagementService');
      return systemRoutes(riskService)(req, res, next);
    });

    this.app.use('/api/analytics', (req, res, next) => {
      if (!this.serviceManager) {
        return res.status(503).json({ error: 'Services not initialized' });
      }
      const analyticsService = this.serviceManager.getService('AnalyticsService');
      return analyticsRoutes(analyticsService)(req, res, next);
    });

    this.app.use('/api/ml', mlRoutes);
  }

  private setupServiceIntegration(): void {
    if (!this.serviceManager) return;

    // Initialize ML routes with services
    const mlLearningService = this.serviceManager.getService('MLLearningService');
    const strategySelectionService = this.serviceManager.getService('StrategySelectionService');
    
    if (mlLearningService && strategySelectionService) {
      initializeMLRoutes(mlLearningService, strategySelectionService);
    }

    // Set up WebSocket broadcasting for service events
    const opportunityService = this.serviceManager.getService('OpportunityDetectionService');
    const executionService = this.serviceManager.getService('ExecutionService');
    const riskService = this.serviceManager.getService('RiskManagementService');

    if (opportunityService) {
      opportunityService.on('opportunity', (opportunity: any) => {
        this.wsManager.broadcast('opportunity', opportunity);
      });
    }

    if (executionService) {
      executionService.on('trade', (trade: any) => {
        this.wsManager.broadcast('trade', trade);
      });

      executionService.on('tradeUpdate', (trade: any) => {
        this.wsManager.broadcast('tradeUpdate', trade);
      });
    }

    if (riskService) {
      riskService.on('emergencyStop', () => {
        this.wsManager.broadcast('emergencyStop', { active: true });
      });
    }

    if (mlLearningService) {
      mlLearningService.on('learningUpdate', (update: any) => {
        this.wsManager.broadcast('mlLearningUpdate', update);
      });
    }

    if (strategySelectionService) {
      strategySelectionService.on('strategyUpdate', (update: any) => {
        this.wsManager.broadcast('strategyUpdate', update);
      });
    }
  }

  private setupWebSocket(): void {
    this.wsManager.on('connection', (ws, request) => {
      logger.info('New WebSocket connection established', {
        ip: request.socket.remoteAddress,
        userAgent: request.headers['user-agent']
      });

      // Send initial data
      ws.send(JSON.stringify({
        type: 'welcome',
        data: {
          message: 'Connected to MEV Arbitrage Bot',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        }
      }));

      // Send current system status
      const status = this.getSystemStatus();
      ws.send(JSON.stringify({
        type: 'systemStatus',
        data: status
      }));
    });

    this.wsManager.on('message', (ws, message) => {
      try {
        const data = JSON.parse(message.toString());
        this.handleWebSocketMessage(ws, data);
      } catch (error) {
        logger.error('Invalid WebSocket message:', error);
        ws.send(JSON.stringify({
          type: 'error',
          data: { message: 'Invalid message format' }
        }));
      }
    });

    this.wsManager.on('error', (error) => {
      logger.error('WebSocket error:', error);
    });
  }

  private handleWebSocketMessage(ws: any, data: any): void {
    switch (data.type) {
      case 'subscribe':
        this.wsManager.subscribe(ws, data.channel);
        ws.send(JSON.stringify({
          type: 'subscribed',
          data: { channel: data.channel }
        }));
        break;

      case 'unsubscribe':
        this.wsManager.unsubscribe(ws, data.channel);
        ws.send(JSON.stringify({
          type: 'unsubscribed',
          data: { channel: data.channel }
        }));
        break;

      case 'ping':
        ws.send(JSON.stringify({ 
          type: 'pong', 
          timestamp: Date.now() 
        }));
        break;

      case 'getStatus':
        const status = this.getSystemStatus();
        ws.send(JSON.stringify({
          type: 'systemStatus',
          data: status
        }));
        break;

      default:
        logger.warn('Unknown WebSocket message type:', data.type);
        ws.send(JSON.stringify({
          type: 'error',
          data: { message: `Unknown message type: ${data.type}` }
        }));
    }
  }

  private getHealthData(): any {
    const baseHealth = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      environment: config.NODE_ENV
    };

    if (this.serviceManager) {
      return {
        ...baseHealth,
        services: this.serviceManager.getServiceStatus()
      };
    }

    return baseHealth;
  }

  private async getDetailedHealthData(): Promise<any> {
    const healthData = this.getHealthData();
    
    if (this.healthMonitor) {
      const metrics = await this.healthMonitor.getHealthMetrics();
      return {
        ...healthData,
        metrics,
        alerts: this.healthMonitor.getAlerts(undefined, 1) // Last hour
      };
    }

    return healthData;
  }

  private getSystemStatus(): any {
    const memUsage = process.memoryUsage();
    
    return {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024),
        total: Math.round(memUsage.heapTotal / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      },
      services: this.serviceManager ? this.serviceManager.getServiceStatus() : {},
      websocket: {
        connections: this.wsManager.getConnectionCount()
      }
    };
  }

  public async start(): Promise<void> {
    if (this.isRunning) return;

    return new Promise((resolve, reject) => {
      const port = parseInt(config.PORT);
      
      this.server.listen(port, (error: any) => {
        if (error) {
          logger.error('Failed to start HTTP server:', error);
          reject(error);
          return;
        }

        this.isRunning = true;
        logger.info(`🌐 HTTP server started on port ${port}`);
        logger.info(`📊 Dashboard: http://localhost:${port}`);
        logger.info(`🔌 WebSocket server ready for connections`);
        resolve();
      });

      this.server.on('error', (error: any) => {
        logger.error('HTTP server error:', error);
        reject(error);
      });
    });
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) return;

    return new Promise((resolve) => {
      logger.info('🛑 Stopping HTTP server...');

      // Close WebSocket server
      this.wss.close(() => {
        logger.info('✅ WebSocket server closed');
      });

      // Close HTTP server
      this.server.close(() => {
        this.isRunning = false;
        logger.info('✅ HTTP server stopped');
        resolve();
      });
    });
  }

  public isServerRunning(): boolean {
    return this.isRunning;
  }

  public getApp(): Application {
    return this.app;
  }

  public getWebSocketManager(): WebSocketManager {
    return this.wsManager;
  }
}
