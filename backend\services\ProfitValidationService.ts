import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity } from './OpportunityDetectionService.js';
import { Trade, TradeStatus } from './ExecutionService.js';
import { ValidationResult } from './PreExecutionValidationService.js';

export interface ProfitValidationResult {
  isValid: boolean;
  reason?: string;
  predictedProfit: number;
  actualProfit: number;
  profitDifference: number;
  profitAccuracy: number; // percentage
  validationPassed: boolean;
  postExecutionPassed: boolean;
  crossChainValidation?: CrossChainValidationResult;
}

export interface CrossChainValidationResult {
  bridgeCompleted: boolean;
  bridgeTransactionHash?: string;
  bridgeCompletionTime: number;
  sourceChainProfit: number;
  targetChainProfit: number;
  bridgeFeeActual: number;
  bridgeFeePredicted: number;
  bridgeFeeAccuracy: number;
  totalExecutionTime: number;
  slippageActual: number;
  slippagePredicted: number;
  networkCongestionImpact: number;
  validationErrors: string[];
}

export interface CrossChainTradeComponents {
  sourceTransaction?: string;
  bridgeTransaction?: string;
  targetTransaction?: string;
  sourceChainStatus: 'pending' | 'confirmed' | 'failed';
  bridgeStatus: 'pending' | 'in_progress' | 'completed' | 'failed';
  targetChainStatus: 'pending' | 'confirmed' | 'failed';
}

export interface ProfitMetrics {
  totalTrades: number;
  profitableTrades: number;
  unprofitableTrades: number;
  totalProfit: number;
  averageProfit: number;
  profitAccuracy: number;
  predictionAccuracy: number;
  rejectedTrades: number;
  rejectionReasons: Map<string, number>;
}

export interface ProfitThresholds {
  minAbsoluteProfit: number; // Minimum profit in USD
  minProfitMargin: number; // Minimum profit margin percentage
  maxAcceptableLoss: number; // Maximum acceptable loss in USD
  profitAccuracyThreshold: number; // Minimum prediction accuracy percentage
}

export class ProfitValidationService extends EventEmitter {
  private isRunning = false;
  private profitMetrics: ProfitMetrics;
  private profitThresholds: ProfitThresholds;
  private tradeHistory: Map<string, ProfitValidationResult> = new Map();

  // Profit validation parameters
  private readonly strictProfitValidation = true;
  private readonly enablePostExecutionValidation = true;
  private readonly enableCrossChainValidation = true;

  // Cross-chain validation tracking
  private crossChainTrades: Map<string, CrossChainTradeComponents> = new Map();
  private bridgeMonitoringInterval: NodeJS.Timeout | null = null;
  private readonly bridgeTimeoutMs = 1800000; // 30 minutes
  private readonly bridgeCheckIntervalMs = 30000; // 30 seconds
  private readonly profitPredictionWindow = 30; // seconds
  
  constructor() {
    super();
    
    this.profitThresholds = {
      minAbsoluteProfit: parseFloat(config.MIN_PROFIT_THRESHOLD || '10'),
      minProfitMargin: parseFloat(config.MIN_PROFIT_MARGIN_BUFFER || '5'),
      maxAcceptableLoss: 0, // No acceptable loss - strict profit validation
      profitAccuracyThreshold: parseFloat(config.PROFIT_ACCURACY_THRESHOLD || '80')
    };
    
    this.profitMetrics = {
      totalTrades: 0,
      profitableTrades: 0,
      unprofitableTrades: 0,
      totalProfit: 0,
      averageProfit: 0,
      profitAccuracy: 0,
      predictionAccuracy: 0,
      rejectedTrades: 0,
      rejectionReasons: new Map()
    };
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Profit Validation Service...');
    this.isRunning = true;

    // Start cross-chain bridge monitoring
    if (this.enableCrossChainValidation) {
      this.startBridgeMonitoring();
    }

    logger.info('Profit Validation Service started with strict validation and cross-chain monitoring enabled');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Profit Validation Service...');
    this.isRunning = false;

    // Stop bridge monitoring
    if (this.bridgeMonitoringInterval) {
      clearInterval(this.bridgeMonitoringInterval);
      this.bridgeMonitoringInterval = null;
    }

    logger.info('Profit Validation Service stopped');
  }

  /**
   * Validate opportunity profitability before execution
   */
  public async validatePreExecution(
    opportunity: ArbitrageOpportunity,
    validationResult?: ValidationResult
  ): Promise<ProfitValidationResult> {
    
    try {
      logger.debug(`Validating pre-execution profit for opportunity ${opportunity.id}`);

      // Use validation result if available, otherwise use opportunity data
      const predictedProfit = validationResult?.simulatedProfit || opportunity.potentialProfit;
      const totalCosts = validationResult?.totalCosts?.totalCosts || 0;
      const netProfit = predictedProfit - totalCosts;

      // Strict profit validation checks
      const validationChecks = await this.performProfitValidationChecks(
        opportunity,
        netProfit,
        validationResult
      );

      const isValid = validationChecks.every(check => check.passed);
      const failedChecks = validationChecks.filter(check => !check.passed);
      
      const result: ProfitValidationResult = {
        isValid,
        reason: isValid ? undefined : failedChecks.map(c => c.reason).join('; '),
        predictedProfit: netProfit,
        actualProfit: 0, // Will be set post-execution
        profitDifference: 0,
        profitAccuracy: 0,
        validationPassed: isValid,
        postExecutionPassed: false // Will be set post-execution
      };

      // Track validation result
      this.tradeHistory.set(opportunity.id, result);

      // Update metrics
      if (!isValid) {
        this.profitMetrics.rejectedTrades++;
        failedChecks.forEach(check => {
          const count = this.profitMetrics.rejectionReasons.get(check.reason) || 0;
          this.profitMetrics.rejectionReasons.set(check.reason, count + 1);
        });
      }

      // Emit validation event
      this.emit('preExecutionValidation', {
        opportunity,
        result,
        validationChecks
      });

      logger.info(`Pre-execution validation for ${opportunity.id}: ${isValid ? 'PASSED' : 'FAILED'}`);
      if (!isValid) {
        logger.info(`  Rejection reason: ${result.reason}`);
      }

      return result;

    } catch (error) {
      logger.error(`Pre-execution validation failed for ${opportunity.id}:`, error);
      
      return {
        isValid: false,
        reason: `Validation error: ${error instanceof Error ? error.message : String(error)}`,
        predictedProfit: 0,
        actualProfit: 0,
        profitDifference: 0,
        profitAccuracy: 0,
        validationPassed: false,
        postExecutionPassed: false
      };
    }
  }

  /**
   * Validate actual profit after trade execution
   */
  public async validatePostExecution(
    opportunity: ArbitrageOpportunity,
    trade: Trade
  ): Promise<ProfitValidationResult> {

    try {
      logger.debug(`Validating post-execution profit for trade ${trade.id}`);

      const preValidationResult = this.tradeHistory.get(opportunity.id);
      if (!preValidationResult) {
        throw new Error('No pre-execution validation result found');
      }

      const actualProfit = trade.executedProfit;
      const predictedProfit = preValidationResult.predictedProfit;
      const profitDifference = actualProfit - predictedProfit;
      const profitAccuracy = predictedProfit > 0 ?
        Math.max(0, 100 - Math.abs(profitDifference / predictedProfit) * 100) : 0;

      // Standard post-execution validation checks
      let postExecutionPassed = this.validatePostExecutionProfit(actualProfit, trade);

      // Enhanced cross-chain validation
      let crossChainValidation: CrossChainValidationResult | undefined;
      if (opportunity.type === 'cross-chain' && this.enableCrossChainValidation) {
        crossChainValidation = await this.validateCrossChainExecution(opportunity, trade);

        // Cross-chain validation affects overall pass/fail status
        postExecutionPassed = postExecutionPassed && crossChainValidation.bridgeCompleted &&
                             crossChainValidation.validationErrors.length === 0;
      }

      const result: ProfitValidationResult = {
        ...preValidationResult,
        actualProfit,
        profitDifference,
        profitAccuracy,
        postExecutionPassed,
        crossChainValidation
      };

      // Update trade history
      this.tradeHistory.set(opportunity.id, result);

      // Update profit metrics
      this.updateProfitMetrics(result, trade);

      // Emit post-execution validation event
      this.emit('postExecutionValidation', {
        opportunity,
        trade,
        result
      });

      logger.info(`Post-execution validation for ${trade.id}: ${postExecutionPassed ? 'PASSED' : 'FAILED'}`);
      logger.info(`  Predicted profit: $${predictedProfit.toFixed(2)}`);
      logger.info(`  Actual profit: $${actualProfit.toFixed(2)}`);
      logger.info(`  Profit accuracy: ${profitAccuracy.toFixed(2)}%`);

      if (crossChainValidation) {
        logger.info(`  Bridge completed: ${crossChainValidation.bridgeCompleted}`);
        logger.info(`  Bridge fee accuracy: ${crossChainValidation.bridgeFeeAccuracy.toFixed(2)}%`);
        logger.info(`  Total execution time: ${crossChainValidation.totalExecutionTime}ms`);
      }

      return result;

    } catch (error) {
      logger.error(`Post-execution validation failed for ${opportunity.id}:`, error);

      const preResult = this.tradeHistory.get(opportunity.id);
      return {
        ...preResult,
        actualProfit: trade.executedProfit,
        profitDifference: 0,
        profitAccuracy: 0,
        postExecutionPassed: false
      } as ProfitValidationResult;
    }
  }

  /**
   * Perform comprehensive profit validation checks
   */
  private async performProfitValidationChecks(
    opportunity: ArbitrageOpportunity,
    netProfit: number,
    validationResult?: ValidationResult
  ): Promise<Array<{check: string, passed: boolean, reason: string}>> {
    
    const checks = [];

    // Check 1: Minimum absolute profit
    checks.push({
      check: 'minAbsoluteProfit',
      passed: netProfit >= this.profitThresholds.minAbsoluteProfit,
      reason: `Net profit $${netProfit.toFixed(2)} below minimum $${this.profitThresholds.minAbsoluteProfit}`
    });

    // Check 2: Minimum profit margin
    const profitMargin = validationResult?.profitMargin || 
      (netProfit / opportunity.potentialProfit) * 100;
    checks.push({
      check: 'minProfitMargin',
      passed: profitMargin >= this.profitThresholds.minProfitMargin,
      reason: `Profit margin ${profitMargin.toFixed(2)}% below minimum ${this.profitThresholds.minProfitMargin}%`
    });

    // Check 3: No acceptable loss
    checks.push({
      check: 'noLoss',
      passed: netProfit > -this.profitThresholds.maxAcceptableLoss,
      reason: `Potential loss $${Math.abs(netProfit).toFixed(2)} exceeds maximum acceptable loss $${this.profitThresholds.maxAcceptableLoss}`
    });

    // Check 4: Risk-adjusted profit validation
    const riskScore = validationResult?.riskScore || 50;
    const riskAdjustedMinProfit = this.profitThresholds.minAbsoluteProfit * (1 + riskScore / 100);
    checks.push({
      check: 'riskAdjustedProfit',
      passed: netProfit >= riskAdjustedMinProfit,
      reason: `Risk-adjusted profit requirement $${riskAdjustedMinProfit.toFixed(2)} not met (risk score: ${riskScore})`
    });

    // Check 5: Flash loan profitability (if applicable)
    if (validationResult?.flashLoanOptimization) {
      const flashLoanProfit = validationResult.flashLoanOptimization.expectedProfit;
      checks.push({
        check: 'flashLoanProfitability',
        passed: flashLoanProfit > 0,
        reason: `Flash loan strategy shows negative profit: $${flashLoanProfit.toFixed(2)}`
      });
    }

    return checks;
  }

  /**
   * Validate post-execution profit
   */
  private validatePostExecutionProfit(actualProfit: number, trade: Trade): boolean {
    // Strict validation: actual profit must be positive
    if (actualProfit <= 0) {
      logger.warn(`Trade ${trade.id} resulted in loss: $${actualProfit.toFixed(2)}`);
      return false;
    }

    // Additional checks can be added here
    return true;
  }

  /**
   * Update profit metrics
   */
  private updateProfitMetrics(result: ProfitValidationResult, trade: Trade) {
    this.profitMetrics.totalTrades++;
    
    if (result.actualProfit > 0) {
      this.profitMetrics.profitableTrades++;
    } else {
      this.profitMetrics.unprofitableTrades++;
    }
    
    this.profitMetrics.totalProfit += result.actualProfit;
    this.profitMetrics.averageProfit = this.profitMetrics.totalProfit / this.profitMetrics.totalTrades;
    
    // Update prediction accuracy
    const accuracySum = Array.from(this.tradeHistory.values())
      .reduce((sum, r) => sum + r.profitAccuracy, 0);
    this.profitMetrics.predictionAccuracy = accuracySum / this.tradeHistory.size;
    
    // Update profit accuracy (percentage of profitable trades)
    this.profitMetrics.profitAccuracy = 
      (this.profitMetrics.profitableTrades / this.profitMetrics.totalTrades) * 100;
  }

  /**
   * Get profit validation statistics
   */
  public getProfitValidationStats() {
    return {
      isRunning: this.isRunning,
      profitMetrics: { ...this.profitMetrics },
      profitThresholds: { ...this.profitThresholds },
      totalValidations: this.tradeHistory.size,
      strictValidationEnabled: this.strictProfitValidation,
      postExecutionValidationEnabled: this.enablePostExecutionValidation
    };
  }

  /**
   * Get detailed profit analysis
   */
  public getProfitAnalysis() {
    const validationResults = Array.from(this.tradeHistory.values());
    
    return {
      totalValidations: validationResults.length,
      preExecutionPassRate: validationResults.filter(r => r.validationPassed).length / validationResults.length * 100,
      postExecutionPassRate: validationResults.filter(r => r.postExecutionPassed).length / validationResults.length * 100,
      averageProfitAccuracy: validationResults.reduce((sum, r) => sum + r.profitAccuracy, 0) / validationResults.length,
      profitDistribution: this.calculateProfitDistribution(validationResults),
      rejectionReasons: Object.fromEntries(this.profitMetrics.rejectionReasons)
    };
  }

  /**
   * Calculate profit distribution
   */
  private calculateProfitDistribution(results: ProfitValidationResult[]) {
    const profitable = results.filter(r => r.actualProfit > 0).length;
    const breakeven = results.filter(r => r.actualProfit === 0).length;
    const unprofitable = results.filter(r => r.actualProfit < 0).length;
    
    return {
      profitable: (profitable / results.length) * 100,
      breakeven: (breakeven / results.length) * 100,
      unprofitable: (unprofitable / results.length) * 100
    };
  }

  /**
   * Check if opportunity should be executed based on profit validation
   */
  public shouldExecuteOpportunity(opportunity: ArbitrageOpportunity, validationResult?: ValidationResult): boolean {
    if (!this.strictProfitValidation) return true;

    // This would typically be called after validatePreExecution
    const result = this.tradeHistory.get(opportunity.id);
    return result ? result.isValid : false;
  }

  /**
   * Validate cross-chain execution components
   */
  private async validateCrossChainExecution(
    opportunity: ArbitrageOpportunity,
    trade: Trade
  ): Promise<CrossChainValidationResult> {

    const startTime = Date.now();
    const validationErrors: string[] = [];

    try {
      // Get cross-chain trade components
      const tradeComponents = this.crossChainTrades.get(trade.id) || {
        sourceChainStatus: 'pending',
        bridgeStatus: 'pending',
        targetChainStatus: 'pending'
      };

      // Validate bridge completion
      const bridgeCompleted = await this.validateBridgeCompletion(trade, tradeComponents);
      if (!bridgeCompleted) {
        validationErrors.push('Bridge transaction not completed');
      }

      // Calculate actual vs predicted costs
      const bridgeFeeActual = await this.calculateActualBridgeFee(trade, opportunity);
      const bridgeFeePredicted = this.extractPredictedBridgeFee(opportunity);
      const bridgeFeeAccuracy = bridgeFeePredicted > 0 ?
        Math.max(0, 100 - Math.abs((bridgeFeeActual - bridgeFeePredicted) / bridgeFeePredicted) * 100) : 0;

      // Validate slippage accuracy
      const slippageActual = await this.calculateActualSlippage(trade, opportunity);
      const slippagePredicted = this.extractPredictedSlippage(opportunity);

      // Calculate profit distribution across chains
      const { sourceChainProfit, targetChainProfit } = await this.calculateChainProfitDistribution(trade, opportunity);

      // Assess network congestion impact
      const networkCongestionImpact = await this.assessNetworkCongestionImpact(opportunity, trade);

      // Validate execution time
      const totalExecutionTime = Date.now() - trade.timestamp;
      const expectedExecutionTime = this.estimateExpectedExecutionTime(opportunity);

      if (totalExecutionTime > expectedExecutionTime * 2) {
        validationErrors.push(`Execution time ${totalExecutionTime}ms exceeded expected ${expectedExecutionTime}ms by >100%`);
      }

      // Validate profit consistency across chains
      if (Math.abs(sourceChainProfit + targetChainProfit - trade.executedProfit) > 1) {
        validationErrors.push('Profit inconsistency detected across chains');
      }

      return {
        bridgeCompleted,
        bridgeTransactionHash: tradeComponents.bridgeTransaction,
        bridgeCompletionTime: totalExecutionTime,
        sourceChainProfit,
        targetChainProfit,
        bridgeFeeActual,
        bridgeFeePredicted,
        bridgeFeeAccuracy,
        totalExecutionTime,
        slippageActual,
        slippagePredicted,
        networkCongestionImpact,
        validationErrors
      };

    } catch (error) {
      logger.error(`Cross-chain validation failed for trade ${trade.id}:`, error);
      validationErrors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);

      return {
        bridgeCompleted: false,
        bridgeCompletionTime: Date.now() - startTime,
        sourceChainProfit: 0,
        targetChainProfit: 0,
        bridgeFeeActual: 0,
        bridgeFeePredicted: 0,
        bridgeFeeAccuracy: 0,
        totalExecutionTime: Date.now() - startTime,
        slippageActual: 0,
        slippagePredicted: 0,
        networkCongestionImpact: 0,
        validationErrors
      };
    }
  }

  /**
   * Start bridge monitoring for cross-chain trades
   */
  private startBridgeMonitoring(): void {
    if (this.bridgeMonitoringInterval) return;

    this.bridgeMonitoringInterval = setInterval(async () => {
      await this.monitorPendingBridges();
    }, this.bridgeCheckIntervalMs);

    logger.info('Cross-chain bridge monitoring started');
  }

  /**
   * Monitor pending bridge transactions
   */
  private async monitorPendingBridges(): Promise<void> {
    const now = Date.now();

    for (const [tradeId, components] of this.crossChainTrades) {
      if (components.bridgeStatus === 'in_progress') {
        try {
          // Check if bridge has completed
          const bridgeCompleted = await this.checkBridgeStatus(components.bridgeTransaction);

          if (bridgeCompleted) {
            components.bridgeStatus = 'completed';
            logger.info(`Bridge completed for trade ${tradeId}`);

            this.emit('bridgeCompleted', {
              tradeId,
              bridgeTransaction: components.bridgeTransaction
            });
          }

          // Check for timeout
          const tradeStartTime = this.getTradeStartTime(tradeId);
          if (now - tradeStartTime > this.bridgeTimeoutMs) {
            components.bridgeStatus = 'failed';
            logger.warn(`Bridge timeout for trade ${tradeId}`);

            this.emit('bridgeTimeout', {
              tradeId,
              bridgeTransaction: components.bridgeTransaction
            });
          }

        } catch (error) {
          logger.error(`Error monitoring bridge for trade ${tradeId}:`, error);
        }
      }
    }
  }

  /**
   * Validate bridge completion
   */
  private async validateBridgeCompletion(
    trade: Trade,
    components: CrossChainTradeComponents
  ): Promise<boolean> {

    if (!components.bridgeTransaction) {
      return false;
    }

    try {
      // In production, would check actual bridge transaction status
      // For now, simulate based on trade status
      return trade.status === TradeStatus.SUCCESS && components.bridgeStatus === 'completed';

    } catch (error) {
      logger.error(`Error validating bridge completion for trade ${trade.id}:`, error);
      return false;
    }
  }

  /**
   * Calculate actual bridge fee from trade execution
   */
  private async calculateActualBridgeFee(
    trade: Trade,
    opportunity: ArbitrageOpportunity
  ): Promise<number> {

    try {
      // Extract bridge fee from trade execution data
      // This would analyze the actual transaction costs

      // For now, estimate based on trade size and network
      const tradeSize = trade.amount || opportunity.requiredCapital;
      const baseFee = 10; // USD
      const variableFee = tradeSize * 0.001; // 0.1%

      return baseFee + variableFee;

    } catch (error) {
      logger.error(`Error calculating actual bridge fee for trade ${trade.id}:`, error);
      return 0;
    }
  }

  /**
   * Extract predicted bridge fee from opportunity
   */
  private extractPredictedBridgeFee(opportunity: ArbitrageOpportunity): number {
    // Extract from opportunity metadata or calculation
    return opportunity.bridgeFee || 0;
  }

  /**
   * Calculate actual slippage from trade execution
   */
  private async calculateActualSlippage(
    trade: Trade,
    opportunity: ArbitrageOpportunity
  ): Promise<number> {

    try {
      // Calculate slippage based on expected vs actual execution prices
      const expectedPrice = opportunity.targetPrice;
      const actualPrice = trade.executionPrice || expectedPrice;

      return Math.abs((actualPrice - expectedPrice) / expectedPrice) * 100;

    } catch (error) {
      logger.error(`Error calculating actual slippage for trade ${trade.id}:`, error);
      return 0;
    }
  }

  /**
   * Extract predicted slippage from opportunity
   */
  private extractPredictedSlippage(opportunity: ArbitrageOpportunity): number {
    // Extract from opportunity calculation
    return opportunity.slippageImpact || 0;
  }

  /**
   * Calculate profit distribution across chains
   */
  private async calculateChainProfitDistribution(
    trade: Trade,
    opportunity: ArbitrageOpportunity
  ): Promise<{ sourceChainProfit: number; targetChainProfit: number }> {

    try {
      // In production, would analyze actual transaction results on each chain
      // For now, estimate based on trade structure

      const totalProfit = trade.executedProfit;
      const sourceChainRatio = 0.3; // Typically 30% on source chain
      const targetChainRatio = 0.7; // Typically 70% on target chain

      return {
        sourceChainProfit: totalProfit * sourceChainRatio,
        targetChainProfit: totalProfit * targetChainRatio
      };

    } catch (error) {
      logger.error(`Error calculating chain profit distribution for trade ${trade.id}:`, error);
      return { sourceChainProfit: 0, targetChainProfit: 0 };
    }
  }

  /**
   * Assess network congestion impact on execution
   */
  private async assessNetworkCongestionImpact(
    opportunity: ArbitrageOpportunity,
    trade: Trade
  ): Promise<number> {

    try {
      // Calculate congestion impact based on execution time vs expected time
      const actualTime = trade.executionTime || 0;
      const expectedTime = this.estimateExpectedExecutionTime(opportunity);

      if (expectedTime === 0) return 0;

      const timeImpact = Math.max(0, (actualTime - expectedTime) / expectedTime);
      return Math.min(100, timeImpact * 100); // Cap at 100%

    } catch (error) {
      logger.error(`Error assessing congestion impact for trade ${trade.id}:`, error);
      return 0;
    }
  }

  /**
   * Estimate expected execution time for opportunity
   */
  private estimateExpectedExecutionTime(opportunity: ArbitrageOpportunity): number {
    if (opportunity.type === 'cross-chain') {
      // Cross-chain trades typically take 10-30 minutes
      return 20 * 60 * 1000; // 20 minutes in milliseconds
    } else {
      // Intra-chain trades typically take 1-5 minutes
      return 3 * 60 * 1000; // 3 minutes in milliseconds
    }
  }

  /**
   * Check bridge transaction status
   */
  private async checkBridgeStatus(bridgeTransactionHash?: string): Promise<boolean> {
    if (!bridgeTransactionHash) return false;

    try {
      // In production, would check actual bridge protocol status
      // For now, simulate based on transaction hash pattern
      return bridgeTransactionHash.startsWith('0x') && bridgeTransactionHash.length === 66;

    } catch (error) {
      logger.error(`Error checking bridge status for ${bridgeTransactionHash}:`, error);
      return false;
    }
  }

  /**
   * Get trade start time
   */
  private getTradeStartTime(tradeId: string): number {
    // In production, would track actual trade start times
    // For now, estimate based on current time minus reasonable execution window
    return Date.now() - (10 * 60 * 1000); // 10 minutes ago
  }

  /**
   * Register cross-chain trade for monitoring
   */
  public registerCrossChainTrade(
    tradeId: string,
    sourceTransaction?: string,
    bridgeTransaction?: string,
    targetTransaction?: string
  ): void {

    this.crossChainTrades.set(tradeId, {
      sourceTransaction,
      bridgeTransaction,
      targetTransaction,
      sourceChainStatus: sourceTransaction ? 'confirmed' : 'pending',
      bridgeStatus: bridgeTransaction ? 'in_progress' : 'pending',
      targetChainStatus: targetTransaction ? 'confirmed' : 'pending'
    });

    logger.debug(`Registered cross-chain trade ${tradeId} for monitoring`);
  }

  /**
   * Update cross-chain trade status
   */
  public updateCrossChainTradeStatus(
    tradeId: string,
    updates: Partial<CrossChainTradeComponents>
  ): void {

    const existing = this.crossChainTrades.get(tradeId);
    if (existing) {
      this.crossChainTrades.set(tradeId, { ...existing, ...updates });
      logger.debug(`Updated cross-chain trade ${tradeId} status`);
    }
  }

  /**
   * Get cross-chain validation statistics
   */
  public getCrossChainValidationStats(): { [key: string]: any } {
    const totalCrossChainTrades = Array.from(this.tradeHistory.values())
      .filter(result => result.crossChainValidation).length;

    const completedBridges = Array.from(this.tradeHistory.values())
      .filter(result => result.crossChainValidation?.bridgeCompleted).length;

    const avgBridgeFeeAccuracy = Array.from(this.tradeHistory.values())
      .filter(result => result.crossChainValidation)
      .reduce((sum, result) => sum + (result.crossChainValidation?.bridgeFeeAccuracy || 0), 0) /
      Math.max(1, totalCrossChainTrades);

    const avgExecutionTime = Array.from(this.tradeHistory.values())
      .filter(result => result.crossChainValidation)
      .reduce((sum, result) => sum + (result.crossChainValidation?.totalExecutionTime || 0), 0) /
      Math.max(1, totalCrossChainTrades);

    return {
      totalCrossChainTrades,
      completedBridges,
      bridgeSuccessRate: totalCrossChainTrades > 0 ?
        (completedBridges / totalCrossChainTrades * 100).toFixed(2) + '%' : 'N/A',
      avgBridgeFeeAccuracy: avgBridgeFeeAccuracy.toFixed(2) + '%',
      avgExecutionTime: Math.round(avgExecutionTime / 1000) + 's',
      activeBridgeMonitoring: this.bridgeMonitoringInterval !== null,
      pendingBridges: Array.from(this.crossChainTrades.values())
        .filter(trade => trade.bridgeStatus === 'in_progress').length
    };
  }

  /**
   * Get enhanced profit validation statistics including cross-chain metrics
   */
  public getEnhancedProfitValidationStats(): { [key: string]: any } {
    const baseStats = this.getProfitValidationStats();
    const crossChainStats = this.getCrossChainValidationStats();

    return {
      ...baseStats,
      crossChainValidation: crossChainStats,
      enableCrossChainValidation: this.enableCrossChainValidation,
      bridgeTimeoutMs: this.bridgeTimeoutMs,
      bridgeCheckIntervalMs: this.bridgeCheckIntervalMs
    };
  }
}
