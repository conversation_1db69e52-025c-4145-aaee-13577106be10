import express from 'express';
import { multiChainService } from '../services/MultiChainService.js';
import { performanceMonitoringService } from '../services/PerformanceMonitoringService.js';
import { databaseManager } from '../services/DatabaseConnectionManager.js';
import { enhancedTokenMonitoringService } from '../services/EnhancedTokenMonitoringService.js';
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/ErrorHandler.js';
import logger from '../utils/logger.js';

const router = express.Router();

/**
 * Get comprehensive network status
 * GET /api/networks/{networkId}/status
 */
router.get('/:networkId/status', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { networkId } = req.params;
    
    // Validate network ID
    const supportedNetworks = multiChainService.getSupportedNetworks();
    const network = supportedNetworks.find(n => n.id === networkId);
    
    if (!network) {
      return res.status(404).json({
        success: false,
        error: `Network not supported: ${networkId}`,
        supportedNetworks: supportedNetworks.map(n => n.id)
      });
    }

    // Get network health from multiple sources
    const [
      networkHealth,
      databaseHealth,
      tokenMonitoringStats,
      performanceMetrics
    ] = await Promise.allSettled([
      multiChainService.getNetworkHealth(networkId),
      databaseManager.getHealthStatus(),
      enhancedTokenMonitoringService.getNetworkStats(networkId),
      performanceMonitoringService.getCurrentMetrics()
    ]);

    // Process results
    const health = networkHealth.status === 'fulfilled' ? networkHealth.value : null;
    const dbHealth = databaseHealth.status === 'fulfilled' ? 
      Array.from(databaseHealth.value.values()).every(h => h.isHealthy) : false;
    const tokenStats = tokenMonitoringStats.status === 'fulfilled' ? tokenMonitoringStats.value : null;
    const metrics = performanceMetrics.status === 'fulfilled' ? performanceMetrics.value : null;

    // Calculate overall network status
    const isHealthy = health?.isConnected && dbHealth;
    const latency = health?.latency || 0;
    const blockHeight = health?.blockNumber || 0;
    const gasPrice = health?.gasPrice || '0';

    // Get network-specific performance data
    const networkLatency = metrics?.serviceLatency?.[networkId] || 0;
    const errorRate = metrics?.errorRates?.[networkId] || 0;
    const throughput = metrics?.throughput?.[networkId] || 0;

    // Calculate network score (0-100)
    let networkScore = 100;
    if (latency > 5000) networkScore -= 30; // High latency penalty
    if (latency > 2000) networkScore -= 15; // Medium latency penalty
    if (errorRate > 5) networkScore -= 20; // High error rate penalty
    if (!isHealthy) networkScore -= 50; // Health penalty

    const responseTime = Date.now() - startTime;

    const networkStatus = {
      networkId,
      name: network.name,
      chainId: network.chainId,
      isHealthy,
      networkScore: Math.max(0, networkScore),
      connectivity: {
        isConnected: health?.isConnected || false,
        latency,
        lastCheck: new Date().toISOString(),
        rpcEndpoint: network.rpcUrl ? 'configured' : 'not_configured'
      },
      blockchain: {
        blockHeight,
        gasPrice,
        avgBlockTime: network.avgBlockTime || 0,
        networkCongestion: this.calculateCongestion(gasPrice, network.avgGasPrice || '0')
      },
      performance: {
        responseTime: networkLatency,
        errorRate,
        throughput,
        uptime: this.calculateUptime(errorRate, throughput)
      },
      tokenMonitoring: tokenStats ? {
        tokensMonitored: tokenStats.tokensMonitored || 0,
        priceUpdates: tokenStats.priceUpdates || 0,
        lastUpdate: tokenStats.lastUpdate ? new Date(tokenStats.lastUpdate).toISOString() : null,
        averageLatency: tokenStats.averageLatency || 0
      } : null,
      database: {
        isHealthy: dbHealth,
        connections: this.getDatabaseConnectionStatus(networkId)
      },
      capabilities: {
        flashLoans: this.getFlashLoanSupport(networkId),
        mevProtection: this.getMEVProtectionSupport(networkId),
        bridgeSupport: this.getBridgeSupport(networkId)
      },
      limits: {
        maxGasPrice: network.maxGasPrice || '0',
        maxTransactionValue: network.maxTransactionValue || '0',
        minProfitThreshold: network.minProfitThreshold || '0'
      }
    };

    // Record performance metrics
    performanceMonitoringService.recordThroughput(`network_${networkId}`, 1);
    performanceMonitoringService.endOperation(`network_status_${networkId}`, 'api');

    res.json({
      success: true,
      data: networkStatus,
      responseTime,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const standardError = await errorHandler.handleError(
      error as Error,
      'NetworksAPI',
      'getNetworkStatus',
      { networkId: req.params.networkId }
    );

    logger.error(`Network status request failed for ${req.params.networkId}:`, error);
    
    res.status(500).json({
      success: false,
      error: standardError.message,
      errorCode: standardError.code,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get all networks status overview
 * GET /api/networks/status
 */
router.get('/status', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const supportedNetworks = multiChainService.getSupportedNetworks();
    
    // Get status for all networks in parallel
    const networkStatuses = await Promise.allSettled(
      supportedNetworks.map(async (network) => {
        try {
          const health = await multiChainService.getNetworkHealth(network.id);
          return {
            networkId: network.id,
            name: network.name,
            isHealthy: health?.isConnected || false,
            latency: health?.latency || 0,
            blockHeight: health?.blockNumber || 0,
            gasPrice: health?.gasPrice || '0'
          };
        } catch (error) {
          return {
            networkId: network.id,
            name: network.name,
            isHealthy: false,
            latency: 0,
            blockHeight: 0,
            gasPrice: '0',
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      })
    );

    const networks = networkStatuses.map(result => 
      result.status === 'fulfilled' ? result.value : result.reason
    );

    // Calculate overall statistics
    const totalNetworks = networks.length;
    const healthyNetworks = networks.filter(n => n.isHealthy).length;
    const averageLatency = networks.reduce((sum, n) => sum + n.latency, 0) / totalNetworks;
    const overallHealth = (healthyNetworks / totalNetworks) * 100;

    const responseTime = Date.now() - startTime;

    res.json({
      success: true,
      data: {
        overview: {
          totalNetworks,
          healthyNetworks,
          unhealthyNetworks: totalNetworks - healthyNetworks,
          overallHealth: Math.round(overallHealth),
          averageLatency: Math.round(averageLatency)
        },
        networks,
        capabilities: {
          crossChainSupported: true,
          flashLoansSupported: true,
          mevProtectionSupported: true,
          totalBridgeProtocols: 7
        }
      },
      responseTime,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const standardError = await errorHandler.handleError(
      error as Error,
      'NetworksAPI',
      'getAllNetworksStatus',
      {}
    );

    logger.error('All networks status request failed:', error);
    
    res.status(500).json({
      success: false,
      error: standardError.message,
      errorCode: standardError.code,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get network configuration
 * GET /api/networks/{networkId}/config
 */
router.get('/:networkId/config', async (req, res) => {
  try {
    const { networkId } = req.params;
    
    const supportedNetworks = multiChainService.getSupportedNetworks();
    const network = supportedNetworks.find(n => n.id === networkId);
    
    if (!network) {
      return res.status(404).json({
        success: false,
        error: `Network not found: ${networkId}`
      });
    }

    const config = {
      networkId: network.id,
      name: network.name,
      chainId: network.chainId,
      rpcUrl: network.rpcUrl ? 'configured' : 'not_configured',
      explorerUrl: network.explorerUrl || null,
      nativeCurrency: {
        name: network.nativeCurrency?.name || 'Unknown',
        symbol: network.nativeCurrency?.symbol || 'UNKNOWN',
        decimals: network.nativeCurrency?.decimals || 18
      },
      gasConfiguration: {
        avgGasPrice: network.avgGasPrice || '0',
        maxGasPrice: network.maxGasPrice || '0',
        avgBlockTime: network.avgBlockTime || 0,
        gasLimit: network.gasLimit || '0'
      },
      dexSupport: network.supportedDEXs || [],
      bridgeSupport: this.getBridgeSupport(networkId),
      flashLoanSupport: this.getFlashLoanSupport(networkId),
      mevProtectionSupport: this.getMEVProtectionSupport(networkId)
    };

    res.json({
      success: true,
      data: config,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error(`Network config request failed for ${req.params.networkId}:`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to fetch network configuration',
      timestamp: new Date().toISOString()
    });
  }
});

// Helper methods
function calculateCongestion(currentGasPrice: string, avgGasPrice: string): string {
  try {
    const current = BigInt(currentGasPrice);
    const average = BigInt(avgGasPrice);
    
    if (average === BigInt(0)) return 'unknown';
    
    const ratio = Number(current * BigInt(100) / average);
    
    if (ratio > 200) return 'very_high';
    if (ratio > 150) return 'high';
    if (ratio > 110) return 'medium';
    return 'low';
  } catch {
    return 'unknown';
  }
}

function calculateUptime(errorRate: number, throughput: number): number {
  if (throughput === 0) return 0;
  return Math.max(0, 100 - (errorRate * 10));
}

function getDatabaseConnectionStatus(networkId: string): any {
  const healthStatus = databaseManager.getHealthStatus();
  return {
    supabase: healthStatus.get('supabase')?.isHealthy || false,
    influxdb: healthStatus.get('influxdb')?.isHealthy || false,
    redis: healthStatus.get('redis')?.isHealthy || false,
    postgres: healthStatus.get('postgres')?.isHealthy || false
  };
}

function getFlashLoanSupport(networkId: string): string[] {
  const support: { [key: string]: string[] } = {
    ethereum: ['aave_v3', 'balancer_v2', 'dydx', 'uniswap_v3'],
    polygon: ['aave_v3', 'balancer_v2', 'uniswap_v3'],
    arbitrum: ['aave_v3', 'balancer_v2', 'uniswap_v3'],
    optimism: ['aave_v3', 'uniswap_v3'],
    base: ['uniswap_v3'],
    avalanche: ['aave_v3'],
    bsc: [],
    fantom: [],
    solana: [],
    sui: []
  };
  
  return support[networkId] || [];
}

function getMEVProtectionSupport(networkId: string): string[] {
  const support: { [key: string]: string[] } = {
    ethereum: ['flashbots_protect', 'flashbots_auction', 'dynamic_gas'],
    polygon: ['private_mempool', 'dynamic_gas'],
    bsc: ['private_mempool', 'dynamic_gas'],
    arbitrum: ['l2_sequencer_priority', 'l2_batch_optimization', 'dynamic_gas'],
    optimism: ['l2_sequencer_priority', 'l2_batch_optimization', 'dynamic_gas'],
    base: ['l2_sequencer_priority', 'dynamic_gas'],
    avalanche: ['private_mempool', 'dynamic_gas'],
    fantom: ['dynamic_gas'],
    solana: ['dynamic_gas'],
    sui: ['dynamic_gas']
  };
  
  return support[networkId] || ['standard'];
}

function getBridgeSupport(networkId: string): string[] {
  const support: { [key: string]: string[] } = {
    ethereum: ['stargate', 'layerzero', 'multichain', 'wormhole', 'celer_cbridge'],
    polygon: ['stargate', 'layerzero', 'multichain', 'wormhole', 'celer_cbridge'],
    bsc: ['stargate', 'layerzero', 'multichain', 'wormhole', 'celer_cbridge'],
    arbitrum: ['stargate', 'layerzero', 'celer_cbridge'],
    optimism: ['stargate', 'layerzero', 'celer_cbridge'],
    base: ['stargate', 'layerzero'],
    avalanche: ['stargate', 'layerzero', 'multichain', 'celer_cbridge'],
    fantom: ['stargate', 'layerzero', 'multichain', 'fantom_bridge', 'celer_cbridge'],
    solana: ['wormhole'],
    sui: ['wormhole', 'sui_bridge']
  };
  
  return support[networkId] || [];
}

export default router;
