import { expect } from "chai";
import hre from "hardhat";
const { ethers } = hre;

describe("Token Discovery Integration Test", function () {
  let owner;
  let user1;
  let tokenDiscovery;
  let mockTokens = {};

  beforeEach(async function () {
    [owner, user1] = await ethers.getSigners();
    
    // Deploy TokenDiscovery contract
    const TokenDiscovery = await ethers.getContractFactory("TokenDiscovery");
    tokenDiscovery = await TokenDiscovery.deploy();
    await tokenDiscovery.waitForDeployment();
    
    // Deploy mock tokens
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    
    // Deploy WETH-like token
    mockTokens.WETH = await MockERC20.deploy(
      "Wrapped Ether",
      "WETH",
      18,
      ethers.parseEther("1000000"),
      owner.address
    );
    await mockTokens.WETH.waitForDeployment();
    
    // Deploy USDC-like token
    mockTokens.USDC = await MockERC20.deploy(
      "USD Coin",
      "USDC",
      6,
      ethers.parseUnits("1000000000", 6),
      owner.address
    );
    await mockTokens.USDC.waitForDeployment();
    
    // Deploy a problematic token (with transfer tax)
    mockTokens.TAXED = await MockERC20.deploy(
      "Taxed Token",
      "TAXED",
      18,
      ethers.parseEther("1000000"),
      owner.address
    );
    await mockTokens.TAXED.waitForDeployment();
    
    // Set transfer tax on the taxed token
    await mockTokens.TAXED.setTransferTax(300); // 3% tax
  });

  describe("Token Whitelisting", function () {
    it("Should whitelist tokens correctly", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      await tokenDiscovery.addToWhitelist(
        wethAddress,
        "Wrapped Ether",
        "WETH",
        ethers.parseEther("1000"), // 1000 ETH min liquidity
        95 // High safety score
      );
      
      const tokenInfo = await tokenDiscovery.getTokenInfo(wethAddress);
      expect(tokenInfo.isWhitelisted).to.be.true;
      expect(tokenInfo.symbol).to.equal("WETH");
      expect(tokenInfo.safetyScore).to.equal(95);
      
      console.log(`WETH whitelisted with safety score: ${tokenInfo.safetyScore}`);
    });

    it("Should validate token safety correctly", async function () {
      const usdcAddress = await mockTokens.USDC.getAddress();
      
      await tokenDiscovery.addToWhitelist(
        usdcAddress,
        "USD Coin",
        "USDC",
        ethers.parseUnits("1000000", 6), // 1M USDC min liquidity
        90
      );
      
      const isValid = await tokenDiscovery.isTokenValid(usdcAddress);
      expect(isValid).to.be.true;
      
      console.log(`USDC validation result: ${isValid}`);
    });

    it("Should handle token blacklisting", async function () {
      const taxedAddress = await mockTokens.TAXED.getAddress();
      
      // First whitelist the token
      await tokenDiscovery.addToWhitelist(
        taxedAddress,
        "Taxed Token",
        "TAXED",
        ethers.parseEther("100"),
        70
      );
      
      // Then blacklist it due to transfer tax
      await tokenDiscovery.blacklistToken(taxedAddress, "High transfer tax detected");
      
      const tokenInfo = await tokenDiscovery.getTokenInfo(taxedAddress);
      expect(tokenInfo.isBlacklisted).to.be.true;
      expect(tokenInfo.isWhitelisted).to.be.false;
      
      const isValid = await tokenDiscovery.isTokenValid(taxedAddress);
      expect(isValid).to.be.false;
      
      console.log(`Taxed token blacklisted successfully`);
    });
  });

  describe("Safety Score Calculation", function () {
    it("Should calculate safety scores using the enhanced algorithm", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Create safety metrics
      const metrics = {
        contractVerified: true,
        hasLiquidity: true,
        holderCount: 50000,
        ageInDays: 500,
        hasValidMetadata: true,
        isNotPaused: true,
        noMintFunction: true,
        noBlacklist: true,
        transferTaxRate: 0
      };
      
      const calculatedScore = await tokenDiscovery.calculateSafetyScore(metrics);
      expect(calculatedScore).to.be.greaterThan(90); // Should be high score
      
      console.log(`Calculated safety score for WETH-like token: ${calculatedScore}`);
    });

    it("Should penalize tokens with transfer taxes", async function () {
      const metrics = {
        contractVerified: true,
        hasLiquidity: true,
        holderCount: 10000,
        ageInDays: 100,
        hasValidMetadata: true,
        isNotPaused: true,
        noMintFunction: true,
        noBlacklist: true,
        transferTaxRate: 500 // 5% tax
      };
      
      const calculatedScore = await tokenDiscovery.calculateSafetyScore(metrics);
      expect(calculatedScore).to.be.lessThan(80); // Should be penalized
      
      console.log(`Safety score with 5% transfer tax: ${calculatedScore}`);
    });
  });

  describe("Token Validation Edge Cases", function () {
    it("Should reject zero address", async function () {
      const isValid = await tokenDiscovery.isTokenValid(ethers.ZeroAddress);
      expect(isValid).to.be.false;
    });

    it("Should reject non-existent tokens", async function () {
      const randomAddress = ethers.Wallet.createRandom().address;
      const isValid = await tokenDiscovery.isTokenValid(randomAddress);
      expect(isValid).to.be.false;
    });

    it("Should handle low safety score tokens", async function () {
      const lowScoreAddress = await mockTokens.TAXED.getAddress();
      
      await tokenDiscovery.addToWhitelist(
        lowScoreAddress,
        "Low Score Token",
        "LOW",
        ethers.parseEther("1"),
        50 // Below minimum threshold of 70
      );
      
      const isValid = await tokenDiscovery.isTokenValid(lowScoreAddress);
      expect(isValid).to.be.false; // Should be invalid due to low score
      
      console.log(`Low safety score token correctly rejected`);
    });
  });

  describe("Performance Validation", function () {
    it("Should handle multiple token operations efficiently", async function () {
      const startTime = Date.now();
      
      // Add multiple tokens
      for (let i = 0; i < 10; i++) {
        const MockERC20 = await ethers.getContractFactory("MockERC20");
        const token = await MockERC20.deploy(
          `Token ${i}`,
          `TK${i}`,
          18,
          ethers.parseEther("1000"),
          owner.address
        );
        await token.waitForDeployment();
        
        await tokenDiscovery.addToWhitelist(
          await token.getAddress(),
          `Token ${i}`,
          `TK${i}`,
          ethers.parseEther("100"),
          80 + i // Varying safety scores
        );
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).to.be.lessThan(30000); // Should complete within 30 seconds
      console.log(`Added 10 tokens in ${duration}ms`);
    });

    it("Should validate tokens quickly", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      await tokenDiscovery.addToWhitelist(
        wethAddress,
        "Wrapped Ether",
        "WETH",
        ethers.parseEther("1000"),
        95
      );
      
      const startTime = Date.now();
      
      // Validate multiple times
      for (let i = 0; i < 100; i++) {
        await tokenDiscovery.isTokenValid(wethAddress);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).to.be.lessThan(5000); // Should complete within 5 seconds
      console.log(`100 validations completed in ${duration}ms`);
    });
  });

  describe("Gas Efficiency", function () {
    it("Should use reasonable gas for token operations", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      const tx = await tokenDiscovery.addToWhitelist(
        wethAddress,
        "Wrapped Ether",
        "WETH",
        ethers.parseEther("1000"),
        95
      );
      
      const receipt = await tx.wait();
      expect(receipt.gasUsed).to.be.lessThan(200000); // Should be efficient
      
      console.log(`Gas used for whitelisting: ${receipt.gasUsed}`);
    });

    it("Should use minimal gas for validation", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      await tokenDiscovery.addToWhitelist(
        wethAddress,
        "Wrapped Ether",
        "WETH",
        ethers.parseEther("1000"),
        95
      );
      
      // Validation is a view function, so no gas cost in actual usage
      // But we can test that it executes without errors
      const isValid = await tokenDiscovery.isTokenValid(wethAddress);
      expect(isValid).to.be.true;
      
      console.log(`Token validation completed successfully`);
    });
  });
});
