import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { InfluxDB, Point, WriteApi } from '@influxdata/influxdb-client';
import { createClient as createRedisClient, RedisClientType } from 'redis';
import { Pool } from 'pg';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/ErrorHandler.js';

export interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  nextAttempt: number;
  halfOpenAttempts: number;
  maxHalfOpenAttempts: number;
  successiveSuccesses: number;
  requiredSuccesses: number;
  totalRequests: number;
  totalFailures: number;
}

export interface ConnectionPoolMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingClients: number;
  totalQueries: number;
  averageQueryTime: number;
  errorRate: number;
}

export interface DatabaseHealth {
  isHealthy: boolean;
  latency: number;
  lastCheck: number;
  errorCount: number;
  circuitBreakerState: CircuitBreakerState;
  poolMetrics?: ConnectionPoolMetrics;
}

export class DatabaseConnectionManager {
  private supabase: SupabaseClient | null = null;
  private influxDB: InfluxDB | null = null;
  private influxWriteApi: WriteApi | null = null;
  private redis: RedisClientType | null = null;
  private postgres: Pool | null = null;

  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();
  private healthStatus: Map<string, DatabaseHealth> = new Map();
  private queryMetrics: Map<string, number[]> = new Map();

  constructor() {
    this.initializeCircuitBreakers();
    this.initializeHealthMonitoring();
  }

  private initializeCircuitBreakers() {
    const services = ['supabase', 'influxdb', 'redis', 'postgres'];
    services.forEach(service => {
      this.circuitBreakers.set(service, {
        failures: 0,
        lastFailureTime: 0,
        state: 'CLOSED',
        nextAttempt: 0,
        halfOpenAttempts: 0,
        maxHalfOpenAttempts: 3,
        successiveSuccesses: 0,
        requiredSuccesses: 2,
        totalRequests: 0,
        totalFailures: 0
      });

      this.healthStatus.set(service, {
        isHealthy: false,
        latency: 0,
        lastCheck: 0,
        errorCount: 0,
        circuitBreakerState: this.circuitBreakers.get(service)!
      });
    });
  }

  private initializeHealthMonitoring() {
    // Health check every 30 seconds
    setInterval(() => {
      this.performHealthChecks();
    }, 30000);

    // Circuit breaker monitoring
    setInterval(() => {
      this.updateCircuitBreakers();
    }, parseInt(config.CIRCUIT_BREAKER_MONITORING_PERIOD));
  }

  async initialize(): Promise<void> {
    logger.info('Initializing database connections with optimized pools...');

    await Promise.allSettled([
      this.initializeSupabase(),
      this.initializeInfluxDB(),
      this.initializeRedis(),
      this.initializePostgres()
    ]);

    logger.info('Database connection manager initialized');
  }

  private async initializeSupabase(): Promise<void> {
    try {
      if (!config.SUPABASE_URL || !config.SUPABASE_SERVICE_ROLE_KEY) {
        logger.warn('Supabase configuration missing');
        return;
      }

      this.supabase = createClient(
        config.SUPABASE_URL,
        config.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          },
          db: {
            schema: 'public'
          },
          global: {
            headers: {
              'x-connection-pool-max': config.SUPABASE_POOL_MAX
            }
          }
        }
      );

      await this.testConnection('supabase');
      logger.info('Supabase connection initialized');
    } catch (error) {
      logger.error('Failed to initialize Supabase:', error);
      this.recordFailure('supabase');
    }
  }

  private async initializeInfluxDB(): Promise<void> {
    try {
      if (!config.INFLUXDB_URL || !config.INFLUXDB_TOKEN) {
        logger.warn('InfluxDB configuration missing');
        return;
      }

      this.influxDB = new InfluxDB({
        url: config.INFLUXDB_URL,
        token: config.INFLUXDB_TOKEN,
        timeout: parseInt(config.CIRCUIT_BREAKER_TIMEOUT)
      });

      this.influxWriteApi = this.influxDB.getWriteApi(
        config.INFLUXDB_ORG,
        config.INFLUXDB_BUCKET,
        'ms',
        {
          batchSize: parseInt(config.INFLUXDB_BATCH_SIZE),
          flushInterval: parseInt(config.INFLUXDB_BATCH_TIMEOUT),
          maxRetries: 3,
          maxRetryDelay: 5000,
          exponentialBase: 2
        }
      );

      await this.testConnection('influxdb');
      logger.info('InfluxDB connection initialized');
    } catch (error) {
      logger.error('Failed to initialize InfluxDB:', error);
      this.recordFailure('influxdb');
    }
  }

  private async initializeRedis(): Promise<void> {
    try {
      this.redis = createRedisClient({
        url: config.REDIS_URL,
        socket: {
          connectTimeout: parseInt(config.CIRCUIT_BREAKER_TIMEOUT),
          lazyConnect: true,
          reconnectStrategy: (retries) => {
            if (retries > 5) return false;
            return Math.min(retries * 1000, 5000);
          }
        },
        database: 0
      });

      this.redis.on('error', (error) => {
        logger.error('Redis connection error:', error);
        this.recordFailure('redis');
      });

      this.redis.on('connect', () => {
        logger.info('Redis connected');
        this.recordSuccess('redis');
      });

      await this.redis.connect();
      await this.testConnection('redis');
      logger.info('Redis connection initialized');
    } catch (error) {
      logger.error('Failed to initialize Redis:', error);
      this.recordFailure('redis');
    }
  }

  private async initializePostgres(): Promise<void> {
    try {
      if (!config.DATABASE_URL) {
        logger.warn('PostgreSQL configuration missing');
        return;
      }

      this.postgres = new Pool({
        connectionString: config.DATABASE_URL,
        max: parseInt(config.POSTGRES_POOL_MAX),
        idleTimeoutMillis: parseInt(config.POSTGRES_POOL_IDLE_TIMEOUT),
        connectionTimeoutMillis: parseInt(config.CIRCUIT_BREAKER_TIMEOUT),
        query_timeout: parseInt(config.MAX_DATABASE_QUERY_TIME),
        statement_timeout: parseInt(config.MAX_DATABASE_QUERY_TIME)
      });

      this.postgres.on('error', (error) => {
        logger.error('PostgreSQL pool error:', error);
        this.recordFailure('postgres');
      });

      this.postgres.on('connect', () => {
        this.recordSuccess('postgres');
      });

      await this.testConnection('postgres');
      logger.info('PostgreSQL connection initialized');
    } catch (error) {
      logger.error('Failed to initialize PostgreSQL:', error);
      this.recordFailure('postgres');
    }
  }

  private async testConnection(service: string): Promise<boolean> {
    const startTime = Date.now();
    
    try {
      switch (service) {
        case 'supabase':
          if (!this.supabase) return false;
          await this.supabase.from('trades').select('count').limit(1);
          break;
        
        case 'influxdb':
          if (!this.influxDB) return false;
          await this.influxDB.getQueryApi(config.INFLUXDB_ORG).queryRaw('buckets()');
          break;
        
        case 'redis':
          if (!this.redis) return false;
          await this.redis.ping();
          break;
        
        case 'postgres':
          if (!this.postgres) return false;
          const client = await this.postgres.connect();
          await client.query('SELECT 1');
          client.release();
          break;
        
        default:
          return false;
      }

      const latency = Date.now() - startTime;
      this.updateHealthStatus(service, true, latency);
      this.recordSuccess(service);
      return true;
    } catch (error) {
      const latency = Date.now() - startTime;
      this.updateHealthStatus(service, false, latency);
      this.recordFailure(service);
      return false;
    }
  }

  private recordSuccess(service: string): void {
    const breaker = this.circuitBreakers.get(service);
    if (breaker) {
      breaker.totalRequests++;

      if (breaker.state === 'HALF_OPEN') {
        breaker.successiveSuccesses++;
        breaker.halfOpenAttempts++;

        if (breaker.successiveSuccesses >= breaker.requiredSuccesses) {
          breaker.state = 'CLOSED';
          breaker.failures = 0;
          breaker.successiveSuccesses = 0;
          breaker.halfOpenAttempts = 0;
          logger.info(`Circuit breaker for ${service} closed (recovered after ${breaker.requiredSuccesses} successful attempts)`);

          // Emit recovery event
          this.emit('circuitBreakerRecovered', {
            service,
            totalFailures: breaker.totalFailures,
            recoveryTime: Date.now() - breaker.lastFailureTime
          });
        }
      } else if (breaker.state === 'CLOSED') {
        breaker.failures = 0;
        breaker.successiveSuccesses = 0;
      }
    }
  }

  private recordFailure(service: string): void {
    const breaker = this.circuitBreakers.get(service);
    if (breaker) {
      breaker.failures++;
      breaker.totalRequests++;
      breaker.totalFailures++;
      breaker.lastFailureTime = Date.now();
      breaker.successiveSuccesses = 0; // Reset success counter

      if (breaker.state === 'HALF_OPEN') {
        // Immediate transition to OPEN on failure in HALF_OPEN state
        breaker.state = 'OPEN';
        breaker.nextAttempt = Date.now() + parseInt(config.CIRCUIT_BREAKER_RESET_TIMEOUT);
        breaker.halfOpenAttempts = 0;
        logger.warn(`Circuit breaker for ${service} opened (failure during half-open state)`);

        // Emit failure event
        this.emit('circuitBreakerOpened', {
          service,
          reason: 'half_open_failure',
          totalFailures: breaker.totalFailures,
          failureRate: breaker.totalFailures / breaker.totalRequests
        });

      } else if (breaker.failures >= parseInt(config.CIRCUIT_BREAKER_FAILURE_THRESHOLD)) {
        breaker.state = 'OPEN';
        breaker.nextAttempt = Date.now() + parseInt(config.CIRCUIT_BREAKER_RESET_TIMEOUT);
        logger.warn(`Circuit breaker for ${service} opened (${breaker.failures} consecutive failures)`);

        // Emit failure event
        this.emit('circuitBreakerOpened', {
          service,
          reason: 'threshold_exceeded',
          consecutiveFailures: breaker.failures,
          totalFailures: breaker.totalFailures,
          failureRate: breaker.totalFailures / breaker.totalRequests
        });
      }
    }
  }

  private updateCircuitBreakers(): void {
    const now = Date.now();

    this.circuitBreakers.forEach((breaker, service) => {
      if (breaker.state === 'OPEN' && now >= breaker.nextAttempt) {
        breaker.state = 'HALF_OPEN';
        breaker.halfOpenAttempts = 0;
        breaker.successiveSuccesses = 0;
        logger.info(`Circuit breaker for ${service} half-open (attempting recovery)`);

        // Emit half-open event
        this.emit('circuitBreakerHalfOpen', {
          service,
          downtime: now - breaker.lastFailureTime,
          totalFailures: breaker.totalFailures
        });
      }

      // Check for stuck half-open state
      if (breaker.state === 'HALF_OPEN' &&
          breaker.halfOpenAttempts >= breaker.maxHalfOpenAttempts &&
          breaker.successiveSuccesses < breaker.requiredSuccesses) {

        breaker.state = 'OPEN';
        breaker.nextAttempt = now + parseInt(config.CIRCUIT_BREAKER_RESET_TIMEOUT);
        breaker.halfOpenAttempts = 0;
        logger.warn(`Circuit breaker for ${service} returned to OPEN (insufficient successes in half-open)`);

        // Emit stuck half-open event
        this.emit('circuitBreakerStuck', {
          service,
          halfOpenAttempts: breaker.halfOpenAttempts,
          successiveSuccesses: breaker.successiveSuccesses,
          requiredSuccesses: breaker.requiredSuccesses
        });
      }
    });
  }

  private updateHealthStatus(service: string, isHealthy: boolean, latency: number): void {
    const health = this.healthStatus.get(service);
    if (health) {
      health.isHealthy = isHealthy;
      health.latency = latency;
      health.lastCheck = Date.now();
      if (!isHealthy) {
        health.errorCount++;
      }
      health.circuitBreakerState = this.circuitBreakers.get(service)!;
    }
  }

  private async performHealthChecks(): Promise<void> {
    const services = ['supabase', 'influxdb', 'redis', 'postgres'];
    
    await Promise.allSettled(
      services.map(service => this.testConnection(service))
    );
  }

  // Public interface methods
  getSupabase(): SupabaseClient | null {
    if (this.isServiceAvailable('supabase')) {
      return this.supabase;
    }
    return null;
  }

  getInfluxDB(): InfluxDB | null {
    if (this.isServiceAvailable('influxdb')) {
      return this.influxDB;
    }
    return null;
  }

  getInfluxWriteApi(): WriteApi | null {
    if (this.isServiceAvailable('influxdb')) {
      return this.influxWriteApi;
    }
    return null;
  }

  getRedis(): RedisClientType | null {
    if (this.isServiceAvailable('redis')) {
      return this.redis;
    }
    return null;
  }

  getPostgres(): Pool | null {
    if (this.isServiceAvailable('postgres')) {
      return this.postgres;
    }
    return null;
  }

  private isServiceAvailable(service: string): boolean {
    const breaker = this.circuitBreakers.get(service);
    return breaker ? breaker.state !== 'OPEN' : false;
  }

  getHealthStatus(): Map<string, DatabaseHealth> {
    return new Map(this.healthStatus);
  }

  /**
   * Execute database operation with circuit breaker protection
   */
  async executeWithCircuitBreaker<T>(
    service: string,
    operation: () => Promise<T>,
    operationName: string = 'database_operation'
  ): Promise<T> {

    const breaker = this.circuitBreakers.get(service);
    if (!breaker) {
      throw new Error(`No circuit breaker found for service: ${service}`);
    }

    // Check if circuit breaker is open
    if (breaker.state === 'OPEN') {
      const error = errorHandler.createError(
        ErrorType.NETWORK_ERROR,
        `Circuit breaker is OPEN for ${service}`,
        {
          service,
          operationName,
          nextAttempt: new Date(breaker.nextAttempt).toISOString(),
          totalFailures: breaker.totalFailures
        },
        undefined,
        ErrorSeverity.HIGH
      );
      throw error;
    }

    // Check half-open state limits
    if (breaker.state === 'HALF_OPEN' && breaker.halfOpenAttempts >= breaker.maxHalfOpenAttempts) {
      const error = errorHandler.createError(
        ErrorType.NETWORK_ERROR,
        `Circuit breaker is HALF_OPEN with max attempts reached for ${service}`,
        {
          service,
          operationName,
          halfOpenAttempts: breaker.halfOpenAttempts,
          maxHalfOpenAttempts: breaker.maxHalfOpenAttempts
        },
        undefined,
        ErrorSeverity.MEDIUM
      );
      throw error;
    }

    const startTime = Date.now();

    try {
      const result = await operation();
      const latency = Date.now() - startTime;

      // Record success
      this.recordSuccess(service);
      this.updateHealthStatus(service, true, latency);

      return result;

    } catch (error) {
      const latency = Date.now() - startTime;

      // Record failure
      this.recordFailure(service);
      this.updateHealthStatus(service, false, latency);

      // Handle error with standardized error handling
      const standardError = await errorHandler.handleError(
        error as Error,
        'DatabaseConnectionManager',
        operationName,
        { service, latency, circuitBreakerState: breaker.state }
      );

      throw standardError;
    }
  }

  /**
   * Execute with fallback to secondary database
   */
  async executeWithFallback<T>(
    primaryService: string,
    secondaryService: string,
    operation: (client: any) => Promise<T>,
    operationName: string = 'database_operation_with_fallback'
  ): Promise<T> {

    // Try primary service
    try {
      const primaryClient = this.getClient(primaryService);
      if (primaryClient) {
        return await this.executeWithCircuitBreaker(
          primaryService,
          () => operation(primaryClient),
          `${operationName}_primary`
        );
      }
    } catch (error) {
      logger.warn(`Primary database ${primaryService} failed, trying fallback:`, error);
    }

    // Try secondary service
    try {
      const secondaryClient = this.getClient(secondaryService);
      if (secondaryClient) {
        logger.info(`Using fallback database: ${secondaryService}`);
        return await this.executeWithCircuitBreaker(
          secondaryService,
          () => operation(secondaryClient),
          `${operationName}_fallback`
        );
      }
    } catch (error) {
      logger.error(`Fallback database ${secondaryService} also failed:`, error);
      throw error;
    }

    throw new Error(`Both primary (${primaryService}) and fallback (${secondaryService}) databases are unavailable`);
  }

  /**
   * Get database client by service name
   */
  private getClient(service: string): any {
    switch (service) {
      case 'supabase':
        return this.getSupabase();
      case 'influxdb':
        return this.getInfluxDB();
      case 'redis':
        return this.getRedis();
      case 'postgres':
        return this.getPostgres();
      default:
        return null;
    }
  }

  /**
   * Get comprehensive circuit breaker statistics
   */
  getCircuitBreakerStats(): { [key: string]: any } {
    const stats: { [key: string]: any } = {};

    this.circuitBreakers.forEach((breaker, service) => {
      const health = this.healthStatus.get(service);

      stats[service] = {
        state: breaker.state,
        failures: breaker.failures,
        totalRequests: breaker.totalRequests,
        totalFailures: breaker.totalFailures,
        failureRate: breaker.totalRequests > 0 ?
          (breaker.totalFailures / breaker.totalRequests * 100).toFixed(2) + '%' : '0%',
        successiveSuccesses: breaker.successiveSuccesses,
        requiredSuccesses: breaker.requiredSuccesses,
        halfOpenAttempts: breaker.halfOpenAttempts,
        maxHalfOpenAttempts: breaker.maxHalfOpenAttempts,
        lastFailureTime: breaker.lastFailureTime > 0 ?
          new Date(breaker.lastFailureTime).toISOString() : null,
        nextAttempt: breaker.nextAttempt > 0 ?
          new Date(breaker.nextAttempt).toISOString() : null,
        isHealthy: health?.isHealthy || false,
        latency: health?.latency || 0,
        errorCount: health?.errorCount || 0
      };
    });

    return stats;
  }

  /**
   * Force circuit breaker recovery for a service
   */
  async forceRecovery(service: string): Promise<boolean> {
    const breaker = this.circuitBreakers.get(service);
    if (!breaker) {
      logger.warn(`No circuit breaker found for service: ${service}`);
      return false;
    }

    logger.info(`Forcing recovery for service: ${service}`);

    // Reset circuit breaker state
    breaker.state = 'HALF_OPEN';
    breaker.failures = 0;
    breaker.halfOpenAttempts = 0;
    breaker.successiveSuccesses = 0;
    breaker.nextAttempt = 0;

    // Test connection
    try {
      const isHealthy = await this.testConnection(service);
      if (isHealthy) {
        breaker.state = 'CLOSED';
        logger.info(`Forced recovery successful for service: ${service}`);

        this.emit('forcedRecovery', {
          service,
          success: true,
          timestamp: Date.now()
        });

        return true;
      } else {
        breaker.state = 'OPEN';
        breaker.nextAttempt = Date.now() + parseInt(config.CIRCUIT_BREAKER_RESET_TIMEOUT);
        logger.warn(`Forced recovery failed for service: ${service}`);

        this.emit('forcedRecovery', {
          service,
          success: false,
          timestamp: Date.now()
        });

        return false;
      }
    } catch (error) {
      logger.error(`Error during forced recovery for ${service}:`, error);
      breaker.state = 'OPEN';
      breaker.nextAttempt = Date.now() + parseInt(config.CIRCUIT_BREAKER_RESET_TIMEOUT);

      this.emit('forcedRecovery', {
        service,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      });

      return false;
    }
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down database connections...');

    await Promise.allSettled([
      this.influxWriteApi?.close(),
      this.redis?.quit(),
      this.postgres?.end()
    ]);

    logger.info('Database connections closed');
  }
}

export const databaseManager = new DatabaseConnectionManager();
