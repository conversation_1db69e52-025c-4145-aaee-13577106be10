/**
 * InfluxDB Measurements Configuration for MEV Arbitrage Bot
 * 
 * This file defines the structure and retention policies for all time-series data
 * stored in InfluxDB, following the comprehensive database architecture guidelines.
 */

export interface MeasurementConfig {
  name: string;
  tags: string[];
  fields: string[];
  retentionPolicy: string;
  description: string;
}

export interface RetentionPolicy {
  name: string;
  duration: string;
  replication: number;
  shardDuration: string;
  default: boolean;
}

/**
 * Tiered Retention Policies
 * - Raw data: 7 days retention
 * - 1-minute aggregates: 30 days retention  
 * - Hourly aggregates: 1 year retention
 */
export const RETENTION_POLICIES: RetentionPolicy[] = [
  {
    name: 'raw_data',
    duration: '7d',
    replication: 1,
    shardDuration: '1h',
    default: true
  },
  {
    name: 'minute_aggregates',
    duration: '30d',
    replication: 1,
    shardDuration: '1d',
    default: false
  },
  {
    name: 'hourly_aggregates',
    duration: '365d',
    replication: 1,
    shardDuration: '7d',
    default: false
  }
];

/**
 * Price Data Measurement
 * Tracks token prices across networks with comprehensive market data
 */
export const PRICE_DATA_MEASUREMENT: MeasurementConfig = {
  name: 'price_data',
  tags: [
    'token_symbol',
    'network',
    'price_source',
    'dex',
    'pair_address'
  ],
  fields: [
    'current_price',
    'volume_24h',
    'market_cap',
    'price_change_24h',
    'liquidity',
    'bid_price',
    'ask_price',
    'spread_percentage',
    'last_updated'
  ],
  retentionPolicy: 'raw_data',
  description: 'Real-time token price data across multiple networks and DEXes'
};

/**
 * Opportunity Metrics Measurement
 * Records metrics about detected arbitrage opportunities
 */
export const OPPORTUNITY_METRICS_MEASUREMENT: MeasurementConfig = {
  name: 'opportunity_metrics',
  tags: [
    'opportunity_id',
    'network',
    'status',
    'opportunity_type',
    'token_symbol',
    'source_dex',
    'target_dex'
  ],
  fields: [
    'detection_latency_ms',
    'validation_latency_ms',
    'expected_profit_usd',
    'profit_percentage',
    'confidence_score',
    'risk_score',
    'gas_estimate',
    'slippage_impact',
    'liquidity_score'
  ],
  retentionPolicy: 'raw_data',
  description: 'Metrics for detected arbitrage opportunities including latency and profitability'
};

/**
 * System Metrics Measurement
 * Monitors system performance and health across all services
 */
export const SYSTEM_METRICS_MEASUREMENT: MeasurementConfig = {
  name: 'system_metrics',
  tags: [
    'service_name',
    'environment',
    'instance_id',
    'network',
    'metric_type'
  ],
  fields: [
    'latency_ms',
    'error_rate',
    'throughput_ops_sec',
    'memory_usage_mb',
    'cpu_usage_percent',
    'active_connections',
    'queue_size',
    'cache_hit_ratio',
    'uptime_seconds'
  ],
  retentionPolicy: 'raw_data',
  description: 'System performance metrics for monitoring service health and performance'
};

/**
 * Execution Metrics Measurement
 * Tracks trade execution performance and outcomes
 */
export const EXECUTION_METRICS_MEASUREMENT: MeasurementConfig = {
  name: 'execution_metrics',
  tags: [
    'trade_id',
    'execution_method',
    'network',
    'token_symbol',
    'strategy_type',
    'flash_loan_provider'
  ],
  fields: [
    'execution_time_ms',
    'gas_used',
    'gas_price_gwei',
    'actual_profit_usd',
    'slippage_actual',
    'mev_protection_fee',
    'flash_loan_fee',
    'total_fees_usd',
    'net_profit_usd',
    'success'
  ],
  retentionPolicy: 'raw_data',
  description: 'Trade execution performance metrics including timing, costs, and outcomes'
};

/**
 * Network Health Measurement
 * Monitors blockchain network conditions and congestion
 */
export const NETWORK_HEALTH_MEASUREMENT: MeasurementConfig = {
  name: 'network_health',
  tags: [
    'network',
    'node_provider',
    'region'
  ],
  fields: [
    'block_time_ms',
    'gas_price_gwei',
    'pending_transactions',
    'network_congestion_score',
    'rpc_latency_ms',
    'sync_status',
    'peer_count',
    'chain_height'
  ],
  retentionPolicy: 'raw_data',
  description: 'Blockchain network health and performance metrics'
};

/**
 * Cache Performance Measurement
 * Tracks caching system performance across tiers
 */
export const CACHE_PERFORMANCE_MEASUREMENT: MeasurementConfig = {
  name: 'cache_performance',
  tags: [
    'cache_tier',
    'cache_type',
    'service_name',
    'data_type'
  ],
  fields: [
    'hit_ratio',
    'miss_ratio',
    'eviction_rate',
    'memory_usage_mb',
    'operation_latency_ms',
    'total_requests',
    'cache_size_mb'
  ],
  retentionPolicy: 'raw_data',
  description: 'Multi-tier cache performance metrics'
};

/**
 * All measurement configurations
 */
export const ALL_MEASUREMENTS: MeasurementConfig[] = [
  PRICE_DATA_MEASUREMENT,
  OPPORTUNITY_METRICS_MEASUREMENT,
  SYSTEM_METRICS_MEASUREMENT,
  EXECUTION_METRICS_MEASUREMENT,
  NETWORK_HEALTH_MEASUREMENT,
  CACHE_PERFORMANCE_MEASUREMENT
];

/**
 * Measurement field types for validation
 */
export const FIELD_TYPES = {
  // Numeric fields
  FLOAT: 'float',
  INTEGER: 'integer',
  // String fields  
  STRING: 'string',
  // Boolean fields
  BOOLEAN: 'boolean',
  // Timestamp fields
  TIMESTAMP: 'timestamp'
} as const;

/**
 * Default aggregation functions for continuous queries
 */
export const AGGREGATION_FUNCTIONS = {
  MEAN: 'mean',
  SUM: 'sum',
  COUNT: 'count',
  MIN: 'min',
  MAX: 'max',
  MEDIAN: 'median',
  STDDEV: 'stddev',
  PERCENTILE_95: 'percentile(95)',
  PERCENTILE_99: 'percentile(99)'
} as const;

/**
 * Continuous queries for automatic downsampling
 */
export const CONTINUOUS_QUERIES = [
  {
    name: 'price_data_1m',
    measurement: 'price_data',
    interval: '1m',
    retention: 'minute_aggregates',
    aggregations: ['mean', 'min', 'max', 'count']
  },
  {
    name: 'system_metrics_1m',
    measurement: 'system_metrics', 
    interval: '1m',
    retention: 'minute_aggregates',
    aggregations: ['mean', 'max', 'count']
  },
  {
    name: 'opportunity_metrics_1h',
    measurement: 'opportunity_metrics',
    interval: '1h', 
    retention: 'hourly_aggregates',
    aggregations: ['count', 'mean', 'sum']
  },
  {
    name: 'execution_metrics_1h',
    measurement: 'execution_metrics',
    interval: '1h',
    retention: 'hourly_aggregates', 
    aggregations: ['count', 'sum', 'mean']
  }
];
