import { Router } from 'express';
import logger from '../utils/logger.js';
import { ExecutionService, TradeStatus } from '../services/ExecutionService.js';

export default function createTradeRoutes(executionService: ExecutionService) {
  const router = Router();

  // Get all trades
  router.get('/', async (req, res) => {
    try {
      const { 
        status, 
        type, 
        network, 
        asset,
        limit = '100',
        sortBy = 'timestamp',
        order = 'desc'
      } = req.query;

      let trades = executionService.getTrades();

      // Apply filters
      if (status && Object.values(TradeStatus).includes(status as TradeStatus)) {
        trades = trades.filter(trade => trade.status === status);
      }

      if (type) {
        trades = trades.filter(trade => trade.type === type);
      }

      if (network) {
        trades = trades.filter(trade => trade.network === network);
      }

      if (asset) {
        const assetStr = (asset as string).toUpperCase();
        trades = trades.filter(trade => 
          trade.assets.some(a => a.toUpperCase().includes(assetStr))
        );
      }

      // Sort trades
      const validSortFields = ['timestamp', 'executedProfit', 'gasFees'];
      const sortField = validSortFields.includes(sortBy as string) ? sortBy as string : 'timestamp';
      const sortOrder = order === 'asc' ? 1 : -1;

      trades.sort((a, b) => {
        const aVal = (a as any)[sortField];
        const bVal = (b as any)[sortField];
        return (aVal - bVal) * sortOrder;
      });

      // Apply limit
      const limitNum = Math.min(parseInt(limit as string) || 100, 500);
      trades = trades.slice(0, limitNum);

      res.json({
        success: true,
        data: trades,
        count: trades.length,
        filters: { status, type, network, asset },
        pagination: { limit: limitNum, sortBy: sortField, order }
      });
    } catch (error) {
      logger.error('Error getting trades:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve trades'
      });
    }
  });

  // Get specific trade
  router.get('/:id', async (req, res) => {
    try {
      const { id } = req.params;
      
      const trade = executionService.getTrade(id);
      
      if (!trade) {
        return res.status(404).json({
          success: false,
          error: 'Trade not found'
        });
      }

      res.json({
        success: true,
        data: trade
      });
    } catch (error) {
      logger.error('Error getting trade:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve trade'
      });
    }
  });

  // Get trades by status
  router.get('/status/:status', async (req, res) => {
    try {
      const { status } = req.params;
      
      if (!Object.values(TradeStatus).includes(status as TradeStatus)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid trade status'
        });
      }

      const trades = executionService.getTrades()
        .filter(trade => trade.status === status);

      res.json({
        success: true,
        data: trades,
        count: trades.length,
        status
      });
    } catch (error) {
      logger.error('Error getting trades by status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve trades by status'
      });
    }
  });

  // Get successful trades
  router.get('/successful/all', async (req, res) => {
    try {
      const { limit = '50' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 50, 200);
      
      const trades = executionService.getTrades()
        .filter(trade => trade.status === TradeStatus.SUCCESS)
        .sort((a, b) => b.executedProfit - a.executedProfit)
        .slice(0, limitNum);

      const totalProfit = trades.reduce((sum, trade) => sum + trade.executedProfit, 0);
      const avgProfit = trades.length > 0 ? totalProfit / trades.length : 0;

      res.json({
        success: true,
        data: trades,
        count: trades.length,
        summary: {
          totalProfit,
          avgProfit,
          limit: limitNum
        }
      });
    } catch (error) {
      logger.error('Error getting successful trades:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve successful trades'
      });
    }
  });

  // Get failed trades
  router.get('/failed/all', async (req, res) => {
    try {
      const { limit = '50' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 50, 200);
      
      const trades = executionService.getTrades()
        .filter(trade => trade.status === TradeStatus.FAILED)
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, limitNum);

      const totalGasFees = trades.reduce((sum, trade) => sum + (trade.gasFees || 0), 0);
      const avgGasFees = trades.length > 0 ? totalGasFees / trades.length : 0;

      // Group by error message
      const errorGroups: Record<string, number> = {};
      trades.forEach(trade => {
        const error = trade.errorMessage || 'Unknown error';
        errorGroups[error] = (errorGroups[error] || 0) + 1;
      });

      res.json({
        success: true,
        data: trades,
        count: trades.length,
        summary: {
          totalGasFees,
          avgGasFees,
          errorGroups,
          limit: limitNum
        }
      });
    } catch (error) {
      logger.error('Error getting failed trades:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve failed trades'
      });
    }
  });

  // Get active trades
  router.get('/active/all', async (req, res) => {
    try {
      const trades = executionService.getTrades()
        .filter(trade => 
          trade.status === TradeStatus.PENDING || 
          trade.status === TradeStatus.EXECUTING
        );

      res.json({
        success: true,
        data: trades,
        count: trades.length
      });
    } catch (error) {
      logger.error('Error getting active trades:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve active trades'
      });
    }
  });

  // Get trade statistics
  router.get('/stats/summary', async (req, res) => {
    try {
      const stats = executionService.getStats();
      const trades = executionService.getTrades();

      // Calculate additional statistics
      const typeDistribution: Record<string, number> = {};
      const networkDistribution: Record<string, number> = {};
      const hourlyStats: Record<string, { count: number; profit: number }> = {};

      trades.forEach(trade => {
        // Type distribution
        typeDistribution[trade.type] = (typeDistribution[trade.type] || 0) + 1;
        
        // Network distribution
        networkDistribution[trade.network] = (networkDistribution[trade.network] || 0) + 1;
        
        // Hourly stats (last 24 hours)
        const hour = new Date(trade.timestamp).getHours();
        const hourKey = hour.toString().padStart(2, '0');
        if (!hourlyStats[hourKey]) {
          hourlyStats[hourKey] = { count: 0, profit: 0 };
        }
        hourlyStats[hourKey].count++;
        if (trade.status === TradeStatus.SUCCESS) {
          hourlyStats[hourKey].profit += trade.executedProfit;
        }
      });

      const enhancedStats = {
        ...stats,
        typeDistribution,
        networkDistribution,
        hourlyStats,
        avgExecutionTime: trades
          .filter(trade => trade.executionTime)
          .reduce((sum, trade, _, arr) => sum + (trade.executionTime! / arr.length), 0)
      };

      res.json({
        success: true,
        data: enhancedStats
      });
    } catch (error) {
      logger.error('Error getting trade stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve trade statistics'
      });
    }
  });

  // Get recent trades
  router.get('/recent/:minutes', async (req, res) => {
    try {
      const { minutes } = req.params;
      const minutesNum = parseInt(minutes);
      
      if (isNaN(minutesNum) || minutesNum <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid minutes parameter'
        });
      }

      const cutoff = Date.now() - minutesNum * 60 * 1000;
      const trades = executionService.getTrades()
        .filter(trade => trade.timestamp >= cutoff)
        .sort((a, b) => b.timestamp - a.timestamp);

      const successfulTrades = trades.filter(trade => trade.status === TradeStatus.SUCCESS);
      const totalProfit = successfulTrades.reduce((sum, trade) => sum + trade.executedProfit, 0);

      res.json({
        success: true,
        data: trades,
        count: trades.length,
        timeframe: `${minutesNum} minutes`,
        summary: {
          successfulTrades: successfulTrades.length,
          totalProfit,
          successRate: trades.length > 0 ? (successfulTrades.length / trades.length) * 100 : 0
        }
      });
    } catch (error) {
      logger.error('Error getting recent trades:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve recent trades'
      });
    }
  });

  // Get execution queue status
  router.get('/queue/status', async (req, res) => {
    try {
      const queueSize = executionService.getQueueSize();
      const activeTradesCount = executionService.getActiveTradesCount();
      
      res.json({
        success: true,
        data: {
          queueSize,
          activeTradesCount,
          isHealthy: executionService.isHealthy(),
          timestamp: Date.now()
        }
      });
    } catch (error) {
      logger.error('Error getting queue status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve queue status'
      });
    }
  });

  return router;
}
