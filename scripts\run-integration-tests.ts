#!/usr/bin/env tsx

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import logger from '../backend/utils/logger.js';

interface TestResult {
  testSuite: string;
  passed: boolean;
  duration: number;
  output: string;
  errors?: string;
}

interface TestSummary {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
  results: TestResult[];
}

class IntegrationTestRunner {
  private testSuites = [
    {
      name: 'Enhanced System Integration',
      path: 'tests/integration/enhanced-system-integration.test.ts',
      timeout: 300000 // 5 minutes
    },
    {
      name: 'Complete Arbitrage Workflow E2E',
      path: 'tests/e2e/complete-arbitrage-workflow.test.ts',
      timeout: 600000 // 10 minutes
    },
    {
      name: 'Enhanced Performance Benchmarks',
      path: 'tests/performance/enhanced-benchmark.test.ts',
      timeout: 900000 // 15 minutes
    },
    {
      name: 'System Integration (Legacy)',
      path: 'tests/integration/system.test.ts',
      timeout: 180000 // 3 minutes
    },
    {
      name: 'Full System E2E (Legacy)',
      path: 'tests/e2e/full-system.test.ts',
      timeout: 300000 // 5 minutes
    }
  ];

  private results: TestResult[] = [];

  async runAllTests(): Promise<TestSummary> {
    logger.info('🚀 Starting comprehensive integration test suite...');
    
    const startTime = Date.now();

    // Pre-test system verification
    await this.verifySystemReadiness();

    // Run each test suite
    for (const testSuite of this.testSuites) {
      logger.info(`\n📋 Running test suite: ${testSuite.name}`);
      
      const result = await this.runTestSuite(testSuite);
      this.results.push(result);
      
      if (result.passed) {
        logger.info(`✅ ${testSuite.name} - PASSED (${result.duration}ms)`);
      } else {
        logger.error(`❌ ${testSuite.name} - FAILED (${result.duration}ms)`);
        logger.error(`Error: ${result.errors}`);
      }
    }

    const endTime = Date.now();
    const totalDuration = endTime - startTime;

    // Generate summary
    const summary = this.generateSummary(totalDuration);
    
    // Save detailed results
    await this.saveTestResults(summary);
    
    // Display summary
    this.displaySummary(summary);

    return summary;
  }

  private async verifySystemReadiness(): Promise<void> {
    logger.info('🔍 Verifying system readiness...');
    
    try {
      // Check if required files exist
      const requiredFiles = [
        'backend/config/index.ts',
        'backend/services/ServiceIntegrator.ts',
        'backend/services/ProfitValidationService.ts',
        'backend/services/EnhancedTokenMonitoringService.ts',
        'backend/services/ProfitPrioritizedExecutionQueue.ts',
        'backend/services/FlashLoanService.ts',
        'backend/services/MEVProtectionService.ts',
        'backend/services/PreExecutionValidationService.ts'
      ];

      for (const file of requiredFiles) {
        try {
          await fs.access(file);
        } catch (error) {
          throw new Error(`Required file missing: ${file}`);
        }
      }

      // Check environment variables
      const requiredEnvVars = [
        'NODE_ENV',
        'ENABLE_PROFIT_VALIDATION',
        'ENABLE_ENHANCED_TOKEN_MONITORING',
        'ENABLE_FLASH_LOANS'
      ];

      for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
          logger.warn(`Environment variable ${envVar} not set, using default`);
        }
      }

      logger.info('✅ System readiness verification completed');
    } catch (error) {
      logger.error('❌ System readiness verification failed:', error);
      throw error;
    }
  }

  private async runTestSuite(testSuite: { name: string; path: string; timeout: number }): Promise<TestResult> {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let output = '';
      let errors = '';

      // Run Jest with specific test file
      const jestProcess = spawn('npx', ['jest', testSuite.path, '--verbose', '--detectOpenHandles'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          NODE_ENV: 'test',
          JEST_TIMEOUT: testSuite.timeout.toString()
        }
      });

      // Capture output
      jestProcess.stdout?.on('data', (data) => {
        const chunk = data.toString();
        output += chunk;
        process.stdout.write(chunk); // Real-time output
      });

      jestProcess.stderr?.on('data', (data) => {
        const chunk = data.toString();
        errors += chunk;
        process.stderr.write(chunk); // Real-time error output
      });

      // Handle process completion
      jestProcess.on('close', (code) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        resolve({
          testSuite: testSuite.name,
          passed: code === 0,
          duration,
          output,
          errors: code !== 0 ? errors : undefined
        });
      });

      // Handle timeout
      setTimeout(() => {
        jestProcess.kill('SIGTERM');
        const endTime = Date.now();
        const duration = endTime - startTime;

        resolve({
          testSuite: testSuite.name,
          passed: false,
          duration,
          output,
          errors: `Test suite timed out after ${testSuite.timeout}ms`
        });
      }, testSuite.timeout);
    });
  }

  private generateSummary(totalDuration: number): TestSummary {
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = this.results.filter(r => !r.passed).length;

    return {
      totalTests: this.results.length,
      passedTests,
      failedTests,
      totalDuration,
      results: this.results
    };
  }

  private async saveTestResults(summary: TestSummary): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const resultsDir = 'test-results';
    
    try {
      await fs.mkdir(resultsDir, { recursive: true });
      
      const resultsFile = path.join(resultsDir, `integration-test-results-${timestamp}.json`);
      await fs.writeFile(resultsFile, JSON.stringify(summary, null, 2));
      
      logger.info(`📄 Test results saved to: ${resultsFile}`);
    } catch (error) {
      logger.error('Failed to save test results:', error);
    }
  }

  private displaySummary(summary: TestSummary): void {
    logger.info('\n' + '='.repeat(80));
    logger.info('🏁 INTEGRATION TEST SUMMARY');
    logger.info('='.repeat(80));
    
    logger.info(`📊 Total Test Suites: ${summary.totalTests}`);
    logger.info(`✅ Passed: ${summary.passedTests}`);
    logger.info(`❌ Failed: ${summary.failedTests}`);
    logger.info(`⏱️  Total Duration: ${(summary.totalDuration / 1000).toFixed(2)}s`);
    
    const successRate = (summary.passedTests / summary.totalTests) * 100;
    logger.info(`📈 Success Rate: ${successRate.toFixed(1)}%`);
    
    logger.info('\n📋 Detailed Results:');
    summary.results.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      const duration = (result.duration / 1000).toFixed(2);
      logger.info(`${index + 1}. ${status} ${result.testSuite} (${duration}s)`);
      
      if (!result.passed && result.errors) {
        logger.info(`   Error: ${result.errors.split('\n')[0]}`);
      }
    });
    
    logger.info('\n' + '='.repeat(80));
    
    if (summary.failedTests === 0) {
      logger.info('🎉 ALL INTEGRATION TESTS PASSED! System is ready for deployment.');
    } else {
      logger.error(`⚠️  ${summary.failedTests} test suite(s) failed. Please review and fix issues before deployment.`);
    }
  }
}

// Main execution
async function main() {
  const runner = new IntegrationTestRunner();
  
  try {
    const summary = await runner.runAllTests();
    
    // Exit with appropriate code
    process.exit(summary.failedTests === 0 ? 0 : 1);
  } catch (error) {
    logger.error('Integration test runner failed:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { IntegrationTestRunner, TestResult, TestSummary };
