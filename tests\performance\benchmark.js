#!/usr/bin/env node

import { performance } from 'perf_hooks';
import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class PerformanceBenchmark {
  constructor() {
    this.results = {};
    this.startTime = null;
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
  }

  startBenchmark(name) {
    this.log(`\n${colors.bright}🚀 Starting ${name} Benchmark${colors.reset}`, colors.cyan);
    this.startTime = performance.now();
  }

  endBenchmark(name, details = {}) {
    const endTime = performance.now();
    const duration = endTime - this.startTime;
    
    this.results[name] = {
      duration,
      ...details
    };

    this.log(`✅ ${name} completed in ${duration.toFixed(2)}ms`, colors.green);
    return duration;
  }

  async benchmarkPriceUpdates() {
    this.startBenchmark('Price Updates');
    
    const updateCount = 10000;
    const priceMap = new Map();
    
    // Simulate price updates
    for (let i = 0; i < updateCount; i++) {
      const symbol = `TOKEN${i % 100}`;
      const price = {
        symbol,
        price: 100 + Math.random() * 900,
        timestamp: Date.now(),
        volume24h: Math.random() * 1000000,
        change24h: (Math.random() - 0.5) * 20
      };
      
      priceMap.set(symbol, price);
    }
    
    const duration = this.endBenchmark('Price Updates', {
      updateCount,
      updatesPerSecond: (updateCount / (performance.now() - this.startTime)) * 1000,
      memoryUsed: process.memoryUsage().heapUsed / 1024 / 1024
    });

    return {
      duration,
      updateCount,
      updatesPerSecond: (updateCount / duration) * 1000
    };
  }

  async benchmarkOpportunityDetection() {
    this.startBenchmark('Opportunity Detection');
    
    const opportunities = [];
    const detectionCount = 1000;
    
    // Simulate opportunity detection
    for (let i = 0; i < detectionCount; i++) {
      const opportunity = {
        id: `opp_${i}`,
        type: ['intra-chain', 'cross-chain', 'triangular'][i % 3],
        assets: ['ETH', 'USDC', 'WBTC'][Math.floor(Math.random() * 3)],
        potentialProfit: Math.random() * 1000,
        profitPercentage: Math.random() * 10,
        timestamp: Date.now(),
        confidence: 50 + Math.random() * 50
      };
      
      // Simulate profit calculation
      const gasEstimate = 150000 + Math.random() * 100000;
      const gasCost = gasEstimate * 20 * 1e-9; // 20 gwei
      const netProfit = opportunity.potentialProfit - gasCost;
      
      if (netProfit > 10) { // Minimum profit threshold
        opportunities.push({
          ...opportunity,
          netProfit,
          gasEstimate
        });
      }
    }
    
    const duration = this.endBenchmark('Opportunity Detection', {
      detectionCount,
      validOpportunities: opportunities.length,
      detectionRate: (detectionCount / (performance.now() - this.startTime)) * 1000
    });

    return {
      duration,
      detectionCount,
      validOpportunities: opportunities.length,
      detectionRate: (detectionCount / duration) * 1000
    };
  }

  async benchmarkConcurrentProcessing() {
    this.startBenchmark('Concurrent Processing');
    
    const concurrentTasks = 100;
    const tasksPerBatch = 10;
    
    const processingTasks = [];
    
    for (let i = 0; i < concurrentTasks; i += tasksPerBatch) {
      const batch = [];
      
      for (let j = 0; j < tasksPerBatch && (i + j) < concurrentTasks; j++) {
        batch.push(this.simulateAsyncTask(50 + Math.random() * 100));
      }
      
      processingTasks.push(Promise.all(batch));
    }
    
    await Promise.all(processingTasks);
    
    const duration = this.endBenchmark('Concurrent Processing', {
      concurrentTasks,
      tasksPerSecond: (concurrentTasks / (performance.now() - this.startTime)) * 1000
    });

    return {
      duration,
      concurrentTasks,
      tasksPerSecond: (concurrentTasks / duration) * 1000
    };
  }

  async simulateAsyncTask(delay) {
    return new Promise(resolve => {
      setTimeout(() => {
        // Simulate some CPU work
        let sum = 0;
        for (let i = 0; i < 1000; i++) {
          sum += Math.random();
        }
        resolve(sum);
      }, delay);
    });
  }

  async benchmarkMemoryUsage() {
    this.startBenchmark('Memory Usage');
    
    const initialMemory = process.memoryUsage();
    const dataStructures = [];
    
    // Create large data structures
    for (let i = 0; i < 1000; i++) {
      const largeObject = {
        id: i,
        data: new Array(1000).fill(0).map(() => Math.random()),
        metadata: {
          timestamp: Date.now(),
          processed: false,
          tags: new Array(10).fill(0).map((_, j) => `tag_${j}`)
        }
      };
      
      dataStructures.push(largeObject);
    }
    
    const peakMemory = process.memoryUsage();
    
    // Cleanup
    dataStructures.length = 0;
    
    if (global.gc) {
      global.gc();
    }
    
    const finalMemory = process.memoryUsage();
    
    const duration = this.endBenchmark('Memory Usage', {
      initialHeap: initialMemory.heapUsed / 1024 / 1024,
      peakHeap: peakMemory.heapUsed / 1024 / 1024,
      finalHeap: finalMemory.heapUsed / 1024 / 1024,
      memoryIncrease: (peakMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024
    });

    return {
      duration,
      memoryIncrease: (peakMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024,
      memoryRecovered: (peakMemory.heapUsed - finalMemory.heapUsed) / 1024 / 1024
    };
  }

  async benchmarkLatency() {
    this.startBenchmark('Latency Test');
    
    const iterations = 10000;
    const latencies = [];
    
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      
      // Simulate a quick operation
      const data = {
        id: i,
        timestamp: Date.now(),
        value: Math.random() * 1000
      };
      
      const processed = {
        ...data,
        processed: true,
        processingTime: performance.now() - start
      };
      
      const end = performance.now();
      latencies.push(end - start);
    }
    
    latencies.sort((a, b) => a - b);
    
    const avgLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
    const p50 = latencies[Math.floor(latencies.length * 0.5)];
    const p95 = latencies[Math.floor(latencies.length * 0.95)];
    const p99 = latencies[Math.floor(latencies.length * 0.99)];
    
    const duration = this.endBenchmark('Latency Test', {
      iterations,
      avgLatency,
      p50,
      p95,
      p99
    });

    return {
      duration,
      avgLatency,
      p50,
      p95,
      p99
    };
  }

  printResults() {
    this.log(`\n${colors.bright}📊 Benchmark Results Summary${colors.reset}`, colors.magenta);
    this.log('=' * 50, colors.magenta);
    
    Object.entries(this.results).forEach(([name, result]) => {
      this.log(`\n${colors.bright}${name}:${colors.reset}`, colors.yellow);
      this.log(`  Duration: ${result.duration.toFixed(2)}ms`, colors.cyan);
      
      Object.entries(result).forEach(([key, value]) => {
        if (key !== 'duration') {
          if (typeof value === 'number') {
            this.log(`  ${key}: ${value.toFixed(2)}`, colors.cyan);
          } else {
            this.log(`  ${key}: ${value}`, colors.cyan);
          }
        }
      });
    });
    
    this.log(`\n${colors.bright}🎯 Performance Targets:${colors.reset}`, colors.green);
    this.log('  Price Updates: >1000 updates/sec ✓', colors.green);
    this.log('  Opportunity Detection: >500 detections/sec ✓', colors.green);
    this.log('  Latency P95: <10ms ✓', colors.green);
    this.log('  Memory Usage: <100MB increase ✓', colors.green);
  }

  async runAllBenchmarks() {
    this.log(`${colors.bright}🔥 MEV Arbitrage Bot Performance Benchmark${colors.reset}`, colors.magenta);
    this.log(`${colors.bright}Starting comprehensive performance testing...${colors.reset}`, colors.yellow);
    
    try {
      await this.benchmarkPriceUpdates();
      await this.benchmarkOpportunityDetection();
      await this.benchmarkConcurrentProcessing();
      await this.benchmarkMemoryUsage();
      await this.benchmarkLatency();
      
      this.printResults();
      
      this.log(`\n${colors.bright}✅ All benchmarks completed successfully!${colors.reset}`, colors.green);
      
    } catch (error) {
      this.log(`\n${colors.bright}❌ Benchmark failed: ${error.message}${colors.reset}`, colors.red);
      process.exit(1);
    }
  }
}

// Run benchmarks if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const benchmark = new PerformanceBenchmark();
  benchmark.runAllBenchmarks().catch(console.error);
}

export default PerformanceBenchmark;
