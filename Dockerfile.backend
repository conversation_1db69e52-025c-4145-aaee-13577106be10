# Backend Dockerfile
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy backend source code
COPY backend/ ./backend/
COPY contracts/ ./contracts/
COPY scripts/ ./scripts/
COPY hardhat.config.ts ./
COPY tsconfig.json ./

# Create logs directory
RUN mkdir -p logs

# Build backend
RUN npm run build:backend

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start backend
CMD ["npm", "start"]
