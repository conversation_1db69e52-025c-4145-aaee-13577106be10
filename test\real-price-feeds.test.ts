import { expect } from "chai";
import { ethers } from "hardhat";
import axios from "axios";

/**
 * Real-World Price Feed Integration Tests
 * 
 * Tests integration with actual price feed APIs and services
 * to validate accuracy and reliability in real market conditions.
 */

interface ExternalPriceSource {
  name: string;
  url: string;
  parseResponse: (data: any) => number;
  rateLimit: number; // requests per minute
}

class RealPriceFeedTester {
  private sources: ExternalPriceSource[] = [];
  private requestCounts: Map<string, number> = new Map();
  private lastRequestTime: Map<string, number> = new Map();

  constructor() {
    this.initializeExternalSources();
  }

  private initializeExternalSources(): void {
    // CoinGecko API
    this.sources.push({
      name: "CoinGecko",
      url: "https://api.coingecko.com/api/v3/simple/price",
      parseResponse: (data: any) => data.ethereum?.usd || 0,
      rateLimit: 50, // 50 requests per minute for free tier
    });

    // CoinMarketCap API (requires API key)
    if (process.env.COINMARKETCAP_API_KEY) {
      this.sources.push({
        name: "CoinMarketCap",
        url: "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest",
        parseResponse: (data: any) => data.data?.["1"]?.quote?.USD?.price || 0,
        rateLimit: 333, // 10,000 requests per month ≈ 333 per day
      });
    }

    // Binance API
    this.sources.push({
      name: "Binance",
      url: "https://api.binance.com/api/v3/ticker/price",
      parseResponse: (data: any) => parseFloat(data.price) || 0,
      rateLimit: 1200, // 1200 requests per minute
    });

    // Kraken API
    this.sources.push({
      name: "Kraken",
      url: "https://api.kraken.com/0/public/Ticker",
      parseResponse: (data: any) => parseFloat(data.result?.XETHZUSD?.c?.[0]) || 0,
      rateLimit: 60, // 60 requests per minute
    });
  }

  private async checkRateLimit(sourceName: string, rateLimit: number): Promise<boolean> {
    const now = Date.now();
    const lastRequest = this.lastRequestTime.get(sourceName) || 0;
    const requestCount = this.requestCounts.get(sourceName) || 0;

    // Reset counter every minute
    if (now - lastRequest > 60000) {
      this.requestCounts.set(sourceName, 0);
    }

    if (requestCount >= rateLimit) {
      return false; // Rate limit exceeded
    }

    this.requestCounts.set(sourceName, requestCount + 1);
    this.lastRequestTime.set(sourceName, now);
    return true;
  }

  async fetchPriceFromSource(source: ExternalPriceSource, symbol: string = "ethereum"): Promise<number> {
    if (!await this.checkRateLimit(source.name, source.rateLimit)) {
      throw new Error(`Rate limit exceeded for ${source.name}`);
    }

    try {
      let url = source.url;
      let headers: any = {};

      // Customize URL and headers based on source
      switch (source.name) {
        case "CoinGecko":
          url += `?ids=${symbol}&vs_currencies=usd`;
          break;
        case "CoinMarketCap":
          url += "?symbol=ETH";
          headers["X-CMC_PRO_API_KEY"] = process.env.COINMARKETCAP_API_KEY;
          break;
        case "Binance":
          url += "?symbol=ETHUSDT";
          break;
        case "Kraken":
          url += "?pair=ETHUSD";
          break;
      }

      const response = await axios.get(url, { 
        headers,
        timeout: 5000, // 5 second timeout
      });

      return source.parseResponse(response.data);
    } catch (error) {
      console.error(`Error fetching from ${source.name}:`, error);
      return 0;
    }
  }

  async validatePriceConsistency(symbol: string = "ethereum", threshold: number = 0.05): Promise<{
    prices: { [source: string]: number };
    consensus: number;
    deviations: { [source: string]: number };
    isConsistent: boolean;
  }> {
    const prices: { [source: string]: number } = {};
    const validPrices: number[] = [];

    // Fetch prices from all sources
    for (const source of this.sources) {
      try {
        const price = await this.fetchPriceFromSource(source, symbol);
        if (price > 0) {
          prices[source.name] = price;
          validPrices.push(price);
        }
      } catch (error) {
        console.warn(`Failed to fetch from ${source.name}:`, error);
      }
    }

    if (validPrices.length === 0) {
      throw new Error("No valid prices obtained from any source");
    }

    // Calculate consensus (median)
    validPrices.sort((a, b) => a - b);
    const mid = Math.floor(validPrices.length / 2);
    const consensus = validPrices.length % 2 === 0
      ? (validPrices[mid - 1] + validPrices[mid]) / 2
      : validPrices[mid];

    // Calculate deviations
    const deviations: { [source: string]: number } = {};
    let isConsistent = true;

    for (const [sourceName, price] of Object.entries(prices)) {
      const deviation = Math.abs(price - consensus) / consensus;
      deviations[sourceName] = deviation;
      
      if (deviation > threshold) {
        isConsistent = false;
      }
    }

    return { prices, consensus, deviations, isConsistent };
  }

  async measureLatency(source: ExternalPriceSource, iterations: number = 5): Promise<{
    averageLatency: number;
    minLatency: number;
    maxLatency: number;
    successRate: number;
  }> {
    const latencies: number[] = [];
    let successCount = 0;

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      
      try {
        await this.fetchPriceFromSource(source);
        const latency = Date.now() - startTime;
        latencies.push(latency);
        successCount++;
      } catch (error) {
        // Failed request
      }

      // Wait between requests to respect rate limits
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (latencies.length === 0) {
      return { averageLatency: 0, minLatency: 0, maxLatency: 0, successRate: 0 };
    }

    return {
      averageLatency: latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length,
      minLatency: Math.min(...latencies),
      maxLatency: Math.max(...latencies),
      successRate: successCount / iterations,
    };
  }
}

describe("Real-World Price Feed Integration", function () {
  let priceFeedTester: RealPriceFeedTester;

  before(function () {
    priceFeedTester = new RealPriceFeedTester();
  });

  describe("External API Integration", function () {
    it("Should fetch prices from CoinGecko successfully", async function () {
      this.timeout(10000); // 10 second timeout for API calls
      
      try {
        const source = priceFeedTester["sources"].find(s => s.name === "CoinGecko");
        if (!source) {
          this.skip();
        }

        const price = await priceFeedTester.fetchPriceFromSource(source);
        expect(price).to.be.greaterThan(0);
        expect(price).to.be.lessThan(10000); // Reasonable ETH price range
        
        console.log(`CoinGecko ETH price: $${price.toFixed(2)}`);
      } catch (error) {
        console.warn("CoinGecko API test skipped:", error);
        this.skip();
      }
    });

    it("Should fetch prices from Binance successfully", async function () {
      this.timeout(10000);
      
      try {
        const source = priceFeedTester["sources"].find(s => s.name === "Binance");
        if (!source) {
          this.skip();
        }

        const price = await priceFeedTester.fetchPriceFromSource(source);
        expect(price).to.be.greaterThan(0);
        expect(price).to.be.lessThan(10000);
        
        console.log(`Binance ETH price: $${price.toFixed(2)}`);
      } catch (error) {
        console.warn("Binance API test skipped:", error);
        this.skip();
      }
    });

    it("Should handle API failures gracefully", async function () {
      const invalidSource = {
        name: "Invalid",
        url: "https://invalid-api-endpoint.com/price",
        parseResponse: (data: any) => data.price,
        rateLimit: 60,
      };

      const price = await priceFeedTester.fetchPriceFromSource(invalidSource);
      expect(price).to.equal(0); // Should return 0 for failed requests
    });
  });

  describe("Price Consistency Validation", function () {
    it("Should validate price consistency across multiple sources", async function () {
      this.timeout(30000); // 30 second timeout for multiple API calls
      
      try {
        const result = await priceFeedTester.validatePriceConsistency("ethereum", 0.05);
        
        expect(Object.keys(result.prices).length).to.be.greaterThan(1);
        expect(result.consensus).to.be.greaterThan(0);
        
        console.log("Price consistency results:");
        console.log(`Consensus price: $${result.consensus.toFixed(2)}`);
        
        for (const [source, price] of Object.entries(result.prices)) {
          const deviation = result.deviations[source];
          console.log(`${source}: $${price.toFixed(2)} (${(deviation * 100).toFixed(2)}% deviation)`);
        }
        
        // At least 80% of sources should be within 5% of consensus
        const consistentSources = Object.values(result.deviations).filter(dev => dev <= 0.05).length;
        const totalSources = Object.keys(result.prices).length;
        const consistencyRate = consistentSources / totalSources;
        
        expect(consistencyRate).to.be.greaterThan(0.8);
        
      } catch (error) {
        console.warn("Price consistency test skipped:", error);
        this.skip();
      }
    });

    it("Should detect price anomalies", async function () {
      this.timeout(20000);
      
      try {
        const result = await priceFeedTester.validatePriceConsistency("ethereum", 0.02); // Stricter threshold
        
        // Check if any source has significant deviation
        const maxDeviation = Math.max(...Object.values(result.deviations));
        
        if (maxDeviation > 0.1) { // 10% deviation threshold for anomaly
          console.warn(`Price anomaly detected: ${(maxDeviation * 100).toFixed(2)}% deviation`);
        }
        
        expect(result.consensus).to.be.greaterThan(0);
        
      } catch (error) {
        console.warn("Price anomaly test skipped:", error);
        this.skip();
      }
    });
  });

  describe("Latency and Performance", function () {
    it("Should measure API latency for each source", async function () {
      this.timeout(60000); // 1 minute timeout for latency tests
      
      for (const source of priceFeedTester["sources"]) {
        try {
          const metrics = await priceFeedTester.measureLatency(source, 3);
          
          console.log(`${source.name} latency metrics:`);
          console.log(`  Average: ${metrics.averageLatency}ms`);
          console.log(`  Min: ${metrics.minLatency}ms`);
          console.log(`  Max: ${metrics.maxLatency}ms`);
          console.log(`  Success rate: ${(metrics.successRate * 100).toFixed(1)}%`);
          
          if (metrics.successRate > 0) {
            expect(metrics.averageLatency).to.be.lessThan(5000); // 5 second max
            expect(metrics.successRate).to.be.greaterThan(0.5); // 50% success rate minimum
          }
          
        } catch (error) {
          console.warn(`Latency test for ${source.name} skipped:`, error);
        }
      }
    });

    it("Should respect rate limits", async function () {
      this.timeout(30000);
      
      const source = priceFeedTester["sources"][0]; // Use first available source
      if (!source) {
        this.skip();
      }

      // Make rapid requests to test rate limiting
      const promises = Array(5).fill(0).map(() => 
        priceFeedTester.fetchPriceFromSource(source)
      );

      const results = await Promise.allSettled(promises);
      
      // Some requests should succeed, rate limiting should prevent all from failing
      const successCount = results.filter(r => r.status === "fulfilled").length;
      expect(successCount).to.be.greaterThan(0);
    });
  });

  describe("Error Handling and Resilience", function () {
    it("Should handle network timeouts gracefully", async function () {
      // This test would require mocking network conditions
      // For now, we'll test with a very short timeout
      const source = {
        name: "Timeout Test",
        url: "https://httpbin.org/delay/10", // 10 second delay
        parseResponse: (data: any) => 2000,
        rateLimit: 60,
      };

      const price = await priceFeedTester.fetchPriceFromSource(source);
      expect(price).to.equal(0); // Should timeout and return 0
    });

    it("Should handle malformed API responses", async function () {
      const source = {
        name: "Malformed Response",
        url: "https://httpbin.org/json", // Returns generic JSON
        parseResponse: (data: any) => data.nonexistent?.price || 0,
        rateLimit: 60,
      };

      const price = await priceFeedTester.fetchPriceFromSource(source);
      expect(price).to.equal(0); // Should handle gracefully
    });

    it("Should provide fallback mechanisms", async function () {
      // Test that system can continue with partial source failures
      const result = await priceFeedTester.validatePriceConsistency("ethereum");
      
      // Even if some sources fail, should get a consensus from available sources
      expect(result.consensus).to.be.greaterThan(0);
      expect(Object.keys(result.prices).length).to.be.greaterThan(0);
    });
  });
});
