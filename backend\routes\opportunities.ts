import { Router } from 'express';
import logger from '../utils/logger.js';
import { OpportunityDetectionService, ArbitrageType } from '../services/OpportunityDetectionService.js';
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/ErrorHandler.js';

export default function createOpportunityRoutes(opportunityService: OpportunityDetectionService) {
  const router = Router();

  // Get all opportunities
  router.get('/', async (req, res) => {
    try {
      const { 
        type, 
        minProfit, 
        maxSlippage, 
        network, 
        asset,
        limit = '50',
        sortBy = 'potentialProfit',
        order = 'desc'
      } = req.query;

      let opportunities = opportunityService.getOpportunities();

      // Apply filters
      if (type && Object.values(ArbitrageType).includes(type as ArbitrageType)) {
        opportunities = opportunities.filter(opp => opp.type === type);
      }

      if (minProfit) {
        const minProfitNum = parseFloat(minProfit as string);
        opportunities = opportunities.filter(opp => opp.potentialProfit >= minProfitNum);
      }

      if (maxSlippage) {
        const maxSlippageNum = parseFloat(maxSlippage as string);
        opportunities = opportunities.filter(opp => opp.slippage <= maxSlippageNum);
      }

      if (network) {
        opportunities = opportunities.filter(opp => opp.network === network);
      }

      if (asset) {
        const assetStr = (asset as string).toUpperCase();
        opportunities = opportunities.filter(opp => 
          opp.assets.some(a => a.toUpperCase().includes(assetStr))
        );
      }

      // Sort opportunities
      const validSortFields = ['potentialProfit', 'profitPercentage', 'timestamp', 'confidence'];
      const sortField = validSortFields.includes(sortBy as string) ? sortBy as string : 'potentialProfit';
      const sortOrder = order === 'asc' ? 1 : -1;

      opportunities.sort((a, b) => {
        const aVal = (a as any)[sortField];
        const bVal = (b as any)[sortField];
        return (aVal - bVal) * sortOrder;
      });

      // Apply limit
      const limitNum = Math.min(parseInt(limit as string) || 50, 100);
      opportunities = opportunities.slice(0, limitNum);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        filters: { type, minProfit, maxSlippage, network, asset },
        pagination: { limit: limitNum, sortBy: sortField, order }
      });
    } catch (error) {
      logger.error('Error getting opportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities'
      });
    }
  });

  // Get specific opportunity
  router.get('/:id', async (req, res) => {
    try {
      const { id } = req.params;
      
      const opportunity = opportunityService.getOpportunity(id);
      
      if (!opportunity) {
        return res.status(404).json({
          success: false,
          error: 'Opportunity not found'
        });
      }

      res.json({
        success: true,
        data: opportunity
      });
    } catch (error) {
      logger.error('Error getting opportunity:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunity'
      });
    }
  });

  // Get opportunities by type
  router.get('/type/:type', async (req, res) => {
    try {
      const { type } = req.params;
      
      if (!Object.values(ArbitrageType).includes(type as ArbitrageType)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid arbitrage type'
        });
      }

      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.type === type);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        type
      });
    } catch (error) {
      logger.error('Error getting opportunities by type:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities by type'
      });
    }
  });

  // Get opportunities by asset
  router.get('/asset/:asset', async (req, res) => {
    try {
      const { asset } = req.params;
      const assetUpper = asset.toUpperCase();
      
      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.assets.some(a => a.toUpperCase() === assetUpper));

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        asset: assetUpper
      });
    } catch (error) {
      logger.error('Error getting opportunities by asset:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities by asset'
      });
    }
  });

  // Get opportunities by network
  router.get('/network/:network', async (req, res) => {
    try {
      const { network } = req.params;
      
      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.network === network);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        network
      });
    } catch (error) {
      logger.error('Error getting opportunities by network:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities by network'
      });
    }
  });

  // Get opportunity statistics
  router.get('/stats/summary', async (req, res) => {
    try {
      const stats = opportunityService.getStats();
      const opportunities = opportunityService.getOpportunities();

      // Calculate additional statistics
      const typeDistribution: Record<string, number> = {};
      const networkDistribution: Record<string, number> = {};
      const profitRanges = {
        low: 0,    // < $50
        medium: 0, // $50 - $200
        high: 0    // > $200
      };

      opportunities.forEach(opp => {
        // Type distribution
        typeDistribution[opp.type] = (typeDistribution[opp.type] || 0) + 1;
        
        // Network distribution
        networkDistribution[opp.network] = (networkDistribution[opp.network] || 0) + 1;
        
        // Profit ranges
        if (opp.potentialProfit < 50) {
          profitRanges.low++;
        } else if (opp.potentialProfit <= 200) {
          profitRanges.medium++;
        } else {
          profitRanges.high++;
        }
      });

      const enhancedStats = {
        ...stats,
        typeDistribution,
        networkDistribution,
        profitRanges,
        totalPotentialProfit: opportunities.reduce((sum, opp) => sum + opp.potentialProfit, 0),
        avgConfidence: opportunities.length > 0 
          ? opportunities.reduce((sum, opp) => sum + opp.confidence, 0) / opportunities.length 
          : 0
      };

      res.json({
        success: true,
        data: enhancedStats
      });
    } catch (error) {
      logger.error('Error getting opportunity stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunity statistics'
      });
    }
  });

  // Get top opportunities
  router.get('/top/profitable', async (req, res) => {
    try {
      const { limit = '10' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 10, 50);
      
      const opportunities = opportunityService.getOpportunities()
        .sort((a, b) => b.potentialProfit - a.potentialProfit)
        .slice(0, limitNum);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        limit: limitNum
      });
    } catch (error) {
      logger.error('Error getting top opportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve top opportunities'
      });
    }
  });

  // Get opportunities with high confidence
  router.get('/high-confidence', async (req, res) => {
    try {
      const { minConfidence = '80' } = req.query;
      const minConf = parseFloat(minConfidence as string);
      
      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.confidence >= minConf)
        .sort((a, b) => b.confidence - a.confidence);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        minConfidence: minConf
      });
    } catch (error) {
      logger.error('Error getting high confidence opportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve high confidence opportunities'
      });
    }
  });

  // Get recent opportunities
  router.get('/recent/:minutes', async (req, res) => {
    try {
      const { minutes } = req.params;
      const minutesNum = parseInt(minutes);
      
      if (isNaN(minutesNum) || minutesNum <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid minutes parameter'
        });
      }

      const cutoff = Date.now() - minutesNum * 60 * 1000;
      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.timestamp >= cutoff)
        .sort((a, b) => b.timestamp - a.timestamp);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        timeframe: `${minutesNum} minutes`
      });
    } catch (error) {
      logger.error('Error getting recent opportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve recent opportunities'
      });
    }
  });

  /**
   * Simulate cross-chain arbitrage opportunity
   * POST /api/opportunities/cross-chain/simulate
   */
  router.post('/cross-chain/simulate', async (req, res) => {
    const startTime = Date.now();

    try {
      const {
        sourceNetwork,
        targetNetwork,
        tokenSymbol,
        amount,
        bridgeProtocol,
        slippageTolerance = 0.5,
        maxGasPrice,
        deadline = 1800 // 30 minutes
      } = req.body;

      // Validate required parameters
      if (!sourceNetwork || !targetNetwork || !tokenSymbol || !amount) {
        return res.status(400).json({
          success: false,
          error: 'Missing required parameters: sourceNetwork, targetNetwork, tokenSymbol, amount',
          requiredFields: ['sourceNetwork', 'targetNetwork', 'tokenSymbol', 'amount']
        });
      }

      // Validate amount
      const amountNum = parseFloat(amount);
      if (isNaN(amountNum) || amountNum <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid amount: must be a positive number'
        });
      }

      // Validate networks are different
      if (sourceNetwork === targetNetwork) {
        return res.status(400).json({
          success: false,
          error: 'Source and target networks must be different'
        });
      }

      // Simulate cross-chain arbitrage
      const simulation = await simulateCrossChainArbitrage({
        sourceNetwork,
        targetNetwork,
        tokenSymbol,
        amount: amountNum,
        bridgeProtocol,
        slippageTolerance,
        maxGasPrice,
        deadline
      });

      const responseTime = Date.now() - startTime;

      res.json({
        success: true,
        data: simulation,
        responseTime,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      const standardError = await errorHandler.handleError(
        error as Error,
        'OpportunitiesAPI',
        'simulateCrossChain',
        {
          sourceNetwork: req.body.sourceNetwork,
          targetNetwork: req.body.targetNetwork,
          tokenSymbol: req.body.tokenSymbol
        }
      );

      logger.error('Cross-chain simulation failed:', error);

      res.status(500).json({
        success: false,
        error: standardError.message,
        errorCode: standardError.code,
        timestamp: new Date().toISOString()
      });
    }
  });

  /**
   * Get cross-chain bridge options
   * GET /api/opportunities/cross-chain/bridges
   */
  router.get('/cross-chain/bridges', async (req, res) => {
    try {
      const { sourceNetwork, targetNetwork } = req.query;

      if (!sourceNetwork || !targetNetwork) {
        return res.status(400).json({
          success: false,
          error: 'Missing required parameters: sourceNetwork, targetNetwork'
        });
      }

      const bridgeOptions = await getCrossChainBridgeOptions(
        sourceNetwork as string,
        targetNetwork as string
      );

      res.json({
        success: true,
        data: bridgeOptions,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Get bridge options failed:', error);

      res.status(500).json({
        success: false,
        error: 'Failed to retrieve bridge options',
        timestamp: new Date().toISOString()
      });
    }
  });

  return router;
}

/**
 * Simulate cross-chain arbitrage opportunity
 */
async function simulateCrossChainArbitrage(params: {
  sourceNetwork: string;
  targetNetwork: string;
  tokenSymbol: string;
  amount: number;
  bridgeProtocol?: string;
  slippageTolerance: number;
  maxGasPrice?: string;
  deadline: number;
}): Promise<any> {

  const {
    sourceNetwork,
    targetNetwork,
    tokenSymbol,
    amount,
    bridgeProtocol,
    slippageTolerance,
    maxGasPrice,
    deadline
  } = params;

  // Simulate price fetching
  const sourcePrice = await simulateTokenPrice(sourceNetwork, tokenSymbol);
  const targetPrice = await simulateTokenPrice(targetNetwork, tokenSymbol);

  if (!sourcePrice || !targetPrice) {
    throw new Error(`Price data not available for ${tokenSymbol} on specified networks`);
  }

  // Calculate price difference
  const priceDifference = targetPrice - sourcePrice;
  const priceDifferencePercentage = (priceDifference / sourcePrice) * 100;

  // Simulate bridge fee calculation
  const bridgeFee = await simulateBridgeFee(sourceNetwork, targetNetwork, amount, bridgeProtocol);

  // Simulate gas costs
  const sourceGasCost = await simulateGasCost(sourceNetwork, 'swap', maxGasPrice);
  const targetGasCost = await simulateGasCost(targetNetwork, 'swap', maxGasPrice);
  const bridgeGasCost = bridgeFee.gasCost;

  // Calculate slippage impact
  const slippageImpact = (amount * slippageTolerance) / 100;

  // Calculate total costs
  const totalCosts = bridgeFee.totalFee + sourceGasCost + targetGasCost + slippageImpact;

  // Calculate potential profit
  const grossProfit = (priceDifference / sourcePrice) * amount;
  const netProfit = grossProfit - totalCosts;
  const netProfitPercentage = (netProfit / amount) * 100;

  // Calculate execution time estimate
  const executionTime = bridgeFee.estimatedTime + 120; // Add 2 minutes for swaps

  // Determine if profitable
  const isProfitable = netProfit > 0;
  const profitabilityScore = Math.max(0, Math.min(100, netProfitPercentage * 10));

  return {
    simulation: {
      sourceNetwork,
      targetNetwork,
      tokenSymbol,
      amount,
      bridgeProtocol: bridgeFee.protocol
    },
    pricing: {
      sourcePrice,
      targetPrice,
      priceDifference,
      priceDifferencePercentage: Number(priceDifferencePercentage.toFixed(4))
    },
    costs: {
      bridgeFee: bridgeFee.totalFee,
      sourceGasCost,
      targetGasCost,
      bridgeGasCost,
      slippageImpact,
      totalCosts
    },
    profitability: {
      grossProfit,
      netProfit,
      netProfitPercentage: Number(netProfitPercentage.toFixed(4)),
      isProfitable,
      profitabilityScore: Number(profitabilityScore.toFixed(2))
    },
    execution: {
      estimatedTime: executionTime,
      deadline,
      bridgeDetails: bridgeFee,
      alternatives: bridgeFee.alternatives || []
    },
    risks: {
      slippageRisk: slippageTolerance > 1 ? 'high' : slippageTolerance > 0.5 ? 'medium' : 'low',
      bridgeRisk: bridgeFee.confidence < 80 ? 'high' : bridgeFee.confidence < 90 ? 'medium' : 'low',
      timingRisk: executionTime > 1200 ? 'high' : executionTime > 600 ? 'medium' : 'low'
    },
    recommendations: generateRecommendations(netProfit, executionTime, bridgeFee.confidence)
  };
}

/**
 * Get available bridge options for network pair
 */
async function getCrossChainBridgeOptions(sourceNetwork: string, targetNetwork: string): Promise<any> {
  // Simulate bridge protocol availability
  const allBridges = [
    { protocol: 'stargate', fee: 0.06, time: 300, reliability: 95 },
    { protocol: 'layerzero', fee: 0.05, time: 600, reliability: 92 },
    { protocol: 'wormhole', fee: 0.08, time: 900, reliability: 90 },
    { protocol: 'multichain', fee: 0.1, time: 720, reliability: 88 },
    { protocol: 'celer_cbridge', fee: 0.07, time: 420, reliability: 93 }
  ];

  // Filter bridges based on network support (simplified)
  const supportedBridges = allBridges.filter(bridge => {
    // Simplified logic - in reality would check actual bridge support
    if (sourceNetwork === 'solana' || targetNetwork === 'solana') {
      return bridge.protocol === 'wormhole';
    }
    if (sourceNetwork === 'sui' || targetNetwork === 'sui') {
      return ['wormhole', 'sui_bridge'].includes(bridge.protocol);
    }
    return true;
  });

  return {
    sourceNetwork,
    targetNetwork,
    availableBridges: supportedBridges.length,
    bridges: supportedBridges.map(bridge => ({
      ...bridge,
      estimatedFeeUSD: (1000 * bridge.fee / 100).toFixed(2), // For $1000 transfer
      estimatedTimeMinutes: Math.round(bridge.time / 60)
    })),
    recommendation: supportedBridges.length > 0 ?
      supportedBridges.reduce((best, current) =>
        (current.reliability / current.fee) > (best.reliability / best.fee) ? current : best
      ) : null
  };
}

// Helper simulation functions
async function simulateTokenPrice(network: string, symbol: string): Promise<number> {
  // Simulate price fetching with some variance
  const basePrices: { [key: string]: number } = {
    'ETH': 2000,
    'WETH': 2000,
    'BTC': 45000,
    'WBTC': 45000,
    'USDC': 1,
    'USDT': 1,
    'DAI': 1
  };

  const basePrice = basePrices[symbol] || 100;
  const variance = (Math.random() - 0.5) * 0.02; // ±1% variance
  return basePrice * (1 + variance);
}

async function simulateBridgeFee(
  sourceNetwork: string,
  targetNetwork: string,
  amount: number,
  preferredProtocol?: string
): Promise<any> {

  const protocols = [
    { protocol: 'stargate', baseFee: 5, variableRate: 0.06, time: 300, confidence: 95 },
    { protocol: 'layerzero', baseFee: 3, variableRate: 0.05, time: 600, confidence: 92 },
    { protocol: 'wormhole', baseFee: 4, variableRate: 0.08, time: 900, confidence: 90 }
  ];

  const selectedProtocol = preferredProtocol ?
    protocols.find(p => p.protocol === preferredProtocol) || protocols[0] :
    protocols[0];

  const variableFee = amount * (selectedProtocol.variableRate / 100);
  const totalFee = selectedProtocol.baseFee + variableFee;

  return {
    protocol: selectedProtocol.protocol,
    baseFee: selectedProtocol.baseFee,
    variableFee,
    totalFee,
    gasCost: 15, // Estimated gas cost in USD
    estimatedTime: selectedProtocol.time,
    confidence: selectedProtocol.confidence,
    alternatives: protocols.filter(p => p.protocol !== selectedProtocol.protocol)
      .map(p => ({
        protocol: p.protocol,
        fee: p.baseFee + (amount * p.variableRate / 100),
        time: p.time,
        reliability: p.confidence
      }))
  };
}

async function simulateGasCost(network: string, operation: string, maxGasPrice?: string): Promise<number> {
  const gasEstimates: { [key: string]: number } = {
    ethereum: 50,
    polygon: 5,
    bsc: 3,
    arbitrum: 8,
    optimism: 10,
    base: 12,
    avalanche: 15,
    fantom: 2
  };

  return gasEstimates[network] || 10;
}

function generateRecommendations(netProfit: number, executionTime: number, bridgeConfidence: number): string[] {
  const recommendations: string[] = [];

  if (netProfit <= 0) {
    recommendations.push('Opportunity is not profitable - consider waiting for better market conditions');
  } else if (netProfit < 10) {
    recommendations.push('Low profit margin - monitor for better entry points');
  }

  if (executionTime > 1200) {
    recommendations.push('Long execution time - consider faster bridge protocols');
  }

  if (bridgeConfidence < 90) {
    recommendations.push('Bridge reliability concerns - consider alternative protocols');
  }

  if (netProfit > 50 && executionTime < 600 && bridgeConfidence > 90) {
    recommendations.push('Excellent opportunity - high profit with low risk');
  }

  return recommendations.length > 0 ? recommendations : ['Opportunity appears viable for execution'];
}
