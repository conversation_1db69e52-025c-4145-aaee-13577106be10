import logger from './logger.js';

export enum ErrorType {
  VALIDATION_ERROR = 'validation_error',
  EXECUTION_ERROR = 'execution_error',
  NETWORK_ERROR = 'network_error',
  TIMEOUT_ERROR = 'timeout_error',
  INSUFFICIENT_FUNDS = 'insufficient_funds',
  SLIPPAGE_EXCEEDED = 'slippage_exceeded',
  GAS_ESTIMATION_FAILED = 'gas_estimation_failed',
  BRIDGE_ERROR = 'bridge_error',
  MEV_PROTECTION_ERROR = 'mev_protection_error',
  FLASH_LOAN_ERROR = 'flash_loan_error',
  PRICE_FEED_ERROR = 'price_feed_error',
  CONFIGURATION_ERROR = 'configuration_error',
  UNKNOWN_ERROR = 'unknown_error'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface StandardError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  code: string;
  context: { [key: string]: any };
  timestamp: number;
  retryable: boolean;
  maxRetries: number;
  currentRetry: number;
  originalError?: Error;
}

export interface ErrorHandlingConfig {
  enableRetry: boolean;
  maxRetries: number;
  retryDelayMs: number;
  exponentialBackoff: boolean;
  enableCircuitBreaker: boolean;
  circuitBreakerThreshold: number;
  enableMetrics: boolean;
}

export interface RetryConfig {
  maxRetries: number;
  delayMs: number;
  exponentialBackoff: boolean;
  retryCondition?: (error: StandardError) => boolean;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private config: ErrorHandlingConfig;
  private errorMetrics: Map<string, ErrorMetrics> = new Map();
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();

  private constructor(config: ErrorHandlingConfig) {
    this.config = config;
  }

  public static getInstance(config?: ErrorHandlingConfig): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler(config || {
        enableRetry: true,
        maxRetries: 3,
        retryDelayMs: 1000,
        exponentialBackoff: true,
        enableCircuitBreaker: true,
        circuitBreakerThreshold: 5,
        enableMetrics: true
      });
    }
    return ErrorHandler.instance;
  }

  /**
   * Create a standardized error
   */
  public createError(
    type: ErrorType,
    message: string,
    context: { [key: string]: any } = {},
    originalError?: Error,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
  ): StandardError {
    
    const errorCode = this.generateErrorCode(type, context);
    const retryable = this.isRetryable(type);
    
    return {
      type,
      severity,
      message,
      code: errorCode,
      context,
      timestamp: Date.now(),
      retryable,
      maxRetries: this.config.maxRetries,
      currentRetry: 0,
      originalError
    };
  }

  /**
   * Handle error with standardized processing
   */
  public async handleError(
    error: StandardError | Error,
    serviceName: string,
    operationName: string,
    context: { [key: string]: any } = {}
  ): Promise<StandardError> {
    
    let standardError: StandardError;
    
    if (this.isStandardError(error)) {
      standardError = error;
    } else {
      standardError = this.createError(
        this.classifyError(error),
        error.message,
        { ...context, serviceName, operationName },
        error
      );
    }

    // Update error metrics
    if (this.config.enableMetrics) {
      this.updateErrorMetrics(serviceName, standardError);
    }

    // Check circuit breaker
    if (this.config.enableCircuitBreaker) {
      this.updateCircuitBreaker(serviceName, standardError);
    }

    // Log error with appropriate level
    this.logError(standardError, serviceName, operationName);

    return standardError;
  }

  /**
   * Execute operation with retry logic
   */
  public async executeWithRetry<T>(
    operation: () => Promise<T>,
    serviceName: string,
    operationName: string,
    retryConfig?: Partial<RetryConfig>,
    context: { [key: string]: any } = {}
  ): Promise<T> {
    
    const config = {
      maxRetries: retryConfig?.maxRetries || this.config.maxRetries,
      delayMs: retryConfig?.delayMs || this.config.retryDelayMs,
      exponentialBackoff: retryConfig?.exponentialBackoff ?? this.config.exponentialBackoff,
      retryCondition: retryConfig?.retryCondition || ((error: StandardError) => error.retryable)
    };

    let lastError: StandardError | null = null;
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        // Check circuit breaker before execution
        if (this.isCircuitBreakerOpen(serviceName)) {
          throw this.createError(
            ErrorType.EXECUTION_ERROR,
            `Circuit breaker open for service: ${serviceName}`,
            { serviceName, operationName, attempt },
            undefined,
            ErrorSeverity.HIGH
          );
        }

        const result = await operation();
        
        // Reset circuit breaker on success
        if (this.config.enableCircuitBreaker) {
          this.resetCircuitBreaker(serviceName);
        }
        
        return result;
        
      } catch (error) {
        lastError = await this.handleError(error as Error, serviceName, operationName, {
          ...context,
          attempt,
          maxRetries: config.maxRetries
        });

        // Check if we should retry
        if (attempt < config.maxRetries && config.retryCondition(lastError)) {
          const delay = config.exponentialBackoff ? 
            config.delayMs * Math.pow(2, attempt) : 
            config.delayMs;
          
          logger.warn(`Retrying ${operationName} in ${delay}ms (attempt ${attempt + 1}/${config.maxRetries + 1})`);
          await this.delay(delay);
          continue;
        }
        
        break;
      }
    }

    // All retries exhausted
    throw lastError;
  }

  /**
   * Check if error is retryable
   */
  private isRetryable(errorType: ErrorType): boolean {
    const retryableErrors = [
      ErrorType.NETWORK_ERROR,
      ErrorType.TIMEOUT_ERROR,
      ErrorType.GAS_ESTIMATION_FAILED,
      ErrorType.PRICE_FEED_ERROR
    ];
    
    return retryableErrors.includes(errorType);
  }

  /**
   * Classify error type from generic Error
   */
  private classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    
    if (message.includes('timeout')) return ErrorType.TIMEOUT_ERROR;
    if (message.includes('network') || message.includes('connection')) return ErrorType.NETWORK_ERROR;
    if (message.includes('gas')) return ErrorType.GAS_ESTIMATION_FAILED;
    if (message.includes('slippage')) return ErrorType.SLIPPAGE_EXCEEDED;
    if (message.includes('insufficient')) return ErrorType.INSUFFICIENT_FUNDS;
    if (message.includes('validation')) return ErrorType.VALIDATION_ERROR;
    if (message.includes('bridge')) return ErrorType.BRIDGE_ERROR;
    if (message.includes('mev')) return ErrorType.MEV_PROTECTION_ERROR;
    if (message.includes('flash loan')) return ErrorType.FLASH_LOAN_ERROR;
    if (message.includes('price')) return ErrorType.PRICE_FEED_ERROR;
    
    return ErrorType.UNKNOWN_ERROR;
  }

  /**
   * Generate unique error code
   */
  private generateErrorCode(type: ErrorType, context: { [key: string]: any }): string {
    const timestamp = Date.now().toString(36);
    const typeCode = type.toUpperCase().replace(/_/g, '');
    const contextHash = this.hashContext(context);
    
    return `${typeCode}-${timestamp}-${contextHash}`;
  }

  /**
   * Hash context for error code generation
   */
  private hashContext(context: { [key: string]: any }): string {
    const str = JSON.stringify(context);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36).substring(0, 6);
  }

  /**
   * Check if error is StandardError
   */
  private isStandardError(error: any): error is StandardError {
    return error && typeof error === 'object' && 'type' in error && 'severity' in error;
  }

  /**
   * Log error with appropriate level
   */
  private logError(error: StandardError, serviceName: string, operationName: string): void {
    const logData = {
      errorCode: error.code,
      errorType: error.type,
      severity: error.severity,
      serviceName,
      operationName,
      context: error.context,
      retryable: error.retryable,
      currentRetry: error.currentRetry
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        logger.error(`CRITICAL ERROR in ${serviceName}.${operationName}: ${error.message}`, logData);
        break;
      case ErrorSeverity.HIGH:
        logger.error(`HIGH SEVERITY ERROR in ${serviceName}.${operationName}: ${error.message}`, logData);
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn(`MEDIUM SEVERITY ERROR in ${serviceName}.${operationName}: ${error.message}`, logData);
        break;
      case ErrorSeverity.LOW:
        logger.info(`LOW SEVERITY ERROR in ${serviceName}.${operationName}: ${error.message}`, logData);
        break;
    }
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update error metrics
   */
  private updateErrorMetrics(serviceName: string, error: StandardError): void {
    if (!this.errorMetrics.has(serviceName)) {
      this.errorMetrics.set(serviceName, {
        totalErrors: 0,
        errorsByType: new Map(),
        errorsBySeverity: new Map(),
        lastErrorTime: 0,
        errorRate: 0
      });
    }

    const metrics = this.errorMetrics.get(serviceName)!;
    metrics.totalErrors++;
    metrics.lastErrorTime = error.timestamp;

    // Update by type
    const typeCount = metrics.errorsByType.get(error.type) || 0;
    metrics.errorsByType.set(error.type, typeCount + 1);

    // Update by severity
    const severityCount = metrics.errorsBySeverity.get(error.severity) || 0;
    metrics.errorsBySeverity.set(error.severity, severityCount + 1);

    // Calculate error rate (errors per minute)
    const timeWindow = 60000; // 1 minute
    const recentErrors = this.getRecentErrorCount(serviceName, timeWindow);
    metrics.errorRate = recentErrors / (timeWindow / 60000);
  }

  /**
   * Update circuit breaker state
   */
  private updateCircuitBreaker(serviceName: string, error: StandardError): void {
    if (!this.circuitBreakers.has(serviceName)) {
      this.circuitBreakers.set(serviceName, {
        failures: 0,
        lastFailureTime: 0,
        state: 'CLOSED',
        nextAttempt: 0
      });
    }

    const breaker = this.circuitBreakers.get(serviceName)!;

    if (error.severity === ErrorSeverity.HIGH || error.severity === ErrorSeverity.CRITICAL) {
      breaker.failures++;
      breaker.lastFailureTime = error.timestamp;

      if (breaker.failures >= this.config.circuitBreakerThreshold) {
        breaker.state = 'OPEN';
        breaker.nextAttempt = error.timestamp + 60000; // 1 minute timeout
        logger.warn(`Circuit breaker opened for service: ${serviceName}`);
      }
    }
  }

  /**
   * Check if circuit breaker is open
   */
  private isCircuitBreakerOpen(serviceName: string): boolean {
    const breaker = this.circuitBreakers.get(serviceName);
    if (!breaker) return false;

    const now = Date.now();

    if (breaker.state === 'OPEN') {
      if (now >= breaker.nextAttempt) {
        breaker.state = 'HALF_OPEN';
        logger.info(`Circuit breaker half-open for service: ${serviceName}`);
        return false;
      }
      return true;
    }

    return false;
  }

  /**
   * Reset circuit breaker on success
   */
  private resetCircuitBreaker(serviceName: string): void {
    const breaker = this.circuitBreakers.get(serviceName);
    if (breaker && breaker.state !== 'CLOSED') {
      breaker.failures = 0;
      breaker.state = 'CLOSED';
      logger.info(`Circuit breaker closed for service: ${serviceName}`);
    }
  }

  /**
   * Get recent error count for rate calculation
   */
  private getRecentErrorCount(serviceName: string, timeWindowMs: number): number {
    // In a real implementation, this would track errors over time
    // For now, return a simplified calculation
    const metrics = this.errorMetrics.get(serviceName);
    if (!metrics) return 0;

    const now = Date.now();
    const timeSinceLastError = now - metrics.lastErrorTime;

    if (timeSinceLastError > timeWindowMs) return 0;

    // Simplified: assume recent errors based on total and time
    return Math.min(metrics.totalErrors, 10);
  }

  /**
   * Get error statistics for a service
   */
  public getErrorStats(serviceName: string): { [key: string]: any } | null {
    const metrics = this.errorMetrics.get(serviceName);
    const breaker = this.circuitBreakers.get(serviceName);

    if (!metrics) return null;

    return {
      totalErrors: metrics.totalErrors,
      errorRate: metrics.errorRate,
      lastErrorTime: new Date(metrics.lastErrorTime).toISOString(),
      errorsByType: Object.fromEntries(metrics.errorsByType),
      errorsBySeverity: Object.fromEntries(metrics.errorsBySeverity),
      circuitBreaker: breaker ? {
        state: breaker.state,
        failures: breaker.failures,
        lastFailureTime: breaker.lastFailureTime > 0 ?
          new Date(breaker.lastFailureTime).toISOString() : null
      } : null
    };
  }

  /**
   * Get overall error statistics
   */
  public getOverallStats(): { [key: string]: any } {
    const services = Array.from(this.errorMetrics.keys());
    const totalErrors = services.reduce((sum, service) =>
      sum + (this.errorMetrics.get(service)?.totalErrors || 0), 0);

    const openCircuitBreakers = services.filter(service =>
      this.isCircuitBreakerOpen(service)).length;

    return {
      totalServices: services.length,
      totalErrors,
      openCircuitBreakers,
      services: services.map(service => ({
        name: service,
        stats: this.getErrorStats(service)
      })),
      config: this.config
    };
  }
}

interface ErrorMetrics {
  totalErrors: number;
  errorsByType: Map<ErrorType, number>;
  errorsBySeverity: Map<ErrorSeverity, number>;
  lastErrorTime: number;
  errorRate: number;
}

interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  nextAttempt: number;
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();
