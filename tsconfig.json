{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": ".", "declaration": true, "sourceMap": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2022"], "types": ["node", "jest"], "baseUrl": ".", "paths": {"@/*": ["backend/*"], "@tests/*": ["tests/*"]}, "allowJs": true, "noEmit": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["backend/**/*.ts", "tests/**/*.ts", "scripts/**/*.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", "coverage"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}