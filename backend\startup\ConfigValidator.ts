import { z } from 'zod';
import fs from 'fs';
import path from 'path';
import logger from '../utils/logger.js';
import config from '../config/index.js';

export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  criticalErrors: string[];
  missingOptional: string[];
  recommendations: string[];
}

export interface ConfigRequirement {
  key: string;
  required: boolean;
  type: 'string' | 'number' | 'boolean' | 'url' | 'privateKey' | 'apiKey';
  description: string;
  validator?: (value: any) => boolean;
  recommendation?: string;
}

export class ConfigValidator {
  private requirements: ConfigRequirement[] = [
    // Critical Database Requirements
    {
      key: 'SUPABASE_URL',
      required: true,
      type: 'url',
      description: 'Supabase project URL for data storage'
    },
    {
      key: 'SUPABASE_SERVICE_ROLE_KEY',
      required: true,
      type: 'apiKey',
      description: 'Supabase service role key for database access'
    },

    // Blockchain Requirements
    {
      key: 'ETHEREUM_RPC_URL',
      required: true,
      type: 'url',
      description: 'Ethereum RPC endpoint for blockchain interaction'
    },
    {
      key: 'POLYGON_RPC_URL',
      required: true,
      type: 'url',
      description: 'Polygon RPC endpoint for multi-chain support'
    },
    {
      key: 'BSC_RPC_URL',
      required: true,
      type: 'url',
      description: 'BSC RPC endpoint for multi-chain support'
    },
    {
      key: 'PRIVATE_KEY',
      required: true,
      type: 'privateKey',
      description: 'Private key for transaction signing'
    },

    // Trading Configuration
    {
      key: 'MIN_PROFIT_THRESHOLD',
      required: true,
      type: 'number',
      description: 'Minimum profit threshold in USD',
      validator: (value) => parseFloat(value) > 0
    },
    {
      key: 'MAX_POSITION_SIZE',
      required: true,
      type: 'number',
      description: 'Maximum position size in USD',
      validator: (value) => parseFloat(value) > 0
    },
    {
      key: 'MAX_SLIPPAGE',
      required: true,
      type: 'number',
      description: 'Maximum slippage tolerance',
      validator: (value) => parseFloat(value) > 0 && parseFloat(value) < 10
    },

    // Optional but Recommended
    {
      key: 'INFLUXDB_URL',
      required: false,
      type: 'url',
      description: 'InfluxDB URL for time-series metrics',
      recommendation: 'Recommended for production monitoring'
    },
    {
      key: 'INFLUXDB_TOKEN',
      required: false,
      type: 'apiKey',
      description: 'InfluxDB authentication token'
    },
    {
      key: 'REDIS_URL',
      required: false,
      type: 'url',
      description: 'Redis URL for caching and pub/sub',
      recommendation: 'Recommended for better performance'
    },
    {
      key: 'COINGECKO_API_KEY',
      required: false,
      type: 'apiKey',
      description: 'CoinGecko API key for market data',
      recommendation: 'Required for production rate limits'
    },
    {
      key: 'ALCHEMY_API_KEY',
      required: false,
      type: 'apiKey',
      description: 'Alchemy API key for enhanced RPC access',
      recommendation: 'Recommended for reliable blockchain access'
    },

    // Security and Monitoring
    {
      key: 'JWT_SECRET',
      required: false,
      type: 'string',
      description: 'JWT secret for API authentication',
      recommendation: 'Required for production API security'
    },
    {
      key: 'TELEGRAM_BOT_TOKEN',
      required: false,
      type: 'apiKey',
      description: 'Telegram bot token for notifications'
    },
    {
      key: 'ALERT_EMAIL',
      required: false,
      type: 'string',
      description: 'Email address for critical alerts'
    }
  ];

  public async validate(): Promise<ConfigValidationResult> {
    logger.info('🔍 Validating system configuration...');

    const result: ConfigValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      criticalErrors: [],
      missingOptional: [],
      recommendations: []
    };

    // Check environment file exists
    await this.checkEnvironmentFile(result);

    // Validate each requirement
    for (const requirement of this.requirements) {
      await this.validateRequirement(requirement, result);
    }

    // Additional validation checks
    await this.validateTradingParameters(result);
    await this.validateNetworkConfiguration(result);
    await this.validateSecuritySettings(result);
    await this.validateDirectoryStructure(result);

    // Determine overall validity
    result.isValid = result.criticalErrors.length === 0;

    // Log results
    this.logValidationResults(result);

    return result;
  }

  private async checkEnvironmentFile(result: ConfigValidationResult): Promise<void> {
    const envPath = path.join(process.cwd(), '.env');
    
    if (!fs.existsSync(envPath)) {
      result.criticalErrors.push('Environment file (.env) not found');
      result.recommendations.push('Create a .env file based on .env.example');
      return;
    }

    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      if (envContent.trim().length === 0) {
        result.warnings.push('Environment file is empty');
      }
    } catch (error) {
      result.errors.push(`Cannot read environment file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async validateRequirement(requirement: ConfigRequirement, result: ConfigValidationResult): Promise<void> {
    const value = process.env[requirement.key];

    if (!value || value.trim() === '') {
      if (requirement.required) {
        result.criticalErrors.push(`Missing required configuration: ${requirement.key} - ${requirement.description}`);
      } else {
        result.missingOptional.push(requirement.key);
        if (requirement.recommendation) {
          result.recommendations.push(`${requirement.key}: ${requirement.recommendation}`);
        }
      }
      return;
    }

    // Type-specific validation
    switch (requirement.type) {
      case 'url':
        if (!this.isValidUrl(value)) {
          result.errors.push(`Invalid URL format for ${requirement.key}: ${value}`);
        }
        break;
      
      case 'number':
        if (isNaN(parseFloat(value))) {
          result.errors.push(`Invalid number format for ${requirement.key}: ${value}`);
        }
        break;
      
      case 'privateKey':
        if (!this.isValidPrivateKey(value)) {
          result.errors.push(`Invalid private key format for ${requirement.key}`);
        }
        break;
      
      case 'apiKey':
        if (value.length < 10) {
          result.warnings.push(`API key for ${requirement.key} seems too short`);
        }
        break;
    }

    // Custom validator
    if (requirement.validator && !requirement.validator(value)) {
      result.errors.push(`Validation failed for ${requirement.key}: ${requirement.description}`);
    }
  }

  private async validateTradingParameters(result: ConfigValidationResult): Promise<void> {
    const minProfit = parseFloat(config.MIN_PROFIT_THRESHOLD);
    const maxPosition = parseFloat(config.MAX_POSITION_SIZE);
    const maxSlippage = parseFloat(config.MAX_SLIPPAGE);

    if (minProfit < 10) {
      result.warnings.push('MIN_PROFIT_THRESHOLD is very low, may result in unprofitable trades after gas fees');
    }

    if (maxPosition > 50000) {
      result.warnings.push('MAX_POSITION_SIZE is very high, consider risk management implications');
    }

    if (maxSlippage > 2) {
      result.warnings.push('MAX_SLIPPAGE is high, may result in poor trade execution');
    }

    if (minProfit >= maxPosition) {
      result.errors.push('MIN_PROFIT_THRESHOLD cannot be greater than or equal to MAX_POSITION_SIZE');
    }
  }

  private async validateNetworkConfiguration(result: ConfigValidationResult): Promise<void> {
    const networks = ['ETHEREUM_RPC_URL', 'POLYGON_RPC_URL', 'BSC_RPC_URL'];
    
    for (const network of networks) {
      const url = process.env[network];
      if (url && url.includes('your-api-key')) {
        result.criticalErrors.push(`${network} contains placeholder value, please update with actual RPC URL`);
      }
    }

    // Check for localhost URLs in production
    if (config.NODE_ENV === 'production') {
      for (const network of networks) {
        const url = process.env[network];
        if (url && (url.includes('localhost') || url.includes('127.0.0.1'))) {
          result.warnings.push(`${network} uses localhost in production environment`);
        }
      }
    }
  }

  private async validateSecuritySettings(result: ConfigValidationResult): Promise<void> {
    if (config.NODE_ENV === 'production') {
      if (!process.env.JWT_SECRET) {
        result.criticalErrors.push('JWT_SECRET is required for production deployment');
      }

      if (process.env.PRIVATE_KEY && process.env.PRIVATE_KEY.length < 64) {
        result.criticalErrors.push('PRIVATE_KEY appears to be invalid for production use');
      }

      if (!process.env.ALERT_EMAIL && !process.env.TELEGRAM_BOT_TOKEN) {
        result.warnings.push('No alerting mechanism configured for production');
      }
    }

    // Check for development keys in production
    if (config.NODE_ENV === 'production' && process.env.PRIVATE_KEY === 'your-private-key-here') {
      result.criticalErrors.push('Using placeholder private key in production');
    }
  }

  private async validateDirectoryStructure(result: ConfigValidationResult): Promise<void> {
    const requiredDirs = [
      'backend/services',
      'backend/utils',
      'backend/config',
      'backend/startup',
      'database',
      'logs'
    ];

    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        if (dir === 'logs') {
          // Create logs directory if it doesn't exist
          try {
            fs.mkdirSync(dir, { recursive: true });
            logger.info(`Created missing directory: ${dir}`);
          } catch (error) {
            result.warnings.push(`Cannot create logs directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        } else {
          result.errors.push(`Missing required directory: ${dir}`);
        }
      }
    }
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private isValidPrivateKey(key: string): boolean {
    // Basic validation for Ethereum private key
    if (key.startsWith('0x')) {
      return key.length === 66 && /^0x[a-fA-F0-9]{64}$/.test(key);
    }
    return key.length === 64 && /^[a-fA-F0-9]{64}$/.test(key);
  }

  private logValidationResults(result: ConfigValidationResult): void {
    if (result.isValid) {
      logger.info('✅ Configuration validation passed');
    } else {
      logger.error('❌ Configuration validation failed');
    }

    if (result.criticalErrors.length > 0) {
      logger.error('Critical configuration errors:');
      result.criticalErrors.forEach(error => logger.error(`  - ${error}`));
    }

    if (result.errors.length > 0) {
      logger.warn('Configuration errors:');
      result.errors.forEach(error => logger.warn(`  - ${error}`));
    }

    if (result.warnings.length > 0) {
      logger.warn('Configuration warnings:');
      result.warnings.forEach(warning => logger.warn(`  - ${warning}`));
    }

    if (result.recommendations.length > 0) {
      logger.info('Configuration recommendations:');
      result.recommendations.forEach(rec => logger.info(`  - ${rec}`));
    }

    if (result.missingOptional.length > 0) {
      logger.info(`Missing optional configurations: ${result.missingOptional.join(', ')}`);
    }
  }
}
