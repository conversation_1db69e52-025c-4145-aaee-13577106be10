'use client';

import { useState, useMemo } from 'react';
import { 
  Shield, 
  ShieldCheck,
  ShieldAlert,
  ShieldX,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  RefreshCw,
  ExternalLink
} from 'lucide-react';

import { useMEVProtectionStatus } from '@/hooks/useApi';
import { 
  formatCurrency, 
  formatPercentage, 
  formatNumber,
  formatTimeAgo,
  getProfitColor,
  getStatusColor
} from '@/lib/utils';
import { LoadingSpinner, SkeletonCard } from '@/components/ui/LoadingSpinner';

interface MEVProtectionStatusProps {
  detailed?: boolean;
}

interface ProtectionMetric {
  title: string;
  value: string;
  change?: number;
  changeLabel?: string;
  icon: React.ComponentType<any>;
  color: string;
  status?: 'active' | 'inactive' | 'warning';
}

interface NetworkProtectionProps {
  network: string;
  status: 'active' | 'inactive' | 'warning';
  provider: string;
  successRate: number;
  avgFee: number;
  totalSaved: number;
  lastUsed?: string;
}

function NetworkProtectionCard({ 
  network, 
  status, 
  provider, 
  successRate, 
  avgFee, 
  totalSaved, 
  lastUsed 
}: NetworkProtectionProps) {
  const statusColors = {
    active: 'text-success-400',
    inactive: 'text-error-400',
    warning: 'text-warning-400',
  };

  const statusIcons = {
    active: ShieldCheck,
    inactive: ShieldX,
    warning: ShieldAlert,
  };

  const StatusIcon = statusIcons[status];
  const statusColor = statusColors[status];

  return (
    <div className="glass-effect rounded-lg p-4 hover:bg-dark-700/30 transition-all duration-200">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <StatusIcon className={`w-5 h-5 ${statusColor}`} />
          <span className="font-medium text-dark-100 capitalize">{network}</span>
        </div>
        
        <div className={`
          px-2 py-1 rounded-full text-xs font-medium
          ${status === 'active' ? 'bg-success-500/20 text-success-400' :
            status === 'warning' ? 'bg-warning-500/20 text-warning-400' :
            'bg-error-500/20 text-error-400'}
        `}>
          {status}
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-xs text-dark-500">Provider</span>
          <span className="text-sm font-medium text-dark-200">{provider}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-xs text-dark-500">Success Rate</span>
          <span className={`text-sm font-medium ${getProfitColor(successRate - 80)}`}>
            {formatPercentage(successRate)}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-xs text-dark-500">Avg Fee</span>
          <span className="text-sm font-medium text-warning-400">
            {formatCurrency(avgFee)}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-xs text-dark-500">Total Saved</span>
          <span className="text-sm font-medium text-success-400">
            {formatCurrency(totalSaved)}
          </span>
        </div>
        
        {lastUsed && (
          <div className="flex items-center justify-between pt-2 border-t border-dark-700">
            <span className="text-xs text-dark-500">Last Used</span>
            <span className="text-xs text-dark-300">
              {formatTimeAgo(lastUsed)}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}

export function MEVProtectionStatus({ detailed = false }: MEVProtectionStatusProps) {
  const { data: protectionData, isLoading, error } = useMEVProtectionStatus();

  // Mock data for demonstration - would come from API
  const mockData = {
    overall_status: 'active',
    total_protected_value: 2450000,
    total_fees_paid: 12500,
    total_mev_saved: 87500,
    protection_rate: 94.5,
    networks: [
      {
        network: 'ethereum',
        status: 'active' as const,
        provider: 'Flashbots Protect',
        successRate: 96.2,
        avgFee: 0.025,
        totalSaved: 45000,
        lastUsed: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
      },
      {
        network: 'polygon',
        status: 'active' as const,
        provider: 'Polygon MEV-Boost',
        successRate: 92.8,
        avgFee: 0.012,
        totalSaved: 18500,
        lastUsed: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      },
      {
        network: 'arbitrum',
        status: 'warning' as const,
        provider: 'Arbitrum Sequencer',
        successRate: 78.5,
        avgFee: 0.008,
        totalSaved: 12000,
        lastUsed: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
      },
      {
        network: 'optimism',
        status: 'inactive' as const,
        provider: 'OP Stack MEV',
        successRate: 0,
        avgFee: 0,
        totalSaved: 0,
        lastUsed: undefined,
      },
    ],
  };

  const data = protectionData || mockData;

  const metrics: ProtectionMetric[] = useMemo(() => [
    {
      title: 'Protection Rate',
      value: formatPercentage(data.protection_rate || 0),
      change: 2.1,
      changeLabel: '24h',
      icon: Shield,
      color: 'text-primary-400',
      status: data.protection_rate > 90 ? 'active' : data.protection_rate > 70 ? 'warning' : 'inactive',
    },
    {
      title: 'Protected Value',
      value: formatCurrency(data.total_protected_value || 0),
      change: 15.3,
      changeLabel: '24h',
      icon: DollarSign,
      color: 'text-success-400',
    },
    {
      title: 'MEV Saved',
      value: formatCurrency(data.total_mev_saved || 0),
      change: 8.7,
      changeLabel: '24h',
      icon: TrendingUp,
      color: 'text-success-400',
    },
    {
      title: 'Protection Fees',
      value: formatCurrency(data.total_fees_paid || 0),
      change: -3.2,
      changeLabel: '24h',
      icon: Zap,
      color: 'text-warning-400',
    },
  ], [data]);

  const overallStatus = data.overall_status || 'inactive';
  const statusColor = overallStatus === 'active' ? 'text-success-400' : 
                     overallStatus === 'warning' ? 'text-warning-400' : 'text-error-400';
  const StatusIcon = overallStatus === 'active' ? ShieldCheck : 
                     overallStatus === 'warning' ? ShieldAlert : ShieldX;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-dark-700 rounded w-1/3 animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <StatusIcon className={`w-6 h-6 ${statusColor}`} />
          <div>
            <h3 className="text-lg font-semibold text-dark-100">
              MEV Protection Status
            </h3>
            <p className="text-sm text-dark-400">
              Real-time MEV protection across all networks
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className={`
            px-3 py-1 rounded-full text-sm font-medium
            ${overallStatus === 'active' ? 'bg-success-500/20 text-success-400' :
              overallStatus === 'warning' ? 'bg-warning-500/20 text-warning-400' :
              'bg-error-500/20 text-error-400'}
          `}>
            {overallStatus.toUpperCase()}
          </div>
          
          <button className="btn-secondary text-sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="glass-effect rounded-lg p-4 border border-error-500/30">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-error-400" />
            <span className="font-medium text-error-400">Error Loading Protection Status</span>
          </div>
          <p className="text-sm text-dark-300">
            Failed to fetch MEV protection status. Please try again.
          </p>
        </div>
      )}

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <div key={index} className="metric-card">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Icon className={`w-5 h-5 ${metric.color}`} />
                  <span className="text-sm font-medium text-dark-300">{metric.title}</span>
                </div>
                {metric.status && (
                  <div className={`w-2 h-2 rounded-full ${
                    metric.status === 'active' ? 'bg-success-500' :
                    metric.status === 'warning' ? 'bg-warning-500' : 'bg-error-500'
                  }`} />
                )}
              </div>
              
              <div className="text-2xl font-bold text-dark-100 mb-1">
                {metric.value}
              </div>
              
              {metric.change !== undefined && (
                <div className="flex items-center space-x-1">
                  <span className={`text-sm font-medium ${getProfitColor(metric.change)}`}>
                    {metric.change > 0 ? '+' : ''}{formatPercentage(metric.change)}
                  </span>
                  {metric.changeLabel && (
                    <span className="text-xs text-dark-500">{metric.changeLabel}</span>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Network Protection Status */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-lg font-semibold text-dark-100">
            Network Protection Status
          </h4>
          <div className="text-sm text-dark-400">
            {data.networks?.filter(n => n.status === 'active').length || 0} of {data.networks?.length || 0} active
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {data.networks?.map((network) => (
            <NetworkProtectionCard
              key={network.network}
              {...network}
            />
          )) || (
            <div className="col-span-full text-center py-8">
              <Shield className="w-12 h-12 text-dark-600 mx-auto mb-4" />
              <p className="text-dark-500">No network protection data available</p>
            </div>
          )}
        </div>
      </div>

      {/* Protection Statistics */}
      {detailed && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="glass-effect rounded-xl p-6">
            <h4 className="text-lg font-semibold text-dark-100 mb-4">
              Protection Efficiency
            </h4>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-dark-400">MEV Attacks Prevented</span>
                <span className="text-lg font-semibold text-success-400">
                  {formatNumber(156)}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-dark-400">Average Protection Fee</span>
                <span className="text-lg font-semibold text-warning-400">
                  {formatCurrency(0.018)}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-dark-400">Protection ROI</span>
                <span className="text-lg font-semibold text-success-400">
                  {formatPercentage(650)}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-dark-400">Failed Protections</span>
                <span className="text-lg font-semibold text-error-400">
                  {formatNumber(8)}
                </span>
              </div>
            </div>
          </div>

          <div className="glass-effect rounded-xl p-6">
            <h4 className="text-lg font-semibold text-dark-100 mb-4">
              Recent Protection Events
            </h4>

            <div className="space-y-3">
              {[
                {
                  network: 'Ethereum',
                  type: 'Sandwich Attack Prevented',
                  saved: 1250,
                  fee: 0.025,
                  time: '2 minutes ago',
                },
                {
                  network: 'Polygon',
                  type: 'Front-running Blocked',
                  saved: 340,
                  fee: 0.012,
                  time: '8 minutes ago',
                },
                {
                  network: 'Arbitrum',
                  type: 'MEV Bundle Protected',
                  saved: 890,
                  fee: 0.018,
                  time: '15 minutes ago',
                },
              ].map((event, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-dark-700/30">
                  <div>
                    <div className="text-sm font-medium text-dark-100">
                      {event.type}
                    </div>
                    <div className="text-xs text-dark-400">
                      {event.network} • {event.time}
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-sm font-medium text-success-400">
                      +{formatCurrency(event.saved)}
                    </div>
                    <div className="text-xs text-warning-400">
                      Fee: {formatCurrency(event.fee)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Info Panel */}
      <div className="glass-effect rounded-lg p-4 border border-primary-500/30">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-primary-400 mt-0.5" />
          <div>
            <h5 className="font-medium text-primary-400 mb-2">MEV Protection Information</h5>
            <div className="text-sm text-dark-300 space-y-1">
              <p>• MEV protection services help prevent front-running and sandwich attacks</p>
              <p>• Protection fees are typically 0.01% - 0.05% of transaction value</p>
              <p>• Success rates vary by network and protection provider</p>
              <p>• Some networks have built-in MEV protection mechanisms</p>
            </div>

            <div className="mt-3 pt-3 border-t border-dark-700">
              <a
                href="#"
                className="inline-flex items-center text-sm text-primary-400 hover:text-primary-300 transition-colors"
              >
                Learn more about MEV protection
                <ExternalLink className="w-3 h-3 ml-1" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
