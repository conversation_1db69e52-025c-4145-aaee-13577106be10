import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { OpportunityDetectionService, ArbitrageType } from '../../../backend/services/OpportunityDetectionService.js';
import { PriceFeedService } from '../../../backend/services/PriceFeedService.js';
import { TokenDiscoveryService } from '../../../backend/services/TokenDiscoveryService.js';

describe('OpportunityDetectionService', () => {
  let opportunityService: OpportunityDetectionService;
  let priceFeedService: PriceFeedService;
  let tokenDiscoveryService: TokenDiscoveryService;

  beforeEach(() => {
    priceFeedService = new PriceFeedService();
    tokenDiscoveryService = new TokenDiscoveryService();
    opportunityService = new OpportunityDetectionService(priceFeedService, tokenDiscoveryService);
  });

  afterEach(async () => {
    if (opportunityService) {
      await opportunityService.stop();
    }
    if (priceFeedService) {
      await priceFeedService.stop();
    }
    if (tokenDiscoveryService) {
      await tokenDiscoveryService.stop();
    }
  });

  describe('Service Lifecycle', () => {
    it('should start and stop successfully', async () => {
      expect(opportunityService.isRunning()).toBe(false);
      
      await opportunityService.start();
      expect(opportunityService.isRunning()).toBe(true);
      
      await opportunityService.stop();
      expect(opportunityService.isRunning()).toBe(false);
    });
  });

  describe('Opportunity Detection', () => {
    beforeEach(async () => {
      await priceFeedService.start();
      await tokenDiscoveryService.start();
      await opportunityService.start();
    });

    it('should detect intra-chain arbitrage opportunities', async () => {
      // Setup price differences between exchanges
      const ethUniswap = {
        ...global.testUtils.mockPriceData,
        symbol: 'ETH',
        price: 2000,
        source: 'uniswap'
      };
      
      const ethSushiswap = {
        ...global.testUtils.mockPriceData,
        symbol: 'ETH',
        price: 2050, // 2.5% difference
        source: 'sushiswap'
      };

      priceFeedService.updatePriceData(ethUniswap);
      priceFeedService.updatePriceData(ethSushiswap);

      // Wait for opportunity detection
      await global.testUtils.delay(100);

      const opportunities = opportunityService.getActiveOpportunities();
      const intraChainOpps = opportunities.filter(opp => opp.type === ArbitrageType.INTRA_CHAIN);
      
      expect(intraChainOpps.length).toBeGreaterThan(0);
      expect(intraChainOpps[0].profitPercentage).toBeWithinRange(2, 3);
    });

    it('should detect triangular arbitrage opportunities', async () => {
      // Setup triangular arbitrage scenario: ETH -> USDC -> WBTC -> ETH
      const prices = [
        { symbol: 'ETH', price: 2000, source: 'uniswap' },
        { symbol: 'USDC', price: 1, source: 'uniswap' },
        { symbol: 'WBTC', price: 40000, source: 'uniswap' },
        // Create price inefficiency
        { symbol: 'ETH', price: 1980, source: 'balancer' }, // Cheaper ETH on Balancer
      ];

      prices.forEach(price => {
        priceFeedService.updatePriceData({
          ...global.testUtils.mockPriceData,
          ...price
        });
      });

      await global.testUtils.delay(200);

      const opportunities = opportunityService.getActiveOpportunities();
      const triangularOpps = opportunities.filter(opp => opp.type === ArbitrageType.TRIANGULAR);
      
      expect(triangularOpps.length).toBeGreaterThan(0);
    });

    it('should calculate profit accurately', () => {
      const opportunity = {
        buyPrice: 2000,
        sellPrice: 2050,
        amount: 1,
        gasEstimate: 150000,
        gasPrice: 20, // gwei
        flashLoanFee: 0.0005 // 0.05%
      };

      const profit = opportunityService.calculateProfit(opportunity);
      
      expect(profit.grossProfit).toBe(50); // 2050 - 2000
      expect(profit.netProfit).toBeLessThan(50); // After fees
      expect(profit.profitPercentage).toBeWithinRange(2, 3);
    });

    it('should filter opportunities by minimum profit threshold', async () => {
      // Set high minimum profit threshold
      opportunityService.setMinProfitThreshold(100);

      const lowProfitPrices = [
        { symbol: 'ETH', price: 2000, source: 'uniswap' },
        { symbol: 'ETH', price: 2010, source: 'sushiswap' } // Only 0.5% difference
      ];

      lowProfitPrices.forEach(price => {
        priceFeedService.updatePriceData({
          ...global.testUtils.mockPriceData,
          ...price
        });
      });

      await global.testUtils.delay(100);

      const opportunities = opportunityService.getActiveOpportunities();
      expect(opportunities.length).toBe(0);
    });
  });

  describe('Risk Assessment', () => {
    beforeEach(async () => {
      await priceFeedService.start();
      await tokenDiscoveryService.start();
      await opportunityService.start();
    });

    it('should assess slippage risk', () => {
      const opportunity = global.testUtils.mockOpportunity;
      const riskAssessment = opportunityService.assessRisk(opportunity);
      
      expect(riskAssessment.slippageRisk).toBeDefined();
      expect(riskAssessment.slippageRisk).toBeWithinRange(0, 100);
    });

    it('should assess liquidity risk', () => {
      const opportunity = global.testUtils.mockOpportunity;
      const riskAssessment = opportunityService.assessRisk(opportunity);
      
      expect(riskAssessment.liquidityRisk).toBeDefined();
      expect(riskAssessment.liquidityRisk).toBeWithinRange(0, 100);
    });

    it('should calculate confidence score', () => {
      const opportunity = global.testUtils.mockOpportunity;
      const riskAssessment = opportunityService.assessRisk(opportunity);
      
      expect(riskAssessment.confidenceScore).toBeDefined();
      expect(riskAssessment.confidenceScore).toBeWithinRange(0, 100);
    });
  });

  describe('Performance Optimization', () => {
    beforeEach(async () => {
      await priceFeedService.start();
      await tokenDiscoveryService.start();
      await opportunityService.start();
    });

    it('should process opportunities within acceptable time', async () => {
      const startTime = Date.now();
      
      // Add multiple price updates
      for (let i = 0; i < 10; i++) {
        priceFeedService.updatePriceData({
          ...global.testUtils.mockPriceData,
          symbol: `TOKEN${i}`,
          price: 100 + Math.random() * 10
        });
      }

      await global.testUtils.delay(100);
      
      const processingTime = Date.now() - startTime;
      expect(processingTime).toBeLessThan(500); // Should process within 500ms
    });

    it('should limit active opportunities to prevent memory issues', async () => {
      // Generate many opportunities
      for (let i = 0; i < 100; i++) {
        const opportunity = {
          ...global.testUtils.mockOpportunity,
          id: `opp-${i}`,
          timestamp: Date.now() + i
        };
        
        opportunityService.addOpportunity(opportunity);
      }

      const activeOpportunities = opportunityService.getActiveOpportunities();
      expect(activeOpportunities.length).toBeLessThanOrEqual(50); // Should limit to reasonable number
    });
  });

  describe('Event Handling', () => {
    beforeEach(async () => {
      await priceFeedService.start();
      await tokenDiscoveryService.start();
      await opportunityService.start();
    });

    it('should emit opportunity events', (done) => {
      opportunityService.on('opportunity', (opportunity) => {
        expect(opportunity).toBeDefined();
        expect(opportunity.id).toBeDefined();
        expect(opportunity.type).toBeDefined();
        done();
      });

      // Trigger opportunity detection
      const opportunity = global.testUtils.mockOpportunity;
      opportunityService.addOpportunity(opportunity);
    });

    it('should emit opportunity expired events', (done) => {
      const opportunity = {
        ...global.testUtils.mockOpportunity,
        timestamp: Date.now() - 60000 // 1 minute ago
      };

      opportunityService.on('opportunityExpired', (expiredOpp) => {
        expect(expiredOpp.id).toBe(opportunity.id);
        done();
      });

      opportunityService.addOpportunity(opportunity);
      opportunityService.cleanupExpiredOpportunities();
    });
  });
});
