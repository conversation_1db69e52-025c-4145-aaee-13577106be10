import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import { MLLearningService } from './MLLearningService.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';

export interface StrategyDecision {
  selectedStrategy: ArbitrageType;
  confidence: number;
  reasoning: string;
  weight: number;
  marketRegime: string;
  alternativeStrategies: {
    strategy: ArbitrageType;
    weight: number;
    reason: string;
  }[];
  executionParameters: {
    gasLimit: number;
    slippageTolerance: number;
    positionSize: number;
    maxExecutionTime: number;
  };
}

export interface StrategyCandidate {
  strategy: ArbitrageType;
  weight: number;
  successRate: number;
  avgProfit: number;
  riskScore: number;
  recentPerformance: number;
  marketSuitability: number;
}

export class StrategySelectionService extends EventEmitter {
  private mlLearningService: MLLearningService;
  private isRunning = false;
  
  // Strategy selection parameters
  private readonly CONFIDENCE_THRESHOLD = 60;
  private readonly MIN_WEIGHT_THRESHOLD = 0.3;
  private readonly EXPLORATION_RATE = 0.1; // For A/B testing new strategies
  private readonly RISK_TOLERANCE = 70; // Max acceptable risk score

  constructor(mlLearningService: MLLearningService) {
    super();
    this.mlLearningService = mlLearningService;
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Strategy Selection Service...');
    this.isRunning = true;

    // Listen for ML learning updates
    this.mlLearningService.on('learningUpdate', (update) => {
      this.onLearningUpdate(update);
    });

    logger.info('Strategy Selection Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Strategy Selection Service...');
    this.isRunning = false;

    this.mlLearningService.removeAllListeners('learningUpdate');
    logger.info('Strategy Selection Service stopped');
  }

  public async selectStrategy(
    opportunities: ArbitrageOpportunity[],
    marketConditions: any,
    network: string
  ): Promise<StrategyDecision | null> {
    try {
      if (opportunities.length === 0) {
        return null;
      }

      // Get current market regime
      const marketRegime = this.mlLearningService.getCurrentMarketRegime();

      // Evaluate all available strategies
      const strategyCandidates = await this.evaluateStrategies(opportunities, marketConditions, network, marketRegime);

      if (strategyCandidates.length === 0) {
        logger.debug('No suitable strategies found');
        return null;
      }

      // Select best strategy using ML weights and current conditions
      const decision = await this.makeStrategyDecision(strategyCandidates, marketConditions, network, marketRegime);

      // Log decision for learning
      this.logStrategyDecision(decision, strategyCandidates);

      return decision;

    } catch (error) {
      logger.error('Error in strategy selection:', error);
      return null;
    }
  }

  private async evaluateStrategies(
    opportunities: ArbitrageOpportunity[],
    marketConditions: any,
    network: string,
    marketRegime: string
  ): Promise<StrategyCandidate[]> {
    const candidates: StrategyCandidate[] = [];

    // Group opportunities by strategy type
    const strategyGroups = this.groupOpportunitiesByStrategy(opportunities);

    for (const [strategyType, strategyOpportunities] of strategyGroups.entries()) {
      const candidate = await this.evaluateStrategy(
        strategyType,
        strategyOpportunities,
        marketConditions,
        network,
        marketRegime
      );

      if (candidate && this.isStrategyViable(candidate)) {
        candidates.push(candidate);
      }
    }

    return candidates.sort((a, b) => this.calculateStrategyScore(b) - this.calculateStrategyScore(a));
  }

  private groupOpportunitiesByStrategy(opportunities: ArbitrageOpportunity[]): Map<ArbitrageType, ArbitrageOpportunity[]> {
    const groups = new Map<ArbitrageType, ArbitrageOpportunity[]>();

    opportunities.forEach(opp => {
      if (!groups.has(opp.type)) {
        groups.set(opp.type, []);
      }
      groups.get(opp.type)!.push(opp);
    });

    return groups;
  }

  private async evaluateStrategy(
    strategyType: ArbitrageType,
    opportunities: ArbitrageOpportunity[],
    marketConditions: any,
    network: string,
    marketRegime: string
  ): Promise<StrategyCandidate | null> {
    try {
      // Get ML-learned weight for this strategy
      const weight = this.mlLearningService.getStrategyWeight(strategyType, network);

      // Calculate average metrics from opportunities
      const avgProfit = opportunities.reduce((sum, opp) => sum + opp.potentialProfit, 0) / opportunities.length;
      const avgConfidence = opportunities.reduce((sum, opp) => sum + (opp.confidence || 50), 0) / opportunities.length;

      // Get historical performance data
      const performanceSummary = await this.mlLearningService.getStrategyPerformanceSummary();
      const strategyPerformance = performanceSummary?.find(p => 
        p.strategy_type === strategyType && p.network === network
      );

      const successRate = strategyPerformance?.success_rate || 50;
      const recentPerformance = strategyPerformance?.recent_performance_score || 50;

      // Calculate risk score based on strategy type and market conditions
      const riskScore = this.calculateStrategyRisk(strategyType, marketConditions, marketRegime);

      // Calculate market suitability
      const marketSuitability = this.calculateMarketSuitability(strategyType, marketConditions, marketRegime);

      return {
        strategy: strategyType,
        weight,
        successRate,
        avgProfit,
        riskScore,
        recentPerformance,
        marketSuitability
      };

    } catch (error) {
      logger.error(`Error evaluating strategy ${strategyType}:`, error);
      return null;
    }
  }

  private calculateStrategyRisk(strategyType: ArbitrageType, marketConditions: any, marketRegime: string): number {
    let baseRisk = 30; // Base risk score

    // Strategy-specific risk adjustments
    switch (strategyType) {
      case ArbitrageType.INTRA_CHAIN:
        baseRisk += 10;
        break;
      case ArbitrageType.CROSS_CHAIN:
        baseRisk += 25; // Higher risk due to bridge complexity
        break;
      case ArbitrageType.TRIANGULAR:
        baseRisk += 15; // Multiple swap risk
        break;
    }

    // Market condition adjustments
    if (marketConditions.volatility > 20) baseRisk += 15;
    if (marketConditions.networkCongestion > 70) baseRisk += 20;
    if (marketConditions.liquidity < 1000000) baseRisk += 10;

    // Market regime adjustments
    switch (marketRegime) {
      case 'high_volatility':
        baseRisk += 20;
        break;
      case 'low_liquidity':
        baseRisk += 15;
        break;
      case 'stable':
        baseRisk -= 10;
        break;
    }

    return Math.max(0, Math.min(100, baseRisk));
  }

  private calculateMarketSuitability(strategyType: ArbitrageType, marketConditions: any, marketRegime: string): number {
    let suitability = 50; // Base suitability

    // Strategy-specific market preferences
    switch (strategyType) {
      case ArbitrageType.INTRA_CHAIN:
        if (marketConditions.networkCongestion < 50) suitability += 20;
        if (marketConditions.volatility > 10 && marketConditions.volatility < 25) suitability += 15;
        break;
      
      case ArbitrageType.CROSS_CHAIN:
        if (marketConditions.volatility > 15) suitability += 25; // Benefits from price differences
        if (marketRegime === 'high_volatility') suitability += 20;
        if (marketConditions.networkCongestion > 60) suitability -= 20; // Bridge delays
        break;
      
      case ArbitrageType.TRIANGULAR:
        if (marketConditions.liquidity > 2000000) suitability += 20;
        if (marketConditions.volatility < 15) suitability += 15; // Prefers stable conditions
        break;
    }

    return Math.max(0, Math.min(100, suitability));
  }

  private calculateStrategyScore(candidate: StrategyCandidate): number {
    // Weighted scoring algorithm
    const weightScore = candidate.weight * 25; // 25% weight
    const successScore = candidate.successRate * 0.2; // 20% success rate
    const profitScore = Math.min(candidate.avgProfit / 100, 10) * 2; // 20% profit (capped)
    const riskScore = (100 - candidate.riskScore) * 0.15; // 15% risk (inverted)
    const performanceScore = candidate.recentPerformance * 0.1; // 10% recent performance
    const suitabilityScore = candidate.marketSuitability * 0.1; // 10% market suitability

    return weightScore + successScore + profitScore + riskScore + performanceScore + suitabilityScore;
  }

  private isStrategyViable(candidate: StrategyCandidate): boolean {
    return (
      candidate.weight >= this.MIN_WEIGHT_THRESHOLD &&
      candidate.riskScore <= this.RISK_TOLERANCE &&
      candidate.avgProfit > 0
    );
  }

  private async makeStrategyDecision(
    candidates: StrategyCandidate[],
    marketConditions: any,
    network: string,
    marketRegime: string
  ): Promise<StrategyDecision> {
    const bestCandidate = candidates[0];
    const score = this.calculateStrategyScore(bestCandidate);
    const confidence = Math.min(95, Math.max(30, score));

    // Exploration vs exploitation
    let selectedStrategy = bestCandidate.strategy;
    if (Math.random() < this.EXPLORATION_RATE && candidates.length > 1) {
      // Occasionally try alternative strategies for learning
      selectedStrategy = candidates[1].strategy;
      logger.debug(`Exploring alternative strategy: ${selectedStrategy}`);
    }

    // Generate execution parameters based on strategy and conditions
    const executionParameters = this.generateExecutionParameters(
      selectedStrategy,
      marketConditions,
      marketRegime
    );

    // Prepare alternative strategies
    const alternatives = candidates.slice(1, 4).map(candidate => ({
      strategy: candidate.strategy,
      weight: candidate.weight,
      reason: `Score: ${this.calculateStrategyScore(candidate).toFixed(1)}, Risk: ${candidate.riskScore.toFixed(1)}`
    }));

    return {
      selectedStrategy,
      confidence,
      reasoning: this.generateReasoning(bestCandidate, marketConditions, marketRegime),
      weight: bestCandidate.weight,
      marketRegime,
      alternativeStrategies: alternatives,
      executionParameters
    };
  }

  private generateExecutionParameters(strategy: ArbitrageType, marketConditions: any, marketRegime: string) {
    let gasLimit = 300000; // Base gas limit
    let slippageTolerance = 0.5; // Base slippage tolerance
    let positionSize = 1000; // Base position size
    let maxExecutionTime = 30000; // Base execution time (30 seconds)

    // Strategy-specific adjustments
    switch (strategy) {
      case ArbitrageType.CROSS_CHAIN:
        gasLimit *= 2; // Higher gas for cross-chain
        slippageTolerance *= 1.5; // Higher slippage tolerance
        maxExecutionTime *= 3; // Longer execution time
        break;
      case ArbitrageType.TRIANGULAR:
        gasLimit *= 1.5; // Multiple swaps
        slippageTolerance *= 1.2;
        maxExecutionTime *= 2;
        break;
    }

    // Market condition adjustments
    if (marketConditions.networkCongestion > 70) {
      gasLimit *= 1.3;
      maxExecutionTime *= 1.5;
    }

    if (marketConditions.volatility > 20) {
      slippageTolerance *= 1.5;
    }

    // Market regime adjustments
    if (marketRegime === 'high_volatility') {
      slippageTolerance *= 1.3;
      positionSize *= 0.8; // Reduce position size in volatile markets
    }

    return {
      gasLimit: Math.round(gasLimit),
      slippageTolerance: Math.round(slippageTolerance * 100) / 100,
      positionSize: Math.round(positionSize),
      maxExecutionTime: Math.round(maxExecutionTime)
    };
  }

  private generateReasoning(candidate: StrategyCandidate, marketConditions: any, marketRegime: string): string {
    const reasons = [];

    reasons.push(`Strategy weight: ${candidate.weight.toFixed(2)}`);
    reasons.push(`Success rate: ${candidate.successRate.toFixed(1)}%`);
    reasons.push(`Risk score: ${candidate.riskScore.toFixed(1)}`);
    reasons.push(`Market regime: ${marketRegime}`);

    if (candidate.recentPerformance > 70) {
      reasons.push('Strong recent performance');
    }

    if (candidate.marketSuitability > 70) {
      reasons.push('High market suitability');
    }

    if (marketConditions.volatility > 20) {
      reasons.push('High volatility detected');
    }

    return reasons.join(', ');
  }

  private logStrategyDecision(decision: StrategyDecision, candidates: StrategyCandidate[]) {
    logger.info(`Strategy selected: ${decision.selectedStrategy} (confidence: ${decision.confidence.toFixed(1)}%)`);
    logger.debug(`Decision reasoning: ${decision.reasoning}`);
    logger.debug(`Alternatives considered: ${candidates.length}`);
  }

  private onLearningUpdate(update: any) {
    logger.debug(`ML learning update received: ${update.totalStrategies} strategies, regime: ${update.marketRegime}`);
    
    // Emit strategy selection update
    this.emit('strategyUpdate', {
      timestamp: update.timestamp,
      marketRegime: update.marketRegime,
      availableStrategies: update.totalStrategies
    });
  }
}
