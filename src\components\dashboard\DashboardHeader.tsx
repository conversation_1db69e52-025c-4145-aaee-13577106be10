'use client';

import { useState } from 'react';
import { useTheme } from 'next-themes';
import { 
  Sun, 
  Moon, 
  Settings, 
  Bell, 
  Wifi, 
  WifiOff, 
  Activity,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { formatTimeAgo } from '@/lib/utils';
import { SystemHealth } from '@/types';

interface DashboardHeaderProps {
  connectionStatus: string;
  isConnected: boolean;
  lastUpdateTime: Date;
  systemHealth?: SystemHealth;
}

export function DashboardHeader({
  connectionStatus,
  isConnected,
  lastUpdateTime,
  systemHealth,
}: DashboardHeaderProps) {
  const { theme, setTheme } = useTheme();
  const [showNotifications, setShowNotifications] = useState(false);

  const getHealthStatusColor = (status?: string) => {
    switch (status) {
      case 'healthy':
        return 'text-success-400';
      case 'degraded':
        return 'text-warning-400';
      case 'unhealthy':
        return 'text-danger-400';
      default:
        return 'text-neutral';
    }
  };

  const getHealthStatusIcon = (status?: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4" />;
      case 'degraded':
        return <AlertTriangle className="w-4 h-4" />;
      case 'unhealthy':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <header className="bg-dark-800/80 backdrop-blur-sm border-b border-dark-700 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <Activity className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-dark-100">
                  MEV Arbitrage Bot
                </h1>
                <p className="text-xs text-dark-400">
                  Professional Trading Dashboard
                </p>
              </div>
            </div>
          </div>

          {/* Status Indicators */}
          <div className="flex items-center space-x-6">
            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <Wifi className="w-4 h-4 text-success-400" />
              ) : (
                <WifiOff className="w-4 h-4 text-danger-400" />
              )}
              <span className={`text-sm font-medium ${
                isConnected ? 'text-success-400' : 'text-danger-400'
              }`}>
                {connectionStatus}
              </span>
            </div>

            {/* System Health */}
            {systemHealth && (
              <div className="flex items-center space-x-2">
                <div className={getHealthStatusColor(systemHealth.status)}>
                  {getHealthStatusIcon(systemHealth.status)}
                </div>
                <span className={`text-sm font-medium ${getHealthStatusColor(systemHealth.status)}`}>
                  System {systemHealth.status}
                </span>
              </div>
            )}

            {/* Last Update */}
            <div className="flex items-center space-x-2 text-dark-400">
              <RefreshCw className="w-4 h-4" />
              <span className="text-sm">
                Updated {formatTimeAgo(lastUpdateTime)}
              </span>
            </div>

            {/* Divider */}
            <div className="w-px h-6 bg-dark-600"></div>

            {/* Controls */}
            <div className="flex items-center space-x-3">
              {/* Notifications */}
              <div className="relative">
                <button
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="p-2 rounded-lg bg-dark-700 hover:bg-dark-600 transition-colors relative"
                >
                  <Bell className="w-4 h-4 text-dark-300" />
                  {/* Notification badge */}
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-danger-500 rounded-full flex items-center justify-center">
                    <span className="text-xs text-white font-bold">3</span>
                  </div>
                </button>

                {/* Notifications Dropdown */}
                {showNotifications && (
                  <div className="absolute right-0 mt-2 w-80 bg-dark-800 border border-dark-700 rounded-lg shadow-lg z-50">
                    <div className="p-4 border-b border-dark-700">
                      <h3 className="font-semibold text-dark-100">Notifications</h3>
                    </div>
                    <div className="max-h-64 overflow-y-auto">
                      <div className="p-3 border-b border-dark-700 hover:bg-dark-700/50">
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-success-500 rounded-full mt-2"></div>
                          <div>
                            <p className="text-sm text-dark-200">
                              New arbitrage opportunity detected
                            </p>
                            <p className="text-xs text-dark-400">2 minutes ago</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-3 border-b border-dark-700 hover:bg-dark-700/50">
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-warning-500 rounded-full mt-2"></div>
                          <div>
                            <p className="text-sm text-dark-200">
                              High gas fees on Ethereum network
                            </p>
                            <p className="text-xs text-dark-400">5 minutes ago</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-3 hover:bg-dark-700/50">
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary-500 rounded-full mt-2"></div>
                          <div>
                            <p className="text-sm text-dark-200">
                              ML strategy weight updated
                            </p>
                            <p className="text-xs text-dark-400">10 minutes ago</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 border-t border-dark-700">
                      <button className="text-sm text-primary-400 hover:text-primary-300">
                        View all notifications
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Theme Toggle */}
              <button
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="p-2 rounded-lg bg-dark-700 hover:bg-dark-600 transition-colors"
              >
                {theme === 'dark' ? (
                  <Sun className="w-4 h-4 text-dark-300" />
                ) : (
                  <Moon className="w-4 h-4 text-dark-300" />
                )}
              </button>

              {/* Settings */}
              <button
                type="button"
                aria-label="Open settings"
                title="Settings"
                className="p-2 rounded-lg bg-dark-700 hover:bg-dark-600 transition-colors"
              >
                <Settings className="w-4 h-4 text-dark-300" />
              </button>
            </div>
          </div>
        </div>

        {/* Quick Stats Bar */}
        <div className="mt-4 flex items-center justify-between text-sm">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-dark-400">Uptime:</span>
              <span className="text-success-400 font-medium">
                {systemHealth ? Math.round(systemHealth.uptime / 3600) : 0}h
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-dark-400">Active Networks:</span>
              <span className="text-primary-400 font-medium">10</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-dark-400">Services:</span>
              <span className="text-success-400 font-medium">
                {systemHealth ? Object.keys(systemHealth.services || {}).length : 0}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-xs text-dark-500">
            <span>v1.0.0</span>
            <span>•</span>
            <span>Production</span>
          </div>
        </div>
      </div>
    </header>
  );
}
