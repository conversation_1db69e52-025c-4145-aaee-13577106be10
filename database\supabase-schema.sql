-- MEV Arbitrage Bot Supabase Database Schema
-- Run this in your Supabase SQL editor to create the required tables

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ================================
-- TRADES TABLE
-- ================================
CREATE TABLE IF NOT EXISTS trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    assets TEXT[] NOT NULL,
    exchanges TEXT[] NOT NULL,
    executed_profit DECIMAL(18, 8) NOT NULL,
    gas_fees DECIMAL(18, 8) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failed', 'pending')),
    timestamp TIMESTAMPTZ NOT NULL,
    network VARCHAR(50) NOT NULL,
    tx_hash VARCHAR(66),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for trades table
CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status);
CREATE INDEX IF NOT EXISTS idx_trades_network ON trades(network);
CREATE INDEX IF NOT EXISTS idx_trades_opportunity_id ON trades(opportunity_id);
CREATE INDEX IF NOT EXISTS idx_trades_created_at ON trades(created_at DESC);

-- ================================
-- OPPORTUNITIES TABLE
-- ================================
CREATE TABLE IF NOT EXISTS opportunities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL,
    assets TEXT[] NOT NULL,
    exchanges TEXT[] NOT NULL,
    potential_profit DECIMAL(18, 8) NOT NULL,
    profit_percentage DECIMAL(8, 4) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    network VARCHAR(50) NOT NULL,
    confidence DECIMAL(5, 2) NOT NULL CHECK (confidence >= 0 AND confidence <= 100),
    slippage DECIMAL(5, 4) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for opportunities table
CREATE INDEX IF NOT EXISTS idx_opportunities_timestamp ON opportunities(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_opportunities_network ON opportunities(network);
CREATE INDEX IF NOT EXISTS idx_opportunities_type ON opportunities(type);
CREATE INDEX IF NOT EXISTS idx_opportunities_profit ON opportunities(potential_profit DESC);
CREATE INDEX IF NOT EXISTS idx_opportunities_created_at ON opportunities(created_at DESC);

-- ================================
-- PERFORMANCE METRICS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE UNIQUE NOT NULL,
    total_trades INTEGER NOT NULL DEFAULT 0,
    successful_trades INTEGER NOT NULL DEFAULT 0,
    failed_trades INTEGER NOT NULL DEFAULT 0,
    total_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    total_loss DECIMAL(18, 8) NOT NULL DEFAULT 0,
    net_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    win_rate DECIMAL(5, 2) NOT NULL DEFAULT 0,
    avg_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    avg_loss DECIMAL(18, 8) NOT NULL DEFAULT 0,
    profit_factor DECIMAL(10, 4) NOT NULL DEFAULT 0,
    sharpe_ratio DECIMAL(10, 4) NOT NULL DEFAULT 0,
    max_drawdown DECIMAL(10, 4) NOT NULL DEFAULT 0,
    roi DECIMAL(10, 4) NOT NULL DEFAULT 0,
    daily_volume DECIMAL(18, 8) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance metrics table
CREATE INDEX IF NOT EXISTS idx_performance_metrics_date ON performance_metrics(date DESC);

-- ================================
-- TOKENS TABLE (Enhanced for Multi-Chain)
-- ================================
CREATE TABLE IF NOT EXISTS tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    address VARCHAR(255) NOT NULL, -- Increased for non-EVM addresses
    name VARCHAR(255) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    decimals INTEGER NOT NULL DEFAULT 18,
    network VARCHAR(50) NOT NULL,
    chain_id INTEGER,
    is_whitelisted BOOLEAN NOT NULL DEFAULT false,
    is_blacklisted BOOLEAN NOT NULL DEFAULT false,
    safety_score INTEGER NOT NULL DEFAULT 0 CHECK (safety_score >= 0 AND safety_score <= 100),
    liquidity DECIMAL(18, 8) NOT NULL DEFAULT 0,
    total_supply VARCHAR(78),
    market_cap DECIMAL(18, 8) DEFAULT 0,
    volume_24h DECIMAL(18, 8) DEFAULT 0,
    price_usd DECIMAL(18, 8) DEFAULT 0,
    price_change_24h DECIMAL(8, 4) DEFAULT 0,
    market_cap_rank INTEGER DEFAULT 999,
    liquidity_score INTEGER DEFAULT 0,
    coingecko_id VARCHAR(100),
    contract_verified BOOLEAN DEFAULT false,
    dexes TEXT[], -- Available DEXes for this token
    bridge_protocols TEXT[], -- Available bridge protocols
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(address, network)
);

-- ================================
-- NETWORKS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS networks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    network_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    chain_id INTEGER,
    native_currency_name VARCHAR(50) NOT NULL,
    native_currency_symbol VARCHAR(10) NOT NULL,
    native_currency_decimals INTEGER NOT NULL DEFAULT 18,
    rpc_urls TEXT[] NOT NULL,
    block_explorer_urls TEXT[] NOT NULL,
    dexes TEXT[] NOT NULL,
    bridge_protocols TEXT[] NOT NULL,
    avg_gas_price DECIMAL(18, 8) NOT NULL,
    avg_block_time DECIMAL(8, 4) NOT NULL,
    bridge_fee_percentage DECIMAL(8, 6) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    risk_score INTEGER DEFAULT 0 CHECK (risk_score >= 0 AND risk_score <= 100),
    tvl DECIMAL(18, 8) DEFAULT 0, -- Total Value Locked
    daily_volume DECIMAL(18, 8) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ================================
-- CROSS_CHAIN_OPPORTUNITIES TABLE
-- ================================
CREATE TABLE IF NOT EXISTS cross_chain_opportunities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) UNIQUE NOT NULL,
    token_symbol VARCHAR(20) NOT NULL,
    source_network VARCHAR(50) NOT NULL,
    target_network VARCHAR(50) NOT NULL,
    source_dex VARCHAR(100) NOT NULL,
    target_dex VARCHAR(100) NOT NULL,
    source_price DECIMAL(18, 8) NOT NULL,
    target_price DECIMAL(18, 8) NOT NULL,
    price_difference DECIMAL(18, 8) NOT NULL,
    price_difference_percentage DECIMAL(8, 4) NOT NULL,
    estimated_profit DECIMAL(18, 8) NOT NULL,
    gas_costs_source DECIMAL(18, 8) NOT NULL,
    gas_costs_target DECIMAL(18, 8) NOT NULL,
    gas_costs_bridge DECIMAL(18, 8) NOT NULL,
    gas_costs_total DECIMAL(18, 8) NOT NULL,
    bridge_fee DECIMAL(18, 8) NOT NULL,
    slippage_impact DECIMAL(18, 8) NOT NULL,
    net_profit DECIMAL(18, 8) NOT NULL,
    net_profit_percentage DECIMAL(8, 4) NOT NULL,
    confidence INTEGER NOT NULL CHECK (confidence >= 0 AND confidence <= 100),
    risk_score INTEGER NOT NULL CHECK (risk_score >= 0 AND risk_score <= 100),
    execution_time INTEGER NOT NULL, -- in minutes
    min_trade_size DECIMAL(18, 8) NOT NULL,
    max_trade_size DECIMAL(18, 8) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'executed', 'expired', 'invalid')),
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for tokens table
CREATE INDEX IF NOT EXISTS idx_tokens_address_network ON tokens(address, network);
CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON tokens(symbol);
CREATE INDEX IF NOT EXISTS idx_tokens_whitelisted ON tokens(is_whitelisted);
CREATE INDEX IF NOT EXISTS idx_tokens_safety_score ON tokens(safety_score DESC);
CREATE INDEX IF NOT EXISTS idx_tokens_network ON tokens(network);
CREATE INDEX IF NOT EXISTS idx_tokens_market_cap_rank ON tokens(market_cap_rank);
CREATE INDEX IF NOT EXISTS idx_tokens_volume_24h ON tokens(volume_24h DESC);
CREATE INDEX IF NOT EXISTS idx_tokens_coingecko_id ON tokens(coingecko_id);

-- Create indexes for networks table
CREATE INDEX IF NOT EXISTS idx_networks_network_id ON networks(network_id);
CREATE INDEX IF NOT EXISTS idx_networks_chain_id ON networks(chain_id);
CREATE INDEX IF NOT EXISTS idx_networks_is_active ON networks(is_active);
CREATE INDEX IF NOT EXISTS idx_networks_risk_score ON networks(risk_score);

-- Create indexes for cross_chain_opportunities table
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_token_symbol ON cross_chain_opportunities(token_symbol);
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_source_network ON cross_chain_opportunities(source_network);
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_target_network ON cross_chain_opportunities(target_network);
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_net_profit_percentage ON cross_chain_opportunities(net_profit_percentage DESC);
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_status ON cross_chain_opportunities(status);
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_created_at ON cross_chain_opportunities(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_expires_at ON cross_chain_opportunities(expires_at);
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_confidence ON cross_chain_opportunities(confidence DESC);
CREATE INDEX IF NOT EXISTS idx_cross_chain_opportunities_risk_score ON cross_chain_opportunities(risk_score);

-- ================================
-- SYSTEM ALERTS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    is_resolved BOOLEAN NOT NULL DEFAULT false,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for system alerts table
CREATE INDEX IF NOT EXISTS idx_system_alerts_type ON system_alerts(type);
CREATE INDEX IF NOT EXISTS idx_system_alerts_severity ON system_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_system_alerts_resolved ON system_alerts(is_resolved);
CREATE INDEX IF NOT EXISTS idx_system_alerts_created_at ON system_alerts(created_at DESC);

-- ================================
-- VALIDATIONS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS validations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) NOT NULL,
    validation_type VARCHAR(50) NOT NULL CHECK (validation_type IN ('pre_execution', 'profit_check', 'risk_assessment', 'liquidity_check', 'gas_estimation')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'passed', 'failed', 'timeout')),
    result JSONB,
    error_message TEXT,
    validation_time_ms INTEGER,
    confidence_score DECIMAL(5, 2) CHECK (confidence_score >= 0 AND confidence_score <= 100),
    risk_score DECIMAL(5, 2) CHECK (risk_score >= 0 AND risk_score <= 100),
    network VARCHAR(50) NOT NULL,
    validator_service VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Create indexes for validations table
CREATE INDEX IF NOT EXISTS idx_validations_opportunity_id ON validations(opportunity_id);
CREATE INDEX IF NOT EXISTS idx_validations_type ON validations(validation_type);
CREATE INDEX IF NOT EXISTS idx_validations_status ON validations(status);
CREATE INDEX IF NOT EXISTS idx_validations_network ON validations(network);
CREATE INDEX IF NOT EXISTS idx_validations_created_at ON validations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_validations_confidence_score ON validations(confidence_score DESC);

-- ================================
-- FLASH LOAN QUOTES TABLE
-- ================================
CREATE TABLE IF NOT EXISTS flash_loan_quotes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) NOT NULL,
    provider VARCHAR(50) NOT NULL CHECK (provider IN ('aave_v3', 'balancer_v2', 'dydx', 'uniswap_v3')),
    asset_address VARCHAR(255) NOT NULL,
    asset_symbol VARCHAR(20) NOT NULL,
    amount DECIMAL(36, 18) NOT NULL,
    fee_amount DECIMAL(36, 18) NOT NULL,
    fee_percentage DECIMAL(8, 6) NOT NULL,
    max_amount DECIMAL(36, 18) NOT NULL,
    available_liquidity DECIMAL(36, 18) NOT NULL,
    execution_gas_estimate INTEGER NOT NULL,
    network VARCHAR(50) NOT NULL,
    quote_valid_until TIMESTAMPTZ NOT NULL,
    priority_score INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'used', 'expired', 'invalid')),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for flash_loan_quotes table
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_opportunity_id ON flash_loan_quotes(opportunity_id);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_provider ON flash_loan_quotes(provider);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_asset_symbol ON flash_loan_quotes(asset_symbol);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_network ON flash_loan_quotes(network);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_status ON flash_loan_quotes(status);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_priority_score ON flash_loan_quotes(priority_score DESC);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_valid_until ON flash_loan_quotes(quote_valid_until);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_fee_percentage ON flash_loan_quotes(fee_percentage);

-- ================================
-- CONFIGURATION TABLE
-- ================================
CREATE TABLE IF NOT EXISTS configuration (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL DEFAULT 'string' CHECK (type IN ('string', 'number', 'boolean', 'json')),
    is_sensitive BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for configuration table
CREATE INDEX IF NOT EXISTS idx_configuration_key ON configuration(key);

-- ================================
-- TRIGGERS FOR UPDATED_AT
-- ================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_trades_updated_at BEFORE UPDATE ON trades
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_performance_metrics_updated_at BEFORE UPDATE ON performance_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_configuration_updated_at BEFORE UPDATE ON configuration
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================
-- ROW LEVEL SECURITY (RLS)
-- ================================
-- Enable RLS on all tables
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE opportunities ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE configuration ENABLE ROW LEVEL SECURITY;

-- Create policies for service role access
CREATE POLICY "Service role can manage trades" ON trades
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage opportunities" ON opportunities
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage performance_metrics" ON performance_metrics
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage tokens" ON tokens
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage system_alerts" ON system_alerts
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage configuration" ON configuration
    FOR ALL USING (auth.role() = 'service_role');

-- ================================
-- INITIAL CONFIGURATION DATA
-- ================================
INSERT INTO configuration (key, value, description, type) VALUES
    ('emergency_stop', 'false', 'Emergency stop flag for the trading system', 'boolean'),
    ('min_profit_threshold', '50', 'Minimum profit threshold in USD', 'number'),
    ('max_position_size', '10000', 'Maximum position size in USD', 'number'),
    ('max_slippage', '0.5', 'Maximum allowed slippage percentage', 'number'),
    ('gas_price_multiplier', '1.1', 'Gas price multiplier for transactions', 'number'),
    ('max_daily_loss', '1000', 'Maximum daily loss limit in USD', 'number'),
    ('position_size_percentage', '2', 'Position size as percentage of total capital', 'number')
ON CONFLICT (key) DO NOTHING;

-- ================================
-- MACHINE LEARNING TABLES
-- ================================

-- Strategy Performance Tracking
CREATE TABLE IF NOT EXISTS strategy_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_type VARCHAR(50) NOT NULL,
    strategy_variant VARCHAR(100), -- For A/B testing different variants
    trade_id UUID REFERENCES trades(id),
    opportunity_id VARCHAR(255) NOT NULL,
    execution_parameters JSONB NOT NULL, -- Gas limits, slippage tolerance, etc.
    market_conditions JSONB NOT NULL, -- Volatility, liquidity, network congestion
    success BOOLEAN NOT NULL,
    profit_loss DECIMAL(18, 8) NOT NULL,
    execution_time_ms INTEGER NOT NULL,
    gas_cost DECIMAL(18, 8) NOT NULL,
    slippage_actual DECIMAL(8, 4),
    confidence_score DECIMAL(5, 2) NOT NULL,
    risk_score DECIMAL(5, 2) NOT NULL,
    network VARCHAR(50) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Strategy Weights and Learning Data
CREATE TABLE IF NOT EXISTS strategy_weights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    strategy_type VARCHAR(50) NOT NULL,
    strategy_variant VARCHAR(100),
    network VARCHAR(50) NOT NULL,
    weight DECIMAL(8, 6) NOT NULL DEFAULT 1.0,
    success_rate DECIMAL(5, 2) NOT NULL DEFAULT 0,
    avg_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    avg_risk_adjusted_return DECIMAL(8, 4) NOT NULL DEFAULT 0,
    total_executions INTEGER NOT NULL DEFAULT 0,
    recent_performance_score DECIMAL(5, 2) NOT NULL DEFAULT 50,
    market_regime VARCHAR(50) NOT NULL DEFAULT 'normal',
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(strategy_type, strategy_variant, network, market_regime)
);

-- Market Regime Classification
CREATE TABLE IF NOT EXISTS market_regimes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    regime_name VARCHAR(50) NOT NULL,
    volatility_min DECIMAL(8, 4) NOT NULL,
    volatility_max DECIMAL(8, 4) NOT NULL,
    liquidity_min DECIMAL(18, 8) NOT NULL,
    network_congestion_min DECIMAL(5, 2) NOT NULL,
    network_congestion_max DECIMAL(5, 2) NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ML Model Training Data
CREATE TABLE IF NOT EXISTS ml_training_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    feature_vector JSONB NOT NULL, -- Normalized features for ML model
    target_success BOOLEAN NOT NULL,
    target_profit DECIMAL(18, 8) NOT NULL,
    strategy_type VARCHAR(50) NOT NULL,
    network VARCHAR(50) NOT NULL,
    market_regime VARCHAR(50) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Strategy Learning Events
CREATE TABLE IF NOT EXISTS strategy_learning_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL, -- 'weight_update', 'strategy_disabled', 'new_variant_tested'
    strategy_type VARCHAR(50) NOT NULL,
    strategy_variant VARCHAR(100),
    old_weight DECIMAL(8, 6),
    new_weight DECIMAL(8, 6),
    reason TEXT NOT NULL,
    confidence DECIMAL(5, 2) NOT NULL,
    network VARCHAR(50) NOT NULL,
    market_regime VARCHAR(50),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for ML tables
CREATE INDEX IF NOT EXISTS idx_strategy_performance_strategy_type ON strategy_performance(strategy_type);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_network ON strategy_performance(network);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_timestamp ON strategy_performance(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_success ON strategy_performance(success);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_trade_id ON strategy_performance(trade_id);

CREATE INDEX IF NOT EXISTS idx_strategy_weights_strategy_type ON strategy_weights(strategy_type);
CREATE INDEX IF NOT EXISTS idx_strategy_weights_network ON strategy_weights(network);
CREATE INDEX IF NOT EXISTS idx_strategy_weights_market_regime ON strategy_weights(market_regime);
CREATE INDEX IF NOT EXISTS idx_strategy_weights_weight ON strategy_weights(weight DESC);

CREATE INDEX IF NOT EXISTS idx_ml_training_data_strategy_type ON ml_training_data(strategy_type);
CREATE INDEX IF NOT EXISTS idx_ml_training_data_network ON ml_training_data(network);
CREATE INDEX IF NOT EXISTS idx_ml_training_data_timestamp ON ml_training_data(timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_strategy_learning_events_strategy_type ON strategy_learning_events(strategy_type);
CREATE INDEX IF NOT EXISTS idx_strategy_learning_events_event_type ON strategy_learning_events(event_type);
CREATE INDEX IF NOT EXISTS idx_strategy_learning_events_created_at ON strategy_learning_events(created_at DESC);

-- ================================
-- VIEWS FOR ANALYTICS
-- ================================
CREATE OR REPLACE VIEW daily_performance AS
SELECT
    DATE(timestamp) as date,
    COUNT(*) as total_trades,
    COUNT(*) FILTER (WHERE status = 'success') as successful_trades,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_trades,
    SUM(executed_profit) FILTER (WHERE status = 'success') as total_profit,
    SUM(ABS(executed_profit)) FILTER (WHERE status = 'failed') as total_loss,
    SUM(executed_profit) as net_profit,
    ROUND(
        (COUNT(*) FILTER (WHERE status = 'success')::DECIMAL / COUNT(*)) * 100,
        2
    ) as win_rate,
    AVG(executed_profit) FILTER (WHERE status = 'success') as avg_profit,
    AVG(ABS(executed_profit)) FILTER (WHERE status = 'failed') as avg_loss,
    SUM(gas_fees) as total_gas_fees
FROM trades
GROUP BY DATE(timestamp)
ORDER BY date DESC;

CREATE OR REPLACE VIEW network_performance AS
SELECT
    network,
    COUNT(*) as total_trades,
    COUNT(*) FILTER (WHERE status = 'success') as successful_trades,
    SUM(executed_profit) as net_profit,
    AVG(executed_profit) FILTER (WHERE status = 'success') as avg_profit,
    SUM(gas_fees) as total_gas_fees
FROM trades
GROUP BY network
ORDER BY net_profit DESC;

-- ML Analytics Views
CREATE OR REPLACE VIEW strategy_performance_summary AS
SELECT
    sp.strategy_type,
    sp.strategy_variant,
    sp.network,
    COUNT(*) as total_executions,
    COUNT(*) FILTER (WHERE sp.success = true) as successful_executions,
    ROUND((COUNT(*) FILTER (WHERE sp.success = true)::DECIMAL / COUNT(*)) * 100, 2) as success_rate,
    AVG(sp.profit_loss) FILTER (WHERE sp.success = true) as avg_profit,
    AVG(ABS(sp.profit_loss)) FILTER (WHERE sp.success = false) as avg_loss,
    AVG(sp.execution_time_ms) as avg_execution_time,
    AVG(sp.gas_cost) as avg_gas_cost,
    AVG(sp.confidence_score) as avg_confidence,
    AVG(sp.risk_score) as avg_risk_score,
    sw.weight as current_weight,
    sw.recent_performance_score
FROM strategy_performance sp
LEFT JOIN strategy_weights sw ON sp.strategy_type = sw.strategy_type
    AND sp.strategy_variant = sw.strategy_variant
    AND sp.network = sw.network
WHERE sp.timestamp >= NOW() - INTERVAL '30 days'
GROUP BY sp.strategy_type, sp.strategy_variant, sp.network, sw.weight, sw.recent_performance_score
ORDER BY success_rate DESC, avg_profit DESC;
