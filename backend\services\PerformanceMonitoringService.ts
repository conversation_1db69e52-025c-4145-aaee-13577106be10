import logger from '../utils/logger.js';
import config from '../config/index.js';
import { databaseManager } from './DatabaseConnectionManager.js';
import { enhancedCacheService } from './EnhancedCacheService.js';
import { dataRoutingService } from './DataRoutingService.js';

export interface PerformanceMetrics {
  timestamp: number;
  serviceLatency: Record<string, number>;
  databaseQueryTime: Record<string, number>;
  apiResponseTime: Record<string, number>;
  queueOperationTime: number;
  systemUptime: number;
  cacheHitRatio: number;
  errorRates: Record<string, number>;
  throughput: Record<string, number>;
  memoryUsage: number;
  cpuUsage: number;
  crossChainMetrics: CrossChainMetrics;
}

export interface CrossChainMetrics {
  totalCrossChainOperations: number;
  successfulCrossChainOperations: number;
  failedCrossChainOperations: number;
  averageBridgeLatency: number;
  averageExecutionTime: number;
  bridgeSuccessRate: number;
  networkPairPerformance: Record<string, NetworkPairMetrics>;
  bridgeProtocolMetrics: Record<string, BridgeProtocolMetrics>;
  crossChainProfitMetrics: CrossChainProfitMetrics;
  recentOperations: CrossChainOperation[];
}

export interface NetworkPairMetrics {
  sourceNetwork: string;
  targetNetwork: string;
  totalOperations: number;
  successfulOperations: number;
  averageLatency: number;
  averageBridgeFee: number;
  averageProfit: number;
  lastOperation: number;
}

export interface BridgeProtocolMetrics {
  protocolName: string;
  totalOperations: number;
  successfulOperations: number;
  averageLatency: number;
  averageFee: number;
  reliability: number;
  lastUsed: number;
}

export interface CrossChainProfitMetrics {
  totalProfit: number;
  averageProfit: number;
  profitByNetworkPair: Record<string, number>;
  profitByToken: Record<string, number>;
  bridgeFeeImpact: number;
  slippageImpact: number;
}

export interface CrossChainOperation {
  operationId: string;
  sourceNetwork: string;
  targetNetwork: string;
  tokenSymbol: string;
  bridgeProtocol: string;
  startTime: number;
  endTime?: number;
  status: 'pending' | 'bridge_in_progress' | 'completed' | 'failed';
  bridgeLatency?: number;
  totalLatency?: number;
  bridgeFee?: number;
  profit?: number;
  errorMessage?: string;
}

export interface PerformanceAlert {
  id: string;
  type: 'latency' | 'uptime' | 'cache' | 'error_rate' | 'throughput';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: number;
  resolved: boolean;
  threshold: number;
  actualValue: number;
}

export interface PerformanceTarget {
  metric: string;
  threshold: number;
  operator: 'lt' | 'gt' | 'eq';
  alertSeverity: 'low' | 'medium' | 'high' | 'critical';
}

export class PerformanceMonitoringService {
  private metrics: PerformanceMetrics;
  private alerts: Map<string, PerformanceAlert> = new Map();
  private performanceHistory: PerformanceMetrics[] = [];
  private maxHistorySize = 1440; // 24 hours of minute-by-minute data
  private startTime = Date.now();
  private operationTimers: Map<string, number> = new Map();

  // Cross-chain specific tracking
  private crossChainOperations: Map<string, CrossChainOperation> = new Map();
  private networkPairMetrics: Map<string, NetworkPairMetrics> = new Map();
  private bridgeProtocolMetrics: Map<string, BridgeProtocolMetrics> = new Map();
  private crossChainProfitHistory: number[] = [];
  private readonly maxCrossChainHistory = 100;

  // Performance targets based on system requirements
  private performanceTargets: PerformanceTarget[] = [
    { metric: 'serviceLatency', threshold: parseInt(config.MAX_SERVICE_LATENCY), operator: 'lt', alertSeverity: 'high' },
    { metric: 'databaseQueryTime', threshold: parseInt(config.MAX_DATABASE_QUERY_TIME), operator: 'lt', alertSeverity: 'medium' },
    { metric: 'apiResponseTime', threshold: parseInt(config.MAX_API_RESPONSE_TIME), operator: 'lt', alertSeverity: 'medium' },
    { metric: 'queueOperationTime', threshold: parseInt(config.MAX_QUEUE_OPERATION_TIME), operator: 'lt', alertSeverity: 'high' },
    { metric: 'systemUptime', threshold: parseInt(config.TARGET_UPTIME_PERCENTAGE), operator: 'gt', alertSeverity: 'critical' },
    { metric: 'cacheHitRatio', threshold: parseInt(config.CACHE_HIT_RATIO_THRESHOLD), operator: 'gt', alertSeverity: 'medium' }
  ];

  constructor() {
    this.metrics = this.initializeMetrics();
    this.startMonitoring();
    this.startAlertProcessing();
  }

  private initializeMetrics(): PerformanceMetrics {
    return {
      timestamp: Date.now(),
      serviceLatency: {},
      databaseQueryTime: {},
      apiResponseTime: {},
      queueOperationTime: 0,
      systemUptime: 0,
      cacheHitRatio: 0,
      errorRates: {},
      throughput: {},
      memoryUsage: 0,
      cpuUsage: 0,
      crossChainMetrics: {
        totalCrossChainOperations: 0,
        successfulCrossChainOperations: 0,
        failedCrossChainOperations: 0,
        averageBridgeLatency: 0,
        averageExecutionTime: 0,
        bridgeSuccessRate: 0,
        networkPairPerformance: {},
        bridgeProtocolMetrics: {},
        crossChainProfitMetrics: {
          totalProfit: 0,
          averageProfit: 0,
          profitByNetworkPair: {},
          profitByToken: {},
          bridgeFeeImpact: 0,
          slippageImpact: 0
        },
        recentOperations: []
      }
    };
  }

  private startMonitoring(): void {
    // Collect metrics every 30 seconds
    setInterval(() => {
      this.collectMetrics();
    }, 30000);

    // Store historical data every minute
    setInterval(() => {
      this.storeHistoricalData();
    }, 60000);

    // Check performance targets every 15 seconds
    setInterval(() => {
      this.checkPerformanceTargets();
    }, 15000);

    // Cleanup old data every hour
    setInterval(() => {
      this.cleanupOldData();
    }, 3600000);
  }

  private async collectMetrics(): Promise<void> {
    try {
      const timestamp = Date.now();
      
      // Collect database health metrics
      const dbHealth = databaseManager.getHealthStatus();
      dbHealth.forEach((health, service) => {
        this.metrics.databaseQueryTime[service] = health.latency;
        this.metrics.errorRates[service] = health.errorCount;
      });

      // Collect cache metrics
      const cacheMetrics = enhancedCacheService.getMetrics();
      this.metrics.cacheHitRatio = cacheMetrics.hitRatio;

      // Collect data routing metrics
      const routingMetrics = dataRoutingService.getMetrics();
      this.metrics.serviceLatency['dataRouting'] = routingMetrics.writeLatency;
      this.metrics.throughput['dataRouting'] = routingMetrics.totalWrites + routingMetrics.totalReads;

      // Calculate system uptime
      const uptimeMs = timestamp - this.startTime;
      this.metrics.systemUptime = (uptimeMs / (1000 * 60 * 60 * 24)) * 100; // Percentage of day

      // Collect system resource metrics
      this.metrics.memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
      this.metrics.cpuUsage = process.cpuUsage().user / 1000000; // Convert to seconds

      // Collect cross-chain metrics
      this.collectCrossChainMetrics();

      this.metrics.timestamp = timestamp;

      // Store metrics in InfluxDB for historical analysis
      await this.storeMetricsInInfluxDB();

    } catch (error) {
      logger.error('Error collecting performance metrics:', error);
    }
  }

  private async storeMetricsInInfluxDB(): Promise<void> {
    try {
      await dataRoutingService.routeData('performance.metrics', {
        tags: {
          service: 'mev-arbitrage-bot',
          environment: config.NODE_ENV
        },
        fields: {
          ...this.metrics.serviceLatency,
          ...this.metrics.databaseQueryTime,
          ...this.metrics.apiResponseTime,
          queueOperationTime: this.metrics.queueOperationTime,
          systemUptime: this.metrics.systemUptime,
          cacheHitRatio: this.metrics.cacheHitRatio,
          memoryUsage: this.metrics.memoryUsage,
          cpuUsage: this.metrics.cpuUsage
        },
        timestamp: this.metrics.timestamp
      });
    } catch (error) {
      logger.error('Error storing metrics in InfluxDB:', error);
    }
  }

  private storeHistoricalData(): void {
    this.performanceHistory.push({ ...this.metrics });
    
    if (this.performanceHistory.length > this.maxHistorySize) {
      this.performanceHistory.shift();
    }
  }

  private checkPerformanceTargets(): void {
    this.performanceTargets.forEach(target => {
      const currentValue = this.getMetricValue(target.metric);
      if (currentValue === null) return;

      const isViolation = this.checkThresholdViolation(currentValue, target.threshold, target.operator);
      
      if (isViolation) {
        this.createAlert(target, currentValue);
      } else {
        this.resolveAlert(target.metric);
      }
    });
  }

  private getMetricValue(metricName: string): number | null {
    switch (metricName) {
      case 'serviceLatency':
        return Math.max(...Object.values(this.metrics.serviceLatency), 0);
      case 'databaseQueryTime':
        return Math.max(...Object.values(this.metrics.databaseQueryTime), 0);
      case 'apiResponseTime':
        return Math.max(...Object.values(this.metrics.apiResponseTime), 0);
      case 'queueOperationTime':
        return this.metrics.queueOperationTime;
      case 'systemUptime':
        return this.metrics.systemUptime;
      case 'cacheHitRatio':
        return this.metrics.cacheHitRatio;
      default:
        return null;
    }
  }

  private checkThresholdViolation(value: number, threshold: number, operator: string): boolean {
    switch (operator) {
      case 'lt':
        return value >= threshold;
      case 'gt':
        return value <= threshold;
      case 'eq':
        return value !== threshold;
      default:
        return false;
    }
  }

  private createAlert(target: PerformanceTarget, actualValue: number): void {
    const alertId = `${target.metric}_${target.threshold}`;
    const existingAlert = this.alerts.get(alertId);

    if (existingAlert && !existingAlert.resolved) {
      return; // Alert already exists and is not resolved
    }

    const alert: PerformanceAlert = {
      id: alertId,
      type: this.getAlertType(target.metric),
      severity: target.alertSeverity,
      message: this.generateAlertMessage(target, actualValue),
      timestamp: Date.now(),
      resolved: false,
      threshold: target.threshold,
      actualValue
    };

    this.alerts.set(alertId, alert);
    logger.warn(`Performance alert created: ${alert.message}`);

    // Store alert in database
    this.storeAlert(alert);
  }

  private resolveAlert(metricName: string): void {
    const alertId = `${metricName}_${this.getThresholdForMetric(metricName)}`;
    const alert = this.alerts.get(alertId);

    if (alert && !alert.resolved) {
      alert.resolved = true;
      logger.info(`Performance alert resolved: ${alert.message}`);
      this.storeAlert(alert);
    }
  }

  private getAlertType(metricName: string): PerformanceAlert['type'] {
    if (metricName.includes('latency') || metricName.includes('Time')) return 'latency';
    if (metricName === 'systemUptime') return 'uptime';
    if (metricName === 'cacheHitRatio') return 'cache';
    if (metricName.includes('error')) return 'error_rate';
    return 'throughput';
  }

  private generateAlertMessage(target: PerformanceTarget, actualValue: number): string {
    const operator = target.operator === 'lt' ? 'exceeded' : 'below';
    return `${target.metric} ${operator} threshold: ${actualValue} ${target.operator === 'lt' ? '>' : '<'} ${target.threshold}`;
  }

  private getThresholdForMetric(metricName: string): number {
    const target = this.performanceTargets.find(t => t.metric === metricName);
    return target ? target.threshold : 0;
  }

  private async storeAlert(alert: PerformanceAlert): Promise<void> {
    try {
      await dataRoutingService.routeData('system.alert', {
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        timestamp: alert.timestamp,
        resolved: alert.resolved,
        threshold: alert.threshold,
        actual_value: alert.actualValue
      });
    } catch (error) {
      logger.error('Error storing alert:', error);
    }
  }

  private cleanupOldData(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    // Clean up old alerts
    this.alerts.forEach((alert, id) => {
      if (alert.timestamp < cutoffTime && alert.resolved) {
        this.alerts.delete(id);
      }
    });

    logger.debug('Cleaned up old performance monitoring data');
  }

  private startAlertProcessing(): void {
    // Process and escalate alerts every 5 minutes
    setInterval(() => {
      this.processAlerts();
    }, 300000);
  }

  private processAlerts(): void {
    const criticalAlerts = Array.from(this.alerts.values()).filter(
      alert => !alert.resolved && alert.severity === 'critical'
    );

    const highAlerts = Array.from(this.alerts.values()).filter(
      alert => !alert.resolved && alert.severity === 'high'
    );

    if (criticalAlerts.length > 0) {
      logger.error(`${criticalAlerts.length} critical performance alerts active`);
    }

    if (highAlerts.length > 0) {
      logger.warn(`${highAlerts.length} high severity performance alerts active`);
    }
  }

  // Public interface methods
  public startOperation(operationId: string): void {
    this.operationTimers.set(operationId, Date.now());
  }

  public endOperation(operationId: string, category: 'service' | 'database' | 'api' | 'queue'): number {
    const startTime = this.operationTimers.get(operationId);
    if (!startTime) return 0;

    const duration = Date.now() - startTime;
    this.operationTimers.delete(operationId);

    // Update appropriate metric
    switch (category) {
      case 'service':
        this.metrics.serviceLatency[operationId] = duration;
        break;
      case 'database':
        this.metrics.databaseQueryTime[operationId] = duration;
        break;
      case 'api':
        this.metrics.apiResponseTime[operationId] = duration;
        break;
      case 'queue':
        this.metrics.queueOperationTime = duration;
        break;
    }

    return duration;
  }

  public recordThroughput(service: string, operations: number): void {
    this.metrics.throughput[service] = (this.metrics.throughput[service] || 0) + operations;
  }

  public recordError(service: string): void {
    this.metrics.errorRates[service] = (this.metrics.errorRates[service] || 0) + 1;
  }

  public getCurrentMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public getHistoricalMetrics(hours: number = 1): PerformanceMetrics[] {
    const cutoffTime = Date.now() - (hours * 60 * 60 * 1000);
    return this.performanceHistory.filter(metric => metric.timestamp >= cutoffTime);
  }

  public getActiveAlerts(): PerformanceAlert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  public getSystemHealth(): { status: 'healthy' | 'degraded' | 'critical', score: number, issues: string[] } {
    const activeAlerts = this.getActiveAlerts();
    const criticalAlerts = activeAlerts.filter(alert => alert.severity === 'critical');
    const highAlerts = activeAlerts.filter(alert => alert.severity === 'high');

    let status: 'healthy' | 'degraded' | 'critical' = 'healthy';
    let score = 100;
    const issues: string[] = [];

    if (criticalAlerts.length > 0) {
      status = 'critical';
      score -= criticalAlerts.length * 30;
      issues.push(`${criticalAlerts.length} critical performance issues`);
    }

    if (highAlerts.length > 0) {
      if (status !== 'critical') status = 'degraded';
      score -= highAlerts.length * 15;
      issues.push(`${highAlerts.length} high severity performance issues`);
    }

    if (this.metrics.systemUptime < parseInt(config.TARGET_UPTIME_PERCENTAGE)) {
      if (status !== 'critical') status = 'degraded';
      score -= 20;
      issues.push('System uptime below target');
    }

    return { status, score: Math.max(0, score), issues };
  }

  /**
   * Collect cross-chain specific metrics
   */
  private collectCrossChainMetrics(): void {
    try {
      // Calculate totals
      const totalOperations = this.crossChainOperations.size;
      const completedOperations = Array.from(this.crossChainOperations.values())
        .filter(op => op.status === 'completed');
      const failedOperations = Array.from(this.crossChainOperations.values())
        .filter(op => op.status === 'failed');

      this.metrics.crossChainMetrics.totalCrossChainOperations = totalOperations;
      this.metrics.crossChainMetrics.successfulCrossChainOperations = completedOperations.length;
      this.metrics.crossChainMetrics.failedCrossChainOperations = failedOperations.length;

      // Calculate success rate
      this.metrics.crossChainMetrics.bridgeSuccessRate = totalOperations > 0 ?
        (completedOperations.length / totalOperations) * 100 : 0;

      // Calculate average latencies
      if (completedOperations.length > 0) {
        const totalBridgeLatency = completedOperations
          .reduce((sum, op) => sum + (op.bridgeLatency || 0), 0);
        const totalExecutionTime = completedOperations
          .reduce((sum, op) => sum + (op.totalLatency || 0), 0);

        this.metrics.crossChainMetrics.averageBridgeLatency = totalBridgeLatency / completedOperations.length;
        this.metrics.crossChainMetrics.averageExecutionTime = totalExecutionTime / completedOperations.length;
      }

      // Update network pair performance
      this.metrics.crossChainMetrics.networkPairPerformance = Object.fromEntries(this.networkPairMetrics);

      // Update bridge protocol metrics
      this.metrics.crossChainMetrics.bridgeProtocolMetrics = Object.fromEntries(this.bridgeProtocolMetrics);

      // Calculate profit metrics
      this.updateCrossChainProfitMetrics();

      // Update recent operations (last 10)
      this.metrics.crossChainMetrics.recentOperations = Array.from(this.crossChainOperations.values())
        .sort((a, b) => b.startTime - a.startTime)
        .slice(0, 10);

    } catch (error) {
      logger.error('Error collecting cross-chain metrics:', error);
    }
  }

  /**
   * Update cross-chain profit metrics
   */
  private updateCrossChainProfitMetrics(): void {
    const completedOperations = Array.from(this.crossChainOperations.values())
      .filter(op => op.status === 'completed' && op.profit !== undefined);

    if (completedOperations.length === 0) return;

    const totalProfit = completedOperations.reduce((sum, op) => sum + (op.profit || 0), 0);
    const averageProfit = totalProfit / completedOperations.length;

    this.metrics.crossChainMetrics.crossChainProfitMetrics.totalProfit = totalProfit;
    this.metrics.crossChainMetrics.crossChainProfitMetrics.averageProfit = averageProfit;

    // Calculate profit by network pair
    const profitByPair: Record<string, number> = {};
    completedOperations.forEach(op => {
      const pairKey = `${op.sourceNetwork}-${op.targetNetwork}`;
      profitByPair[pairKey] = (profitByPair[pairKey] || 0) + (op.profit || 0);
    });
    this.metrics.crossChainMetrics.crossChainProfitMetrics.profitByNetworkPair = profitByPair;

    // Calculate profit by token
    const profitByToken: Record<string, number> = {};
    completedOperations.forEach(op => {
      profitByToken[op.tokenSymbol] = (profitByToken[op.tokenSymbol] || 0) + (op.profit || 0);
    });
    this.metrics.crossChainMetrics.crossChainProfitMetrics.profitByToken = profitByToken;

    // Calculate bridge fee impact (simplified)
    const totalBridgeFees = completedOperations.reduce((sum, op) => sum + (op.bridgeFee || 0), 0);
    this.metrics.crossChainMetrics.crossChainProfitMetrics.bridgeFeeImpact =
      totalProfit > 0 ? (totalBridgeFees / totalProfit) * 100 : 0;
  }

  /**
   * Start a cross-chain operation tracking
   */
  public startCrossChainOperation(
    operationId: string,
    sourceNetwork: string,
    targetNetwork: string,
    tokenSymbol: string,
    bridgeProtocol: string
  ): void {

    const operation: CrossChainOperation = {
      operationId,
      sourceNetwork,
      targetNetwork,
      tokenSymbol,
      bridgeProtocol,
      startTime: Date.now(),
      status: 'pending'
    };

    this.crossChainOperations.set(operationId, operation);

    // Initialize network pair metrics if not exists
    const pairKey = `${sourceNetwork}-${targetNetwork}`;
    if (!this.networkPairMetrics.has(pairKey)) {
      this.networkPairMetrics.set(pairKey, {
        sourceNetwork,
        targetNetwork,
        totalOperations: 0,
        successfulOperations: 0,
        averageLatency: 0,
        averageBridgeFee: 0,
        averageProfit: 0,
        lastOperation: Date.now()
      });
    }

    // Initialize bridge protocol metrics if not exists
    if (!this.bridgeProtocolMetrics.has(bridgeProtocol)) {
      this.bridgeProtocolMetrics.set(bridgeProtocol, {
        protocolName: bridgeProtocol,
        totalOperations: 0,
        successfulOperations: 0,
        averageLatency: 0,
        averageFee: 0,
        reliability: 0,
        lastUsed: Date.now()
      });
    }

    logger.debug(`Started tracking cross-chain operation: ${operationId}`);
  }

  /**
   * Update cross-chain operation status
   */
  public updateCrossChainOperation(
    operationId: string,
    updates: Partial<CrossChainOperation>
  ): void {

    const operation = this.crossChainOperations.get(operationId);
    if (!operation) {
      logger.warn(`Cross-chain operation not found: ${operationId}`);
      return;
    }

    // Update operation
    Object.assign(operation, updates);

    // Calculate latencies if operation completed
    if (updates.status === 'completed' && updates.endTime) {
      operation.totalLatency = updates.endTime - operation.startTime;
    }

    // Update network pair metrics
    this.updateNetworkPairMetrics(operation);

    // Update bridge protocol metrics
    this.updateBridgeProtocolMetrics(operation);

    logger.debug(`Updated cross-chain operation: ${operationId}`, updates);
  }

  /**
   * Update network pair metrics
   */
  private updateNetworkPairMetrics(operation: CrossChainOperation): void {
    const pairKey = `${operation.sourceNetwork}-${operation.targetNetwork}`;
    const metrics = this.networkPairMetrics.get(pairKey);

    if (!metrics) return;

    metrics.totalOperations++;
    metrics.lastOperation = Date.now();

    if (operation.status === 'completed') {
      metrics.successfulOperations++;

      if (operation.totalLatency) {
        metrics.averageLatency = (metrics.averageLatency * (metrics.successfulOperations - 1) + operation.totalLatency) / metrics.successfulOperations;
      }

      if (operation.bridgeFee) {
        metrics.averageBridgeFee = (metrics.averageBridgeFee * (metrics.successfulOperations - 1) + operation.bridgeFee) / metrics.successfulOperations;
      }

      if (operation.profit) {
        metrics.averageProfit = (metrics.averageProfit * (metrics.successfulOperations - 1) + operation.profit) / metrics.successfulOperations;
      }
    }
  }

  /**
   * Update bridge protocol metrics
   */
  private updateBridgeProtocolMetrics(operation: CrossChainOperation): void {
    const metrics = this.bridgeProtocolMetrics.get(operation.bridgeProtocol);

    if (!metrics) return;

    metrics.totalOperations++;
    metrics.lastUsed = Date.now();

    if (operation.status === 'completed') {
      metrics.successfulOperations++;

      if (operation.bridgeLatency) {
        metrics.averageLatency = (metrics.averageLatency * (metrics.successfulOperations - 1) + operation.bridgeLatency) / metrics.successfulOperations;
      }

      if (operation.bridgeFee) {
        metrics.averageFee = (metrics.averageFee * (metrics.successfulOperations - 1) + operation.bridgeFee) / metrics.successfulOperations;
      }
    }

    // Calculate reliability
    metrics.reliability = metrics.totalOperations > 0 ?
      (metrics.successfulOperations / metrics.totalOperations) * 100 : 0;
  }

  /**
   * Complete a cross-chain operation
   */
  public completeCrossChainOperation(
    operationId: string,
    bridgeLatency: number,
    bridgeFee: number,
    profit: number,
    success: boolean = true
  ): void {

    this.updateCrossChainOperation(operationId, {
      status: success ? 'completed' : 'failed',
      endTime: Date.now(),
      bridgeLatency,
      bridgeFee,
      profit
    });

    // Add to profit history
    if (success && profit > 0) {
      this.crossChainProfitHistory.push(profit);
      if (this.crossChainProfitHistory.length > this.maxCrossChainHistory) {
        this.crossChainProfitHistory.shift();
      }
    }

    logger.info(`Cross-chain operation ${success ? 'completed' : 'failed'}: ${operationId}`, {
      bridgeLatency,
      bridgeFee,
      profit,
      success
    });
  }

  /**
   * Get cross-chain performance statistics
   */
  public getCrossChainStats(): { [key: string]: any } {
    return {
      totalOperations: this.metrics.crossChainMetrics.totalCrossChainOperations,
      successRate: this.metrics.crossChainMetrics.bridgeSuccessRate.toFixed(2) + '%',
      averageBridgeLatency: Math.round(this.metrics.crossChainMetrics.averageBridgeLatency) + 'ms',
      averageExecutionTime: Math.round(this.metrics.crossChainMetrics.averageExecutionTime) + 'ms',
      totalProfit: this.metrics.crossChainMetrics.crossChainProfitMetrics.totalProfit.toFixed(2),
      averageProfit: this.metrics.crossChainMetrics.crossChainProfitMetrics.averageProfit.toFixed(2),
      bridgeFeeImpact: this.metrics.crossChainMetrics.crossChainProfitMetrics.bridgeFeeImpact.toFixed(2) + '%',
      activeOperations: Array.from(this.crossChainOperations.values())
        .filter(op => op.status === 'pending' || op.status === 'bridge_in_progress').length,
      networkPairs: Object.keys(this.metrics.crossChainMetrics.networkPairPerformance).length,
      bridgeProtocols: Object.keys(this.metrics.crossChainMetrics.bridgeProtocolMetrics).length
    };
  }

  /**
   * Get detailed cross-chain metrics
   */
  public getDetailedCrossChainMetrics(): CrossChainMetrics {
    return { ...this.metrics.crossChainMetrics };
  }

  /**
   * Clean up old cross-chain operations
   */
  public cleanupOldCrossChainOperations(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours

    for (const [operationId, operation] of this.crossChainOperations) {
      if (operation.startTime < cutoffTime &&
          (operation.status === 'completed' || operation.status === 'failed')) {
        this.crossChainOperations.delete(operationId);
      }
    }

    logger.debug(`Cleaned up old cross-chain operations, remaining: ${this.crossChainOperations.size}`);
  }
}

export const performanceMonitoringService = new PerformanceMonitoringService();
