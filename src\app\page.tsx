'use client';

import { Suspense } from 'react';
import { Dashboard } from '@/components/Dashboard';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';

export default function HomePage() {
  return (
    <ErrorBoundary>
      <main className="min-h-screen">
        <Suspense fallback={<LoadingSpinner />}>
          <Dashboard />
        </Suspense>
      </main>
    </ErrorBoundary>
  );
}
