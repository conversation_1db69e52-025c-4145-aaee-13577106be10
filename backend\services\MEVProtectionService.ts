import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import axios from 'axios';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';

export interface MEVProtectionResult {
  success: boolean;
  transactionHash?: string;
  bundleHash?: string;
  protectionMethod: MEVProtectionMethod;
  gasUsed?: number;
  effectiveGasPrice?: number;
  mevRevenue?: number;
  error?: string;
  executionTime: number;
  l2BatchId?: string;
  sequencerPriority?: number;
}

export interface L2SequencerConfig {
  sequencerUrl: string;
  priorityFeeEndpoint: string;
  batchSubmissionInterval: number;
  maxBatchSize: number;
  priorityGasMultiplier: number;
}

export interface L2BatchSubmission {
  batchId: string;
  transactions: string[];
  targetSequencerSlot: number;
  priorityLevel: number;
  estimatedInclusionTime: number;
}

export enum MEVProtectionMethod {
  FLASHBOTS_PROTECT = 'flashbots_protect',
  FLASHBOTS_AUCTION = 'flashbots_auction',
  PRIVATE_MEMPOOL = 'private_mempool',
  DYNAMIC_GAS = 'dynamic_gas',
  L2_SEQUENCER_PRIORITY = 'l2_sequencer_priority',
  L2_BATCH_OPTIMIZATION = 'l2_batch_optimization',
  STANDARD = 'standard'
}

export interface TransactionBundle {
  transactions: string[];
  blockNumber?: number;
  minTimestamp?: number;
  maxTimestamp?: number;
  revertingTxHashes?: string[];
}

export interface FlashbotsBundle {
  signedTransactions: string[];
  targetBlockNumber: number;
  opts?: {
    minTimestamp?: number;
    maxTimestamp?: number;
    revertingTxHashes?: string[];
  };
}

export class MEVProtectionService extends EventEmitter {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private flashbotsProviders: Map<string, any> = new Map();
  private isRunning = false;
  
  // MEV Protection parameters
  private readonly enableMevProtection = config.ENABLE_MEV_PROTECTION === 'true';
  private readonly flashbotsRelayUrl = config.FLASHBOTS_RELAY_URL;
  private readonly flashbotsProtectUrl = config.FLASHBOTS_PROTECT_URL;
  private readonly mevProtectionTimeout = parseInt(config.MEV_PROTECTION_TIMEOUT);
  private readonly enablePrivateMempool = config.ENABLE_PRIVATE_MEMPOOL === 'true';
  private readonly dynamicGasPricing = config.DYNAMIC_GAS_PRICING === 'true';
  private readonly mevProtectionFallback = config.MEV_PROTECTION_FALLBACK === 'true';

  // Network-specific MEV protection availability
  private readonly mevProtectionSupport: Map<string, MEVProtectionMethod[]> = new Map([
    ['ethereum', [MEVProtectionMethod.FLASHBOTS_PROTECT, MEVProtectionMethod.FLASHBOTS_AUCTION, MEVProtectionMethod.DYNAMIC_GAS]],
    ['polygon', [MEVProtectionMethod.PRIVATE_MEMPOOL, MEVProtectionMethod.DYNAMIC_GAS]],
    ['bsc', [MEVProtectionMethod.PRIVATE_MEMPOOL, MEVProtectionMethod.DYNAMIC_GAS]],
    ['arbitrum', [MEVProtectionMethod.L2_SEQUENCER_PRIORITY, MEVProtectionMethod.L2_BATCH_OPTIMIZATION, MEVProtectionMethod.DYNAMIC_GAS]],
    ['optimism', [MEVProtectionMethod.L2_SEQUENCER_PRIORITY, MEVProtectionMethod.L2_BATCH_OPTIMIZATION, MEVProtectionMethod.DYNAMIC_GAS]],
    ['base', [MEVProtectionMethod.L2_SEQUENCER_PRIORITY, MEVProtectionMethod.DYNAMIC_GAS]],
    ['avalanche', [MEVProtectionMethod.PRIVATE_MEMPOOL, MEVProtectionMethod.DYNAMIC_GAS]],
    ['fantom', [MEVProtectionMethod.DYNAMIC_GAS]]
  ]);

  // L2-specific sequencer endpoints and configurations
  private readonly l2SequencerConfigs: Map<string, L2SequencerConfig> = new Map([
    ['arbitrum', {
      sequencerUrl: config.ARBITRUM_SEQUENCER_URL || 'https://arb1.arbitrum.io/rpc',
      priorityFeeEndpoint: 'https://arb1.arbitrum.io/rpc',
      batchSubmissionInterval: 15000, // 15 seconds
      maxBatchSize: 100,
      priorityGasMultiplier: 1.1
    }],
    ['optimism', {
      sequencerUrl: config.OPTIMISM_SEQUENCER_URL || 'https://mainnet.optimism.io',
      priorityFeeEndpoint: 'https://mainnet.optimism.io',
      batchSubmissionInterval: 12000, // 12 seconds
      maxBatchSize: 150,
      priorityGasMultiplier: 1.05
    }],
    ['base', {
      sequencerUrl: config.BASE_SEQUENCER_URL || 'https://mainnet.base.org',
      priorityFeeEndpoint: 'https://mainnet.base.org',
      batchSubmissionInterval: 10000, // 10 seconds
      maxBatchSize: 120,
      priorityGasMultiplier: 1.08
    }]
  ]);

  constructor() {
    super();
    this.initializeProviders();
    this.initializeFlashbotsProviders();
  }

  private initializeProviders() {
    // Initialize L1 providers
    this.providers.set('ethereum', new ethers.JsonRpcProvider(config.ETHEREUM_RPC_URL));
    this.providers.set('polygon', new ethers.JsonRpcProvider(config.POLYGON_RPC_URL));
    this.providers.set('bsc', new ethers.JsonRpcProvider(config.BSC_RPC_URL));
    this.providers.set('avalanche', new ethers.JsonRpcProvider(config.AVALANCHE_RPC_URL));
    this.providers.set('fantom', new ethers.JsonRpcProvider(config.FANTOM_RPC_URL));

    // Initialize L2 providers with enhanced configurations
    this.providers.set('arbitrum', new ethers.JsonRpcProvider(config.ARBITRUM_RPC_URL));
    this.providers.set('optimism', new ethers.JsonRpcProvider(config.OPTIMISM_RPC_URL));
    this.providers.set('base', new ethers.JsonRpcProvider(config.BASE_RPC_URL));

    // Initialize L2-specific sequencer connections
    this.initializeL2Sequencers();
  }

  private async initializeL2Sequencers() {
    try {
      for (const [network, config] of this.l2SequencerConfigs) {
        // Test sequencer connectivity
        const sequencerHealth = await this.testSequencerHealth(network, config);
        if (sequencerHealth) {
          logger.info(`L2 sequencer initialized for ${network}`);
        } else {
          logger.warn(`L2 sequencer connection failed for ${network}`);
        }
      }
    } catch (error) {
      logger.error('Failed to initialize L2 sequencers:', error);
    }
  }

  private async testSequencerHealth(network: string, config: L2SequencerConfig): Promise<boolean> {
    try {
      const response = await axios.post(config.sequencerUrl, {
        method: 'eth_blockNumber',
        params: [],
        id: 1,
        jsonrpc: '2.0'
      }, {
        timeout: 5000,
        headers: { 'Content-Type': 'application/json' }
      });

      return response.data && !response.data.error;
    } catch (error) {
      logger.warn(`Sequencer health check failed for ${network}:`, error);
      return false;
    }
  }

  private async initializeFlashbotsProviders() {
    if (!this.enableMevProtection) return;

    try {
      // Initialize Flashbots provider for Ethereum
      if (config.FLASHBOTS_PRIVATE_KEY) {
        // In production, would use actual Flashbots SDK
        logger.info('Flashbots provider initialized for Ethereum');
      }
    } catch (error) {
      logger.error('Failed to initialize Flashbots providers:', error);
    }
  }

  public async start() {
    if (this.isRunning) return;
    
    logger.info('Starting MEV Protection Service...');
    this.isRunning = true;
    
    if (this.enableMevProtection) {
      logger.info('MEV Protection enabled');
    } else {
      logger.warn('MEV Protection disabled - transactions will use standard submission');
    }
    
    logger.info('MEV Protection Service started');
  }

  public async stop() {
    if (!this.isRunning) return;
    
    logger.info('Stopping MEV Protection Service...');
    this.isRunning = false;
    logger.info('MEV Protection Service stopped');
  }

  /**
   * Main method to submit transaction with MEV protection
   */
  public async submitTransactionWithProtection(
    opportunity: ArbitrageOpportunity,
    signedTransaction: string,
    wallet: ethers.Wallet
  ): Promise<MEVProtectionResult> {
    
    const startTime = Date.now();
    
    try {
      if (!this.enableMevProtection) {
        return await this.submitStandardTransaction(signedTransaction, opportunity.network, startTime);
      }

      // Determine best MEV protection method for the network
      const protectionMethod = this.selectOptimalProtectionMethod(opportunity);
      
      logger.info(`Using MEV protection method: ${protectionMethod} for opportunity ${opportunity.id}`);

      switch (protectionMethod) {
        case MEVProtectionMethod.FLASHBOTS_PROTECT:
          return await this.submitViaFlashbotsProtect(signedTransaction, opportunity, startTime);

        case MEVProtectionMethod.FLASHBOTS_AUCTION:
          return await this.submitViaFlashbotsAuction(signedTransaction, opportunity, wallet, startTime);

        case MEVProtectionMethod.PRIVATE_MEMPOOL:
          return await this.submitViaPrivateMempool(signedTransaction, opportunity, startTime);

        case MEVProtectionMethod.DYNAMIC_GAS:
          return await this.submitWithDynamicGas(signedTransaction, opportunity, wallet, startTime);

        case MEVProtectionMethod.L2_SEQUENCER_PRIORITY:
          return await this.submitViaL2SequencerPriority(signedTransaction, opportunity, startTime);

        case MEVProtectionMethod.L2_BATCH_OPTIMIZATION:
          return await this.submitViaL2BatchOptimization(signedTransaction, opportunity, startTime);

        default:
          return await this.submitStandardTransaction(signedTransaction, opportunity.network, startTime);
      }

    } catch (error) {
      logger.error(`MEV protection failed for opportunity ${opportunity.id}:`, error);
      
      // Fallback to standard submission if enabled
      if (this.mevProtectionFallback) {
        logger.info(`Falling back to standard transaction submission for opportunity ${opportunity.id}`);
        return await this.submitStandardTransaction(signedTransaction, opportunity.network, startTime);
      }
      
      return {
        success: false,
        protectionMethod: MEVProtectionMethod.STANDARD,
        error: `MEV protection failed: ${error instanceof Error ? error.message : String(error)}`,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Select optimal MEV protection method based on network and opportunity
   */
  private selectOptimalProtectionMethod(opportunity: ArbitrageOpportunity): MEVProtectionMethod {
    const supportedMethods = this.mevProtectionSupport.get(opportunity.network) || [];

    if (supportedMethods.length === 0) {
      return MEVProtectionMethod.STANDARD;
    }

    // For Ethereum, prefer Flashbots Protect for high-value opportunities
    if (opportunity.network === 'ethereum' && opportunity.potentialProfit > 500) {
      if (supportedMethods.includes(MEVProtectionMethod.FLASHBOTS_PROTECT)) {
        return MEVProtectionMethod.FLASHBOTS_PROTECT;
      }
      if (supportedMethods.includes(MEVProtectionMethod.FLASHBOTS_AUCTION)) {
        return MEVProtectionMethod.FLASHBOTS_AUCTION;
      }
    }

    // For L2 networks (Arbitrum, Optimism, Base), prefer sequencer-based protection
    const l2Networks = ['arbitrum', 'optimism', 'base'];
    if (l2Networks.includes(opportunity.network)) {
      // For high-value opportunities, use batch optimization
      if (opportunity.potentialProfit > 200 && supportedMethods.includes(MEVProtectionMethod.L2_BATCH_OPTIMIZATION)) {
        return MEVProtectionMethod.L2_BATCH_OPTIMIZATION;
      }

      // For medium-value opportunities, use sequencer priority
      if (opportunity.potentialProfit > 50 && supportedMethods.includes(MEVProtectionMethod.L2_SEQUENCER_PRIORITY)) {
        return MEVProtectionMethod.L2_SEQUENCER_PRIORITY;
      }
    }

    // For other networks or lower-value opportunities, use private mempool if available
    if (supportedMethods.includes(MEVProtectionMethod.PRIVATE_MEMPOOL)) {
      return MEVProtectionMethod.PRIVATE_MEMPOOL;
    }

    // Fallback to dynamic gas pricing
    if (supportedMethods.includes(MEVProtectionMethod.DYNAMIC_GAS)) {
      return MEVProtectionMethod.DYNAMIC_GAS;
    }

    return MEVProtectionMethod.STANDARD;
  }

  /**
   * Submit transaction via Flashbots Protect
   */
  private async submitViaFlashbotsProtect(
    signedTransaction: string,
    opportunity: ArbitrageOpportunity,
    startTime: number
  ): Promise<MEVProtectionResult> {
    
    try {
      // In production, would use actual Flashbots Protect API
      const response = await axios.post(this.flashbotsProtectUrl, {
        method: 'eth_sendRawTransaction',
        params: [signedTransaction],
        id: 1,
        jsonrpc: '2.0'
      }, {
        timeout: this.mevProtectionTimeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.error) {
        throw new Error(response.data.error.message);
      }

      return {
        success: true,
        transactionHash: response.data.result,
        protectionMethod: MEVProtectionMethod.FLASHBOTS_PROTECT,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      logger.error('Flashbots Protect submission failed:', error);
      throw error;
    }
  }

  /**
   * Submit transaction via Flashbots Auction
   */
  private async submitViaFlashbotsAuction(
    signedTransaction: string,
    opportunity: ArbitrageOpportunity,
    wallet: ethers.Wallet,
    startTime: number
  ): Promise<MEVProtectionResult> {
    
    try {
      const provider = this.providers.get(opportunity.network);
      if (!provider) {
        throw new Error(`No provider for network: ${opportunity.network}`);
      }

      // Get current block number
      const currentBlock = await provider.getBlockNumber();
      const targetBlock = currentBlock + 1;

      // Create Flashbots bundle
      const bundle: FlashbotsBundle = {
        signedTransactions: [signedTransaction],
        targetBlockNumber: targetBlock,
        opts: {
          minTimestamp: Math.floor(Date.now() / 1000),
          maxTimestamp: Math.floor(Date.now() / 1000) + 120 // 2 minutes
        }
      };

      // In production, would use actual Flashbots SDK to submit bundle
      logger.info(`Simulating Flashbots bundle submission for block ${targetBlock}`);
      
      // Simulate bundle submission
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        bundleHash: `0x${Math.random().toString(16).substr(2, 64)}`,
        protectionMethod: MEVProtectionMethod.FLASHBOTS_AUCTION,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      logger.error('Flashbots Auction submission failed:', error);
      throw error;
    }
  }

  /**
   * Submit transaction via private mempool
   */
  private async submitViaPrivateMempool(
    signedTransaction: string,
    opportunity: ArbitrageOpportunity,
    startTime: number
  ): Promise<MEVProtectionResult> {
    
    try {
      // In production, would use network-specific private mempool services
      // For example: Eden Network for Ethereum, or similar services for other chains
      
      logger.info(`Submitting via private mempool for ${opportunity.network}`);
      
      // Simulate private mempool submission
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const provider = this.providers.get(opportunity.network);
      if (!provider) {
        throw new Error(`No provider for network: ${opportunity.network}`);
      }

      // In production, would submit to actual private mempool
      const txResponse = await provider.broadcastTransaction(signedTransaction);

      return {
        success: true,
        transactionHash: txResponse.hash,
        protectionMethod: MEVProtectionMethod.PRIVATE_MEMPOOL,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      logger.error('Private mempool submission failed:', error);
      throw error;
    }
  }

  /**
   * Submit transaction with dynamic gas pricing
   */
  private async submitWithDynamicGas(
    signedTransaction: string,
    opportunity: ArbitrageOpportunity,
    wallet: ethers.Wallet,
    startTime: number
  ): Promise<MEVProtectionResult> {

    try {
      const provider = this.providers.get(opportunity.network);
      if (!provider) {
        throw new Error(`No provider for network: ${opportunity.network}`);
      }

      // Calculate optimal gas price based on network conditions
      const optimalGasPrice = await this.calculateOptimalGasPrice(opportunity.network);

      // Parse the original transaction to modify gas price
      const tx = ethers.Transaction.from(signedTransaction);

      // Create new transaction with optimized gas price
      const newTx = {
        ...tx,
        gasPrice: optimalGasPrice,
        nonce: await wallet.getNonce()
      };

      // Sign the new transaction
      const signedOptimizedTx = await wallet.signTransaction(newTx);

      // Submit with optimized gas
      const txResponse = await provider.broadcastTransaction(signedOptimizedTx);

      return {
        success: true,
        transactionHash: txResponse.hash,
        protectionMethod: MEVProtectionMethod.DYNAMIC_GAS,
        effectiveGasPrice: Number(optimalGasPrice),
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      logger.error('Dynamic gas submission failed:', error);
      throw error;
    }
  }

  /**
   * Submit standard transaction without MEV protection
   */
  private async submitStandardTransaction(
    signedTransaction: string,
    network: string,
    startTime: number
  ): Promise<MEVProtectionResult> {

    try {
      const provider = this.providers.get(network);
      if (!provider) {
        throw new Error(`No provider for network: ${network}`);
      }

      const txResponse = await provider.broadcastTransaction(signedTransaction);

      return {
        success: true,
        transactionHash: txResponse.hash,
        protectionMethod: MEVProtectionMethod.STANDARD,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      logger.error('Standard transaction submission failed:', error);
      throw error;
    }
  }

  /**
   * Calculate optimal gas price based on network conditions
   */
  private async calculateOptimalGasPrice(network: string): Promise<bigint> {
    try {
      const provider = this.providers.get(network);
      if (!provider) {
        throw new Error(`No provider for network: ${network}`);
      }

      // Get current fee data
      const feeData = await provider.getFeeData();
      let gasPrice = feeData.gasPrice || BigInt(0);

      if (!this.dynamicGasPricing) {
        return gasPrice;
      }

      // Apply dynamic pricing based on network conditions
      const congestion = await this.getNetworkCongestion(network);

      // Increase gas price based on congestion (up to 50% increase)
      const congestionMultiplier = 1 + (congestion / 100) * 0.5;
      gasPrice = BigInt(Math.floor(Number(gasPrice) * congestionMultiplier));

      // Apply configured gas price multiplier
      const configMultiplier = parseFloat(config.GAS_PRICE_MULTIPLIER);
      gasPrice = BigInt(Math.floor(Number(gasPrice) * configMultiplier));

      return gasPrice;

    } catch (error) {
      logger.error(`Failed to calculate optimal gas price for ${network}:`, error);
      // Return a reasonable default
      return BigInt(20000000000); // 20 Gwei
    }
  }

  /**
   * Get network congestion level
   */
  private async getNetworkCongestion(network: string): Promise<number> {
    try {
      const provider = this.providers.get(network);
      if (!provider) return 50;

      const latestBlock = await provider.getBlock('latest');
      if (!latestBlock) return 50;

      const gasUsedRatio = Number(latestBlock.gasUsed) / Number(latestBlock.gasLimit);
      return Math.min(gasUsedRatio * 100, 100);

    } catch (error) {
      logger.warn(`Failed to get network congestion for ${network}:`, error);
      return 50; // Default moderate congestion
    }
  }

  /**
   * Submit transaction via L2 sequencer priority
   */
  private async submitViaL2SequencerPriority(
    signedTransaction: string,
    opportunity: ArbitrageOpportunity,
    startTime: number
  ): Promise<MEVProtectionResult> {

    try {
      const sequencerConfig = this.l2SequencerConfigs.get(opportunity.network);
      if (!sequencerConfig) {
        throw new Error(`No sequencer configuration for network: ${opportunity.network}`);
      }

      // Calculate priority level based on opportunity value
      const priorityLevel = this.calculateSequencerPriority(opportunity);

      // Get optimal sequencer slot
      const targetSlot = await this.getOptimalSequencerSlot(opportunity.network, sequencerConfig);

      // Submit with priority to sequencer
      const response = await axios.post(sequencerConfig.sequencerUrl, {
        method: 'eth_sendRawTransactionWithPriority',
        params: [signedTransaction, priorityLevel, targetSlot],
        id: 1,
        jsonrpc: '2.0'
      }, {
        timeout: this.mevProtectionTimeout,
        headers: {
          'Content-Type': 'application/json',
          'X-Priority-Level': priorityLevel.toString()
        }
      });

      if (response.data.error) {
        throw new Error(response.data.error.message);
      }

      return {
        success: true,
        transactionHash: response.data.result,
        protectionMethod: MEVProtectionMethod.L2_SEQUENCER_PRIORITY,
        sequencerPriority: priorityLevel,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      logger.error(`L2 sequencer priority submission failed for ${opportunity.network}:`, error);
      throw error;
    }
  }

  /**
   * Submit transaction via L2 batch optimization
   */
  private async submitViaL2BatchOptimization(
    signedTransaction: string,
    opportunity: ArbitrageOpportunity,
    startTime: number
  ): Promise<MEVProtectionResult> {

    try {
      const sequencerConfig = this.l2SequencerConfigs.get(opportunity.network);
      if (!sequencerConfig) {
        throw new Error(`No sequencer configuration for network: ${opportunity.network}`);
      }

      // Create optimized batch submission
      const batchSubmission = await this.createOptimizedBatch(
        signedTransaction,
        opportunity,
        sequencerConfig
      );

      // Submit batch to sequencer
      const response = await axios.post(sequencerConfig.sequencerUrl, {
        method: 'eth_submitBatch',
        params: [batchSubmission],
        id: 1,
        jsonrpc: '2.0'
      }, {
        timeout: this.mevProtectionTimeout * 2, // Longer timeout for batch processing
        headers: {
          'Content-Type': 'application/json',
          'X-Batch-Priority': batchSubmission.priorityLevel.toString()
        }
      });

      if (response.data.error) {
        throw new Error(response.data.error.message);
      }

      return {
        success: true,
        transactionHash: response.data.result.transactionHash,
        l2BatchId: response.data.result.batchId,
        protectionMethod: MEVProtectionMethod.L2_BATCH_OPTIMIZATION,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      logger.error(`L2 batch optimization submission failed for ${opportunity.network}:`, error);
      throw error;
    }
  }

  /**
   * Calculate sequencer priority based on opportunity characteristics
   */
  private calculateSequencerPriority(opportunity: ArbitrageOpportunity): number {
    let priority = 1; // Base priority

    // Increase priority based on profit potential
    if (opportunity.potentialProfit > 1000) priority = 5;
    else if (opportunity.potentialProfit > 500) priority = 4;
    else if (opportunity.potentialProfit > 200) priority = 3;
    else if (opportunity.potentialProfit > 100) priority = 2;

    // Adjust for opportunity type
    if (opportunity.type === ArbitrageType.CROSS_CHAIN) priority += 1;
    if (opportunity.type === ArbitrageType.TRIANGULAR) priority += 1;

    // Adjust for time sensitivity
    const timeWindow = opportunity.expirationTime - Date.now();
    if (timeWindow < 30000) priority += 2; // Less than 30 seconds
    else if (timeWindow < 60000) priority += 1; // Less than 1 minute

    return Math.min(priority, 10); // Cap at maximum priority
  }

  /**
   * Get optimal sequencer slot for transaction submission
   */
  private async getOptimalSequencerSlot(network: string, config: L2SequencerConfig): Promise<number> {
    try {
      const response = await axios.post(config.priorityFeeEndpoint, {
        method: 'eth_getSequencerStatus',
        params: [],
        id: 1,
        jsonrpc: '2.0'
      }, {
        timeout: 5000,
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.data.result) {
        const currentSlot = response.data.result.currentSlot;
        const queueLength = response.data.result.queueLength;

        // Target next available slot with some buffer
        return currentSlot + Math.max(1, Math.floor(queueLength / config.maxBatchSize));
      }

      // Fallback to current time-based slot
      return Math.floor(Date.now() / config.batchSubmissionInterval);

    } catch (error) {
      logger.warn(`Failed to get optimal sequencer slot for ${network}:`, error);
      return Math.floor(Date.now() / config.batchSubmissionInterval);
    }
  }

  /**
   * Create optimized batch submission for L2
   */
  private async createOptimizedBatch(
    signedTransaction: string,
    opportunity: ArbitrageOpportunity,
    config: L2SequencerConfig
  ): Promise<L2BatchSubmission> {

    const priorityLevel = this.calculateSequencerPriority(opportunity);
    const targetSlot = await this.getOptimalSequencerSlot(opportunity.network, config);

    return {
      batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      transactions: [signedTransaction],
      targetSequencerSlot: targetSlot,
      priorityLevel,
      estimatedInclusionTime: Date.now() + config.batchSubmissionInterval
    };
  }

  /**
   * Monitor MEV protection performance
   */
  public async monitorProtectionPerformance(result: MEVProtectionResult, opportunity: ArbitrageOpportunity) {
    try {
      // Emit performance data for ML learning
      this.emit('mevProtectionResult', {
        opportunity,
        result,
        timestamp: Date.now()
      });

      // Log performance metrics
      logger.info(`MEV protection performance for ${opportunity.id}`, {
        method: result.protectionMethod,
        success: result.success,
        executionTime: result.executionTime,
        gasUsed: result.gasUsed,
        effectiveGasPrice: result.effectiveGasPrice,
        mevRevenue: result.mevRevenue
      });

    } catch (error) {
      logger.error('Failed to monitor MEV protection performance:', error);
    }
  }

  /**
   * Get MEV protection statistics
   */
  public getMEVProtectionStats() {
    return {
      isRunning: this.isRunning,
      enableMevProtection: this.enableMevProtection,
      enablePrivateMempool: this.enablePrivateMempool,
      dynamicGasPricing: this.dynamicGasPricing,
      mevProtectionFallback: this.mevProtectionFallback,
      supportedNetworks: Array.from(this.mevProtectionSupport.keys()),
      protectionMethods: Object.values(MEVProtectionMethod)
    };
  }

  /**
   * Check if MEV protection is available for a network
   */
  public isMEVProtectionAvailable(network: string): boolean {
    const supportedMethods = this.mevProtectionSupport.get(network);
    return supportedMethods ? supportedMethods.length > 0 : false;
  }

  /**
   * Get available MEV protection methods for a network
   */
  public getAvailableProtectionMethods(network: string): MEVProtectionMethod[] {
    return this.mevProtectionSupport.get(network) || [MEVProtectionMethod.STANDARD];
  }
}
