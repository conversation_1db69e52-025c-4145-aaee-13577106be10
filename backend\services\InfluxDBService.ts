import { InfluxDB, Point, WriteApi, QueryApi } from '@influxdata/influxdb-client';
import config from '../config/index.js';
import logger from '../utils/logger.js';

export interface MetricPoint {
  measurement: string;
  tags: Record<string, string>;
  fields: Record<string, number | string | boolean>;
  timestamp?: Date;
}

export interface PriceMetric {
  symbol: string;
  price: number;
  volume24h: number;
  change24h: number;
  exchange: string;
  network: string;
}

export interface OpportunityMetric {
  type: string;
  profit: number;
  profitPercentage: number;
  confidence: number;
  network: string;
  exchange: string;
}

export interface TradeMetric {
  type: string;
  profit: number;
  gasFees: number;
  success: boolean;
  network: string;
  exchange: string;
  executionTime: number;
}

export class InfluxDBService {
  private influxDB: InfluxDB | null = null;
  private writeApi: WriteApi | null = null;
  private queryApi: QueryApi | null = null;
  private isConnected = false;

  constructor() {
    this.initialize();
  }

  private initialize() {
    try {
      if (!config.INFLUXDB_URL || !config.INFLUXDB_TOKEN) {
        logger.warn('InfluxDB configuration not found, service will be disabled');
        return;
      }

      this.influxDB = new InfluxDB({
        url: config.INFLUXDB_URL,
        token: config.INFLUXDB_TOKEN,
      });

      this.writeApi = this.influxDB.getWriteApi(
        config.INFLUXDB_ORG,
        config.INFLUXDB_BUCKET,
        'ns'
      );

      this.queryApi = this.influxDB.getQueryApi(config.INFLUXDB_ORG);

      // Configure write options
      this.writeApi.useDefaultTags({
        application: 'mev-arbitrage-bot',
        environment: config.NODE_ENV
      });

      this.isConnected = true;
      logger.info('InfluxDB service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize InfluxDB service:', error);
      this.isConnected = false;
    }
  }

  // Generic metric writing
  async writeMetric(metric: MetricPoint): Promise<boolean> {
    if (!this.isConnected || !this.writeApi) {
      logger.warn('InfluxDB not connected, skipping metric write');
      return false;
    }

    try {
      const point = new Point(metric.measurement);
      
      // Add tags
      Object.entries(metric.tags).forEach(([key, value]) => {
        point.tag(key, value);
      });

      // Add fields
      Object.entries(metric.fields).forEach(([key, value]) => {
        if (typeof value === 'number') {
          point.floatField(key, value);
        } else if (typeof value === 'boolean') {
          point.booleanField(key, value);
        } else {
          point.stringField(key, value.toString());
        }
      });

      // Set timestamp
      if (metric.timestamp) {
        point.timestamp(metric.timestamp);
      }

      this.writeApi.writePoint(point);
      return true;
    } catch (error) {
      logger.error('Failed to write metric to InfluxDB:', error);
      return false;
    }
  }

  // Price metrics
  async writePriceMetric(priceMetric: PriceMetric): Promise<boolean> {
    return this.writeMetric({
      measurement: 'price_data',
      tags: {
        symbol: priceMetric.symbol,
        exchange: priceMetric.exchange,
        network: priceMetric.network
      },
      fields: {
        price: priceMetric.price,
        volume24h: priceMetric.volume24h,
        change24h: priceMetric.change24h
      }
    });
  }

  // Opportunity metrics
  async writeOpportunityMetric(opportunityMetric: OpportunityMetric): Promise<boolean> {
    return this.writeMetric({
      measurement: 'opportunities',
      tags: {
        type: opportunityMetric.type,
        network: opportunityMetric.network,
        exchange: opportunityMetric.exchange
      },
      fields: {
        profit: opportunityMetric.profit,
        profitPercentage: opportunityMetric.profitPercentage,
        confidence: opportunityMetric.confidence
      }
    });
  }

  // Trade metrics
  async writeTradeMetric(tradeMetric: TradeMetric): Promise<boolean> {
    return this.writeMetric({
      measurement: 'trades',
      tags: {
        type: tradeMetric.type,
        network: tradeMetric.network,
        exchange: tradeMetric.exchange,
        success: tradeMetric.success.toString()
      },
      fields: {
        profit: tradeMetric.profit,
        gasFees: tradeMetric.gasFees,
        executionTime: tradeMetric.executionTime,
        success: tradeMetric.success
      }
    });
  }

  // System metrics
  async writeSystemMetric(
    metric: string,
    value: number,
    tags: Record<string, string> = {}
  ): Promise<boolean> {
    return this.writeMetric({
      measurement: 'system_metrics',
      tags: {
        metric,
        ...tags
      },
      fields: {
        value
      }
    });
  }

  // Query methods
  async queryPriceHistory(
    symbol: string,
    timeRange: string = '-1h'
  ): Promise<any[]> {
    if (!this.isConnected || !this.queryApi) {
      return [];
    }

    try {
      const query = `
        from(bucket: "${config.INFLUXDB_BUCKET}")
          |> range(start: ${timeRange})
          |> filter(fn: (r) => r._measurement == "price_data")
          |> filter(fn: (r) => r.symbol == "${symbol}")
          |> filter(fn: (r) => r._field == "price")
          |> aggregateWindow(every: 1m, fn: mean, createEmpty: false)
          |> yield(name: "mean")
      `;

      const result: any[] = [];
      await this.queryApi.queryRows(query, {
        next(row, tableMeta) {
          const o = tableMeta.toObject(row);
          result.push(o);
        },
        error(error) {
          logger.error('InfluxDB query error:', error);
        },
        complete() {
          logger.debug('Query completed');
        },
      });

      return result;
    } catch (error) {
      logger.error('Failed to query price history:', error);
      return [];
    }
  }

  async queryOpportunityStats(timeRange: string = '-24h'): Promise<any> {
    if (!this.isConnected || !this.queryApi) {
      return null;
    }

    try {
      const query = `
        from(bucket: "${config.INFLUXDB_BUCKET}")
          |> range(start: ${timeRange})
          |> filter(fn: (r) => r._measurement == "opportunities")
          |> group(columns: ["type"])
          |> count()
          |> yield(name: "count")
      `;

      const result: any[] = [];
      await this.queryApi.queryRows(query, {
        next(row, tableMeta) {
          const o = tableMeta.toObject(row);
          result.push(o);
        },
        error(error) {
          logger.error('InfluxDB query error:', error);
        },
        complete() {
          logger.debug('Query completed');
        },
      });

      return result;
    } catch (error) {
      logger.error('Failed to query opportunity stats:', error);
      return null;
    }
  }

  async queryTradePerformance(timeRange: string = '-24h'): Promise<any> {
    if (!this.isConnected || !this.queryApi) {
      return null;
    }

    try {
      const query = `
        from(bucket: "${config.INFLUXDB_BUCKET}")
          |> range(start: ${timeRange})
          |> filter(fn: (r) => r._measurement == "trades")
          |> filter(fn: (r) => r._field == "profit")
          |> group()
          |> aggregateWindow(every: 1h, fn: sum, createEmpty: false)
          |> yield(name: "hourly_profit")
      `;

      const result: any[] = [];
      await this.queryApi.queryRows(query, {
        next(row, tableMeta) {
          const o = tableMeta.toObject(row);
          result.push(o);
        },
        error(error) {
          logger.error('InfluxDB query error:', error);
        },
        complete() {
          logger.debug('Query completed');
        },
      });

      return result;
    } catch (error) {
      logger.error('Failed to query trade performance:', error);
      return null;
    }
  }

  // Flush pending writes
  async flush(): Promise<void> {
    if (this.writeApi) {
      try {
        await this.writeApi.flush();
      } catch (error) {
        logger.error('Failed to flush InfluxDB writes:', error);
      }
    }
  }

  // Health check
  isHealthy(): boolean {
    return this.isConnected;
  }

  async testConnection(): Promise<boolean> {
    if (!this.queryApi) {
      return false;
    }

    try {
      const query = `
        from(bucket: "${config.INFLUXDB_BUCKET}")
          |> range(start: -1m)
          |> limit(n: 1)
      `;

      let hasData = false;
      await this.queryApi.queryRows(query, {
        next() {
          hasData = true;
        },
        error(error) {
          logger.error('InfluxDB connection test failed:', error);
        },
        complete() {
          // Connection successful even if no data
        },
      });

      return true; // Connection successful
    } catch (error) {
      logger.error('InfluxDB connection test failed:', error);
      return false;
    }
  }

  // Cleanup
  async close(): Promise<void> {
    if (this.writeApi) {
      try {
        await this.writeApi.close();
      } catch (error) {
        logger.error('Error closing InfluxDB write API:', error);
      }
    }
  }
}
