import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import { Trade, TradeStatus } from './ExecutionService.js';
import { ArbitrageType } from './OpportunityDetectionService.js';

export interface PerformanceMetrics {
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  totalProfit: number;
  totalLoss: number;
  netProfit: number;
  winRate: number;
  avgProfit: number;
  avgLoss: number;
  profitFactor: number;
  sharpeRatio: number;
  maxDrawdown: number;
  roi: number;
  dailyVolume: number;
}

export interface StrategyMetrics {
  [ArbitrageType.INTRA_CHAIN]: PerformanceMetrics;
  [ArbitrageType.CROSS_CHAIN]: PerformanceMetrics;
  [ArbitrageType.TRIANGULAR]: PerformanceMetrics;
}

export interface TimeSeriesData {
  timestamp: number;
  profit: number;
  cumulativeProfit: number;
  tradeCount: number;
  winRate: number;
}

export interface AssetPerformance {
  asset: string;
  tradeCount: number;
  totalProfit: number;
  winRate: number;
  avgProfit: number;
}

export class AnalyticsService extends EventEmitter {
  private trades: Map<string, Trade> = new Map();
  private timeSeriesData: TimeSeriesData[] = [];
  private isRunning = false;
  private analyticsInterval: NodeJS.Timeout | null = null;

  // Performance tracking
  private dailyMetrics: Map<string, PerformanceMetrics> = new Map();
  private strategyMetrics: StrategyMetrics;

  constructor() {
    super();
    
    this.strategyMetrics = {
      [ArbitrageType.INTRA_CHAIN]: this.createEmptyMetrics(),
      [ArbitrageType.CROSS_CHAIN]: this.createEmptyMetrics(),
      [ArbitrageType.TRIANGULAR]: this.createEmptyMetrics()
    };
  }

  private createEmptyMetrics(): PerformanceMetrics {
    return {
      totalTrades: 0,
      successfulTrades: 0,
      failedTrades: 0,
      totalProfit: 0,
      totalLoss: 0,
      netProfit: 0,
      winRate: 0,
      avgProfit: 0,
      avgLoss: 0,
      profitFactor: 0,
      sharpeRatio: 0,
      maxDrawdown: 0,
      roi: 0,
      dailyVolume: 0
    };
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Analytics Service...');
    this.isRunning = true;

    // Start analytics processing
    this.analyticsInterval = setInterval(() => {
      this.processAnalytics();
    }, 30000); // Process every 30 seconds

    // Initial processing
    await this.processAnalytics();

    logger.info('Analytics Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Analytics Service...');
    this.isRunning = false;

    if (this.analyticsInterval) {
      clearInterval(this.analyticsInterval);
      this.analyticsInterval = null;
    }

    logger.info('Analytics Service stopped');
  }

  public recordTrade(trade: Trade) {
    this.trades.set(trade.id, trade);
    
    // Update real-time metrics
    this.updateMetrics(trade);
    
    // Add to time series
    this.addToTimeSeries(trade);
    
    logger.debug(`Trade recorded for analytics: ${trade.id}`);
  }

  private updateMetrics(trade: Trade) {
    // Update strategy-specific metrics
    const strategyMetrics = this.strategyMetrics[trade.type];
    strategyMetrics.totalTrades++;

    if (trade.status === TradeStatus.SUCCESS) {
      strategyMetrics.successfulTrades++;
      if (trade.executedProfit > 0) {
        strategyMetrics.totalProfit += trade.executedProfit;
      } else {
        strategyMetrics.totalLoss += Math.abs(trade.executedProfit);
      }
    } else if (trade.status === TradeStatus.FAILED) {
      strategyMetrics.failedTrades++;
      strategyMetrics.totalLoss += trade.gasFees || 0;
    }

    // Recalculate derived metrics
    this.recalculateMetrics(strategyMetrics);

    // Update daily metrics
    this.updateDailyMetrics(trade);
  }

  private recalculateMetrics(metrics: PerformanceMetrics) {
    // Win rate
    metrics.winRate = metrics.totalTrades > 0 
      ? (metrics.successfulTrades / metrics.totalTrades) * 100 
      : 0;

    // Net profit
    metrics.netProfit = metrics.totalProfit - metrics.totalLoss;

    // Average profit/loss
    metrics.avgProfit = metrics.successfulTrades > 0 
      ? metrics.totalProfit / metrics.successfulTrades 
      : 0;
    
    metrics.avgLoss = metrics.failedTrades > 0 
      ? metrics.totalLoss / metrics.failedTrades 
      : 0;

    // Profit factor
    metrics.profitFactor = metrics.totalLoss > 0 
      ? metrics.totalProfit / metrics.totalLoss 
      : metrics.totalProfit > 0 ? Infinity : 0;

    // ROI (simplified calculation)
    const initialCapital = 10000; // Assume $10,000 initial capital
    metrics.roi = (metrics.netProfit / initialCapital) * 100;

    // Daily volume (simplified)
    metrics.dailyVolume = metrics.totalProfit + metrics.totalLoss;
  }

  private updateDailyMetrics(trade: Trade) {
    const dateKey = new Date(trade.timestamp).toDateString();
    
    if (!this.dailyMetrics.has(dateKey)) {
      this.dailyMetrics.set(dateKey, this.createEmptyMetrics());
    }

    const dailyMetrics = this.dailyMetrics.get(dateKey)!;
    dailyMetrics.totalTrades++;

    if (trade.status === TradeStatus.SUCCESS) {
      dailyMetrics.successfulTrades++;
      if (trade.executedProfit > 0) {
        dailyMetrics.totalProfit += trade.executedProfit;
      } else {
        dailyMetrics.totalLoss += Math.abs(trade.executedProfit);
      }
    } else if (trade.status === TradeStatus.FAILED) {
      dailyMetrics.failedTrades++;
      dailyMetrics.totalLoss += trade.gasFees || 0;
    }

    this.recalculateMetrics(dailyMetrics);
  }

  private addToTimeSeries(trade: Trade) {
    const now = Date.now();
    const profit = trade.status === TradeStatus.SUCCESS ? trade.executedProfit : -(trade.gasFees || 0);
    
    // Calculate cumulative profit
    const lastEntry = this.timeSeriesData[this.timeSeriesData.length - 1];
    const cumulativeProfit = (lastEntry?.cumulativeProfit || 0) + profit;

    // Calculate current win rate
    const recentTrades = Array.from(this.trades.values())
      .filter(t => t.timestamp >= now - 24 * 60 * 60 * 1000); // Last 24 hours
    
    const winRate = recentTrades.length > 0
      ? (recentTrades.filter(t => t.status === TradeStatus.SUCCESS).length / recentTrades.length) * 100
      : 0;

    const dataPoint: TimeSeriesData = {
      timestamp: now,
      profit,
      cumulativeProfit,
      tradeCount: this.trades.size,
      winRate
    };

    this.timeSeriesData.push(dataPoint);

    // Keep only last 1000 data points
    if (this.timeSeriesData.length > 1000) {
      this.timeSeriesData = this.timeSeriesData.slice(-1000);
    }
  }

  private async processAnalytics() {
    try {
      // Calculate advanced metrics
      this.calculateAdvancedMetrics();
      
      // Generate insights
      const insights = this.generateInsights();
      
      // Emit analytics update
      this.emit('analyticsUpdate', {
        metrics: this.getOverallMetrics(),
        strategyMetrics: this.strategyMetrics,
        insights,
        timestamp: Date.now()
      });

    } catch (error) {
      logger.error('Error processing analytics:', error);
    }
  }

  private calculateAdvancedMetrics() {
    // Calculate Sharpe ratio for each strategy
    Object.values(this.strategyMetrics).forEach(metrics => {
      metrics.sharpeRatio = this.calculateSharpeRatio(metrics);
      metrics.maxDrawdown = this.calculateMaxDrawdown(metrics);
    });
  }

  private calculateSharpeRatio(metrics: PerformanceMetrics): number {
    if (metrics.totalTrades < 10) return 0;

    // Get trades for this strategy
    const strategyTrades = Array.from(this.trades.values())
      .filter(trade => {
        // This is simplified - in production you'd filter by strategy type
        return trade.status === TradeStatus.SUCCESS || trade.status === TradeStatus.FAILED;
      });

    if (strategyTrades.length < 2) return 0;

    // Calculate returns
    const returns = strategyTrades.map(trade => 
      trade.status === TradeStatus.SUCCESS ? trade.executedProfit : -(trade.gasFees || 0)
    );

    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    if (stdDev === 0) return 0;

    const riskFreeRate = 0; // Assume 0 for simplicity
    return (avgReturn - riskFreeRate) / stdDev;
  }

  private calculateMaxDrawdown(metrics: PerformanceMetrics): number {
    // This is simplified - in production you'd track running P&L
    return Math.max(0, metrics.totalLoss);
  }

  private generateInsights(): string[] {
    const insights: string[] = [];
    const overall = this.getOverallMetrics();

    // Performance insights
    if (overall.winRate > 80) {
      insights.push('Excellent win rate performance');
    } else if (overall.winRate < 60) {
      insights.push('Win rate below optimal threshold');
    }

    if (overall.profitFactor > 2) {
      insights.push('Strong profit factor indicates good risk management');
    } else if (overall.profitFactor < 1.5) {
      insights.push('Consider improving trade selection criteria');
    }

    // Strategy insights
    const bestStrategy = Object.entries(this.strategyMetrics)
      .reduce((best, [type, metrics]) => 
        metrics.netProfit > best.metrics.netProfit ? { type, metrics } : best,
        { type: '', metrics: this.createEmptyMetrics() }
      );

    if (bestStrategy.type) {
      insights.push(`${bestStrategy.type} strategy showing best performance`);
    }

    // Volume insights
    if (overall.dailyVolume > 100000) {
      insights.push('High trading volume indicates active market participation');
    }

    return insights;
  }

  public getOverallMetrics(): PerformanceMetrics {
    const overall = this.createEmptyMetrics();
    
    Object.values(this.strategyMetrics).forEach(metrics => {
      overall.totalTrades += metrics.totalTrades;
      overall.successfulTrades += metrics.successfulTrades;
      overall.failedTrades += metrics.failedTrades;
      overall.totalProfit += metrics.totalProfit;
      overall.totalLoss += metrics.totalLoss;
      overall.dailyVolume += metrics.dailyVolume;
    });

    this.recalculateMetrics(overall);
    return overall;
  }

  public getStrategyMetrics(): StrategyMetrics {
    return { ...this.strategyMetrics };
  }

  public getTimeSeriesData(hours: number = 24): TimeSeriesData[] {
    const cutoff = Date.now() - hours * 60 * 60 * 1000;
    return this.timeSeriesData.filter(data => data.timestamp >= cutoff);
  }

  public getDailyMetrics(days: number = 7): Array<{ date: string; metrics: PerformanceMetrics }> {
    const result: Array<{ date: string; metrics: PerformanceMetrics }> = [];
    const now = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateKey = date.toDateString();
      
      result.push({
        date: dateKey,
        metrics: this.dailyMetrics.get(dateKey) || this.createEmptyMetrics()
      });
    }
    
    return result;
  }

  public getAssetPerformance(): AssetPerformance[] {
    const assetMap: Map<string, AssetPerformance> = new Map();
    
    Array.from(this.trades.values()).forEach(trade => {
      trade.assets.forEach(asset => {
        if (!assetMap.has(asset)) {
          assetMap.set(asset, {
            asset,
            tradeCount: 0,
            totalProfit: 0,
            winRate: 0,
            avgProfit: 0
          });
        }
        
        const assetPerf = assetMap.get(asset)!;
        assetPerf.tradeCount++;
        
        if (trade.status === TradeStatus.SUCCESS) {
          assetPerf.totalProfit += trade.executedProfit;
        }
      });
    });

    // Calculate derived metrics
    assetMap.forEach(assetPerf => {
      const assetTrades = Array.from(this.trades.values())
        .filter(trade => trade.assets.includes(assetPerf.asset));
      
      const successfulTrades = assetTrades.filter(trade => trade.status === TradeStatus.SUCCESS);
      assetPerf.winRate = assetTrades.length > 0 
        ? (successfulTrades.length / assetTrades.length) * 100 
        : 0;
      
      assetPerf.avgProfit = successfulTrades.length > 0
        ? assetPerf.totalProfit / successfulTrades.length
        : 0;
    });

    return Array.from(assetMap.values())
      .sort((a, b) => b.totalProfit - a.totalProfit);
  }

  public getTrades(limit: number = 100): Trade[] {
    return Array.from(this.trades.values())
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  public isHealthy(): boolean {
    return this.isRunning;
  }

  public getStats() {
    const overall = this.getOverallMetrics();
    
    return {
      ...overall,
      totalDataPoints: this.timeSeriesData.length,
      strategiesTracked: Object.keys(this.strategyMetrics).length,
      assetsTracked: this.getAssetPerformance().length,
      isRunning: this.isRunning
    };
  }
}
