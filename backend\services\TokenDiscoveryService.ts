import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import axios from 'axios';
import logger from '../utils/logger.js';
import config from '../config/index.js';

export interface Token {
  id: string;
  name: string;
  symbol: string;
  address: string;
  liquidity: number;
  safetyScore: number;
  isWhitelisted: boolean;
  network: string;
  decimals: number;
  totalSupply: string;
  marketCap?: number;
  volume24h?: number;
  priceUSD?: number;
  lastUpdated: number;
}

export interface TokenValidationResult {
  isValid: boolean;
  safetyScore: number;
  reasons: string[];
  liquidity: number;
}

export class TokenDiscoveryService extends EventEmitter {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private whitelistedTokens: Map<string, Token> = new Map();
  private blacklistedTokens: Set<string> = new Set();
  private scanInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor() {
    super();
    this.initializeProviders();
    this.loadInitialTokens();
  }

  private initializeProviders() {
    // Initialize blockchain providers
    this.providers.set('ethereum', new ethers.JsonRpcProvider(config.ETHEREUM_RPC_URL));
    this.providers.set('polygon', new ethers.JsonRpcProvider(config.POLYGON_RPC_URL));
    this.providers.set('bsc', new ethers.JsonRpcProvider(config.BSC_RPC_URL));
  }

  private loadInitialTokens() {
    // Load well-known tokens
    const initialTokens: Partial<Token>[] = [
      {
        name: 'Ethereum',
        symbol: 'ETH',
        address: '******************************************',
        network: 'ethereum',
        decimals: 18,
        safetyScore: 100,
        isWhitelisted: true
      },
      {
        name: 'USD Coin',
        symbol: 'USDC',
        address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
        network: 'ethereum',
        decimals: 6,
        safetyScore: 95,
        isWhitelisted: true
      },
      {
        name: 'Wrapped Bitcoin',
        symbol: 'WBTC',
        address: '******************************************',
        network: 'ethereum',
        decimals: 8,
        safetyScore: 90,
        isWhitelisted: true
      }
    ];

    initialTokens.forEach(tokenData => {
      const token: Token = {
        id: `${tokenData.network}_${tokenData.address}`,
        name: tokenData.name!,
        symbol: tokenData.symbol!,
        address: tokenData.address!,
        liquidity: 0,
        safetyScore: tokenData.safetyScore!,
        isWhitelisted: tokenData.isWhitelisted!,
        network: tokenData.network!,
        decimals: tokenData.decimals!,
        totalSupply: '0',
        lastUpdated: Date.now()
      };

      this.whitelistedTokens.set(token.id, token);
    });

    logger.info(`Loaded ${initialTokens.length} initial tokens`);
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Token Discovery Service...');
    this.isRunning = true;

    // Start periodic scanning
    this.scanInterval = setInterval(() => {
      this.scanForNewTokens();
    }, 60000); // Scan every minute

    // Initial scan
    await this.scanForNewTokens();
    
    logger.info('Token Discovery Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Token Discovery Service...');
    this.isRunning = false;

    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }

    logger.info('Token Discovery Service stopped');
  }

  private async scanForNewTokens() {
    try {
      logger.debug('Scanning for new tokens...');

      // Scan each network
      for (const [network, provider] of this.providers) {
        await this.scanNetwork(network, provider);
      }

      // Update token prices and liquidity
      await this.updateTokenMetadata();

    } catch (error) {
      logger.error('Error scanning for tokens:', error);
    }
  }

  private async scanNetwork(network: string, provider: ethers.JsonRpcProvider) {
    try {
      // Get latest block
      const latestBlock = await provider.getBlock('latest');
      if (!latestBlock) return;

      // Scan recent blocks for token creation events
      const fromBlock = Math.max(0, latestBlock.number - 100); // Last 100 blocks
      
      // This is a simplified implementation
      // In production, you would listen for specific events like token creation
      logger.debug(`Scanned ${network} blocks ${fromBlock} to ${latestBlock.number}`);

    } catch (error) {
      logger.error(`Error scanning ${network}:`, error);
    }
  }

  private async updateTokenMetadata() {
    try {
      const tokens = Array.from(this.whitelistedTokens.values());
      
      for (const token of tokens) {
        await this.updateTokenData(token);
      }

    } catch (error) {
      logger.error('Error updating token metadata:', error);
    }
  }

  private async updateTokenData(token: Token) {
    try {
      // Update liquidity from DEX pools
      const liquidity = await this.getTokenLiquidity(token);
      
      // Update price data
      const priceData = await this.getTokenPrice(token);
      
      // Update token
      const updatedToken: Token = {
        ...token,
        liquidity,
        priceUSD: priceData?.price,
        volume24h: priceData?.volume24h,
        marketCap: priceData?.marketCap,
        lastUpdated: Date.now()
      };

      this.whitelistedTokens.set(token.id, updatedToken);
      this.emit('tokenUpdated', updatedToken);

    } catch (error) {
      logger.error(`Error updating token ${token.symbol}:`, error);
    }
  }

  private async getTokenLiquidity(token: Token): Promise<number> {
    try {
      // This would integrate with DEX APIs to get actual liquidity
      // For now, return a simulated value
      return Math.random() * 1000000;
    } catch (error) {
      logger.error(`Error getting liquidity for ${token.symbol}:`, error);
      return 0;
    }
  }

  private async getTokenPrice(token: Token): Promise<{ price: number; volume24h: number; marketCap: number } | null> {
    try {
      // This would integrate with price APIs like CoinGecko
      // For now, return simulated data
      return {
        price: Math.random() * 1000,
        volume24h: Math.random() * 10000000,
        marketCap: Math.random() * **********
      };
    } catch (error) {
      logger.error(`Error getting price for ${token.symbol}:`, error);
      return null;
    }
  }

  public async validateToken(address: string, network: string): Promise<TokenValidationResult> {
    try {
      const provider = this.providers.get(network);
      if (!provider) {
        return {
          isValid: false,
          safetyScore: 0,
          reasons: ['Unsupported network'],
          liquidity: 0
        };
      }

      // Check if token is blacklisted
      const tokenId = `${network}_${address}`;
      if (this.blacklistedTokens.has(tokenId)) {
        return {
          isValid: false,
          safetyScore: 0,
          reasons: ['Token is blacklisted'],
          liquidity: 0
        };
      }

      // Validate token contract
      const code = await provider.getCode(address);
      if (code === '0x') {
        return {
          isValid: false,
          safetyScore: 0,
          reasons: ['No contract code at address'],
          liquidity: 0
        };
      }

      // Get token info
      const tokenContract = new ethers.Contract(address, [
        'function name() view returns (string)',
        'function symbol() view returns (string)',
        'function decimals() view returns (uint8)',
        'function totalSupply() view returns (uint256)'
      ], provider);

      const [name, symbol, decimals, totalSupply] = await Promise.all([
        tokenContract.name().catch(() => 'Unknown'),
        tokenContract.symbol().catch(() => 'UNK'),
        tokenContract.decimals().catch(() => 18),
        tokenContract.totalSupply().catch(() => '0')
      ]);

      // Calculate safety score
      let safetyScore = 50; // Base score
      const reasons: string[] = [];

      // Check liquidity
      const liquidity = await this.getTokenLiquidity({ address, network } as Token);
      if (liquidity < 2000) { // Less than 2 ETH equivalent
        safetyScore -= 30;
        reasons.push('Low liquidity');
      } else {
        safetyScore += 20;
      }

      // Check if it's a known token
      if (this.whitelistedTokens.has(tokenId)) {
        safetyScore += 30;
        reasons.push('Previously validated');
      }

      return {
        isValid: safetyScore >= 70,
        safetyScore,
        reasons,
        liquidity
      };

    } catch (error) {
      logger.error(`Error validating token ${address}:`, error);
      return {
        isValid: false,
        safetyScore: 0,
        reasons: ['Validation error'],
        liquidity: 0
      };
    }
  }

  public async addToWhitelist(token: Partial<Token>): Promise<boolean> {
    try {
      if (!token.address || !token.network) {
        throw new Error('Token address and network are required');
      }

      const validation = await this.validateToken(token.address, token.network);
      if (!validation.isValid) {
        logger.warn(`Cannot whitelist token ${token.address}: ${validation.reasons.join(', ')}`);
        return false;
      }

      const tokenId = `${token.network}_${token.address}`;
      const fullToken: Token = {
        id: tokenId,
        name: token.name || 'Unknown',
        symbol: token.symbol || 'UNK',
        address: token.address,
        liquidity: validation.liquidity,
        safetyScore: validation.safetyScore,
        isWhitelisted: true,
        network: token.network,
        decimals: token.decimals || 18,
        totalSupply: token.totalSupply || '0',
        lastUpdated: Date.now()
      };

      this.whitelistedTokens.set(tokenId, fullToken);
      this.emit('tokenWhitelisted', fullToken);
      
      logger.info(`Token ${token.symbol} whitelisted with safety score ${validation.safetyScore}`);
      return true;

    } catch (error) {
      logger.error('Error adding token to whitelist:', error);
      return false;
    }
  }

  public addToBlacklist(address: string, network: string, reason: string) {
    const tokenId = `${network}_${address}`;
    this.blacklistedTokens.add(tokenId);
    
    // Remove from whitelist if present
    if (this.whitelistedTokens.has(tokenId)) {
      const token = this.whitelistedTokens.get(tokenId)!;
      this.whitelistedTokens.delete(tokenId);
      this.emit('tokenBlacklisted', { ...token, reason });
    }

    logger.info(`Token ${address} blacklisted: ${reason}`);
  }

  public getWhitelistedTokens(): Token[] {
    return Array.from(this.whitelistedTokens.values());
  }

  public getToken(address: string, network: string): Token | undefined {
    const tokenId = `${network}_${address}`;
    return this.whitelistedTokens.get(tokenId);
  }

  public isTokenWhitelisted(address: string, network: string): boolean {
    const tokenId = `${network}_${address}`;
    return this.whitelistedTokens.has(tokenId);
  }

  public isTokenBlacklisted(address: string, network: string): boolean {
    const tokenId = `${network}_${address}`;
    return this.blacklistedTokens.has(tokenId);
  }

  public isHealthy(): boolean {
    return this.isRunning && this.whitelistedTokens.size > 0;
  }

  public getStats() {
    return {
      whitelistedTokens: this.whitelistedTokens.size,
      blacklistedTokens: this.blacklistedTokens.size,
      supportedNetworks: this.providers.size,
      isRunning: this.isRunning
    };
  }
}
