#!/usr/bin/env node

/**
 * Setup ML Database Schema
 * 
 * This script initializes the machine learning tables in the Supabase database.
 * Run this after setting up the basic schema to add ML learning capabilities.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing Supabase configuration. Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function setupMLDatabase() {
  console.log('🚀 Setting up ML Database Schema...');

  try {
    // Read the schema file
    const schemaPath = path.join(__dirname, '..', 'database', 'supabase-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');

    // Extract ML-specific tables from the schema
    const mlTables = [
      'strategy_performance',
      'strategy_weights', 
      'market_regimes',
      'ml_training_data',
      'strategy_learning_events'
    ];

    console.log('📊 Creating ML tables...');

    // Create strategy_performance table
    console.log('  - Creating strategy_performance table...');
    const { error: strategyPerfError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS strategy_performance (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          strategy_type VARCHAR(50) NOT NULL,
          strategy_variant VARCHAR(100),
          trade_id UUID,
          opportunity_id VARCHAR(255) NOT NULL,
          execution_parameters JSONB NOT NULL,
          market_conditions JSONB NOT NULL,
          success BOOLEAN NOT NULL,
          profit_loss DECIMAL(18, 8) NOT NULL,
          execution_time_ms INTEGER NOT NULL,
          gas_cost DECIMAL(18, 8) NOT NULL,
          slippage_actual DECIMAL(8, 4),
          confidence_score DECIMAL(5, 2) NOT NULL,
          risk_score DECIMAL(5, 2) NOT NULL,
          network VARCHAR(50) NOT NULL,
          timestamp TIMESTAMPTZ NOT NULL,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_strategy_performance_strategy_type ON strategy_performance(strategy_type);
        CREATE INDEX IF NOT EXISTS idx_strategy_performance_network ON strategy_performance(network);
        CREATE INDEX IF NOT EXISTS idx_strategy_performance_timestamp ON strategy_performance(timestamp DESC);
        CREATE INDEX IF NOT EXISTS idx_strategy_performance_success ON strategy_performance(success);
      `
    });

    if (strategyPerfError) {
      console.error('❌ Error creating strategy_performance table:', strategyPerfError);
    } else {
      console.log('  ✅ strategy_performance table created');
    }

    // Create strategy_weights table
    console.log('  - Creating strategy_weights table...');
    const { error: strategyWeightsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS strategy_weights (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          strategy_type VARCHAR(50) NOT NULL,
          strategy_variant VARCHAR(100),
          network VARCHAR(50) NOT NULL,
          weight DECIMAL(8, 6) NOT NULL DEFAULT 1.0,
          success_rate DECIMAL(5, 2) NOT NULL DEFAULT 0,
          avg_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
          avg_risk_adjusted_return DECIMAL(8, 4) NOT NULL DEFAULT 0,
          total_executions INTEGER NOT NULL DEFAULT 0,
          recent_performance_score DECIMAL(5, 2) NOT NULL DEFAULT 50,
          market_regime VARCHAR(50) NOT NULL DEFAULT 'normal',
          last_updated TIMESTAMPTZ DEFAULT NOW(),
          created_at TIMESTAMPTZ DEFAULT NOW(),
          UNIQUE(strategy_type, strategy_variant, network, market_regime)
        );
        
        CREATE INDEX IF NOT EXISTS idx_strategy_weights_strategy_type ON strategy_weights(strategy_type);
        CREATE INDEX IF NOT EXISTS idx_strategy_weights_network ON strategy_weights(network);
        CREATE INDEX IF NOT EXISTS idx_strategy_weights_market_regime ON strategy_weights(market_regime);
        CREATE INDEX IF NOT EXISTS idx_strategy_weights_weight ON strategy_weights(weight DESC);
      `
    });

    if (strategyWeightsError) {
      console.error('❌ Error creating strategy_weights table:', strategyWeightsError);
    } else {
      console.log('  ✅ strategy_weights table created');
    }

    // Create market_regimes table
    console.log('  - Creating market_regimes table...');
    const { error: marketRegimesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS market_regimes (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          regime_name VARCHAR(50) NOT NULL,
          volatility_min DECIMAL(8, 4) NOT NULL,
          volatility_max DECIMAL(8, 4) NOT NULL,
          liquidity_min DECIMAL(18, 8) NOT NULL,
          network_congestion_min DECIMAL(5, 2) NOT NULL,
          network_congestion_max DECIMAL(5, 2) NOT NULL,
          description TEXT,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
      `
    });

    if (marketRegimesError) {
      console.error('❌ Error creating market_regimes table:', marketRegimesError);
    } else {
      console.log('  ✅ market_regimes table created');
    }

    // Create ml_training_data table
    console.log('  - Creating ml_training_data table...');
    const { error: mlTrainingError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS ml_training_data (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          feature_vector JSONB NOT NULL,
          target_success BOOLEAN NOT NULL,
          target_profit DECIMAL(18, 8) NOT NULL,
          strategy_type VARCHAR(50) NOT NULL,
          network VARCHAR(50) NOT NULL,
          market_regime VARCHAR(50) NOT NULL,
          timestamp TIMESTAMPTZ NOT NULL,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_ml_training_data_strategy_type ON ml_training_data(strategy_type);
        CREATE INDEX IF NOT EXISTS idx_ml_training_data_network ON ml_training_data(network);
        CREATE INDEX IF NOT EXISTS idx_ml_training_data_timestamp ON ml_training_data(timestamp DESC);
      `
    });

    if (mlTrainingError) {
      console.error('❌ Error creating ml_training_data table:', mlTrainingError);
    } else {
      console.log('  ✅ ml_training_data table created');
    }

    // Create strategy_learning_events table
    console.log('  - Creating strategy_learning_events table...');
    const { error: learningEventsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS strategy_learning_events (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          event_type VARCHAR(50) NOT NULL,
          strategy_type VARCHAR(50) NOT NULL,
          strategy_variant VARCHAR(100),
          old_weight DECIMAL(8, 6),
          new_weight DECIMAL(8, 6),
          reason TEXT NOT NULL,
          confidence DECIMAL(5, 2) NOT NULL,
          network VARCHAR(50) NOT NULL,
          market_regime VARCHAR(50),
          metadata JSONB,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_strategy_learning_events_strategy_type ON strategy_learning_events(strategy_type);
        CREATE INDEX IF NOT EXISTS idx_strategy_learning_events_event_type ON strategy_learning_events(event_type);
        CREATE INDEX IF NOT EXISTS idx_strategy_learning_events_created_at ON strategy_learning_events(created_at DESC);
      `
    });

    if (learningEventsError) {
      console.error('❌ Error creating strategy_learning_events table:', learningEventsError);
    } else {
      console.log('  ✅ strategy_learning_events table created');
    }

    // Create ML analytics view
    console.log('  - Creating ML analytics view...');
    const { error: viewError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE OR REPLACE VIEW strategy_performance_summary AS
        SELECT 
          sp.strategy_type,
          sp.strategy_variant,
          sp.network,
          COUNT(*) as total_executions,
          COUNT(*) FILTER (WHERE sp.success = true) as successful_executions,
          ROUND((COUNT(*) FILTER (WHERE sp.success = true)::DECIMAL / COUNT(*)) * 100, 2) as success_rate,
          AVG(sp.profit_loss) FILTER (WHERE sp.success = true) as avg_profit,
          AVG(ABS(sp.profit_loss)) FILTER (WHERE sp.success = false) as avg_loss,
          AVG(sp.execution_time_ms) as avg_execution_time,
          AVG(sp.gas_cost) as avg_gas_cost,
          AVG(sp.confidence_score) as avg_confidence,
          AVG(sp.risk_score) as avg_risk_score,
          sw.weight as current_weight,
          sw.recent_performance_score
        FROM strategy_performance sp
        LEFT JOIN strategy_weights sw ON sp.strategy_type = sw.strategy_type 
          AND sp.strategy_variant = sw.strategy_variant 
          AND sp.network = sw.network
        WHERE sp.timestamp >= NOW() - INTERVAL '30 days'
        GROUP BY sp.strategy_type, sp.strategy_variant, sp.network, sw.weight, sw.recent_performance_score
        ORDER BY success_rate DESC, avg_profit DESC;
      `
    });

    if (viewError) {
      console.error('❌ Error creating ML analytics view:', viewError);
    } else {
      console.log('  ✅ ML analytics view created');
    }

    // Insert initial market regimes
    console.log('  - Inserting initial market regimes...');
    const { error: regimeInsertError } = await supabase
      .from('market_regimes')
      .upsert([
        {
          regime_name: 'stable',
          volatility_min: 0,
          volatility_max: 5,
          liquidity_min: 2000000,
          network_congestion_min: 0,
          network_congestion_max: 20,
          description: 'Low volatility, high liquidity, low congestion',
          is_active: true
        },
        {
          regime_name: 'normal',
          volatility_min: 5,
          volatility_max: 20,
          liquidity_min: 1000000,
          network_congestion_min: 20,
          network_congestion_max: 60,
          description: 'Normal market conditions',
          is_active: true
        },
        {
          regime_name: 'high_volatility',
          volatility_min: 20,
          volatility_max: 100,
          liquidity_min: 500000,
          network_congestion_min: 60,
          network_congestion_max: 100,
          description: 'High volatility or network congestion',
          is_active: true
        },
        {
          regime_name: 'low_liquidity',
          volatility_min: 0,
          volatility_max: 100,
          liquidity_min: 0,
          network_congestion_min: 0,
          network_congestion_max: 100,
          description: 'Low liquidity conditions',
          is_active: true
        }
      ], { onConflict: 'regime_name' });

    if (regimeInsertError) {
      console.error('❌ Error inserting market regimes:', regimeInsertError);
    } else {
      console.log('  ✅ Initial market regimes inserted');
    }

    console.log('✅ ML Database Schema setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('  - strategy_performance: Records all strategy execution attempts');
    console.log('  - strategy_weights: Stores ML-learned strategy weights');
    console.log('  - market_regimes: Defines different market conditions');
    console.log('  - ml_training_data: Stores normalized features for ML models');
    console.log('  - strategy_learning_events: Logs all learning decisions');
    console.log('  - strategy_performance_summary: Analytics view for performance tracking');
    console.log('\n🎯 The ML learning system is now ready to start learning from trade executions!');

  } catch (error) {
    console.error('❌ Error setting up ML database:', error);
    process.exit(1);
  }
}

// Run the setup
setupMLDatabase();
