import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import { ServiceManager } from './ServiceManager.js';
import { DependencyChecker } from './DependencyChecker.js';

export interface HealthMetrics {
  timestamp: Date;
  uptime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: number;
  services: { [key: string]: boolean };
  dependencies: { [key: string]: boolean };
  alerts: HealthAlert[];
  performance: {
    responseTime: number;
    throughput: number;
    errorRate: number;
  };
}

export interface HealthAlert {
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: Date;
  service?: string;
  resolved: boolean;
}

export interface HealthThresholds {
  memoryUsagePercent: number;
  cpuUsagePercent: number;
  responseTimeMs: number;
  errorRatePercent: number;
  serviceDowntimeMs: number;
}

export class HealthMonitor extends EventEmitter {
  private serviceManager: ServiceManager | null = null;
  private dependencyChecker: DependencyChecker | null = null;
  private isRunning = false;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private alerts: HealthAlert[] = [];
  private performanceMetrics: {
    requests: number;
    errors: number;
    totalResponseTime: number;
    startTime: number;
  } = {
    requests: 0,
    errors: 0,
    totalResponseTime: 0,
    startTime: Date.now()
  };

  private thresholds: HealthThresholds = {
    memoryUsagePercent: 85,
    cpuUsagePercent: 80,
    responseTimeMs: 5000,
    errorRatePercent: 5,
    serviceDowntimeMs: 30000
  };

  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
  private readonly MAX_ALERTS = 100;

  constructor(serviceManager?: ServiceManager, dependencyChecker?: DependencyChecker) {
    super();
    this.serviceManager = serviceManager || null;
    this.dependencyChecker = dependencyChecker || null;
  }

  public setServiceManager(serviceManager: ServiceManager): void {
    this.serviceManager = serviceManager;
  }

  public setDependencyChecker(dependencyChecker: DependencyChecker): void {
    this.dependencyChecker = dependencyChecker;
  }

  public async start(): Promise<void> {
    if (this.isRunning) return;

    logger.info('💓 Starting Health Monitor...');
    this.isRunning = true;

    // Initial health check
    await this.runHealthCheck();

    // Start periodic health checks
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.runHealthCheck();
      } catch (error) {
        logger.error('Error during health check:', error);
        this.addAlert('error', 'Health check failed', undefined, error instanceof Error ? error.message : 'Unknown error');
      }
    }, this.HEALTH_CHECK_INTERVAL);

    logger.info('✅ Health Monitor started');
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) return;

    logger.info('Stopping Health Monitor...');
    this.isRunning = false;

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    logger.info('✅ Health Monitor stopped');
  }

  public async runHealthCheck(): Promise<HealthMetrics> {
    const startTime = Date.now();
    
    // Gather system metrics
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime() * 1000; // Convert to milliseconds
    
    // Check services
    const services = this.serviceManager ? this.serviceManager.getServiceStatus() : {};
    
    // Check dependencies
    const dependencies: { [key: string]: boolean } = {};
    if (this.dependencyChecker) {
      const deps = this.dependencyChecker.getAllDependencies();
      deps.forEach(dep => {
        dependencies[dep.name] = dep.status === 'healthy';
      });
    }

    // Calculate performance metrics
    const performance = this.calculatePerformanceMetrics();
    
    // Check for issues and generate alerts
    await this.checkForIssues(memoryUsage, performance, services, dependencies);

    const responseTime = Date.now() - startTime;
    
    const metrics: HealthMetrics = {
      timestamp: new Date(),
      uptime,
      memoryUsage,
      cpuUsage: await this.getCPUUsage(),
      services,
      dependencies,
      alerts: this.getRecentAlerts(),
      performance: {
        ...performance,
        responseTime
      }
    };

    this.emit('healthCheckCompleted', metrics);
    
    // Log health status
    this.logHealthStatus(metrics);
    
    return metrics;
  }

  private async checkForIssues(
    memoryUsage: NodeJS.MemoryUsage,
    performance: any,
    services: { [key: string]: boolean },
    dependencies: { [key: string]: boolean }
  ): Promise<void> {
    // Check memory usage
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    if (memoryUsagePercent > this.thresholds.memoryUsagePercent) {
      this.addAlert('warning', `High memory usage: ${memoryUsagePercent.toFixed(1)}%`);
    }

    // Check CPU usage
    const cpuUsage = await this.getCPUUsage();
    if (cpuUsage > this.thresholds.cpuUsagePercent) {
      this.addAlert('warning', `High CPU usage: ${cpuUsage.toFixed(1)}%`);
    }

    // Check response time
    if (performance.responseTime > this.thresholds.responseTimeMs) {
      this.addAlert('warning', `High response time: ${performance.responseTime}ms`);
    }

    // Check error rate
    if (performance.errorRate > this.thresholds.errorRatePercent) {
      this.addAlert('error', `High error rate: ${performance.errorRate.toFixed(1)}%`);
    }

    // Check service health
    Object.entries(services).forEach(([serviceName, isHealthy]) => {
      if (!isHealthy) {
        this.addAlert('error', `Service unhealthy: ${serviceName}`, serviceName);
        this.emit('serviceUnhealthy', serviceName);
      }
    });

    // Check dependency health
    Object.entries(dependencies).forEach(([depName, isHealthy]) => {
      if (!isHealthy) {
        this.addAlert('warning', `Dependency unhealthy: ${depName}`);
      }
    });
  }

  private calculatePerformanceMetrics() {
    const now = Date.now();
    const timeElapsed = now - this.performanceMetrics.startTime;
    const timeElapsedSeconds = timeElapsed / 1000;

    const throughput = timeElapsedSeconds > 0 ? this.performanceMetrics.requests / timeElapsedSeconds : 0;
    const errorRate = this.performanceMetrics.requests > 0 
      ? (this.performanceMetrics.errors / this.performanceMetrics.requests) * 100 
      : 0;
    const avgResponseTime = this.performanceMetrics.requests > 0 
      ? this.performanceMetrics.totalResponseTime / this.performanceMetrics.requests 
      : 0;

    return {
      responseTime: avgResponseTime,
      throughput,
      errorRate
    };
  }

  private async getCPUUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      const startTime = Date.now();

      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const endTime = Date.now();
        
        const totalTime = (endTime - startTime) * 1000; // Convert to microseconds
        const totalCPUTime = endUsage.user + endUsage.system;
        
        const cpuPercent = (totalCPUTime / totalTime) * 100;
        resolve(Math.min(100, Math.max(0, cpuPercent)));
      }, 100);
    });
  }

  private addAlert(level: HealthAlert['level'], message: string, service?: string, details?: string): void {
    const alert: HealthAlert = {
      level,
      message: details ? `${message}: ${details}` : message,
      timestamp: new Date(),
      service,
      resolved: false
    };

    this.alerts.unshift(alert);

    // Keep only recent alerts
    if (this.alerts.length > this.MAX_ALERTS) {
      this.alerts = this.alerts.slice(0, this.MAX_ALERTS);
    }

    // Emit alert event
    this.emit('alert', alert);

    // Log based on severity
    switch (level) {
      case 'critical':
        logger.error(`🚨 CRITICAL: ${alert.message}`);
        break;
      case 'error':
        logger.error(`❌ ERROR: ${alert.message}`);
        break;
      case 'warning':
        logger.warn(`⚠️ WARNING: ${alert.message}`);
        break;
      case 'info':
        logger.info(`ℹ️ INFO: ${alert.message}`);
        break;
    }
  }

  private getRecentAlerts(hours: number = 1): HealthAlert[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.alerts.filter(alert => alert.timestamp >= cutoff);
  }

  private logHealthStatus(metrics: HealthMetrics): void {
    const healthyServices = Object.values(metrics.services).filter(Boolean).length;
    const totalServices = Object.keys(metrics.services).length;
    const healthyDeps = Object.values(metrics.dependencies).filter(Boolean).length;
    const totalDeps = Object.keys(metrics.dependencies).length;

    const memoryUsagePercent = (metrics.memoryUsage.heapUsed / metrics.memoryUsage.heapTotal) * 100;

    logger.debug(`Health Check: Services ${healthyServices}/${totalServices}, ` +
      `Dependencies ${healthyDeps}/${totalDeps}, ` +
      `Memory ${memoryUsagePercent.toFixed(1)}%, ` +
      `CPU ${metrics.cpuUsage.toFixed(1)}%, ` +
      `Response ${metrics.performance.responseTime.toFixed(0)}ms`);

    // Log warnings for unhealthy components
    if (healthyServices < totalServices) {
      const unhealthyServices = Object.entries(metrics.services)
        .filter(([_, healthy]) => !healthy)
        .map(([name, _]) => name);
      logger.warn(`Unhealthy services: ${unhealthyServices.join(', ')}`);
    }

    if (healthyDeps < totalDeps) {
      const unhealthyDeps = Object.entries(metrics.dependencies)
        .filter(([_, healthy]) => !healthy)
        .map(([name, _]) => name);
      logger.warn(`Unhealthy dependencies: ${unhealthyDeps.join(', ')}`);
    }
  }

  // Public methods for external monitoring
  public recordRequest(responseTime: number, isError: boolean = false): void {
    this.performanceMetrics.requests++;
    this.performanceMetrics.totalResponseTime += responseTime;
    
    if (isError) {
      this.performanceMetrics.errors++;
    }
  }

  public getHealthMetrics(): HealthMetrics | null {
    if (!this.isRunning) return null;

    // Return cached metrics or run immediate check
    return this.runHealthCheck();
  }

  public getAlerts(level?: HealthAlert['level'], hours: number = 24): HealthAlert[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    let filteredAlerts = this.alerts.filter(alert => alert.timestamp >= cutoff);
    
    if (level) {
      filteredAlerts = filteredAlerts.filter(alert => alert.level === level);
    }
    
    return filteredAlerts;
  }

  public resolveAlert(alertIndex: number): void {
    if (alertIndex >= 0 && alertIndex < this.alerts.length) {
      this.alerts[alertIndex].resolved = true;
      this.emit('alertResolved', this.alerts[alertIndex]);
    }
  }

  public clearAlerts(): void {
    this.alerts = [];
    this.emit('alertsCleared');
  }

  public updateThresholds(newThresholds: Partial<HealthThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    logger.info('Health monitoring thresholds updated');
  }

  public getThresholds(): HealthThresholds {
    return { ...this.thresholds };
  }

  public isHealthy(): boolean {
    const recentCriticalAlerts = this.getAlerts('critical', 0.5); // Last 30 minutes
    const recentErrorAlerts = this.getAlerts('error', 0.5);
    
    return recentCriticalAlerts.length === 0 && recentErrorAlerts.length < 3;
  }
}
