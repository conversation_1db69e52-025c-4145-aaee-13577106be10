#!/usr/bin/env node

/**
 * Database Schema Validation Script for MEV Arbitrage Bot
 * =======================================================
 * 
 * This script validates all database schemas and ensures proper setup:
 * 1. Supabase schema validation
 * 2. PostgreSQL schema validation  
 * 3. InfluxDB bucket and measurement validation
 * 4. Redis connection and basic operations
 * 5. Cross-database data consistency checks
 */

import { createClient } from '@supabase/supabase-js';
import { InfluxDB } from '@influxdata/influxdb-client';
import { createClient as createRedisClient } from 'redis';
import pkg from 'pg';
const { Client } = pkg;
import dotenv from 'dotenv';

dotenv.config();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Required tables for each database
const REQUIRED_SUPABASE_TABLES = [
  'trades', 'opportunities', 'performance_metrics', 'tokens', 
  'system_alerts', 'configuration', 'networks', 'validations'
];

const REQUIRED_POSTGRES_TABLES = [
  'trades', 'opportunities', 'performance_metrics', 'tokens',
  'system_alerts', 'configuration', 'networks', 'validations',
  'sync_status', 'sync_conflicts', 'flash_loan_quotes'
];

// Logging functions
function log(message, color = 'reset') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logStep(step, message) {
  log(`\n🔄 Step ${step}: ${message}`, 'cyan');
}

// Validate Supabase schema
async function validateSupabaseSchema() {
  logStep(1, 'Validating Supabase Schema');
  
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    logInfo('Testing Supabase connection...');
    
    // Test basic connection
    const { data: testData, error: testError } = await supabase
      .from('trades')
      .select('count')
      .limit(1);
    
    if (testError && !testError.message.includes('relation "trades" does not exist')) {
      throw new Error(`Supabase connection failed: ${testError.message}`);
    }

    logSuccess('Supabase connection established');

    // Check for required tables
    const missingTables = [];
    for (const table of REQUIRED_SUPABASE_TABLES) {
      try {
        const { error } = await supabase.from(table).select('*').limit(1);
        if (error && error.message.includes('does not exist')) {
          missingTables.push(table);
        }
      } catch (err) {
        missingTables.push(table);
      }
    }

    if (missingTables.length > 0) {
      logWarning(`Missing Supabase tables: ${missingTables.join(', ')}`);
      logInfo('Run the Supabase schema script to create missing tables');
      return false;
    }

    logSuccess(`All ${REQUIRED_SUPABASE_TABLES.length} Supabase tables found`);

    // Test write/read operations
    const testRecord = {
      opportunity_id: `test_${Date.now()}`,
      type: 'arbitrage',
      assets: ['ETH', 'USDC'],
      exchanges: ['uniswap', 'sushiswap'],
      potential_profit: 100.50,
      profit_percentage: 2.5,
      timestamp: new Date().toISOString(),
      network: 'ethereum',
      confidence: 95.0,
      slippage: 0.5
    };

    const { data: insertData, error: insertError } = await supabase
      .from('opportunities')
      .insert(testRecord)
      .select();

    if (insertError) {
      throw new Error(`Supabase write test failed: ${insertError.message}`);
    }

    // Clean up test record
    await supabase
      .from('opportunities')
      .delete()
      .eq('opportunity_id', testRecord.opportunity_id);

    logSuccess('Supabase write/read operations successful');
    return true;

  } catch (error) {
    logError(`Supabase validation failed: ${error.message}`);
    return false;
  }
}

// Validate PostgreSQL schema
async function validatePostgreSQLSchema() {
  logStep(2, 'Validating PostgreSQL Schema');
  
  try {
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'mev_arbitrage_bot',
      user: 'mev_user',
      password: 'mev_password'
    });

    await client.connect();
    logSuccess('PostgreSQL connection established');

    // Check for required tables
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
    `);

    const existingTables = result.rows.map(row => row.table_name);
    const missingTables = REQUIRED_POSTGRES_TABLES.filter(
      table => !existingTables.includes(table)
    );

    if (missingTables.length > 0) {
      logWarning(`Missing PostgreSQL tables: ${missingTables.join(', ')}`);
      return false;
    }

    logSuccess(`All ${REQUIRED_POSTGRES_TABLES.length} PostgreSQL tables found`);

    // Test sync status table
    await client.query(`
      INSERT INTO sync_status (table_name, last_sync_timestamp, sync_direction, sync_status)
      VALUES ('test_table', NOW(), 'bidirectional', 'success')
      ON CONFLICT (table_name, sync_direction) 
      DO UPDATE SET last_sync_timestamp = NOW()
    `);

    await client.query(`DELETE FROM sync_status WHERE table_name = 'test_table'`);
    
    logSuccess('PostgreSQL write/read operations successful');
    await client.end();
    return true;

  } catch (error) {
    logError(`PostgreSQL validation failed: ${error.message}`);
    return false;
  }
}

// Validate InfluxDB setup
async function validateInfluxDBSetup() {
  logStep(3, 'Validating InfluxDB Setup');
  
  try {
    const influxDB = new InfluxDB({
      url: process.env.INFLUXDB_URL,
      token: process.env.INFLUXDB_TOKEN
    });

    const queryApi = influxDB.getQueryApi(process.env.INFLUXDB_ORG);
    const writeApi = influxDB.getWriteApi(process.env.INFLUXDB_ORG, process.env.INFLUXDB_BUCKET);

    logInfo('Testing InfluxDB connection...');

    // Test write operation
    const point = {
      measurement: 'test_validation',
      tags: { source: 'validation_script' },
      fields: { value: 1 },
      timestamp: new Date()
    };

    writeApi.writePoint(point);
    await writeApi.close();

    logSuccess('InfluxDB write operation successful');

    // Test read operation
    const query = `
      from(bucket: "${process.env.INFLUXDB_BUCKET}")
        |> range(start: -1h)
        |> filter(fn: (r) => r._measurement == "test_validation")
        |> limit(n: 1)
    `;

    const result = await queryApi.collectRows(query);
    logSuccess('InfluxDB read operation successful');

    return true;

  } catch (error) {
    logError(`InfluxDB validation failed: ${error.message}`);
    return false;
  }
}

// Validate Redis setup
async function validateRedisSetup() {
  logStep(4, 'Validating Redis Setup');
  
  try {
    const redis = createRedisClient({
      url: process.env.REDIS_URL
    });

    await redis.connect();
    logSuccess('Redis connection established');

    // Test write/read operations
    await redis.set('test_validation', 'success');
    const value = await redis.get('test_validation');
    
    if (value !== 'success') {
      throw new Error('Redis read/write test failed');
    }

    await redis.del('test_validation');
    await redis.disconnect();

    logSuccess('Redis write/read operations successful');
    return true;

  } catch (error) {
    logError(`Redis validation failed: ${error.message}`);
    return false;
  }
}

// Main validation function
async function validateDatabaseSchema() {
  try {
    log(`${colors.bright}🔍 MEV ARBITRAGE BOT - DATABASE SCHEMA VALIDATION${colors.reset}`);
    log('=' .repeat(60));
    
    const results = {
      supabase: await validateSupabaseSchema(),
      postgresql: await validatePostgreSQLSchema(),
      influxdb: await validateInfluxDBSetup(),
      redis: await validateRedisSetup()
    };

    // Display results summary
    logStep(5, 'Validation Summary');
    console.log('\n📊 Database Validation Results:');
    console.log('================================');
    
    const services = [
      { name: 'Supabase', status: results.supabase },
      { name: 'PostgreSQL', status: results.postgresql },
      { name: 'InfluxDB', status: results.influxdb },
      { name: 'Redis', status: results.redis }
    ];

    let allPassed = true;
    services.forEach(service => {
      const status = service.status ? '✅ Valid' : '❌ Invalid';
      console.log(`${service.name.padEnd(12)}: ${status}`);
      if (!service.status) allPassed = false;
    });

    if (allPassed) {
      logSuccess('\n🎉 All database schemas are valid and operational!');
      logInfo('System is ready for backend integration');
    } else {
      logWarning('\n⚠️  Some database validations failed. Check the logs above.');
      logInfo('Fix the issues before proceeding with backend integration');
    }

    return allPassed;

  } catch (error) {
    logError(`Database schema validation failed: ${error.message}`);
    return false;
  }
}

// Run validation
validateDatabaseSchema();
