import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import { SupabaseService } from '../services/SupabaseService.js';
import { TokenDiscoveryService } from '../services/TokenDiscoveryService.js';
import { PriceFeedService } from '../services/PriceFeedService.js';
import { OpportunityDetectionService } from '../services/OpportunityDetectionService.js';
import { ExecutionService } from '../services/ExecutionService.js';
import { RiskManagementService } from '../services/RiskManagementService.js';
import { AnalyticsService } from '../services/AnalyticsService.js';
import { MLLearningService } from '../services/MLLearningService.js';
import { StrategySelectionService } from '../services/StrategySelectionService.js';

export interface ServiceDefinition {
  name: string;
  instance: any;
  dependencies: string[];
  critical: boolean;
  startTimeout: number;
  stopTimeout: number;
  healthCheck?: () => boolean;
}

export interface ServiceInitResult {
  started: string[];
  failed: string[];
  warnings: string[];
  criticalFailures: string[];
}

export class ServiceManager extends EventEmitter {
  private services: Map<string, ServiceDefinition> = new Map();
  private serviceInstances: Map<string, any> = new Map();
  private startupOrder: string[] = [];
  private shutdownOrder: string[] = [];
  private isInitialized = false;

  constructor() {
    super();
    this.defineServices();
    this.calculateStartupOrder();
  }

  private defineServices(): void {
    // Core infrastructure services
    this.addService({
      name: 'SupabaseService',
      instance: null, // Will be created during initialization
      dependencies: [],
      critical: true,
      startTimeout: 10000,
      stopTimeout: 5000,
      healthCheck: () => this.serviceInstances.get('SupabaseService')?.isHealthy() || false
    });

    // Data services
    this.addService({
      name: 'TokenDiscoveryService',
      instance: null,
      dependencies: [],
      critical: true,
      startTimeout: 15000,
      stopTimeout: 5000,
      healthCheck: () => this.serviceInstances.get('TokenDiscoveryService')?.isHealthy() || false
    });

    this.addService({
      name: 'PriceFeedService',
      instance: null,
      dependencies: [],
      critical: true,
      startTimeout: 20000,
      stopTimeout: 10000,
      healthCheck: () => this.serviceInstances.get('PriceFeedService')?.isHealthy() || false
    });

    // Core business logic services
    this.addService({
      name: 'OpportunityDetectionService',
      instance: null,
      dependencies: ['PriceFeedService', 'TokenDiscoveryService'],
      critical: true,
      startTimeout: 15000,
      stopTimeout: 10000,
      healthCheck: () => this.serviceInstances.get('OpportunityDetectionService')?.isHealthy() || false
    });

    this.addService({
      name: 'RiskManagementService',
      instance: null,
      dependencies: [],
      critical: true,
      startTimeout: 10000,
      stopTimeout: 5000,
      healthCheck: () => this.serviceInstances.get('RiskManagementService')?.isHealthy() || false
    });

    this.addService({
      name: 'AnalyticsService',
      instance: null,
      dependencies: ['SupabaseService'],
      critical: false,
      startTimeout: 10000,
      stopTimeout: 5000,
      healthCheck: () => this.serviceInstances.get('AnalyticsService')?.isHealthy() || false
    });

    // ML services
    this.addService({
      name: 'MLLearningService',
      instance: null,
      dependencies: ['SupabaseService'],
      critical: false,
      startTimeout: 15000,
      stopTimeout: 10000,
      healthCheck: () => true // ML service doesn't have health check yet
    });

    this.addService({
      name: 'StrategySelectionService',
      instance: null,
      dependencies: ['MLLearningService'],
      critical: false,
      startTimeout: 10000,
      stopTimeout: 5000,
      healthCheck: () => true // Strategy service doesn't have health check yet
    });

    // Execution service (depends on most other services)
    this.addService({
      name: 'ExecutionService',
      instance: null,
      dependencies: ['OpportunityDetectionService', 'RiskManagementService', 'MLLearningService', 'StrategySelectionService'],
      critical: true,
      startTimeout: 20000,
      stopTimeout: 15000,
      healthCheck: () => this.serviceInstances.get('ExecutionService')?.isHealthy() || false
    });
  }

  private addService(definition: ServiceDefinition): void {
    this.services.set(definition.name, definition);
  }

  private calculateStartupOrder(): void {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const order: string[] = [];

    const visit = (serviceName: string) => {
      if (visiting.has(serviceName)) {
        throw new Error(`Circular dependency detected involving ${serviceName}`);
      }
      if (visited.has(serviceName)) {
        return;
      }

      visiting.add(serviceName);
      const service = this.services.get(serviceName);
      
      if (service) {
        for (const dependency of service.dependencies) {
          visit(dependency);
        }
      }

      visiting.delete(serviceName);
      visited.add(serviceName);
      order.push(serviceName);
    };

    // Visit all services
    for (const serviceName of this.services.keys()) {
      visit(serviceName);
    }

    this.startupOrder = order;
    this.shutdownOrder = [...order].reverse();

    logger.debug(`Service startup order: ${this.startupOrder.join(' -> ')}`);
  }

  public async initializeAll(): Promise<ServiceInitResult> {
    logger.info('⚙️ Initializing services...');

    const result: ServiceInitResult = {
      started: [],
      failed: [],
      warnings: [],
      criticalFailures: []
    };

    // Create service instances
    this.createServiceInstances();

    // Start services in dependency order
    for (const serviceName of this.startupOrder) {
      try {
        await this.startService(serviceName);
        result.started.push(serviceName);
        this.emit('serviceStarted', serviceName);
        
      } catch (error) {
        const service = this.services.get(serviceName);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        result.failed.push(serviceName);
        
        if (service?.critical) {
          result.criticalFailures.push(`Critical service ${serviceName} failed to start: ${errorMessage}`);
        } else {
          result.warnings.push(`Optional service ${serviceName} failed to start: ${errorMessage}`);
        }
        
        this.emit('serviceFailed', serviceName, error);
        logger.error(`❌ Failed to start ${serviceName}:`, error);

        // Stop if critical service fails
        if (service?.critical) {
          logger.error('Critical service failed, stopping initialization');
          break;
        }
      }
    }

    // Set up service event handlers
    this.setupServiceEventHandlers();

    this.isInitialized = result.criticalFailures.length === 0;
    
    logger.info(`✅ Service initialization completed: ${result.started.length} started, ${result.failed.length} failed`);
    
    return result;
  }

  private createServiceInstances(): void {
    // Create instances in the correct order
    const supabaseService = new SupabaseService();
    this.serviceInstances.set('SupabaseService', supabaseService);

    const tokenDiscoveryService = new TokenDiscoveryService();
    this.serviceInstances.set('TokenDiscoveryService', tokenDiscoveryService);

    const priceFeedService = new PriceFeedService();
    this.serviceInstances.set('PriceFeedService', priceFeedService);

    const opportunityDetectionService = new OpportunityDetectionService(
      priceFeedService,
      tokenDiscoveryService
    );
    this.serviceInstances.set('OpportunityDetectionService', opportunityDetectionService);

    const executionService = new ExecutionService();
    this.serviceInstances.set('ExecutionService', executionService);

    const riskManagementService = new RiskManagementService();
    this.serviceInstances.set('RiskManagementService', riskManagementService);

    const analyticsService = new AnalyticsService();
    this.serviceInstances.set('AnalyticsService', analyticsService);

    // ML services
    const mlLearningService = new MLLearningService(supabaseService);
    this.serviceInstances.set('MLLearningService', mlLearningService);

    const strategySelectionService = new StrategySelectionService(mlLearningService);
    this.serviceInstances.set('StrategySelectionService', strategySelectionService);

    // Integrate ML services with execution service
    executionService.setMLServices(mlLearningService, strategySelectionService);

    logger.info('✅ Service instances created');
  }

  private async startService(serviceName: string): Promise<void> {
    const service = this.services.get(serviceName);
    const instance = this.serviceInstances.get(serviceName);

    if (!service || !instance) {
      throw new Error(`Service ${serviceName} not found`);
    }

    logger.info(`Starting ${serviceName}...`);

    // Check dependencies are started
    for (const depName of service.dependencies) {
      const depInstance = this.serviceInstances.get(depName);
      if (!depInstance || (depInstance.isHealthy && !depInstance.isHealthy())) {
        throw new Error(`Dependency ${depName} is not available for ${serviceName}`);
      }
    }

    // Start the service with timeout
    const startPromise = instance.start ? instance.start() : Promise.resolve();
    
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Service ${serviceName} start timeout`)), service.startTimeout);
    });

    await Promise.race([startPromise, timeoutPromise]);

    // Verify service is healthy
    if (service.healthCheck && !service.healthCheck()) {
      throw new Error(`Service ${serviceName} failed health check after start`);
    }

    logger.info(`✅ ${serviceName} started successfully`);
  }

  private setupServiceEventHandlers(): void {
    // Set up cross-service event handling
    const opportunityService = this.serviceInstances.get('OpportunityDetectionService');
    const executionService = this.serviceInstances.get('ExecutionService');
    const analyticsService = this.serviceInstances.get('AnalyticsService');
    const riskService = this.serviceInstances.get('RiskManagementService');
    const mlLearningService = this.serviceInstances.get('MLLearningService');
    const strategyService = this.serviceInstances.get('StrategySelectionService');

    if (opportunityService && executionService) {
      opportunityService.on('opportunity', (opportunity: any) => {
        executionService.evaluateOpportunity(opportunity);
      });
    }

    if (executionService && analyticsService) {
      executionService.on('trade', (trade: any) => {
        analyticsService.recordTrade(trade);
      });
    }

    if (riskService && executionService) {
      riskService.on('emergencyStop', () => {
        executionService.pause();
      });
    }

    if (mlLearningService) {
      mlLearningService.on('learningUpdate', (update: any) => {
        this.emit('mlLearningUpdate', update);
      });
    }

    if (strategyService) {
      strategyService.on('strategyUpdate', (update: any) => {
        this.emit('strategyUpdate', update);
      });
    }

    logger.info('✅ Service event handlers configured');
  }

  public async shutdownAll(): Promise<void> {
    logger.info('🛑 Shutting down services...');

    // Shutdown in reverse order
    for (const serviceName of this.shutdownOrder) {
      try {
        await this.stopService(serviceName);
        logger.info(`✅ ${serviceName} stopped`);
      } catch (error) {
        logger.error(`❌ Error stopping ${serviceName}:`, error);
      }
    }

    this.isInitialized = false;
    logger.info('✅ All services shut down');
  }

  private async stopService(serviceName: string): Promise<void> {
    const service = this.services.get(serviceName);
    const instance = this.serviceInstances.get(serviceName);

    if (!service || !instance) {
      return; // Service not found or not started
    }

    if (!instance.stop) {
      return; // Service doesn't have stop method
    }

    const stopPromise = instance.stop();
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Service ${serviceName} stop timeout`)), service.stopTimeout);
    });

    await Promise.race([stopPromise, timeoutPromise]);
  }

  public getService<T>(serviceName: string): T | undefined {
    return this.serviceInstances.get(serviceName) as T;
  }

  public getServiceStatus(): { [key: string]: boolean } {
    const status: { [key: string]: boolean } = {};
    
    for (const [name, service] of this.services.entries()) {
      const instance = this.serviceInstances.get(name);
      status[name] = service.healthCheck ? service.healthCheck() : false;
    }
    
    return status;
  }

  public isServiceHealthy(serviceName: string): boolean {
    const service = this.services.get(serviceName);
    return service?.healthCheck ? service.healthCheck() : false;
  }

  public getStartupOrder(): string[] {
    return [...this.startupOrder];
  }

  public getShutdownOrder(): string[] {
    return [...this.shutdownOrder];
  }

  public isSystemInitialized(): boolean {
    return this.isInitialized;
  }

  public async restartService(serviceName: string): Promise<void> {
    logger.info(`🔄 Restarting service: ${serviceName}`);
    
    try {
      await this.stopService(serviceName);
      await this.startService(serviceName);
      logger.info(`✅ Service ${serviceName} restarted successfully`);
    } catch (error) {
      logger.error(`❌ Failed to restart service ${serviceName}:`, error);
      throw error;
    }
  }
}
