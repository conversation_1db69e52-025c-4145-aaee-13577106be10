# 🚀 MEV Arbitrage Bot - Full System Startup Guide

## 📋 Overview

This guide provides step-by-step instructions to run the complete MEV Arbitrage Bot system with all databases, proper data routing, and full automation.

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Enhanced      │    │   Databases     │
│   Dashboard     │────│   Backend       │────│   (Multi-DB)    │
│   (Browser)     │    │   (Port 8080)   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │              ┌─────────────────┐
         │                       │              │     Redis       │
         │                       │              │   (Caching)     │
         │                       │              └─────────────────┘
         │                       │              ┌─────────────────┐
         │                       │              │   PostgreSQL    │
         │                       │              │  (Persistent)   │
         │                       │              └─────────────────┘
         │                       │              ┌─────────────────┐
         │                       │              │    InfluxDB     │
         │                       │              │ (Time-series)   │
         │                       │              └─────────────────┘
         │                       │              ┌─────────────────┐
         │                       │              │    Supabase     │
         │                       │              │   (Cloud DB)    │
         └───────────────────────┼──────────────┴─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Real-time     │
                    │   Data Flow     │
                    │   & Routing     │
                    └─────────────────┘
```

## 🎯 Quick Start (Automated)

### Option 1: Full Automated Startup

```bash
# Start the complete system with all databases
npm run start:full
```

This command will:
- ✅ Start all database containers (Redis, PostgreSQL, InfluxDB)
- ✅ Initialize database connections
- ✅ Start enhanced backend with database integration
- ✅ Verify all services are running
- ✅ Display system status and URLs

### Option 2: Enhanced Backend Only

```bash
# Start enhanced backend with database connections
npm run start:enhanced
```

### Option 3: System Verification

```bash
# Verify all system components
npm run verify
```

## 📋 Prerequisites

### Required Software

1. **Docker Desktop** - For database containers
   ```bash
   # Download from: https://www.docker.com/products/docker-desktop
   # Verify installation:
   docker --version
   docker-compose --version
   ```

2. **Node.js 18+** - For backend services
   ```bash
   # Verify installation:
   node --version
   npm --version
   ```

3. **Git** - For version control
   ```bash
   git --version
   ```

### Environment Setup

The system uses the existing `.env` file with all necessary configurations:

- ✅ **Database URLs** - Redis, PostgreSQL, InfluxDB
- ✅ **Supabase Credentials** - Cloud database integration
- ✅ **API Keys** - External service integrations
- ✅ **Security Settings** - JWT secrets, CORS origins

## 🗄️ Database Services

### 1. Redis (Caching & Real-time)
- **Port**: 6379
- **Purpose**: Caching, session storage, real-time data
- **Status**: Auto-started with Docker

### 2. PostgreSQL (Local Persistent)
- **Port**: 5432
- **Database**: `mev_arbitrage_bot`
- **User**: `mev_user`
- **Purpose**: Local persistent storage
- **Status**: Auto-started with Docker

### 3. InfluxDB (Time-series)
- **Port**: 8086
- **Organization**: `Dev-KE`
- **Bucket**: `mev-monitoring`
- **Purpose**: Price data, metrics, analytics
- **UI**: http://localhost:8086
- **Status**: Auto-started with Docker

### 4. Supabase (Cloud Database)
- **URL**: https://sbgeliidkalbnywvaiwe.supabase.co
- **Purpose**: Structured data, trades, opportunities
- **Status**: Cloud service (always available)

## 🔄 Data Flow & Routing

### Data Sources → Storage Routing

```
Opportunities Data:
├── Generated/Detected → Supabase (opportunities table)
├── Metrics → InfluxDB (opportunities measurement)
└── Cache → Redis (real-time access)

Trades Data:
├── Executed Trades → Supabase (trades table)
├── Performance → InfluxDB (trades measurement)
└── Recent → Redis (quick access)

Price Data:
├── Real-time Prices → InfluxDB (price_data measurement)
├── Historical → InfluxDB (time-series queries)
└── Current → Redis (fast lookup)

System Metrics:
├── Performance → Supabase (performance_metrics table)
├── Health → InfluxDB (system_health measurement)
└── Status → Redis (real-time monitoring)
```

### API Endpoints → Database Mapping

| Endpoint | Primary Source | Fallback | Cache |
|----------|---------------|----------|-------|
| `/api/opportunities` | Supabase | Mock Data | Redis |
| `/api/trades` | Supabase | Mock Data | Redis |
| `/api/tokens` | Supabase | Mock Data | Redis |
| `/api/analytics/performance` | Supabase | Calculated | InfluxDB |
| `/api/system/health` | Live Status | - | Redis |
| `/api/realtime/update` | Generated | - | Redis |

## 🚀 Step-by-Step Manual Startup

### Step 1: Start Database Services

```bash
# Start all database containers
docker-compose up -d redis postgres influxdb

# Verify containers are running
docker-compose ps
```

### Step 2: Wait for Services to Initialize

```bash
# Check Redis
redis-cli ping

# Check PostgreSQL
docker exec mev-postgres pg_isready -U mev_user -d mev_arbitrage_bot

# Check InfluxDB
curl http://localhost:8086/health
```

### Step 3: Start Enhanced Backend

```bash
# Start backend with database integration
npm run start:enhanced
```

### Step 4: Verify System Health

```bash
# Run comprehensive verification
npm run verify
```

### Step 5: Access Frontend

Open in browser: `file:///path/to/your/project/index.html`

## 📊 Monitoring & Verification

### Health Check URLs

- **Backend Health**: http://localhost:8080/health
- **System Status**: http://localhost:8080/api/system/health
- **InfluxDB UI**: http://localhost:8086
- **Supabase Dashboard**: https://sbgeliidkalbnywvaiwe.supabase.co

### Real-time Monitoring

```bash
# Watch backend logs
npm run start:enhanced

# Monitor database connections
npm run verify

# Check Docker container status
docker-compose ps

# View container logs
docker-compose logs redis
docker-compose logs postgres
docker-compose logs influxdb
```

## 🔧 Troubleshooting

### Common Issues

1. **Docker not running**
   ```bash
   # Start Docker Desktop
   # Then retry: docker-compose up -d
   ```

2. **Port conflicts**
   ```bash
   # Check what's using ports
   netstat -an | findstr :8080
   netstat -an | findstr :6379
   netstat -an | findstr :5432
   netstat -an | findstr :8086
   ```

3. **Database connection failures**
   ```bash
   # Restart database containers
   docker-compose restart redis postgres influxdb
   
   # Check logs
   docker-compose logs
   ```

4. **Backend startup issues**
   ```bash
   # Check environment variables
   cat .env
   
   # Verify dependencies
   npm install
   
   # Try simple backend first
   npm run start
   ```

### Reset System

```bash
# Stop all services
docker-compose down

# Remove volumes (WARNING: deletes all data)
docker-compose down -v

# Restart fresh
npm run start:full
```

## 📈 Performance Optimization

### Database Tuning

1. **Redis**: Already optimized for caching
2. **PostgreSQL**: Connection pooling enabled
3. **InfluxDB**: Batch writes configured
4. **Supabase**: Row-level security enabled

### Monitoring Metrics

- **Response Times**: < 100ms for cached data
- **Database Connections**: Auto-managed
- **Memory Usage**: Monitored via health endpoints
- **Error Rates**: Logged and tracked

## 🎉 Success Indicators

When the system is fully operational, you should see:

✅ **All database containers running**  
✅ **Backend server responding on port 8080**  
✅ **All API endpoints returning data**  
✅ **Frontend dashboard accessible**  
✅ **Real-time data updates working**  
✅ **Database connections established**  
✅ **Data routing functioning correctly**  

## 📞 Support

If you encounter issues:

1. Run `npm run verify` for detailed diagnostics
2. Check the troubleshooting section above
3. Review container logs: `docker-compose logs`
4. Ensure all prerequisites are installed
5. Verify environment variables in `.env`

---

**🚀 Your MEV Arbitrage Bot system is now fully operational with complete database integration and automated data routing!**
