import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { PriceFeedService } from '../../../backend/services/PriceFeedService.js';

describe('PriceFeedService', () => {
  let priceFeedService: PriceFeedService;

  beforeEach(() => {
    priceFeedService = new PriceFeedService();
  });

  afterEach(async () => {
    if (priceFeedService) {
      await priceFeedService.stop();
    }
  });

  describe('Service Lifecycle', () => {
    it('should start and stop successfully', async () => {
      expect(priceFeedService.isRunning()).toBe(false);
      
      await priceFeedService.start();
      expect(priceFeedService.isRunning()).toBe(true);
      
      await priceFeedService.stop();
      expect(priceFeedService.isRunning()).toBe(false);
    });

    it('should not start twice', async () => {
      await priceFeedService.start();
      expect(priceFeedService.isRunning()).toBe(true);
      
      // Starting again should not throw error
      await priceFeedService.start();
      expect(priceFeedService.isRunning()).toBe(true);
    });
  });

  describe('Price Data Management', () => {
    beforeEach(async () => {
      await priceFeedService.start();
    });

    it('should store and retrieve price data', async () => {
      const priceData = global.testUtils.mockPriceData;
      
      priceFeedService.updatePriceData(priceData);
      
      const retrievedPrice = priceFeedService.getPrice('ETH');
      expect(retrievedPrice).toEqual(priceData);
    });

    it('should return null for non-existent symbols', () => {
      const price = priceFeedService.getPrice('NONEXISTENT');
      expect(price).toBeNull();
    });

    it('should get all prices', async () => {
      const priceData1 = { ...global.testUtils.mockPriceData, symbol: 'ETH' };
      const priceData2 = { ...global.testUtils.mockPriceData, symbol: 'BTC', price: 40000 };
      
      priceFeedService.updatePriceData(priceData1);
      priceFeedService.updatePriceData(priceData2);
      
      const allPrices = priceFeedService.getAllPrices();
      expect(Object.keys(allPrices)).toHaveLength(2);
      expect(allPrices['ETH']).toEqual(priceData1);
      expect(allPrices['BTC']).toEqual(priceData2);
    });

    it('should emit price update events', (done) => {
      const priceData = global.testUtils.mockPriceData;
      
      priceFeedService.on('priceUpdate', (data) => {
        expect(data).toEqual(priceData);
        done();
      });
      
      priceFeedService.updatePriceData(priceData);
    });
  });

  describe('Price Validation', () => {
    beforeEach(async () => {
      await priceFeedService.start();
    });

    it('should validate price data format', () => {
      const invalidPriceData = {
        symbol: 'ETH',
        // Missing required fields
      };
      
      expect(() => {
        priceFeedService.updatePriceData(invalidPriceData as any);
      }).toThrow();
    });

    it('should handle price updates with different sources', () => {
      const priceData1 = { ...global.testUtils.mockPriceData, source: 'uniswap', price: 2000 };
      const priceData2 = { ...global.testUtils.mockPriceData, source: 'sushiswap', price: 2010 };
      
      priceFeedService.updatePriceData(priceData1);
      priceFeedService.updatePriceData(priceData2);
      
      // Should store the latest update
      const retrievedPrice = priceFeedService.getPrice('ETH');
      expect(retrievedPrice?.source).toBe('sushiswap');
      expect(retrievedPrice?.price).toBe(2010);
    });
  });

  describe('Performance Metrics', () => {
    beforeEach(async () => {
      await priceFeedService.start();
    });

    it('should track update frequency', async () => {
      const priceData = global.testUtils.mockPriceData;
      
      // Update multiple times
      for (let i = 0; i < 5; i++) {
        priceFeedService.updatePriceData({ ...priceData, timestamp: Date.now() + i });
        await global.testUtils.delay(10);
      }
      
      const metrics = priceFeedService.getMetrics();
      expect(metrics.totalUpdates).toBeGreaterThanOrEqual(5);
      expect(metrics.updateRate).toBeGreaterThan(0);
    });

    it('should measure latency', async () => {
      const startTime = Date.now();
      const priceData = { ...global.testUtils.mockPriceData, timestamp: startTime };
      
      priceFeedService.updatePriceData(priceData);
      
      const metrics = priceFeedService.getMetrics();
      expect(metrics.averageLatency).toBeDefined();
      expect(metrics.averageLatency).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      // Mock a service error
      const originalConsoleError = console.error;
      console.error = jest.fn();
      
      try {
        // Force an error condition
        await priceFeedService.start();
        
        // Simulate network error
        priceFeedService.emit('error', new Error('Network error'));
        
        // Service should still be running
        expect(priceFeedService.isRunning()).toBe(true);
      } finally {
        console.error = originalConsoleError;
      }
    });
  });
});
