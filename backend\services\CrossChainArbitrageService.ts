import { MultiChainService, CrossChainOpportunity, NetworkConfig } from './MultiChainService.js';
import { MarketDataService } from './MarketDataService.js';
import logger from '../utils/logger.js';

export interface PriceData {
  networkId: string;
  dex: string;
  price: number;
  liquidity: number;
  slippage: number;
  timestamp: number;
}

export interface ArbitrageCalculation {
  tokenSymbol: string;
  sourceNetwork: string;
  targetNetwork: string;
  sourceDex: string;
  targetDex: string;
  sourcePrice: number;
  targetPrice: number;
  priceDifference: number;
  priceDifferencePercentage: number;
  gasCosts: {
    source: number;
    target: number;
    bridge: number;
    total: number;
  };
  bridgeFee: number;
  bridgeDetails: BridgeCalculation;
  slippageImpact: number;
  netProfit: number;
  netProfitPercentage: number;
  confidence: number;
  riskScore: number;
  executionTime: number;
  minTradeSize: number;
  maxTradeSize: number;
}

export interface BridgeCalculation {
  protocol: string;
  baseFee: number;
  variableFeeRate: number;
  variableFee: number;
  congestionMultiplier: number;
  totalFee: number;
  estimatedTime: number;
  confidence: number;
  alternatives: BridgeAlternative[];
}

export interface BridgeAlternative {
  protocol: string;
  fee: number;
  time: number;
  reliability: number;
}

export interface BridgeProtocolConfig {
  name: string;
  baseFeeUSD: number;
  variableFeeRate: number; // percentage
  minFee: number;
  maxFee: number;
  avgProcessingTime: number; // minutes
  reliability: number; // 0-100
  supportedNetworks: string[];
  congestionSensitivity: number; // 0-1
}

export class CrossChainArbitrageService {
  private multiChainService: MultiChainService;
  private marketDataService: MarketDataService;
  private priceFeeds: Map<string, Map<string, PriceData[]>> = new Map(); // token -> network -> prices
  private opportunities: CrossChainOpportunity[] = [];
  private updateInterval: NodeJS.Timeout | null = null;

  // Bridge protocol configurations with enhanced support for Sui and Fantom
  private readonly bridgeProtocols: Map<string, BridgeProtocolConfig> = new Map([
    ['stargate', {
      name: 'Stargate Finance',
      baseFeeUSD: 5,
      variableFeeRate: 0.06, // 0.06%
      minFee: 5,
      maxFee: 1000,
      avgProcessingTime: 8,
      reliability: 95,
      supportedNetworks: ['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'avalanche', 'fantom'],
      congestionSensitivity: 0.3
    }],
    ['layerzero', {
      name: 'LayerZero',
      baseFeeUSD: 3,
      variableFeeRate: 0.05, // 0.05%
      minFee: 3,
      maxFee: 800,
      avgProcessingTime: 6,
      reliability: 92,
      supportedNetworks: ['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'avalanche', 'fantom'],
      congestionSensitivity: 0.25
    }],
    ['multichain', {
      name: 'Multichain',
      baseFeeUSD: 8,
      variableFeeRate: 0.1, // 0.1%
      minFee: 8,
      maxFee: 1500,
      avgProcessingTime: 12,
      reliability: 88,
      supportedNetworks: ['ethereum', 'bsc', 'polygon', 'avalanche', 'fantom'],
      congestionSensitivity: 0.4
    }],
    ['wormhole', {
      name: 'Wormhole',
      baseFeeUSD: 4,
      variableFeeRate: 0.08, // 0.08%
      minFee: 4,
      maxFee: 1200,
      avgProcessingTime: 10,
      reliability: 90,
      supportedNetworks: ['ethereum', 'bsc', 'polygon', 'solana', 'sui'],
      congestionSensitivity: 0.35
    }],
    ['celer_cbridge', {
      name: 'Celer cBridge',
      baseFeeUSD: 6,
      variableFeeRate: 0.07, // 0.07%
      minFee: 6,
      maxFee: 900,
      avgProcessingTime: 7,
      reliability: 93,
      supportedNetworks: ['ethereum', 'bsc', 'polygon', 'arbitrum', 'optimism', 'avalanche', 'fantom'],
      congestionSensitivity: 0.28
    }],
    ['sui_bridge', {
      name: 'Sui Native Bridge',
      baseFeeUSD: 2,
      variableFeeRate: 0.12, // 0.12%
      minFee: 2,
      maxFee: 500,
      avgProcessingTime: 15,
      reliability: 85,
      supportedNetworks: ['ethereum', 'sui'],
      congestionSensitivity: 0.5
    }],
    ['fantom_bridge', {
      name: 'Fantom Multichain Bridge',
      baseFeeUSD: 7,
      variableFeeRate: 0.09, // 0.09%
      minFee: 7,
      maxFee: 1000,
      avgProcessingTime: 9,
      reliability: 87,
      supportedNetworks: ['ethereum', 'bsc', 'polygon', 'fantom'],
      congestionSensitivity: 0.38
    }]
  ]);

  constructor(multiChainService: MultiChainService, marketDataService: MarketDataService) {
    this.multiChainService = multiChainService;
    this.marketDataService = marketDataService;
    logger.info('CrossChainArbitrageService initialized');
  }

  /**
   * Start monitoring cross-chain arbitrage opportunities
   */
  startMonitoring(intervalMs: number = 60000): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    // Initial scan
    this.scanForOpportunities().catch(error => {
      logger.error('Initial arbitrage scan failed:', error);
    });

    // Schedule regular scans
    this.updateInterval = setInterval(async () => {
      try {
        await this.scanForOpportunities();
      } catch (error) {
        logger.error('Scheduled arbitrage scan failed:', error);
      }
    }, intervalMs);

    logger.info(`Cross-chain arbitrage monitoring started with ${intervalMs}ms interval`);
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      logger.info('Cross-chain arbitrage monitoring stopped');
    }
  }

  /**
   * Scan for cross-chain arbitrage opportunities
   */
  async scanForOpportunities(): Promise<void> {
    try {
      logger.info('Scanning for cross-chain arbitrage opportunities...');
      
      const targetTokens = this.multiChainService.getTargetTokens();
      const networks = this.multiChainService.getSupportedNetworks();
      const newOpportunities: CrossChainOpportunity[] = [];

      for (const token of targetTokens) {
        // Get price data for this token across all networks
        const tokenPrices = await this.getTokenPricesAcrossNetworks(token.symbol);
        
        if (tokenPrices.size < 2) continue; // Need at least 2 networks for arbitrage

        // Find arbitrage opportunities between network pairs
        const networkIds = Array.from(tokenPrices.keys());
        
        for (let i = 0; i < networkIds.length; i++) {
          for (let j = i + 1; j < networkIds.length; j++) {
            const sourceNetworkId = networkIds[i];
            const targetNetworkId = networkIds[j];
            
            const sourcePrices = tokenPrices.get(sourceNetworkId) || [];
            const targetPrices = tokenPrices.get(targetNetworkId) || [];
            
            if (sourcePrices.length === 0 || targetPrices.length === 0) continue;

            // Find best prices on each network
            const bestSourcePrice = this.getBestPrice(sourcePrices, 'sell');
            const bestTargetPrice = this.getBestPrice(targetPrices, 'buy');
            
            if (!bestSourcePrice || !bestTargetPrice) continue;

            // Calculate arbitrage opportunity
            const calculation = await this.calculateArbitrage(
              token.symbol,
              sourceNetworkId,
              targetNetworkId,
              bestSourcePrice,
              bestTargetPrice
            );

            if (calculation && calculation.netProfitPercentage > 0.5) { // Minimum 0.5% profit
              const opportunity: CrossChainOpportunity = {
                id: `arb_${token.symbol}_${sourceNetworkId}_${targetNetworkId}_${Date.now()}`,
                tokenSymbol: calculation.tokenSymbol,
                sourceNetwork: calculation.sourceNetwork,
                targetNetwork: calculation.targetNetwork,
                sourceDex: calculation.sourceDex,
                targetDex: calculation.targetDex,
                sourcePrice: calculation.sourcePrice,
                targetPrice: calculation.targetPrice,
                priceDifference: calculation.priceDifference,
                priceDifferencePercentage: calculation.priceDifferencePercentage,
                estimatedProfit: calculation.netProfit,
                gasCosts: calculation.gasCosts,
                bridgeFee: calculation.bridgeFee,
                slippageImpact: calculation.slippageImpact,
                netProfit: calculation.netProfit,
                netProfitPercentage: calculation.netProfitPercentage,
                confidence: calculation.confidence,
                riskScore: calculation.riskScore,
                executionTime: calculation.executionTime,
                minTradeSize: calculation.minTradeSize,
                maxTradeSize: calculation.maxTradeSize,
                created_at: new Date().toISOString()
              };

              newOpportunities.push(opportunity);
            }
          }
        }
      }

      // Update opportunities list
      this.opportunities = newOpportunities.sort((a, b) => b.netProfitPercentage - a.netProfitPercentage);
      
      logger.info(`Found ${this.opportunities.length} cross-chain arbitrage opportunities`);
      
    } catch (error) {
      logger.error('Error scanning for arbitrage opportunities:', error);
    }
  }

  /**
   * Get token prices across all networks
   */
  private async getTokenPricesAcrossNetworks(tokenSymbol: string): Promise<Map<string, PriceData[]>> {
    const priceMap = new Map<string, PriceData[]>();
    const networks = this.multiChainService.getSupportedNetworks();

    for (const network of networks) {
      const prices = await this.getTokenPricesOnNetwork(tokenSymbol, network.id);
      if (prices.length > 0) {
        priceMap.set(network.id, prices);
      }
    }

    return priceMap;
  }

  /**
   * Get token prices on a specific network
   */
  private async getTokenPricesOnNetwork(tokenSymbol: string, networkId: string): Promise<PriceData[]> {
    const network = this.multiChainService.getNetwork(networkId);
    if (!network) return [];

    const prices: PriceData[] = [];
    
    // Simulate price data from different DEXes (in real implementation, would call actual DEX APIs)
    for (const dex of network.dexes) {
      const basePrice = await this.getBaseTokenPrice(tokenSymbol);
      if (basePrice > 0) {
        // Add some variance to simulate different DEX prices
        const variance = (Math.random() - 0.5) * 0.02; // ±1% variance
        const dexPrice = basePrice * (1 + variance);
        
        prices.push({
          networkId,
          dex,
          price: dexPrice,
          liquidity: Math.random() * 1000000 + 100000, // $100k - $1M liquidity
          slippage: Math.random() * 0.5 + 0.1, // 0.1% - 0.6% slippage
          timestamp: Date.now()
        });
      }
    }

    return prices;
  }

  /**
   * Get base token price from market data
   */
  private async getBaseTokenPrice(tokenSymbol: string): Promise<number> {
    try {
      // Map token symbols to CoinGecko IDs
      const tokenMap: { [key: string]: string } = {
        'BTC': 'bitcoin',
        'WBTC': 'wrapped-bitcoin',
        'ETH': 'ethereum',
        'WETH': 'weth',
        'USDT': 'tether',
        'USDC': 'usd-coin',
        'BUSD': 'binance-usd',
        'BNB': 'binancecoin',
        'SOL': 'solana',
        'WSOL': 'wrapped-solana',
        'MATIC': 'matic-network',
        'WMATIC': 'wmatic',
        'ADA': 'cardano',
        'SUI': 'sui',
        'AVAX': 'avalanche-2',
        'HYPE': 'hyperliquid'
      };

      const coingeckoId = tokenMap[tokenSymbol];
      if (!coingeckoId) return 0;

      // Use existing market data service
      const prices = await this.marketDataService.getTokenPrices([coingeckoId]);
      return prices[coingeckoId]?.usd || 0;
    } catch (error) {
      logger.error(`Error getting price for ${tokenSymbol}:`, error);
      return 0;
    }
  }

  /**
   * Get best price from available options
   */
  private getBestPrice(prices: PriceData[], side: 'buy' | 'sell'): PriceData | null {
    if (prices.length === 0) return null;

    return prices.reduce((best, current) => {
      if (side === 'buy') {
        return current.price < best.price ? current : best;
      } else {
        return current.price > best.price ? current : best;
      }
    });
  }

  /**
   * Calculate arbitrage opportunity
   */
  private async calculateArbitrage(
    tokenSymbol: string,
    sourceNetwork: string,
    targetNetwork: string,
    sourcePrice: PriceData,
    targetPrice: PriceData
  ): Promise<ArbitrageCalculation | null> {
    try {
      const priceDifference = targetPrice.price - sourcePrice.price;
      const priceDifferencePercentage = (priceDifference / sourcePrice.price) * 100;

      // Skip if price difference is negative or too small
      if (priceDifferencePercentage < 0.1) return null;

      // Calculate costs
      const sourceGasCost = this.multiChainService.calculateGasCosts(sourceNetwork, 200000);
      const targetGasCost = this.multiChainService.calculateGasCosts(targetNetwork, 200000);
      const bridgeGasCost = this.multiChainService.calculateGasCosts(sourceNetwork, 300000);
      
      const totalGasCosts = sourceGasCost + targetGasCost + bridgeGasCost;
      
      // Assume $1000 trade size for calculation
      const tradeSize = 1000;
      const bridgeCalculation = await this.calculateEnhancedBridgeFee(sourceNetwork, targetNetwork, tradeSize);
      const slippageImpact = (sourcePrice.slippage + targetPrice.slippage) / 100 * tradeSize;
      
      const grossProfit = (priceDifference / sourcePrice.price) * tradeSize;
      const netProfit = grossProfit - totalGasCosts - bridgeCalculation.totalFee - slippageImpact;
      const netProfitPercentage = (netProfit / tradeSize) * 100;

      // Calculate confidence and risk scores
      const confidence = this.calculateConfidence(sourcePrice, targetPrice, sourceNetwork, targetNetwork);
      const riskScore = this.calculateRiskScore(sourceNetwork, targetNetwork, tokenSymbol);
      
      // Calculate trade size limits
      const minLiquidity = Math.min(sourcePrice.liquidity, targetPrice.liquidity);
      const minTradeSize = Math.max(100, totalGasCosts * 10); // Minimum to cover costs
      const maxTradeSize = Math.min(50000, minLiquidity * 0.1); // Max 10% of liquidity

      const executionTime = this.multiChainService.estimateExecutionTime(sourceNetwork, targetNetwork);

      return {
        tokenSymbol,
        sourceNetwork,
        targetNetwork,
        sourceDex: sourcePrice.dex,
        targetDex: targetPrice.dex,
        sourcePrice: sourcePrice.price,
        targetPrice: targetPrice.price,
        priceDifference,
        priceDifferencePercentage,
        gasCosts: {
          source: sourceGasCost,
          target: targetGasCost,
          bridge: bridgeGasCost,
          total: totalGasCosts
        },
        bridgeFee: bridgeCalculation.totalFee,
        bridgeDetails: bridgeCalculation,
        slippageImpact,
        netProfit,
        netProfitPercentage,
        confidence,
        riskScore,
        executionTime,
        minTradeSize,
        maxTradeSize
      };
    } catch (error) {
      logger.error('Error calculating arbitrage:', error);
      return null;
    }
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(
    sourcePrice: PriceData,
    targetPrice: PriceData,
    sourceNetwork: string,
    targetNetwork: string
  ): number {
    let confidence = 100;

    // Reduce confidence based on slippage
    confidence -= (sourcePrice.slippage + targetPrice.slippage) * 10;

    // Reduce confidence based on liquidity
    const minLiquidity = Math.min(sourcePrice.liquidity, targetPrice.liquidity);
    if (minLiquidity < 100000) confidence -= 20;
    if (minLiquidity < 50000) confidence -= 30;

    // Reduce confidence for newer/riskier networks
    const riskierNetworks = ['sui', 'base'];
    if (riskierNetworks.includes(sourceNetwork) || riskierNetworks.includes(targetNetwork)) {
      confidence -= 15;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * Calculate risk score
   */
  private calculateRiskScore(sourceNetwork: string, targetNetwork: string, tokenSymbol: string): number {
    let risk = 0;

    // Network risk
    const networkRisk: { [key: string]: number } = {
      'ethereum': 5,
      'bsc': 10,
      'polygon': 15,
      'arbitrum': 10,
      'optimism': 10,
      'avalanche': 20,
      'solana': 25,
      'base': 30,
      'fantom': 35,
      'sui': 40
    };

    risk += (networkRisk[sourceNetwork] || 50) + (networkRisk[targetNetwork] || 50);

    // Token risk
    const stablecoins = ['USDT', 'USDC', 'BUSD'];
    if (!stablecoins.includes(tokenSymbol)) {
      risk += 20; // Higher risk for volatile tokens
    }

    // Bridge risk
    risk += 15; // Base bridge risk

    return Math.min(100, risk);
  }

  /**
   * Get current opportunities
   */
  getOpportunities(limit: number = 20): CrossChainOpportunity[] {
    return this.opportunities.slice(0, limit);
  }

  /**
   * Get opportunities for specific token
   */
  getOpportunitiesForToken(tokenSymbol: string): CrossChainOpportunity[] {
    return this.opportunities.filter(opp => opp.tokenSymbol === tokenSymbol);
  }

  /**
   * Get opportunities between specific networks
   */
  getOpportunitiesBetweenNetworks(sourceNetwork: string, targetNetwork: string): CrossChainOpportunity[] {
    return this.opportunities.filter(opp => 
      opp.sourceNetwork === sourceNetwork && opp.targetNetwork === targetNetwork
    );
  }

  /**
   * Get statistics
   */
  getStatistics() {
    const opportunities = this.opportunities;
    
    if (opportunities.length === 0) {
      return {
        totalOpportunities: 0,
        avgProfitPercentage: 0,
        avgExecutionTime: 0,
        topTokens: [],
        topNetworkPairs: [],
        totalPotentialProfit: 0
      };
    }

    const avgProfitPercentage = opportunities.reduce((sum, opp) => sum + opp.netProfitPercentage, 0) / opportunities.length;
    const avgExecutionTime = opportunities.reduce((sum, opp) => sum + opp.executionTime, 0) / opportunities.length;
    const totalPotentialProfit = opportunities.reduce((sum, opp) => sum + opp.netProfit, 0);

    // Top tokens by opportunity count
    const tokenCounts = new Map<string, number>();
    opportunities.forEach(opp => {
      tokenCounts.set(opp.tokenSymbol, (tokenCounts.get(opp.tokenSymbol) || 0) + 1);
    });
    const topTokens = Array.from(tokenCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([token, count]) => ({ token, count }));

    // Top network pairs
    const pairCounts = new Map<string, number>();
    opportunities.forEach(opp => {
      const pair = `${opp.sourceNetwork}-${opp.targetNetwork}`;
      pairCounts.set(pair, (pairCounts.get(pair) || 0) + 1);
    });
    const topNetworkPairs = Array.from(pairCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([pair, count]) => ({ pair, count }));

    return {
      totalOpportunities: opportunities.length,
      avgProfitPercentage,
      avgExecutionTime,
      topTokens,
      topNetworkPairs,
      totalPotentialProfit,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Calculate enhanced bridge fees with multi-protocol support for Sui and Fantom
   */
  private async calculateEnhancedBridgeFee(
    sourceNetwork: string,
    targetNetwork: string,
    amount: number
  ): Promise<BridgeCalculation> {

    try {
      // Find available bridge protocols for this network pair
      const availableBridges = this.getAvailableBridges(sourceNetwork, targetNetwork);

      if (availableBridges.length === 0) {
        // Fallback to basic calculation
        return {
          protocol: 'generic',
          baseFee: 10,
          variableFeeRate: 0.1,
          variableFee: amount * 0.001,
          congestionMultiplier: 1,
          totalFee: 10 + (amount * 0.001),
          estimatedTime: 15,
          confidence: 50,
          alternatives: []
        };
      }

      // Calculate fees for all available bridges
      const bridgeCalculations = await Promise.all(
        availableBridges.map(protocol => this.calculateBridgeProtocolFee(protocol, amount, sourceNetwork, targetNetwork))
      );

      // Select optimal bridge (lowest fee with acceptable reliability)
      const optimalBridge = this.selectOptimalBridge(bridgeCalculations);

      // Generate alternatives
      const alternatives = bridgeCalculations
        .filter(calc => calc.protocol !== optimalBridge.protocol)
        .map(calc => ({
          protocol: calc.protocol,
          fee: calc.totalFee,
          time: calc.estimatedTime,
          reliability: this.bridgeProtocols.get(calc.protocol)?.reliability || 80
        }))
        .sort((a, b) => a.fee - b.fee)
        .slice(0, 3); // Top 3 alternatives

      return {
        ...optimalBridge,
        alternatives
      };

    } catch (error) {
      logger.error(`Enhanced bridge fee calculation failed for ${sourceNetwork} -> ${targetNetwork}:`, error);

      // Fallback calculation
      return {
        protocol: 'fallback',
        baseFee: 15,
        variableFeeRate: 0.15,
        variableFee: amount * 0.0015,
        congestionMultiplier: 1.2,
        totalFee: 15 + (amount * 0.0015 * 1.2),
        estimatedTime: 20,
        confidence: 30,
        alternatives: []
      };
    }
  }

  /**
   * Get available bridge protocols for a network pair
   */
  private getAvailableBridges(sourceNetwork: string, targetNetwork: string): string[] {
    const availableBridges: string[] = [];

    for (const [protocolId, config] of this.bridgeProtocols) {
      if (config.supportedNetworks.includes(sourceNetwork) &&
          config.supportedNetworks.includes(targetNetwork)) {
        availableBridges.push(protocolId);
      }
    }

    return availableBridges;
  }

  /**
   * Calculate fee for a specific bridge protocol
   */
  private async calculateBridgeProtocolFee(
    protocolId: string,
    amount: number,
    sourceNetwork: string,
    targetNetwork: string
  ): Promise<BridgeCalculation> {

    const protocol = this.bridgeProtocols.get(protocolId);
    if (!protocol) {
      throw new Error(`Unknown bridge protocol: ${protocolId}`);
    }

    // Calculate base and variable fees
    const baseFee = protocol.baseFeeUSD;
    const variableFee = amount * (protocol.variableFeeRate / 100);

    // Get network congestion multiplier
    const congestionMultiplier = await this.getNetworkCongestionMultiplier(
      sourceNetwork,
      targetNetwork,
      protocol.congestionSensitivity
    );

    // Calculate total fee with congestion adjustment
    const totalFee = Math.max(
      protocol.minFee,
      Math.min(
        protocol.maxFee,
        (baseFee + variableFee) * congestionMultiplier
      )
    );

    // Adjust processing time based on congestion
    const estimatedTime = Math.round(protocol.avgProcessingTime * congestionMultiplier);

    // Calculate confidence based on protocol reliability and network conditions
    const confidence = Math.round(protocol.reliability * (2 - congestionMultiplier));

    return {
      protocol: protocolId,
      baseFee,
      variableFeeRate: protocol.variableFeeRate,
      variableFee,
      congestionMultiplier,
      totalFee,
      estimatedTime,
      confidence,
      alternatives: []
    };
  }

  /**
   * Get network congestion multiplier for bridge fee calculation
   */
  private async getNetworkCongestionMultiplier(
    sourceNetwork: string,
    targetNetwork: string,
    sensitivity: number
  ): Promise<number> {

    try {
      // Get congestion data for both networks
      const sourceCongestion = await this.getNetworkCongestion(sourceNetwork);
      const targetCongestion = await this.getNetworkCongestion(targetNetwork);

      // Use the higher congestion level
      const maxCongestion = Math.max(sourceCongestion, targetCongestion);

      // Apply sensitivity factor
      const congestionImpact = (maxCongestion / 100) * sensitivity;

      // Return multiplier (1.0 = no impact, higher = more expensive)
      return 1 + congestionImpact;

    } catch (error) {
      logger.warn('Failed to get network congestion, using default multiplier:', error);
      return 1.1; // Default 10% increase
    }
  }

  /**
   * Get network congestion level (0-100)
   */
  private async getNetworkCongestion(network: string): Promise<number> {
    try {
      // This would integrate with actual network monitoring
      // For now, return simulated congestion based on network characteristics
      const congestionLevels: { [key: string]: number } = {
        'ethereum': 70, // High congestion
        'bsc': 45,      // Medium congestion
        'polygon': 35,  // Low-medium congestion
        'arbitrum': 25, // Low congestion
        'optimism': 30, // Low congestion
        'avalanche': 40, // Medium congestion
        'fantom': 20,   // Low congestion - Enhanced for Fantom
        'sui': 15,      // Very low congestion - Enhanced for Sui
        'solana': 50,   // Medium-high congestion
        'base': 28      // Low congestion
      };

      return congestionLevels[network] || 50;

    } catch (error) {
      logger.warn(`Failed to get congestion for ${network}:`, error);
      return 50; // Default moderate congestion
    }
  }

  /**
   * Select optimal bridge based on fee, reliability, and time
   */
  private selectOptimalBridge(calculations: BridgeCalculation[]): BridgeCalculation {
    if (calculations.length === 0) {
      throw new Error('No bridge calculations available');
    }

    if (calculations.length === 1) {
      return calculations[0];
    }

    // Score each bridge based on multiple factors
    const scoredBridges = calculations.map(calc => {
      const protocol = this.bridgeProtocols.get(calc.protocol);
      if (!protocol) return { ...calc, score: 0 };

      // Scoring factors (lower is better for fees/time, higher is better for reliability)
      const feeScore = 100 - Math.min(100, (calc.totalFee / 100) * 10); // Normalize fee impact
      const timeScore = 100 - Math.min(100, (calc.estimatedTime / 30) * 100); // Normalize time impact
      const reliabilityScore = protocol.reliability;

      // Weighted score (fee: 40%, reliability: 35%, time: 25%)
      const score = (feeScore * 0.4) + (reliabilityScore * 0.35) + (timeScore * 0.25);

      return { ...calc, score };
    });

    // Return bridge with highest score
    return scoredBridges.reduce((best, current) =>
      current.score > best.score ? current : best
    );
  }

  /**
   * Get bridge protocol information for a specific network pair
   */
  public getBridgeProtocolInfo(sourceNetwork: string, targetNetwork: string): BridgeProtocolConfig[] {
    const availableBridges = this.getAvailableBridges(sourceNetwork, targetNetwork);
    return availableBridges
      .map(protocolId => this.bridgeProtocols.get(protocolId))
      .filter((protocol): protocol is BridgeProtocolConfig => protocol !== undefined);
  }

  /**
   * Estimate bridge fee for a specific amount and network pair
   */
  public async estimateBridgeFee(
    sourceNetwork: string,
    targetNetwork: string,
    amount: number
  ): Promise<BridgeCalculation> {
    return await this.calculateEnhancedBridgeFee(sourceNetwork, targetNetwork, amount);
  }
}
