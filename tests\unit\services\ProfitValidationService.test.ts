/**
 * Unit Tests for Profit Validation Service
 * 
 * Tests profit validation functionality including:
 * - 100% positive profit guarantee validation
 * - Gas fee calculation and validation
 * - Slippage impact assessment
 * - Multi-chain profit validation
 * - Risk assessment and safety margins
 */

import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { ProfitValidationService } from '../../../backend/services/ProfitValidationService.js';

// Mock dependencies
jest.mock('../../../backend/utils/logger.js', () => ({
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

jest.mock('../../../backend/services/EnhancedCacheService.js', () => ({
  enhancedCacheService: {
    get: jest.fn(),
    set: jest.fn()
  }
}));

jest.mock('../../../backend/services/EnhancedDatabaseConnectionManager.js', () => ({
  enhancedDatabaseManager: {
    getRedisClient: jest.fn(() => Promise.resolve({
      get: jest.fn(),
      set: jest.fn(),
      hget: jest.fn(),
      hset: jest.fn()
    }))
  }
}));

describe('ProfitValidationService', () => {
  let service: ProfitValidationService;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new ProfitValidationService();
  });

  afterEach(async () => {
    if (service) {
      await service.shutdown();
    }
  });

  describe('Profit Validation', () => {
    const mockOpportunity = {
      id: 'test-opp-1',
      type: 'intra-chain' as const,
      assets: ['ETH', 'USDC'],
      exchanges: ['Uniswap', 'SushiSwap'],
      potentialProfit: 100,
      profitPercentage: 2.5,
      timestamp: Date.now(),
      network: 'ethereum',
      route: {
        path: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        amounts: [1, 2000]
      },
      estimatedGas: 150000,
      slippage: 0.5,
      confidence: 85
    };

    it('should validate profitable opportunities', async () => {
      const result = await service.validateProfit(mockOpportunity);
      
      expect(result).toBeDefined();
      expect(result.isValid).toBe(true);
      expect(result.netProfit).toBeGreaterThan(0);
      expect(result.profitMargin).toBeGreaterThan(0);
    });

    it('should reject opportunities with insufficient profit', async () => {
      const unprofitableOpportunity = {
        ...mockOpportunity,
        potentialProfit: 1, // Very low profit
        estimatedGas: 200000 // High gas cost
      };

      const result = await service.validateProfit(unprofitableOpportunity);
      
      expect(result.isValid).toBe(false);
      expect(result.rejectionReason).toContain('insufficient profit');
    });

    it('should calculate gas fees correctly', async () => {
      const result = await service.validateProfit(mockOpportunity);
      
      expect(result.gasFees).toBeDefined();
      expect(result.gasFees).toBeGreaterThan(0);
      expect(result.totalCosts).toBeGreaterThanOrEqual(result.gasFees);
    });

    it('should account for slippage in profit calculation', async () => {
      const highSlippageOpportunity = {
        ...mockOpportunity,
        slippage: 5.0 // High slippage
      };

      const result = await service.validateProfit(highSlippageOpportunity);
      
      expect(result.slippageImpact).toBeGreaterThan(0);
      expect(result.netProfit).toBeLessThan(mockOpportunity.potentialProfit);
    });

    it('should apply safety margins', async () => {
      const result = await service.validateProfit(mockOpportunity);
      
      expect(result.safetyMargin).toBeDefined();
      expect(result.safetyMargin).toBeGreaterThan(0);
      expect(result.netProfit).toBeLessThan(mockOpportunity.potentialProfit);
    });
  });

  describe('Multi-Chain Validation', () => {
    const mockCrossChainOpportunity = {
      id: 'test-cross-chain-1',
      type: 'cross-chain' as const,
      assets: ['ETH', 'WETH'],
      exchanges: ['Uniswap', 'PancakeSwap'],
      potentialProfit: 200,
      profitPercentage: 3.0,
      timestamp: Date.now(),
      sourceNetwork: 'ethereum',
      targetNetwork: 'bsc',
      route: {
        path: ['ETH', 'WETH'],
        exchanges: ['Uniswap', 'PancakeSwap'],
        amounts: [1, 1]
      },
      estimatedGas: 300000,
      slippage: 1.0,
      confidence: 80,
      bridgeFees: 50,
      bridgeTime: 600000 // 10 minutes
    };

    it('should validate cross-chain opportunities', async () => {
      const result = await service.validateCrossChainProfit(mockCrossChainOpportunity);
      
      expect(result).toBeDefined();
      expect(result.isValid).toBe(true);
      expect(result.bridgeFees).toBe(50);
      expect(result.totalCosts).toBeGreaterThan(result.bridgeFees);
    });

    it('should reject cross-chain opportunities with high bridge fees', async () => {
      const highBridgeFeeOpportunity = {
        ...mockCrossChainOpportunity,
        bridgeFees: 180 // High bridge fees
      };

      const result = await service.validateCrossChainProfit(highBridgeFeeOpportunity);
      
      expect(result.isValid).toBe(false);
      expect(result.rejectionReason).toContain('bridge fees');
    });

    it('should account for bridge time in validation', async () => {
      const slowBridgeOpportunity = {
        ...mockCrossChainOpportunity,
        bridgeTime: 3600000 // 1 hour
      };

      const result = await service.validateCrossChainProfit(slowBridgeOpportunity);
      
      expect(result.timeRisk).toBeGreaterThan(0);
      expect(result.adjustedProfit).toBeLessThan(result.grossProfit);
    });
  });

  describe('Risk Assessment', () => {
    const mockOpportunity = {
      id: 'test-risk-1',
      type: 'intra-chain' as const,
      assets: ['ETH', 'USDC'],
      exchanges: ['Uniswap', 'SushiSwap'],
      potentialProfit: 100,
      profitPercentage: 2.5,
      timestamp: Date.now(),
      network: 'ethereum',
      route: {
        path: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        amounts: [1, 2000]
      },
      estimatedGas: 150000,
      slippage: 0.5,
      confidence: 85
    };

    it('should assess liquidity risk', async () => {
      const result = await service.validateProfit(mockOpportunity);
      
      expect(result.riskAssessment).toBeDefined();
      expect(result.riskAssessment.liquidityRisk).toBeDefined();
      expect(result.riskAssessment.liquidityRisk).toBeGreaterThanOrEqual(0);
      expect(result.riskAssessment.liquidityRisk).toBeLessThanOrEqual(100);
    });

    it('should assess market volatility risk', async () => {
      const result = await service.validateProfit(mockOpportunity);
      
      expect(result.riskAssessment.volatilityRisk).toBeDefined();
      expect(result.riskAssessment.volatilityRisk).toBeGreaterThanOrEqual(0);
      expect(result.riskAssessment.volatilityRisk).toBeLessThanOrEqual(100);
    });

    it('should assess execution risk', async () => {
      const result = await service.validateProfit(mockOpportunity);
      
      expect(result.riskAssessment.executionRisk).toBeDefined();
      expect(result.riskAssessment.executionRisk).toBeGreaterThanOrEqual(0);
      expect(result.riskAssessment.executionRisk).toBeLessThanOrEqual(100);
    });

    it('should calculate overall risk score', async () => {
      const result = await service.validateProfit(mockOpportunity);
      
      expect(result.riskAssessment.overallRisk).toBeDefined();
      expect(result.riskAssessment.overallRisk).toBeGreaterThanOrEqual(0);
      expect(result.riskAssessment.overallRisk).toBeLessThanOrEqual(100);
    });

    it('should reject high-risk opportunities', async () => {
      const highRiskOpportunity = {
        ...mockOpportunity,
        confidence: 30, // Low confidence
        slippage: 10.0, // High slippage
        estimatedGas: 500000 // Very high gas
      };

      const result = await service.validateProfit(highRiskOpportunity);
      
      expect(result.isValid).toBe(false);
      expect(result.rejectionReason).toContain('risk');
    });
  });

  describe('Performance Metrics', () => {
    it('should track validation performance', async () => {
      const mockOpportunity = {
        id: 'test-perf-1',
        type: 'intra-chain' as const,
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 100,
        profitPercentage: 2.5,
        timestamp: Date.now(),
        network: 'ethereum',
        route: {
          path: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          amounts: [1, 2000]
        },
        estimatedGas: 150000,
        slippage: 0.5,
        confidence: 85
      };

      const startTime = Date.now();
      await service.validateProfit(mockOpportunity);
      const endTime = Date.now();

      const metrics = service.getMetrics();
      expect(metrics.totalValidations).toBeGreaterThan(0);
      expect(metrics.averageValidationTime).toBeGreaterThan(0);
      expect(metrics.averageValidationTime).toBeLessThan(3000); // Should be under 3 seconds
    });

    it('should track validation success rate', async () => {
      const profitableOpportunity = {
        id: 'test-success-1',
        type: 'intra-chain' as const,
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 100,
        profitPercentage: 2.5,
        timestamp: Date.now(),
        network: 'ethereum',
        route: {
          path: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          amounts: [1, 2000]
        },
        estimatedGas: 150000,
        slippage: 0.5,
        confidence: 85
      };

      await service.validateProfit(profitableOpportunity);

      const metrics = service.getMetrics();
      expect(metrics.successfulValidations).toBeGreaterThan(0);
      expect(metrics.successRate).toBeGreaterThan(0);
      expect(metrics.successRate).toBeLessThanOrEqual(100);
    });
  });

  describe('Health Monitoring', () => {
    it('should report healthy status', () => {
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should return service metrics', () => {
      const metrics = service.getMetrics();
      
      expect(metrics).toHaveProperty('totalValidations');
      expect(metrics).toHaveProperty('successfulValidations');
      expect(metrics).toHaveProperty('failedValidations');
      expect(metrics).toHaveProperty('averageValidationTime');
      expect(metrics).toHaveProperty('successRate');
    });
  });

  describe('Configuration', () => {
    it('should use configurable profit thresholds', async () => {
      const lowProfitOpportunity = {
        id: 'test-config-1',
        type: 'intra-chain' as const,
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 5, // Low profit
        profitPercentage: 0.1,
        timestamp: Date.now(),
        network: 'ethereum',
        route: {
          path: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          amounts: [1, 2000]
        },
        estimatedGas: 150000,
        slippage: 0.5,
        confidence: 85
      };

      const result = await service.validateProfit(lowProfitOpportunity);
      
      // Should be rejected due to low profit
      expect(result.isValid).toBe(false);
    });

    it('should use configurable safety margins', async () => {
      const opportunity = {
        id: 'test-margin-1',
        type: 'intra-chain' as const,
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 100,
        profitPercentage: 2.5,
        timestamp: Date.now(),
        network: 'ethereum',
        route: {
          path: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          amounts: [1, 2000]
        },
        estimatedGas: 150000,
        slippage: 0.5,
        confidence: 85
      };

      const result = await service.validateProfit(opportunity);
      
      expect(result.safetyMargin).toBeGreaterThan(0);
      expect(result.safetyMargin).toBeLessThan(opportunity.potentialProfit);
    });
  });
});
