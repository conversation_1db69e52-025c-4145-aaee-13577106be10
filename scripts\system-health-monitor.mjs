#!/usr/bin/env node

/**
 * System Health Monitor for MEV Arbitrage Bot
 * ==========================================
 * 
 * Comprehensive health monitoring with:
 * 1. Real-time system health monitoring
 * 2. Performance metrics collection and analysis
 * 3. Automated alerting and notification system
 * 4. Health check scheduling and management
 * 5. System recovery and rollback capabilities
 * 6. Detailed logging and reporting
 * 7. Integration with external monitoring services
 */

import { EventEmitter } from 'events';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { writeFileSync, existsSync, mkdirSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Health monitoring configuration
const HEALTH_CONFIG = {
  intervals: {
    critical: 10000,    // 10 seconds
    important: 30000,   // 30 seconds
    standard: 60000,    // 1 minute
    detailed: 300000    // 5 minutes
  },
  
  thresholds: {
    memory: {
      warning: 400 * 1024 * 1024,  // 400MB
      critical: 500 * 1024 * 1024  // 500MB
    },
    cpu: {
      warning: 70,  // 70%
      critical: 90  // 90%
    },
    latency: {
      warning: 500,   // 500ms
      critical: 1000  // 1000ms
    },
    uptime: {
      minimum: 99  // 99%
    }
  },
  
  services: {
    backend: { port: 3001, endpoint: '/api/health', critical: true },
    database: { endpoint: '/api/health/database', critical: true },
    cache: { endpoint: '/api/health/cache', critical: true },
    multichain: { endpoint: '/api/health/multichain', critical: false },
    opportunities: { endpoint: '/api/health/opportunities', critical: true },
    execution: { endpoint: '/api/health/execution', critical: true }
  },
  
  databases: {
    redis: { port: 6379, critical: true },
    postgres: { port: 5432, critical: true },
    influxdb: { port: 8086, critical: true },
    supabase: { critical: true }
  }
};

class SystemHealthMonitor extends EventEmitter {
  constructor() {
    super();
    
    this.isRunning = false;
    this.startTime = Date.now();
    this.healthHistory = [];
    this.alertHistory = [];
    this.intervals = new Map();
    
    this.currentHealth = {
      overall: 'unknown',
      services: {},
      databases: {},
      performance: {},
      uptime: 0,
      lastCheck: null,
      alerts: []
    };
    
    this.setupLogging();
    this.setupEventHandlers();
  }
  
  setupLogging() {
    const logsDir = join(rootDir, 'logs');
    if (!existsSync(logsDir)) {
      mkdirSync(logsDir, { recursive: true });
    }
    
    this.logFile = join(logsDir, 'health-monitor.log');
  }
  
  setupEventHandlers() {
    this.on('healthCheck', (health) => this.handleHealthCheck(health));
    this.on('alert', (alert) => this.handleAlert(alert));
    this.on('recovery', (recovery) => this.handleRecovery(recovery));
  }
  
  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
    
    console.log(logEntry.trim());
    
    try {
      writeFileSync(this.logFile, logEntry, { flag: 'a' });
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }
  
  async startMonitoring() {
    if (this.isRunning) {
      this.log('Health monitor is already running', 'warn');
      return;
    }
    
    this.isRunning = true;
    this.log('🏥 Starting System Health Monitor...', 'info');
    
    // Setup monitoring intervals
    this.setupMonitoringIntervals();
    
    // Perform initial health check
    await this.performComprehensiveHealthCheck();
    
    this.log('✅ System Health Monitor started successfully', 'info');
    this.emit('monitoringStarted');
  }
  
  setupMonitoringIntervals() {
    // Critical health checks (every 10 seconds)
    this.intervals.set('critical', setInterval(async () => {
      await this.performCriticalHealthCheck();
    }, HEALTH_CONFIG.intervals.critical));
    
    // Important health checks (every 30 seconds)
    this.intervals.set('important', setInterval(async () => {
      await this.performImportantHealthCheck();
    }, HEALTH_CONFIG.intervals.important));
    
    // Standard health checks (every minute)
    this.intervals.set('standard', setInterval(async () => {
      await this.performStandardHealthCheck();
    }, HEALTH_CONFIG.intervals.standard));
    
    // Detailed health checks (every 5 minutes)
    this.intervals.set('detailed', setInterval(async () => {
      await this.performDetailedHealthCheck();
    }, HEALTH_CONFIG.intervals.detailed));
  }
  
  async performCriticalHealthCheck() {
    const health = {
      timestamp: Date.now(),
      type: 'critical',
      services: {},
      alerts: []
    };
    
    // Check critical services
    for (const [serviceName, config] of Object.entries(HEALTH_CONFIG.services)) {
      if (config.critical) {
        health.services[serviceName] = await this.checkServiceHealth(serviceName, config);
      }
    }
    
    // Check memory usage
    const memoryUsage = process.memoryUsage();
    health.memory = {
      heapUsed: memoryUsage.heapUsed,
      heapTotal: memoryUsage.heapTotal,
      status: this.getMemoryStatus(memoryUsage.heapUsed)
    };
    
    // Generate alerts for critical issues
    this.generateCriticalAlerts(health);
    
    this.currentHealth.services = { ...this.currentHealth.services, ...health.services };
    this.currentHealth.performance.memory = health.memory;
    this.currentHealth.lastCheck = health.timestamp;
    
    this.emit('healthCheck', health);
  }
  
  async performImportantHealthCheck() {
    const health = {
      timestamp: Date.now(),
      type: 'important',
      databases: {},
      alerts: []
    };
    
    // Check database health
    for (const [dbName, config] of Object.entries(HEALTH_CONFIG.databases)) {
      if (config.critical) {
        health.databases[dbName] = await this.checkDatabaseHealth(dbName, config);
      }
    }
    
    this.currentHealth.databases = { ...this.currentHealth.databases, ...health.databases };
    this.emit('healthCheck', health);
  }
  
  async performStandardHealthCheck() {
    const health = {
      timestamp: Date.now(),
      type: 'standard',
      performance: {},
      alerts: []
    };
    
    // CPU usage check
    const cpuUsage = process.cpuUsage();
    health.performance.cpu = {
      user: cpuUsage.user,
      system: cpuUsage.system,
      status: this.getCPUStatus(cpuUsage)
    };
    
    // Uptime calculation
    const uptime = Date.now() - this.startTime;
    health.performance.uptime = {
      milliseconds: uptime,
      seconds: Math.floor(uptime / 1000),
      minutes: Math.floor(uptime / 60000),
      hours: Math.floor(uptime / 3600000)
    };
    
    this.currentHealth.performance = { ...this.currentHealth.performance, ...health.performance };
    this.currentHealth.uptime = uptime;
    
    this.emit('healthCheck', health);
  }
  
  async performDetailedHealthCheck() {
    const health = {
      timestamp: Date.now(),
      type: 'detailed',
      comprehensive: {},
      alerts: []
    };
    
    // Comprehensive system analysis
    health.comprehensive = await this.performComprehensiveAnalysis();
    
    // Update health history
    this.updateHealthHistory(health);
    
    // Generate detailed report
    this.generateDetailedReport(health);
    
    this.emit('healthCheck', health);
  }
  
  async performComprehensiveHealthCheck() {
    this.log('🔍 Performing comprehensive health check...', 'info');
    
    const health = {
      timestamp: Date.now(),
      type: 'comprehensive',
      services: {},
      databases: {},
      performance: {},
      alerts: []
    };
    
    // Check all services
    for (const [serviceName, config] of Object.entries(HEALTH_CONFIG.services)) {
      health.services[serviceName] = await this.checkServiceHealth(serviceName, config);
    }
    
    // Check all databases
    for (const [dbName, config] of Object.entries(HEALTH_CONFIG.databases)) {
      health.databases[dbName] = await this.checkDatabaseHealth(dbName, config);
    }
    
    // Performance metrics
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    health.performance = {
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        status: this.getMemoryStatus(memoryUsage.heapUsed)
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        status: this.getCPUStatus(cpuUsage)
      },
      uptime: Date.now() - this.startTime
    };
    
    // Determine overall health
    health.overall = this.calculateOverallHealth(health);
    
    this.currentHealth = {
      ...health,
      lastCheck: health.timestamp
    };
    
    this.log(`Overall system health: ${health.overall}`, 
             health.overall === 'healthy' ? 'info' : 'warn');
    
    this.emit('healthCheck', health);
    return health;
  }
  
  async checkServiceHealth(serviceName, config) {
    const result = {
      status: 'unknown',
      latency: null,
      error: null,
      timestamp: Date.now()
    };
    
    try {
      const startTime = Date.now();
      const response = await fetch(`http://localhost:${config.port || 3001}${config.endpoint}`);
      result.latency = Date.now() - startTime;
      
      if (response.ok) {
        const data = await response.json();
        result.status = data.status === 'healthy' ? 'healthy' : 'degraded';
        result.data = data;
      } else {
        result.status = 'unhealthy';
        result.error = `HTTP ${response.status}: ${response.statusText}`;
      }
      
    } catch (error) {
      result.status = 'unhealthy';
      result.error = error.message;
    }
    
    return result;
  }
  
  async checkDatabaseHealth(dbName, config) {
    const result = {
      status: 'unknown',
      latency: null,
      error: null,
      timestamp: Date.now()
    };
    
    try {
      const startTime = Date.now();
      
      switch (dbName) {
        case 'redis':
          await this.checkRedisHealth(result);
          break;
        case 'postgres':
          await this.checkPostgresHealth(result);
          break;
        case 'influxdb':
          await this.checkInfluxDBHealth(result);
          break;
        case 'supabase':
          await this.checkSupabaseHealth(result);
          break;
      }
      
      result.latency = Date.now() - startTime;
      
    } catch (error) {
      result.status = 'unhealthy';
      result.error = error.message;
    }
    
    return result;
  }
  
  async checkRedisHealth(result) {
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);
    
    await execAsync('redis-cli -h localhost -p 6379 ping');
    result.status = 'healthy';
  }
  
  async checkPostgresHealth(result) {
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);
    
    await execAsync('pg_isready -h localhost -p 5432 -U mev_user');
    result.status = 'healthy';
  }
  
  async checkInfluxDBHealth(result) {
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);
    
    await execAsync('influx ping --host http://localhost:8086');
    result.status = 'healthy';
  }
  
  async checkSupabaseHealth(result) {
    if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('Supabase configuration missing');
    }
    
    const response = await fetch(`${process.env.SUPABASE_URL}/rest/v1/`, {
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY
      }
    });
    
    if (response.ok) {
      result.status = 'healthy';
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }
  
  getMemoryStatus(heapUsed) {
    if (heapUsed >= HEALTH_CONFIG.thresholds.memory.critical) {
      return 'critical';
    } else if (heapUsed >= HEALTH_CONFIG.thresholds.memory.warning) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }
  
  getCPUStatus(cpuUsage) {
    // Simplified CPU usage calculation
    const totalUsage = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds
    const percentage = (totalUsage / (Date.now() - this.startTime)) * 100;
    
    if (percentage >= HEALTH_CONFIG.thresholds.cpu.critical) {
      return 'critical';
    } else if (percentage >= HEALTH_CONFIG.thresholds.cpu.warning) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }
  
  calculateOverallHealth(health) {
    let healthyCount = 0;
    let totalCount = 0;
    
    // Count service health
    for (const service of Object.values(health.services)) {
      totalCount++;
      if (service.status === 'healthy') healthyCount++;
    }
    
    // Count database health
    for (const db of Object.values(health.databases)) {
      totalCount++;
      if (db.status === 'healthy') healthyCount++;
    }
    
    // Check performance metrics
    const memoryStatus = health.performance.memory?.status || 'unknown';
    const cpuStatus = health.performance.cpu?.status || 'unknown';
    
    if (memoryStatus === 'critical' || cpuStatus === 'critical') {
      return 'critical';
    }
    
    const healthPercentage = (healthyCount / totalCount) * 100;
    
    if (healthPercentage >= 90) {
      return 'healthy';
    } else if (healthPercentage >= 70) {
      return 'degraded';
    } else {
      return 'unhealthy';
    }
  }
  
  generateCriticalAlerts(health) {
    const alerts = [];
    
    // Check for unhealthy critical services
    for (const [serviceName, serviceHealth] of Object.entries(health.services)) {
      if (serviceHealth.status === 'unhealthy') {
        alerts.push({
          type: 'critical',
          service: serviceName,
          message: `Critical service ${serviceName} is unhealthy: ${serviceHealth.error}`,
          timestamp: Date.now()
        });
      }
    }
    
    // Check memory usage
    if (health.memory?.status === 'critical') {
      alerts.push({
        type: 'critical',
        service: 'system',
        message: `Critical memory usage: ${Math.round(health.memory.heapUsed / 1024 / 1024)}MB`,
        timestamp: Date.now()
      });
    }
    
    // Emit alerts
    alerts.forEach(alert => this.emit('alert', alert));
    
    health.alerts = alerts;
  }
  
  async performComprehensiveAnalysis() {
    return {
      systemLoad: process.loadavg(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      uptime: process.uptime(),
      version: process.version,
      platform: process.platform,
      arch: process.arch
    };
  }
  
  updateHealthHistory(health) {
    this.healthHistory.push({
      timestamp: health.timestamp,
      overall: this.currentHealth.overall,
      summary: {
        services: Object.keys(this.currentHealth.services).length,
        databases: Object.keys(this.currentHealth.databases).length,
        alerts: this.currentHealth.alerts.length
      }
    });
    
    // Keep only last 100 entries
    if (this.healthHistory.length > 100) {
      this.healthHistory = this.healthHistory.slice(-100);
    }
  }
  
  generateDetailedReport(health) {
    const report = {
      timestamp: new Date().toISOString(),
      overall: this.currentHealth.overall,
      uptime: Math.round((Date.now() - this.startTime) / 1000),
      services: this.currentHealth.services,
      databases: this.currentHealth.databases,
      performance: this.currentHealth.performance,
      alerts: this.currentHealth.alerts,
      history: this.healthHistory.slice(-10) // Last 10 entries
    };
    
    const reportFile = join(rootDir, 'logs', `health-report-${Date.now()}.json`);
    
    try {
      writeFileSync(reportFile, JSON.stringify(report, null, 2));
      this.log(`Detailed health report saved: ${reportFile}`, 'info');
    } catch (error) {
      this.log(`Failed to save health report: ${error.message}`, 'error');
    }
  }
  
  handleHealthCheck(health) {
    // Log significant health changes
    if (health.type === 'critical' && health.alerts.length > 0) {
      this.log(`Critical health check: ${health.alerts.length} alert(s)`, 'warn');
    }
  }
  
  handleAlert(alert) {
    this.alertHistory.push(alert);
    this.currentHealth.alerts.push(alert);
    
    this.log(`🚨 ALERT [${alert.type.toUpperCase()}]: ${alert.message}`, 'error');
    
    // Keep only last 50 alerts
    if (this.alertHistory.length > 50) {
      this.alertHistory = this.alertHistory.slice(-50);
    }
    
    if (this.currentHealth.alerts.length > 10) {
      this.currentHealth.alerts = this.currentHealth.alerts.slice(-10);
    }
  }
  
  handleRecovery(recovery) {
    this.log(`✅ RECOVERY: ${recovery.message}`, 'info');
  }
  
  getHealthStatus() {
    return {
      ...this.currentHealth,
      isMonitoring: this.isRunning,
      startTime: this.startTime,
      alertHistory: this.alertHistory.slice(-10),
      healthHistory: this.healthHistory.slice(-10)
    };
  }
  
  async stopMonitoring() {
    if (!this.isRunning) {
      return;
    }
    
    this.log('🛑 Stopping System Health Monitor...', 'info');
    
    // Clear all intervals
    for (const [name, interval] of this.intervals) {
      clearInterval(interval);
    }
    
    this.intervals.clear();
    this.isRunning = false;
    
    this.log('✅ System Health Monitor stopped', 'info');
    this.emit('monitoringStopped');
  }
}

// Main execution for standalone usage
async function main() {
  const monitor = new SystemHealthMonitor();
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down health monitor...');
    await monitor.stopMonitoring();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    await monitor.stopMonitoring();
    process.exit(0);
  });
  
  await monitor.startMonitoring();
  
  // Keep the process running
  console.log('Health monitor is running. Press Ctrl+C to stop.');
}

// Handle direct execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('💥 Health monitor failed:', error);
    process.exit(1);
  });
}

export default SystemHealthMonitor;
