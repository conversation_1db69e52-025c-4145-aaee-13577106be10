/**
 * Redis Data Structures Configuration for MEV Arbitrage Bot
 * 
 * Defines optimized Redis data structures for:
 * - Current token prices with 30-second TTL
 * - Active opportunities as sorted sets with 60-second TTL
 * - Execution queue with priority scoring and 120-second TTL
 * - System health with 15-second TTL
 * - Multi-tier caching with appropriate TTLs
 */

export interface RedisKeyConfig {
  pattern: string;
  ttl: number;
  dataType: 'string' | 'hash' | 'list' | 'set' | 'zset';
  description: string;
  compression?: boolean;
  priority?: 'low' | 'medium' | 'high';
}

export interface CacheConfig {
  keyPrefix: string;
  defaultTTL: number;
  maxMemory: string;
  evictionPolicy: 'allkeys-lru' | 'volatile-lru' | 'allkeys-lfu' | 'volatile-lfu';
  compressionThreshold: number;
}

/**
 * Redis Configuration Settings
 */
export const REDIS_CONFIG: CacheConfig = {
  keyPrefix: 'mev_bot:',
  defaultTTL: 300, // 5 minutes default
  maxMemory: '500mb',
  evictionPolicy: 'allkeys-lru',
  compressionThreshold: 1024 // Compress values larger than 1KB
};

/**
 * Current Token Prices
 * Structure: Hash with token_symbol:network as field, price data as value
 * TTL: 30 seconds for real-time price updates
 */
export const TOKEN_PRICES_CONFIG: RedisKeyConfig = {
  pattern: 'prices:current',
  ttl: 30,
  dataType: 'hash',
  description: 'Current token prices by network and symbol',
  compression: false,
  priority: 'high'
};

/**
 * Active Opportunities
 * Structure: Sorted Set with opportunity_id as member, profit score as score
 * TTL: 60 seconds for opportunity lifecycle management
 */
export const ACTIVE_OPPORTUNITIES_CONFIG: RedisKeyConfig = {
  pattern: 'opportunities:active',
  ttl: 60,
  dataType: 'zset',
  description: 'Active arbitrage opportunities ranked by profit potential',
  compression: false,
  priority: 'high'
};

/**
 * Execution Queue
 * Structure: Sorted Set with trade_id as member, priority score as score
 * TTL: 120 seconds for trade execution management
 */
export const EXECUTION_QUEUE_CONFIG: RedisKeyConfig = {
  pattern: 'queue:execution',
  ttl: 120,
  dataType: 'zset',
  description: 'Trade execution queue with priority scoring',
  compression: false,
  priority: 'high'
};

/**
 * System Health Status
 * Structure: Hash with service_name as field, health data as value
 * TTL: 15 seconds for real-time health monitoring
 */
export const SYSTEM_HEALTH_CONFIG: RedisKeyConfig = {
  pattern: 'system:health',
  ttl: 15,
  dataType: 'hash',
  description: 'Real-time system health status for all services',
  compression: false,
  priority: 'medium'
};

/**
 * Price History Cache
 * Structure: List with recent price points
 * TTL: 300 seconds (5 minutes) for short-term price history
 */
export const PRICE_HISTORY_CONFIG: RedisKeyConfig = {
  pattern: 'prices:history:{symbol}:{network}',
  ttl: 300,
  dataType: 'list',
  description: 'Short-term price history for technical analysis',
  compression: true,
  priority: 'medium'
};

/**
 * Validation Results Cache
 * Structure: Hash with validation_id as field, result as value
 * TTL: 180 seconds for validation result caching
 */
export const VALIDATION_CACHE_CONFIG: RedisKeyConfig = {
  pattern: 'validation:results',
  ttl: 180,
  dataType: 'hash',
  description: 'Cached validation results to avoid duplicate validations',
  compression: true,
  priority: 'medium'
};

/**
 * Flash Loan Quotes Cache
 * Structure: Hash with provider:asset as field, quote data as value
 * TTL: 30 seconds for flash loan quote caching
 */
export const FLASH_LOAN_QUOTES_CONFIG: RedisKeyConfig = {
  pattern: 'flashloan:quotes',
  ttl: 30,
  dataType: 'hash',
  description: 'Cached flash loan quotes from multiple providers',
  compression: false,
  priority: 'high'
};

/**
 * Network Status Cache
 * Structure: Hash with network as field, status data as value
 * TTL: 60 seconds for network health monitoring
 */
export const NETWORK_STATUS_CONFIG: RedisKeyConfig = {
  pattern: 'network:status',
  ttl: 60,
  dataType: 'hash',
  description: 'Network health and congestion status',
  compression: false,
  priority: 'medium'
};

/**
 * Performance Metrics Cache
 * Structure: Hash with metric_name as field, value as value
 * TTL: 30 seconds for performance monitoring
 */
export const PERFORMANCE_METRICS_CONFIG: RedisKeyConfig = {
  pattern: 'metrics:performance',
  ttl: 30,
  dataType: 'hash',
  description: 'Real-time performance metrics for monitoring',
  compression: false,
  priority: 'medium'
};

/**
 * API Response Cache
 * Structure: String with JSON response data
 * TTL: 60 seconds for API response caching
 */
export const API_CACHE_CONFIG: RedisKeyConfig = {
  pattern: 'api:cache:{endpoint}:{params_hash}',
  ttl: 60,
  dataType: 'string',
  description: 'Cached API responses to reduce external calls',
  compression: true,
  priority: 'low'
};

/**
 * Session Data Cache
 * Structure: Hash with session_id as key, user data as value
 * TTL: 3600 seconds (1 hour) for user session management
 */
export const SESSION_CACHE_CONFIG: RedisKeyConfig = {
  pattern: 'session:{session_id}',
  ttl: 3600,
  dataType: 'hash',
  description: 'User session data for frontend authentication',
  compression: true,
  priority: 'low'
};

/**
 * Rate Limiting Cache
 * Structure: String with request count
 * TTL: 60 seconds for rate limiting windows
 */
export const RATE_LIMIT_CONFIG: RedisKeyConfig = {
  pattern: 'ratelimit:{ip}:{endpoint}',
  ttl: 60,
  dataType: 'string',
  description: 'Rate limiting counters for API endpoints',
  compression: false,
  priority: 'high'
};

/**
 * All Redis key configurations
 */
export const ALL_REDIS_CONFIGS: RedisKeyConfig[] = [
  TOKEN_PRICES_CONFIG,
  ACTIVE_OPPORTUNITIES_CONFIG,
  EXECUTION_QUEUE_CONFIG,
  SYSTEM_HEALTH_CONFIG,
  PRICE_HISTORY_CONFIG,
  VALIDATION_CACHE_CONFIG,
  FLASH_LOAN_QUOTES_CONFIG,
  NETWORK_STATUS_CONFIG,
  PERFORMANCE_METRICS_CONFIG,
  API_CACHE_CONFIG,
  SESSION_CACHE_CONFIG,
  RATE_LIMIT_CONFIG
];

/**
 * Key generation utilities
 */
export class RedisKeyGenerator {
  private static prefix = REDIS_CONFIG.keyPrefix;

  static tokenPrice(symbol: string, network: string): string {
    return `${this.prefix}${TOKEN_PRICES_CONFIG.pattern}`;
  }

  static activeOpportunities(): string {
    return `${this.prefix}${ACTIVE_OPPORTUNITIES_CONFIG.pattern}`;
  }

  static executionQueue(): string {
    return `${this.prefix}${EXECUTION_QUEUE_CONFIG.pattern}`;
  }

  static systemHealth(): string {
    return `${this.prefix}${SYSTEM_HEALTH_CONFIG.pattern}`;
  }

  static priceHistory(symbol: string, network: string): string {
    return `${this.prefix}${PRICE_HISTORY_CONFIG.pattern}`
      .replace('{symbol}', symbol)
      .replace('{network}', network);
  }

  static validationResults(): string {
    return `${this.prefix}${VALIDATION_CACHE_CONFIG.pattern}`;
  }

  static flashLoanQuotes(): string {
    return `${this.prefix}${FLASH_LOAN_QUOTES_CONFIG.pattern}`;
  }

  static networkStatus(): string {
    return `${this.prefix}${NETWORK_STATUS_CONFIG.pattern}`;
  }

  static performanceMetrics(): string {
    return `${this.prefix}${PERFORMANCE_METRICS_CONFIG.pattern}`;
  }

  static apiCache(endpoint: string, paramsHash: string): string {
    return `${this.prefix}${API_CACHE_CONFIG.pattern}`
      .replace('{endpoint}', endpoint)
      .replace('{params_hash}', paramsHash);
  }

  static session(sessionId: string): string {
    return `${this.prefix}${SESSION_CACHE_CONFIG.pattern}`
      .replace('{session_id}', sessionId);
  }

  static rateLimit(ip: string, endpoint: string): string {
    return `${this.prefix}${RATE_LIMIT_CONFIG.pattern}`
      .replace('{ip}', ip)
      .replace('{endpoint}', endpoint);
  }
}

/**
 * TTL constants for easy reference
 */
export const TTL = {
  PRICE_CURRENT: TOKEN_PRICES_CONFIG.ttl,
  OPPORTUNITIES: ACTIVE_OPPORTUNITIES_CONFIG.ttl,
  EXECUTION_QUEUE: EXECUTION_QUEUE_CONFIG.ttl,
  SYSTEM_HEALTH: SYSTEM_HEALTH_CONFIG.ttl,
  PRICE_HISTORY: PRICE_HISTORY_CONFIG.ttl,
  VALIDATION: VALIDATION_CACHE_CONFIG.ttl,
  FLASH_LOANS: FLASH_LOAN_QUOTES_CONFIG.ttl,
  NETWORK_STATUS: NETWORK_STATUS_CONFIG.ttl,
  PERFORMANCE: PERFORMANCE_METRICS_CONFIG.ttl,
  API_CACHE: API_CACHE_CONFIG.ttl,
  SESSION: SESSION_CACHE_CONFIG.ttl,
  RATE_LIMIT: RATE_LIMIT_CONFIG.ttl
} as const;
