'use client';

import { TrendingUp, TrendingDown, DollarSign, Target, Zap, Brain } from 'lucide-react';
import { formatCurrency, formatPercentage, formatNumber, getProfitColor } from '@/lib/utils';
import { PerformanceMetrics, SystemHealth } from '@/types';

interface MetricsOverviewProps {
  performance?: PerformanceMetrics;
  mlStats?: any;
  systemHealth?: SystemHealth;
}

interface MetricCardProps {
  title: string;
  value: string;
  change?: number;
  changeLabel?: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  trend?: 'up' | 'down' | 'neutral';
  loading?: boolean;
}

function MetricCard({ 
  title, 
  value, 
  change, 
  changeLabel, 
  icon: Icon, 
  color, 
  trend,
  loading = false 
}: MetricCardProps) {
  if (loading) {
    return (
      <div className="metric-card animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="h-4 bg-dark-700 rounded w-1/2"></div>
          <div className="h-6 w-6 bg-dark-700 rounded"></div>
        </div>
        <div className="h-8 bg-dark-700 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-dark-700 rounded w-1/3"></div>
      </div>
    );
  }

  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUp className="w-3 h-3" />;
    if (trend === 'down') return <TrendingDown className="w-3 h-3" />;
    return null;
  };

  const getTrendColor = () => {
    if (trend === 'up') return 'text-success-400';
    if (trend === 'down') return 'text-danger-400';
    return 'text-neutral';
  };

  return (
    <div className="metric-card group hover:scale-105 transition-transform duration-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-dark-400 group-hover:text-dark-300 transition-colors">
          {title}
        </h3>
        <div className={`p-2 rounded-lg ${color} bg-opacity-20`}>
          <Icon className={`w-5 h-5 ${color}`} />
        </div>
      </div>
      
      <div className="mb-2">
        <p className="text-2xl font-bold text-dark-100 group-hover:text-white transition-colors">
          {value}
        </p>
      </div>
      
      {change !== undefined && (
        <div className={`flex items-center space-x-1 text-sm ${getTrendColor()}`}>
          {getTrendIcon()}
          <span className="font-medium">
            {change > 0 ? '+' : ''}{formatPercentage(change)}
          </span>
          {changeLabel && (
            <span className="text-dark-500">
              {changeLabel}
            </span>
          )}
        </div>
      )}
    </div>
  );
}

export function MetricsOverview({ performance, mlStats, systemHealth }: MetricsOverviewProps) {
  const isLoading = !performance && !mlStats && !systemHealth;

  // Calculate trends (mock data for now - would come from historical data)
  const profitTrend = performance?.net_profit && performance.net_profit > 0 ? 'up' : 'down';
  const winRateTrend = performance?.win_rate && performance.win_rate > 50 ? 'up' : 'neutral';
  const volumeTrend = 'up'; // Would be calculated from historical data
  const mlTrend = mlStats?.adaptation_rate > 0 ? 'up' : 'neutral';

  const metrics = [
    {
      title: 'Total Profit',
      value: formatCurrency(performance?.net_profit || performance?.total_profit || 0),
      change: 12.5, // Mock change percentage
      changeLabel: '24h',
      icon: DollarSign,
      color: getProfitColor(performance?.net_profit || 0),
      trend: profitTrend,
    },
    {
      title: 'Win Rate',
      value: formatPercentage(performance?.win_rate || 0),
      change: 2.3,
      changeLabel: '7d',
      icon: Target,
      color: 'text-primary-400',
      trend: winRateTrend,
    },
    {
      title: 'Daily Volume',
      value: formatCurrency(performance?.daily_volume || 0),
      change: 8.7,
      changeLabel: '24h',
      icon: TrendingUp,
      color: 'text-warning-400',
      trend: volumeTrend,
    },
    {
      title: 'Total Trades',
      value: formatNumber(performance?.total_trades || 0),
      change: 15.2,
      changeLabel: '24h',
      icon: Zap,
      color: 'text-purple-400',
      trend: 'up',
    },
    {
      title: 'Active Strategies',
      value: formatNumber(mlStats?.total_strategies || 0),
      change: undefined,
      icon: Brain,
      color: 'text-blue-400',
      trend: mlTrend,
    },
    {
      title: 'System Uptime',
      value: formatPercentage(99.8), // Mock uptime
      change: undefined,
      icon: TrendingUp,
      color: 'text-success-400',
      trend: 'up',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-dark-100">Performance Overview</h2>
          <p className="text-dark-400 mt-1">
            Real-time metrics and system performance indicators
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-dark-400">Live Data</span>
          </div>
          
          {systemHealth && (
            <div className={`px-3 py-1 rounded-full text-xs font-medium ${
              systemHealth.status === 'healthy' 
                ? 'bg-success-500/20 text-success-400' 
                : systemHealth.status === 'degraded'
                ? 'bg-warning-500/20 text-warning-400'
                : 'bg-danger-500/20 text-danger-400'
            }`}>
              {systemHealth.status.toUpperCase()}
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard
            key={metric.title}
            {...metric}
            loading={isLoading}
          />
        ))}
      </div>

      {/* Additional Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="glass-effect rounded-xl p-6">
          <h3 className="text-lg font-semibold text-dark-100 mb-4">Network Performance</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Average Latency</span>
              <span className="text-sm font-medium text-dark-200">
                {performance?.latency ? `${performance.latency.toFixed(0)}ms` : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Cache Hit Ratio</span>
              <span className="text-sm font-medium text-success-400">
                {performance?.cache_hit_ratio ? formatPercentage(performance.cache_hit_ratio) : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Error Rate</span>
              <span className="text-sm font-medium text-danger-400">
                {performance?.error_rate ? formatPercentage(performance.error_rate) : 'N/A'}
              </span>
            </div>
          </div>
        </div>

        <div className="glass-effect rounded-xl p-6">
          <h3 className="text-lg font-semibold text-dark-100 mb-4">Trading Performance</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Successful Trades</span>
              <span className="text-sm font-medium text-success-400">
                {formatNumber(performance?.successful_trades || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Failed Trades</span>
              <span className="text-sm font-medium text-danger-400">
                {formatNumber(performance?.failed_trades || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Average Profit</span>
              <span className="text-sm font-medium text-dark-200">
                {formatCurrency(performance?.average_profit || 0)}
              </span>
            </div>
          </div>
        </div>

        <div className="glass-effect rounded-xl p-6">
          <h3 className="text-lg font-semibold text-dark-100 mb-4">ML Performance</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Market Regime</span>
              <span className="text-sm font-medium text-primary-400">
                {mlStats?.current_market_regime || 'Normal'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Adaptation Rate</span>
              <span className="text-sm font-medium text-warning-400">
                {mlStats?.adaptation_rate ? `${mlStats.adaptation_rate.toFixed(1)}/hr` : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-dark-400">Learning Events</span>
              <span className="text-sm font-medium text-dark-200">
                {formatNumber(mlStats?.total_learning_events || 0)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
