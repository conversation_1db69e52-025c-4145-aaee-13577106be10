import { Point } from '@influxdata/influxdb-client';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { databaseManager } from './DatabaseConnectionManager.js';
import { enhancedCacheService } from './EnhancedCacheService.js';
import { enhancedInfluxDBService } from './EnhancedInfluxDBService.js';
import { RedisKeyGenerator, TTL } from '../config/redis-structures.js';
import { v4 as uuidv4 } from 'uuid';

export interface DataRoute {
  primary: 'supabase' | 'influxdb' | 'redis' | 'postgres';
  secondary?: 'supabase' | 'influxdb' | 'redis' | 'postgres';
  cache?: boolean;
  cacheTTL?: number;
  batchSize?: number;
  retentionPolicy?: string;
}

export interface DataFlowMetrics {
  totalWrites: number;
  totalReads: number;
  writeLatency: number;
  readLatency: number;
  errorRate: number;
  batchEfficiency: number;
  cacheHitRate: number;
  routingLatency: number;
  fallbackUsage: number;
}

export interface OpportunityLifecycleData {
  opportunityId: string;
  stage: 'detected' | 'validated' | 'profit_checked' | 'queued' | 'flash_loan_quoted' | 'mev_protected' | 'executed';
  data: any;
  timestamp: number;
  correlationId: string;
  network: string;
  tokenSymbol: string;
}

export interface BatchProcessingConfig {
  maxBatchSize: number;
  batchTimeoutMs: number;
  retryAttempts: number;
  retryDelayMs: number;
}

export interface RoutingOptions {
  skipCache?: boolean;
  forceRefresh?: boolean;
  priority?: 'low' | 'medium' | 'high';
  timeout?: number;
  correlationId?: string;
}

export class EnhancedDataRoutingService {
  private dataRoutes: Map<string, DataRoute> = new Map();
  private batchQueues: Map<string, any[]> = new Map();
  private batchTimers: Map<string, NodeJS.Timeout> = new Map();
  private opportunityLifecycle: Map<string, OpportunityLifecycleData[]> = new Map();
  private correlationTracker: Map<string, string[]> = new Map();
  private metrics: DataFlowMetrics = {
    totalWrites: 0,
    totalReads: 0,
    writeLatency: 0,
    readLatency: 0,
    errorRate: 0,
    batchEfficiency: 0,
    cacheHitRate: 0,
    routingLatency: 0,
    fallbackUsage: 0
  };

  private batchConfig: BatchProcessingConfig = {
    maxBatchSize: parseInt(config.SUPABASE_BATCH_SIZE),
    batchTimeoutMs: parseInt(config.SUPABASE_BATCH_TIMEOUT),
    retryAttempts: 3,
    retryDelayMs: 1000
  };

  constructor() {
    this.initializeDataRoutes();
    this.startMetricsCollection();
  }

  private initializeDataRoutes(): void {
    // Opportunity lifecycle routing
    this.dataRoutes.set('opportunity.detected', {
      primary: 'supabase',
      secondary: 'postgres',
      cache: true,
      cacheTTL: parseInt(config.OPPORTUNITY_CACHE_TTL),
      batchSize: parseInt(config.SUPABASE_BATCH_SIZE)
    });

    this.dataRoutes.set('opportunity.validation', {
      primary: 'supabase',
      cache: true,
      cacheTTL: 60,
      batchSize: parseInt(config.SUPABASE_BATCH_SIZE)
    });

    this.dataRoutes.set('opportunity.profit_check', {
      primary: 'influxdb',
      cache: true,
      cacheTTL: 30,
      batchSize: parseInt(config.INFLUXDB_BATCH_SIZE),
      retentionPolicy: config.INFLUXDB_RAW_DATA_RETENTION
    });

    this.dataRoutes.set('opportunity.queue_entry', {
      primary: 'redis',
      cache: false,
      cacheTTL: parseInt(config.QUEUE_CACHE_TTL)
    });

    this.dataRoutes.set('opportunity.flash_loan', {
      primary: 'supabase',
      secondary: 'redis',
      cache: true,
      cacheTTL: TTL.FLASH_LOANS,
      batchSize: parseInt(config.SUPABASE_BATCH_SIZE)
    });

    this.dataRoutes.set('opportunity.mev_protection', {
      primary: 'influxdb',
      cache: true,
      cacheTTL: 60,
      batchSize: parseInt(config.INFLUXDB_BATCH_SIZE)
    });

    this.dataRoutes.set('opportunity.execution', {
      primary: 'supabase',
      secondary: 'influxdb',
      cache: true,
      cacheTTL: 300,
      batchSize: parseInt(config.SUPABASE_BATCH_SIZE)
    });

    // Price and market data routing
    this.dataRoutes.set('price.update', {
      primary: 'influxdb',
      secondary: 'redis',
      cache: true,
      cacheTTL: parseInt(config.PRICE_CACHE_TTL),
      batchSize: parseInt(config.INFLUXDB_BATCH_SIZE)
    });

    this.dataRoutes.set('token.monitoring', {
      primary: 'influxdb',
      cache: true,
      cacheTTL: 300,
      batchSize: parseInt(config.INFLUXDB_BATCH_SIZE)
    });

    // System metrics routing
    this.dataRoutes.set('system.health', {
      primary: 'influxdb',
      cache: true,
      cacheTTL: parseInt(config.HEALTH_CACHE_TTL),
      batchSize: parseInt(config.INFLUXDB_BATCH_SIZE)
    });

    this.dataRoutes.set('performance.metrics', {
      primary: 'influxdb',
      secondary: 'redis',
      cache: true,
      cacheTTL: 30,
      batchSize: parseInt(config.INFLUXDB_BATCH_SIZE)
    });

    logger.info('Enhanced data routing configuration initialized');
  }

  // Opportunity Lifecycle Management
  async routeOpportunityLifecycle(
    stage: OpportunityLifecycleData['stage'],
    opportunityId: string,
    data: any,
    options: RoutingOptions = {}
  ): Promise<boolean> {
    const startTime = Date.now();
    const correlationId = options.correlationId || uuidv4();

    try {
      // Create lifecycle data entry
      const lifecycleData: OpportunityLifecycleData = {
        opportunityId,
        stage,
        data,
        timestamp: Date.now(),
        correlationId,
        network: data.network || 'unknown',
        tokenSymbol: data.tokenSymbol || 'unknown'
      };

      // Track in lifecycle map
      if (!this.opportunityLifecycle.has(opportunityId)) {
        this.opportunityLifecycle.set(opportunityId, []);
      }
      this.opportunityLifecycle.get(opportunityId)!.push(lifecycleData);

      // Track correlation
      if (!this.correlationTracker.has(correlationId)) {
        this.correlationTracker.set(correlationId, []);
      }
      this.correlationTracker.get(correlationId)!.push(opportunityId);

      // Route based on stage
      const routeKey = `opportunity.${stage}`;
      const success = await this.routeData(routeKey, data, options);

      // Update metrics
      const latency = Date.now() - startTime;
      this.updateRoutingMetrics(latency, success);

      // Log lifecycle progression
      logger.info(`Opportunity lifecycle: ${opportunityId} -> ${stage}`, {
        correlationId,
        latency,
        success,
        network: lifecycleData.network,
        tokenSymbol: lifecycleData.tokenSymbol
      });

      return success;
    } catch (error) {
      logger.error(`Failed to route opportunity lifecycle: ${stage}`, error);
      this.metrics.errorRate++;
      return false;
    }
  }

  // Enhanced Data Routing with Fallback
  async routeData(
    dataType: string,
    data: any,
    options: RoutingOptions = {}
  ): Promise<boolean> {
    const startTime = Date.now();
    const route = this.dataRoutes.get(dataType);

    if (!route) {
      logger.warn(`No route configured for data type: ${dataType}`);
      return false;
    }

    try {
      // Try primary database
      const primarySuccess = await this.writeToDatabase(route.primary, dataType, data, options);

      if (primarySuccess) {
        // Cache if enabled
        if (route.cache) {
          await this.cacheData(dataType, data, route.cacheTTL || 300);
        }

        // Write to secondary if configured
        if (route.secondary) {
          // Don't wait for secondary write to complete
          this.writeToDatabase(route.secondary, dataType, data, options)
            .catch(error => logger.warn(`Secondary write failed for ${dataType}:`, error));
        }

        this.updateWriteMetrics(Date.now() - startTime, true);
        return true;
      }

      // Fallback to secondary if primary fails
      if (route.secondary) {
        logger.warn(`Primary write failed for ${dataType}, trying secondary`);
        const secondarySuccess = await this.writeToDatabase(route.secondary, dataType, data, options);

        if (secondarySuccess) {
          this.metrics.fallbackUsage++;
          this.updateWriteMetrics(Date.now() - startTime, true);
          return true;
        }
      }

      this.updateWriteMetrics(Date.now() - startTime, false);
      return false;
    } catch (error) {
      logger.error(`Failed to route data for ${dataType}:`, error);
      this.updateWriteMetrics(Date.now() - startTime, false);
      return false;
    }
  }

  // Enhanced Database Writing with Retry Logic
  private async writeToDatabase(
    database: string,
    dataType: string,
    data: any,
    options: RoutingOptions,
    retryCount: number = 0
  ): Promise<boolean> {
    try {
      switch (database) {
        case 'supabase':
          return await this.writeToSupabase(dataType, data);
        case 'influxdb':
          return await this.writeToInfluxDB(dataType, data);
        case 'redis':
          return await this.writeToRedis(dataType, data);
        case 'postgres':
          return await this.writeToPostgres(dataType, data);
        default:
          logger.error(`Unknown database type: ${database}`);
          return false;
      }
    } catch (error) {
      if (retryCount < this.batchConfig.retryAttempts) {
        logger.warn(`Write failed, retrying (${retryCount + 1}/${this.batchConfig.retryAttempts}):`, error);
        await this.delay(this.batchConfig.retryDelayMs * Math.pow(2, retryCount));
        return this.writeToDatabase(database, dataType, data, options, retryCount + 1);
      }

      logger.error(`Write failed after ${this.batchConfig.retryAttempts} retries:`, error);
      return false;
    }
  }

  async routeData(dataType: string, data: any, options?: { skipCache?: boolean, priority?: 'high' | 'medium' | 'low' }): Promise<boolean> {
    const startTime = Date.now();
    const route = this.dataRoutes.get(dataType);
    
    if (!route) {
      logger.warn(`No routing configuration found for data type: ${dataType}`);
      return false;
    }

    try {
      let success = false;

      // Primary storage
      success = await this.writeToDatabase(route.primary, dataType, data, route);

      // Secondary storage (if configured)
      if (route.secondary) {
        await this.writeToDatabase(route.secondary, dataType, data, route);
      }

      // Cache storage (if enabled and not skipped)
      if (route.cache && !options?.skipCache) {
        await this.writeToCache(dataType, data, route.cacheTTL);
      }

      const latency = Date.now() - startTime;
      this.updateWriteMetrics(latency, success);

      return success;
    } catch (error) {
      logger.error(`Error routing data for type ${dataType}:`, error);
      this.updateErrorMetrics();
      return false;
    }
  }

  async retrieveData(dataType: string, query: any, options?: { skipCache?: boolean }): Promise<any> {
    const startTime = Date.now();
    const route = this.dataRoutes.get(dataType);
    
    if (!route) {
      logger.warn(`No routing configuration found for data type: ${dataType}`);
      return null;
    }

    try {
      // Try cache first (if enabled and not skipped)
      if (route.cache && !options?.skipCache) {
        const cacheKey = this.generateCacheKey(dataType, query);
        const cachedData = await enhancedCacheService.get(cacheKey, dataType);
        if (cachedData) {
          this.updateReadMetrics(Date.now() - startTime, true, true);
          return cachedData;
        }
      }

      // Read from primary database
      const data = await this.readFromDatabase(route.primary, dataType, query);
      
      if (data && route.cache) {
        // Store in cache for future requests
        const cacheKey = this.generateCacheKey(dataType, query);
        await enhancedCacheService.set(cacheKey, data, route.cacheTTL, dataType);
      }

      const latency = Date.now() - startTime;
      this.updateReadMetrics(latency, data !== null, false);

      return data;
    } catch (error) {
      logger.error(`Error retrieving data for type ${dataType}:`, error);
      this.updateErrorMetrics();
      return null;
    }
  }

  private async writeToDatabase(database: string, dataType: string, data: any, route: DataRoute): Promise<boolean> {
    switch (database) {
      case 'supabase':
        return this.writeToSupabase(dataType, data, route);
      case 'influxdb':
        return this.writeToInfluxDB(dataType, data, route);
      case 'redis':
        return this.writeToRedis(dataType, data, route);
      case 'postgres':
        return this.writeToPostgres(dataType, data, route);
      default:
        logger.error(`Unknown database type: ${database}`);
        return false;
    }
  }

  private async readFromDatabase(database: string, dataType: string, query: any): Promise<any> {
    switch (database) {
      case 'supabase':
        return this.readFromSupabase(dataType, query);
      case 'influxdb':
        return this.readFromInfluxDB(dataType, query);
      case 'redis':
        return this.readFromRedis(dataType, query);
      case 'postgres':
        return this.readFromPostgres(dataType, query);
      default:
        logger.error(`Unknown database type: ${database}`);
        return null;
    }
  }

  private async writeToSupabase(dataType: string, data: any, route: DataRoute): Promise<boolean> {
    const supabase = databaseManager.getSupabase();
    if (!supabase) return false;

    try {
      const tableName = this.getTableName(dataType);
      
      if (route.batchSize && route.batchSize > 1) {
        return this.addToBatch(`supabase:${tableName}`, data, route.batchSize, async (batch) => {
          const { error } = await supabase.from(tableName).insert(batch);
          return !error;
        });
      } else {
        const { error } = await supabase.from(tableName).insert([data]);
        return !error;
      }
    } catch (error) {
      logger.error(`Supabase write error for ${dataType}:`, error);
      return false;
    }
  }

  private async writeToInfluxDB(dataType: string, data: any, route: DataRoute): Promise<boolean> {
    const writeApi = databaseManager.getInfluxWriteApi();
    if (!writeApi) return false;

    try {
      const point = this.createInfluxPoint(dataType, data);
      
      if (route.batchSize && route.batchSize > 1) {
        return this.addToBatch(`influxdb:${dataType}`, point, route.batchSize, async (batch) => {
          writeApi.writePoints(batch);
          await writeApi.flush();
          return true;
        });
      } else {
        writeApi.writePoint(point);
        await writeApi.flush();
        return true;
      }
    } catch (error) {
      logger.error(`InfluxDB write error for ${dataType}:`, error);
      return false;
    }
  }

  private async writeToRedis(dataType: string, data: any, route: DataRoute): Promise<boolean> {
    const redis = databaseManager.getRedis();
    if (!redis) return false;

    try {
      const key = this.generateRedisKey(dataType, data);
      const ttl = route.cacheTTL || parseInt(config.REDIS_DEFAULT_TTL);
      
      await redis.setEx(key, ttl, JSON.stringify(data));
      return true;
    } catch (error) {
      logger.error(`Redis write error for ${dataType}:`, error);
      return false;
    }
  }

  private async writeToPostgres(dataType: string, data: any, route: DataRoute): Promise<boolean> {
    const postgres = databaseManager.getPostgres();
    if (!postgres) return false;

    try {
      const tableName = this.getTableName(dataType);
      const columns = Object.keys(data).join(', ');
      const values = Object.values(data);
      const placeholders = values.map((_, i) => `$${i + 1}`).join(', ');
      
      const query = `INSERT INTO ${tableName} (${columns}) VALUES (${placeholders})`;
      await postgres.query(query, values);
      return true;
    } catch (error) {
      logger.error(`PostgreSQL write error for ${dataType}:`, error);
      return false;
    }
  }

  private async readFromSupabase(dataType: string, query: any): Promise<any> {
    const supabase = databaseManager.getSupabase();
    if (!supabase) return null;

    try {
      const tableName = this.getTableName(dataType);
      let queryBuilder = supabase.from(tableName).select('*');
      
      // Apply query filters
      if (query.limit) queryBuilder = queryBuilder.limit(query.limit);
      if (query.offset) queryBuilder = queryBuilder.range(query.offset, query.offset + (query.limit || 100) - 1);
      if (query.orderBy) queryBuilder = queryBuilder.order(query.orderBy.field, { ascending: query.orderBy.ascending });
      if (query.filters) {
        Object.entries(query.filters).forEach(([field, value]) => {
          queryBuilder = queryBuilder.eq(field, value);
        });
      }

      const { data, error } = await queryBuilder;
      return error ? null : data;
    } catch (error) {
      logger.error(`Supabase read error for ${dataType}:`, error);
      return null;
    }
  }

  private async readFromInfluxDB(dataType: string, query: any): Promise<any> {
    const influxDB = databaseManager.getInfluxDB();
    if (!influxDB) return null;

    try {
      const measurement = this.getMeasurementName(dataType);
      const queryApi = influxDB.getQueryApi(config.INFLUXDB_ORG);
      
      let fluxQuery = `from(bucket: "${config.INFLUXDB_BUCKET}")
        |> range(start: ${query.start || '-1h'})
        |> filter(fn: (r) => r._measurement == "${measurement}")`;
      
      if (query.tags) {
        Object.entries(query.tags).forEach(([tag, value]) => {
          fluxQuery += `\n  |> filter(fn: (r) => r.${tag} == "${value}")`;
        });
      }
      
      if (query.limit) {
        fluxQuery += `\n  |> limit(n: ${query.limit})`;
      }

      const result = await queryApi.collectRows(fluxQuery);
      return result;
    } catch (error) {
      logger.error(`InfluxDB read error for ${dataType}:`, error);
      return null;
    }
  }

  private async readFromRedis(dataType: string, query: any): Promise<any> {
    const redis = databaseManager.getRedis();
    if (!redis) return null;

    try {
      const key = query.key || this.generateRedisKey(dataType, query);
      const value = await redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error(`Redis read error for ${dataType}:`, error);
      return null;
    }
  }

  private async readFromPostgres(dataType: string, query: any): Promise<any> {
    const postgres = databaseManager.getPostgres();
    if (!postgres) return null;

    try {
      const tableName = this.getTableName(dataType);
      let sql = `SELECT * FROM ${tableName}`;
      const values: any[] = [];
      
      if (query.filters) {
        const conditions = Object.entries(query.filters).map(([field, value], index) => {
          values.push(value);
          return `${field} = $${index + 1}`;
        });
        sql += ` WHERE ${conditions.join(' AND ')}`;
      }
      
      if (query.orderBy) {
        sql += ` ORDER BY ${query.orderBy.field} ${query.orderBy.ascending ? 'ASC' : 'DESC'}`;
      }
      
      if (query.limit) {
        sql += ` LIMIT ${query.limit}`;
      }
      
      if (query.offset) {
        sql += ` OFFSET ${query.offset}`;
      }

      const result = await postgres.query(sql, values);
      return result.rows;
    } catch (error) {
      logger.error(`PostgreSQL read error for ${dataType}:`, error);
      return null;
    }
  }

  private async addToBatch(batchKey: string, data: any, batchSize: number, processor: (batch: any[]) => Promise<boolean>): Promise<boolean> {
    if (!this.batchQueues.has(batchKey)) {
      this.batchQueues.set(batchKey, []);
    }

    const queue = this.batchQueues.get(batchKey)!;
    queue.push(data);

    if (queue.length >= batchSize) {
      return this.processBatch(batchKey, processor);
    } else {
      // Set timer to process batch after timeout
      if (!this.batchTimers.has(batchKey)) {
        const timer = setTimeout(() => {
          this.processBatch(batchKey, processor);
        }, parseInt(config.SUPABASE_BATCH_TIMEOUT));
        this.batchTimers.set(batchKey, timer);
      }
      return true;
    }
  }

  private async processBatch(batchKey: string, processor: (batch: any[]) => Promise<boolean>): Promise<boolean> {
    const queue = this.batchQueues.get(batchKey);
    if (!queue || queue.length === 0) return true;

    const batch = queue.splice(0);
    this.batchQueues.set(batchKey, []);

    // Clear timer
    const timer = this.batchTimers.get(batchKey);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(batchKey);
    }

    try {
      const success = await processor(batch);
      this.updateBatchMetrics(batch.length, success);
      return success;
    } catch (error) {
      logger.error(`Batch processing error for ${batchKey}:`, error);
      return false;
    }
  }

  private createInfluxPoint(dataType: string, data: any): Point {
    const measurement = this.getMeasurementName(dataType);
    const point = new Point(measurement);

    // Add tags (indexed fields)
    if (data.tags) {
      Object.entries(data.tags).forEach(([key, value]) => {
        point.tag(key, String(value));
      });
    }

    // Add fields (non-indexed data)
    if (data.fields) {
      Object.entries(data.fields).forEach(([key, value]) => {
        if (typeof value === 'number') {
          point.floatField(key, value);
        } else if (typeof value === 'boolean') {
          point.booleanField(key, value);
        } else {
          point.stringField(key, String(value));
        }
      });
    }

    // Set timestamp
    if (data.timestamp) {
      point.timestamp(new Date(data.timestamp));
    }

    return point;
  }

  private getTableName(dataType: string): string {
    const tableMap: Record<string, string> = {
      'opportunity.detected': 'opportunities',
      'opportunity.validation': 'validations',
      'opportunity.flash_loan': 'flash_loan_quotes',
      'opportunity.execution': 'trades'
    };
    return tableMap[dataType] || dataType.replace('.', '_');
  }

  private getMeasurementName(dataType: string): string {
    const measurementMap: Record<string, string> = {
      'opportunity.profit_check': 'profit_validations',
      'opportunity.mev_protection': 'mev_protection',
      'price.update': 'price_updates',
      'token.monitoring': 'token_monitoring',
      'system.health': 'system_health',
      'performance.metrics': 'performance_metrics'
    };
    return measurementMap[dataType] || dataType.replace('.', '_');
  }

  private generateRedisKey(dataType: string, data: any): string {
    const keyMap: Record<string, (data: any) => string> = {
      'opportunity.queue_entry': (data) => `queue:execution:${data.id}`,
      'price.update': (data) => `price:${data.symbol}:${data.network}`,
      'system.health': (data) => `health:${data.service}`
    };
    
    const generator = keyMap[dataType];
    return generator ? generator(data) : `${dataType}:${data.id || Date.now()}`;
  }

  private generateCacheKey(dataType: string, query: any): string {
    const queryString = JSON.stringify(query);
    const hash = Buffer.from(queryString).toString('base64').slice(0, 16);
    return `${dataType}:${hash}`;
  }

  private updateWriteMetrics(latency: number, success: boolean): void {
    this.metrics.totalWrites++;
    this.metrics.writeLatency = (this.metrics.writeLatency + latency) / 2;
    if (!success) {
      this.updateErrorMetrics();
    }
  }

  private updateReadMetrics(latency: number, success: boolean, fromCache: boolean): void {
    this.metrics.totalReads++;
    this.metrics.readLatency = (this.metrics.readLatency + latency) / 2;
    if (fromCache) {
      this.metrics.cacheHitRate = (this.metrics.cacheHitRate + 1) / 2;
    }
    if (!success) {
      this.updateErrorMetrics();
    }
  }

  private updateErrorMetrics(): void {
    const totalOperations = this.metrics.totalWrites + this.metrics.totalReads;
    this.metrics.errorRate = totalOperations > 0 ? (this.metrics.errorRate * (totalOperations - 1) + 1) / totalOperations : 1;
  }

  private updateBatchMetrics(batchSize: number, success: boolean): void {
    this.metrics.batchEfficiency = (this.metrics.batchEfficiency + (success ? batchSize : 0)) / 2;
  }

  private startMetricsCollection(): void {
    setInterval(() => {
      logger.debug('Data routing metrics:', this.metrics);
    }, 60000); // Log metrics every minute
  }

  getMetrics(): DataFlowMetrics {
    return { ...this.metrics };
  }
}

export const dataRoutingService = new DataRoutingService();
