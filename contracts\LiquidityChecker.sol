// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "./interfaces/IFlashLoanReceiver.sol";
import "./interfaces/IERC20.sol";

/**
 * @title LiquidityChecker
 * @dev Contract for checking liquidity across different DEX pools
 */
contract LiquidityChecker {

    struct PoolInfo {
        address poolAddress;
        address token0;
        address token1;
        uint256 reserve0;
        uint256 reserve1;
        uint256 lastUpdated;
    }

    mapping(address => PoolInfo) public pools;
    address[] public trackedPools;

    event PoolAdded(address indexed poolAddress, address token0, address token1);
    event LiquidityUpdated(address indexed poolAddress, uint256 reserve0, uint256 reserve1);

    /**
     * @dev Add a pool to track
     */
    function addPool(address poolAddress) external {
        require(poolAddress != address(0), "Invalid pool address");
        require(pools[poolAddress].poolAddress == address(0), "Pool already tracked");

        IUniswapV2Pair pair = IUniswapV2Pair(poolAddress);
        address token0 = pair.token0();
        address token1 = pair.token1();

        (uint112 reserve0, uint112 reserve1,) = pair.getReserves();

        pools[poolAddress] = PoolInfo({
            poolAddress: poolAddress,
            token0: token0,
            token1: token1,
            reserve0: uint256(reserve0),
            reserve1: uint256(reserve1),
            lastUpdated: block.timestamp
        });

        trackedPools.push(poolAddress);

        emit PoolAdded(poolAddress, token0, token1);
    }

    /**
     * @dev Update liquidity for a specific pool
     */
    function updatePoolLiquidity(address poolAddress) external {
        require(pools[poolAddress].poolAddress != address(0), "Pool not tracked");

        IUniswapV2Pair pair = IUniswapV2Pair(poolAddress);
        (uint112 reserve0, uint112 reserve1,) = pair.getReserves();

        pools[poolAddress].reserve0 = uint256(reserve0);
        pools[poolAddress].reserve1 = uint256(reserve1);
        pools[poolAddress].lastUpdated = block.timestamp;

        emit LiquidityUpdated(poolAddress, uint256(reserve0), uint256(reserve1));
    }

    /**
     * @dev Get current reserves for a pool
     */
    function getPoolReserves(address poolAddress) external view returns (uint256 reserve0, uint256 reserve1) {
        IUniswapV2Pair pair = IUniswapV2Pair(poolAddress);
        (uint112 _reserve0, uint112 _reserve1,) = pair.getReserves();
        return (uint256(_reserve0), uint256(_reserve1));
    }

    /**
     * @dev Check if pool has sufficient liquidity for trade
     */
    function hasSufficientLiquidity(
        address poolAddress,
        address tokenIn,
        uint256 amountIn,
        uint256 minLiquidityRatio
    ) external view returns (bool) {
        require(pools[poolAddress].poolAddress != address(0), "Pool not tracked");

        PoolInfo memory pool = pools[poolAddress];
        uint256 tokenInReserve;

        if (tokenIn == pool.token0) {
            tokenInReserve = pool.reserve0;
        } else if (tokenIn == pool.token1) {
            tokenInReserve = pool.reserve1;
        } else {
            return false;
        }

        // Check if trade amount is less than specified percentage of pool liquidity
        return (amountIn * 100) <= (tokenInReserve * minLiquidityRatio);
    }

    /**
     * @dev Calculate price impact for a trade
     */
    function calculatePriceImpact(
        address poolAddress,
        address tokenIn,
        uint256 amountIn
    ) external view returns (uint256 priceImpact) {
        require(pools[poolAddress].poolAddress != address(0), "Pool not tracked");

        IUniswapV2Pair pair = IUniswapV2Pair(poolAddress);
        (uint112 reserve0, uint112 reserve1,) = pair.getReserves();

        uint256 reserveIn;
        uint256 reserveOut;

        if (tokenIn == pair.token0()) {
            reserveIn = uint256(reserve0);
            reserveOut = uint256(reserve1);
        } else {
            reserveIn = uint256(reserve1);
            reserveOut = uint256(reserve0);
        }

        // Calculate price impact using constant product formula
        uint256 amountInWithFee = amountIn * 997;
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = (reserveIn * 1000) + amountInWithFee;
        uint256 amountOut = numerator / denominator;

        // Price impact = (amountIn / reserveIn) * 100
        priceImpact = (amountIn * 10000) / reserveIn; // Returns in basis points
    }

    /**
     * @dev Get all tracked pools
     */
    function getTrackedPools() external view returns (address[] memory) {
        return trackedPools;
    }

    /**
     * @dev Get pool information
     */
    function getPoolInfo(address poolAddress) external view returns (PoolInfo memory) {
        return pools[poolAddress];
    }

    /**
     * @dev Batch update multiple pools
     */
    function batchUpdatePools(address[] calldata poolAddresses) external {
        for (uint i = 0; i < poolAddresses.length; i++) {
            if (pools[poolAddresses[i]].poolAddress != address(0)) {
                this.updatePoolLiquidity(poolAddresses[i]);
            }
        }
    }
}
