import { expect } from "chai";
import hre from "hardhat";
const { ethers } = hre;

describe("Basic Setup Test", function () {
  let owner;
  let user1;
  let user2;

  beforeEach(async function () {
    [owner, user1, user2] = await ethers.getSigners();
  });

  describe("Environment Setup", function () {
    it("Should have test accounts with ETH", async function () {
      const balance = await ethers.provider.getBalance(owner.address);
      expect(balance).to.be.greaterThan(ethers.parseEther("10000"));
      console.log(`Owner balance: ${ethers.formatEther(balance)} ETH`);
    });

    it("Should be able to deploy MockERC20 contract", async function () {
      const MockERC20 = await ethers.getContractFactory("MockERC20");
      const token = await MockERC20.deploy(
        "Test Token",
        "TEST",
        18,
        ethers.parseEther("1000000"),
        owner.address
      );
      await token.waitForDeployment();
      
      const address = await token.getAddress();
      expect(address).to.be.properAddress;
      
      const name = await token.name();
      expect(name).to.equal("Test Token");
      
      const symbol = await token.symbol();
      expect(symbol).to.equal("TEST");
      
      const decimals = await token.decimals();
      expect(decimals).to.equal(18);
      
      console.log(`MockERC20 deployed at: ${address}`);
    });

    it("Should be able to deploy MockPriceOracle contract", async function () {
      const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
      const oracle = await MockPriceOracle.deploy();
      await oracle.waitForDeployment();
      
      const address = await oracle.getAddress();
      expect(address).to.be.properAddress;
      
      // Test setting and getting price
      const testTokenAddress = ethers.Wallet.createRandom().address;
      const testPrice = ethers.parseUnits("2000", 8); // $2000 with 8 decimals
      
      await oracle.setPrice(testTokenAddress, testPrice);
      const retrievedPrice = await oracle.getPrice(testTokenAddress);
      expect(retrievedPrice).to.equal(testPrice);
      
      console.log(`MockPriceOracle deployed at: ${address}`);
      console.log(`Test price set and retrieved: $${ethers.formatUnits(retrievedPrice, 8)}`);
    });

    it("Should be able to deploy TokenDiscovery contract", async function () {
      const TokenDiscovery = await ethers.getContractFactory("TokenDiscovery");
      const tokenDiscovery = await TokenDiscovery.deploy();
      await tokenDiscovery.waitForDeployment();
      
      const address = await tokenDiscovery.getAddress();
      expect(address).to.be.properAddress;
      
      console.log(`TokenDiscovery deployed at: ${address}`);
    });

    it("Should be able to deploy ArbitrageExecutor contract", async function () {
      // First deploy dependencies
      const TokenDiscovery = await ethers.getContractFactory("TokenDiscovery");
      const tokenDiscovery = await TokenDiscovery.deploy();
      await tokenDiscovery.waitForDeployment();
      
      const LiquidityChecker = await ethers.getContractFactory("LiquidityChecker");
      const liquidityChecker = await LiquidityChecker.deploy();
      await liquidityChecker.waitForDeployment();
      
      // Deploy ArbitrageExecutor
      const ArbitrageExecutor = await ethers.getContractFactory("ArbitrageExecutor");
      const arbitrageExecutor = await ArbitrageExecutor.deploy(
        await tokenDiscovery.getAddress(),
        await liquidityChecker.getAddress()
      );
      await arbitrageExecutor.waitForDeployment();
      
      const address = await arbitrageExecutor.getAddress();
      expect(address).to.be.properAddress;
      
      console.log(`ArbitrageExecutor deployed at: ${address}`);
    });
  });

  describe("Gas Usage Testing", function () {
    it("Should measure gas usage for token transfers", async function () {
      const MockERC20 = await ethers.getContractFactory("MockERC20");
      const token = await MockERC20.deploy(
        "Gas Test Token",
        "GAS",
        18,
        ethers.parseEther("1000000"),
        owner.address
      );
      await token.waitForDeployment();
      
      // Test normal transfer
      const transferAmount = ethers.parseEther("100");
      const tx = await token.transfer(user1.address, transferAmount);
      const receipt = await tx.wait();
      
      expect(receipt.gasUsed).to.be.lessThan(100000); // Should be efficient
      console.log(`Gas used for transfer: ${receipt.gasUsed}`);
      
      // Verify transfer worked
      const balance = await token.balanceOf(user1.address);
      expect(balance).to.equal(transferAmount);
    });

    it("Should measure gas usage for contract deployments", async function () {
      const MockERC20 = await ethers.getContractFactory("MockERC20");
      
      const deployTx = await MockERC20.deploy(
        "Deploy Test",
        "DEPLOY",
        18,
        ethers.parseEther("1000"),
        owner.address
      );
      
      const receipt = await deployTx.deploymentTransaction().wait();
      console.log(`Gas used for MockERC20 deployment: ${receipt.gasUsed}`);
      
      // Should be reasonable gas usage
      expect(receipt.gasUsed).to.be.lessThan(2000000); // 2M gas limit
    });
  });

  describe("Performance Testing", function () {
    it("Should handle multiple contract interactions efficiently", async function () {
      const startTime = Date.now();
      
      // Deploy multiple contracts
      const MockERC20 = await ethers.getContractFactory("MockERC20");
      const contracts = [];
      
      for (let i = 0; i < 5; i++) {
        const token = await MockERC20.deploy(
          `Token ${i}`,
          `TK${i}`,
          18,
          ethers.parseEther("1000"),
          owner.address
        );
        await token.waitForDeployment();
        contracts.push(token);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(contracts.length).to.equal(5);
      expect(duration).to.be.lessThan(30000); // Should complete within 30 seconds
      
      console.log(`Deployed 5 contracts in ${duration}ms`);
    });
  });
});
