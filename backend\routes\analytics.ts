import { Router } from 'express';
import logger from '../utils/logger.js';
import { AnalyticsService } from '../services/AnalyticsService.js';
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/ErrorHandler.js';

export default function createAnalyticsRoutes(analyticsService: AnalyticsService) {
  const router = Router();

  // Get overall performance metrics
  router.get('/performance', async (req, res) => {
    try {
      const metrics = analyticsService.getOverallMetrics();
      
      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      logger.error('Error getting performance metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve performance metrics'
      });
    }
  });

  // Get strategy-specific metrics
  router.get('/performance/strategies', async (req, res) => {
    try {
      const strategyMetrics = analyticsService.getStrategyMetrics();
      
      res.json({
        success: true,
        data: strategyMetrics
      });
    } catch (error) {
      logger.error('Error getting strategy metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve strategy metrics'
      });
    }
  });

  // Get time series data
  router.get('/timeseries', async (req, res) => {
    try {
      const { hours = '24' } = req.query;
      const hoursNum = parseInt(hours as string);
      
      if (isNaN(hoursNum) || hoursNum <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid hours parameter'
        });
      }

      const timeSeriesData = analyticsService.getTimeSeriesData(hoursNum);
      
      res.json({
        success: true,
        data: timeSeriesData,
        count: timeSeriesData.length,
        timeframe: `${hoursNum} hours`
      });
    } catch (error) {
      logger.error('Error getting time series data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve time series data'
      });
    }
  });

  // Get daily metrics
  router.get('/daily', async (req, res) => {
    try {
      const { days = '7' } = req.query;
      const daysNum = parseInt(days as string);
      
      if (isNaN(daysNum) || daysNum <= 0 || daysNum > 30) {
        return res.status(400).json({
          success: false,
          error: 'Days parameter must be between 1 and 30'
        });
      }

      const dailyMetrics = analyticsService.getDailyMetrics(daysNum);
      
      res.json({
        success: true,
        data: dailyMetrics,
        count: dailyMetrics.length,
        timeframe: `${daysNum} days`
      });
    } catch (error) {
      logger.error('Error getting daily metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve daily metrics'
      });
    }
  });

  // Get asset performance
  router.get('/assets', async (req, res) => {
    try {
      const { limit = '20' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 20, 100);
      
      let assetPerformance = analyticsService.getAssetPerformance();
      assetPerformance = assetPerformance.slice(0, limitNum);
      
      res.json({
        success: true,
        data: assetPerformance,
        count: assetPerformance.length,
        limit: limitNum
      });
    } catch (error) {
      logger.error('Error getting asset performance:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve asset performance'
      });
    }
  });

  // Get top performing assets
  router.get('/assets/top', async (req, res) => {
    try {
      const { limit = '10', sortBy = 'totalProfit' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 10, 50);
      
      let assetPerformance = analyticsService.getAssetPerformance();
      
      // Sort by specified metric
      if (sortBy === 'winRate') {
        assetPerformance.sort((a, b) => b.winRate - a.winRate);
      } else if (sortBy === 'tradeCount') {
        assetPerformance.sort((a, b) => b.tradeCount - a.tradeCount);
      } else if (sortBy === 'avgProfit') {
        assetPerformance.sort((a, b) => b.avgProfit - a.avgProfit);
      } else {
        // Default: totalProfit
        assetPerformance.sort((a, b) => b.totalProfit - a.totalProfit);
      }
      
      assetPerformance = assetPerformance.slice(0, limitNum);
      
      res.json({
        success: true,
        data: assetPerformance,
        count: assetPerformance.length,
        sortBy,
        limit: limitNum
      });
    } catch (error) {
      logger.error('Error getting top performing assets:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve top performing assets'
      });
    }
  });

  // Get recent trades
  router.get('/trades/recent', async (req, res) => {
    try {
      const { limit = '50' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 50, 200);
      
      const trades = analyticsService.getTrades(limitNum);
      
      res.json({
        success: true,
        data: trades,
        count: trades.length,
        limit: limitNum
      });
    } catch (error) {
      logger.error('Error getting recent trades:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve recent trades'
      });
    }
  });

  // Get profit/loss distribution
  router.get('/pnl/distribution', async (req, res) => {
    try {
      const trades = analyticsService.getTrades(1000); // Get last 1000 trades
      
      const distribution = {
        profitable: 0,
        breakeven: 0,
        loss: 0,
        ranges: {
          'loss_high': 0,      // < -$100
          'loss_medium': 0,    // -$100 to -$10
          'loss_low': 0,       // -$10 to $0
          'profit_low': 0,     // $0 to $10
          'profit_medium': 0,  // $10 to $100
          'profit_high': 0     // > $100
        }
      };

      trades.forEach(trade => {
        const profit = trade.executedProfit;
        
        if (profit > 0) {
          distribution.profitable++;
          if (profit > 100) distribution.ranges.profit_high++;
          else if (profit > 10) distribution.ranges.profit_medium++;
          else distribution.ranges.profit_low++;
        } else if (profit < 0) {
          distribution.loss++;
          if (profit < -100) distribution.ranges.loss_high++;
          else if (profit < -10) distribution.ranges.loss_medium++;
          else distribution.ranges.loss_low++;
        } else {
          distribution.breakeven++;
        }
      });

      res.json({
        success: true,
        data: distribution,
        totalTrades: trades.length
      });
    } catch (error) {
      logger.error('Error getting P&L distribution:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve P&L distribution'
      });
    }
  });

  // Get analytics summary
  router.get('/summary', async (req, res) => {
    try {
      const overallMetrics = analyticsService.getOverallMetrics();
      const strategyMetrics = analyticsService.getStrategyMetrics();
      const assetPerformance = analyticsService.getAssetPerformance().slice(0, 5); // Top 5 assets
      const stats = analyticsService.getStats();

      const summary = {
        overall: overallMetrics,
        strategies: strategyMetrics,
        topAssets: assetPerformance,
        systemStats: stats,
        timestamp: Date.now()
      };

      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      logger.error('Error getting analytics summary:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve analytics summary'
      });
    }
  });

  // Get performance comparison
  router.get('/performance/compare', async (req, res) => {
    try {
      const { period1 = '7', period2 = '7' } = req.query;
      const period1Days = parseInt(period1 as string);
      const period2Days = parseInt(period2 as string);
      
      if (isNaN(period1Days) || isNaN(period2Days) || period1Days <= 0 || period2Days <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid period parameters'
        });
      }

      const now = Date.now();
      const period1Start = now - period1Days * 24 * 60 * 60 * 1000;
      const period2Start = period1Start - period2Days * 24 * 60 * 60 * 1000;

      const allTrades = analyticsService.getTrades(10000); // Get many trades
      
      const period1Trades = allTrades.filter(trade => 
        trade.timestamp >= period1Start && trade.timestamp < now
      );
      
      const period2Trades = allTrades.filter(trade => 
        trade.timestamp >= period2Start && trade.timestamp < period1Start
      );

      const calculateMetrics = (trades: any[]) => {
        const successful = trades.filter(t => t.executedProfit > 0);
        const totalProfit = successful.reduce((sum, t) => sum + t.executedProfit, 0);
        const totalLoss = trades.filter(t => t.executedProfit < 0)
          .reduce((sum, t) => sum + Math.abs(t.executedProfit), 0);
        
        return {
          totalTrades: trades.length,
          successfulTrades: successful.length,
          winRate: trades.length > 0 ? (successful.length / trades.length) * 100 : 0,
          totalProfit,
          totalLoss,
          netProfit: totalProfit - totalLoss,
          avgProfit: successful.length > 0 ? totalProfit / successful.length : 0
        };
      };

      const comparison = {
        period1: {
          days: period1Days,
          metrics: calculateMetrics(period1Trades)
        },
        period2: {
          days: period2Days,
          metrics: calculateMetrics(period2Trades)
        }
      };

      res.json({
        success: true,
        data: comparison
      });
    } catch (error) {
      logger.error('Error getting performance comparison:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve performance comparison'
      });
    }
  });

  // Get analytics statistics
  router.get('/stats', async (req, res) => {
    try {
      const stats = analyticsService.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting analytics stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve analytics statistics'
      });
    }
  });

  /**
   * Get historical performance data
   * GET /api/analytics/performance/historical
   */
  router.get('/performance/historical', async (req, res) => {
    const startTime = Date.now();

    try {
      const {
        timeframe = '24h',
        granularity = '1h',
        metrics = 'all',
        networks,
        strategies
      } = req.query;

      // Validate timeframe
      const validTimeframes = ['1h', '6h', '24h', '7d', '30d'];
      if (!validTimeframes.includes(timeframe as string)) {
        return res.status(400).json({
          success: false,
          error: `Invalid timeframe. Must be one of: ${validTimeframes.join(', ')}`,
          validTimeframes
        });
      }

      // Validate granularity
      const validGranularities = ['1m', '5m', '15m', '1h', '6h', '24h'];
      if (!validGranularities.includes(granularity as string)) {
        return res.status(400).json({
          success: false,
          error: `Invalid granularity. Must be one of: ${validGranularities.join(', ')}`,
          validGranularities
        });
      }

      // Calculate time range
      const timeRanges = {
        '1h': 60 * 60 * 1000,
        '6h': 6 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      };

      const endTime = Date.now();
      const startTimeRange = endTime - timeRanges[timeframe as keyof typeof timeRanges];

      // Get historical data
      const historicalData = await getHistoricalPerformanceData({
        startTime: startTimeRange,
        endTime,
        granularity: granularity as string,
        metrics: metrics as string,
        networks: networks ? (networks as string).split(',') : undefined,
        strategies: strategies ? (strategies as string).split(',') : undefined
      });

      const responseTime = Date.now() - startTime;

      res.json({
        success: true,
        data: historicalData,
        metadata: {
          timeframe,
          granularity,
          startTime: new Date(startTimeRange).toISOString(),
          endTime: new Date(endTime).toISOString(),
          dataPoints: historicalData.dataPoints?.length || 0,
          responseTime
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      const standardError = await errorHandler.handleError(
        error as Error,
        'AnalyticsAPI',
        'getHistoricalPerformance',
        {
          timeframe: req.query.timeframe,
          granularity: req.query.granularity,
          metrics: req.query.metrics
        }
      );

      logger.error('Historical performance request failed:', error);

      res.status(500).json({
        success: false,
        error: standardError.message,
        errorCode: standardError.code,
        timestamp: new Date().toISOString()
      });
    }
  });

  /**
   * Get cross-chain analytics
   * GET /api/analytics/cross-chain
   */
  router.get('/cross-chain', async (req, res) => {
    try {
      const {
        timeframe = '24h',
        networkPair,
        bridgeProtocol
      } = req.query;

      const crossChainAnalytics = await getCrossChainAnalytics({
        timeframe: timeframe as string,
        networkPair: networkPair as string,
        bridgeProtocol: bridgeProtocol as string
      });

      res.json({
        success: true,
        data: crossChainAnalytics,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Cross-chain analytics request failed:', error);

      res.status(500).json({
        success: false,
        error: 'Failed to retrieve cross-chain analytics',
        timestamp: new Date().toISOString()
      });
    }
  });

  /**
   * Get performance comparison
   * GET /api/analytics/performance/compare
   */
  router.get('/performance/compare', async (req, res) => {
    try {
      const {
        period1 = '24h',
        period2 = '7d',
        metrics = 'profit,success_rate,execution_time'
      } = req.query;

      const comparison = await getPerformanceComparison({
        period1: period1 as string,
        period2: period2 as string,
        metrics: (metrics as string).split(',')
      });

      res.json({
        success: true,
        data: comparison,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Performance comparison request failed:', error);

      res.status(500).json({
        success: false,
        error: 'Failed to retrieve performance comparison',
        timestamp: new Date().toISOString()
      });
    }
  });

  return router;
}

/**
 * Get historical performance data from database
 */
async function getHistoricalPerformanceData(params: {
  startTime: number;
  endTime: number;
  granularity: string;
  metrics: string;
  networks?: string[];
  strategies?: string[];
}): Promise<any> {

  const {
    startTime,
    endTime,
    granularity,
    metrics,
    networks,
    strategies
  } = params;

  // Simulate historical data retrieval
  // In production, this would query InfluxDB or similar time-series database

  const granularityMs = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000
  };

  const interval = granularityMs[granularity as keyof typeof granularityMs] || granularityMs['1h'];
  const dataPoints = [];

  for (let time = startTime; time <= endTime; time += interval) {
    const dataPoint = {
      timestamp: new Date(time).toISOString(),
      timestampMs: time,
      metrics: generateHistoricalMetrics(time, metrics, networks, strategies)
    };

    dataPoints.push(dataPoint);
  }

  // Calculate summary statistics
  const summary = calculateSummaryStatistics(dataPoints, metrics);

  return {
    dataPoints,
    summary,
    filters: {
      networks: networks || [],
      strategies: strategies || [],
      metrics: metrics === 'all' ? ['profit', 'success_rate', 'execution_time', 'gas_costs', 'slippage'] : metrics.split(',')
    }
  };
}

/**
 * Generate simulated historical metrics
 */
function generateHistoricalMetrics(
  timestamp: number,
  metrics: string,
  networks?: string[],
  strategies?: string[]
): any {

  const baseMetrics = {
    totalProfit: Math.random() * 1000 + 100,
    successRate: Math.random() * 20 + 80, // 80-100%
    executionTime: Math.random() * 300 + 60, // 60-360 seconds
    gasCosts: Math.random() * 50 + 10,
    slippage: Math.random() * 2, // 0-2%
    opportunitiesDetected: Math.floor(Math.random() * 50) + 10,
    opportunitiesExecuted: Math.floor(Math.random() * 20) + 5,
    averageProfit: Math.random() * 100 + 20,
    crossChainOperations: Math.floor(Math.random() * 10),
    bridgeLatency: Math.random() * 600 + 300, // 300-900 seconds
    mevProtectionSuccess: Math.random() * 10 + 90 // 90-100%
  };

  // Add network-specific metrics if networks filter is applied
  if (networks && networks.length > 0) {
    baseMetrics.networkBreakdown = networks.reduce((acc, network) => {
      acc[network] = {
        profit: Math.random() * 200 + 50,
        operations: Math.floor(Math.random() * 10) + 1,
        successRate: Math.random() * 15 + 85
      };
      return acc;
    }, {} as any);
  }

  // Add strategy-specific metrics if strategies filter is applied
  if (strategies && strategies.length > 0) {
    baseMetrics.strategyBreakdown = strategies.reduce((acc, strategy) => {
      acc[strategy] = {
        profit: Math.random() * 150 + 30,
        operations: Math.floor(Math.random() * 8) + 1,
        successRate: Math.random() * 20 + 75
      };
      return acc;
    }, {} as any);
  }

  return baseMetrics;
}

/**
 * Calculate summary statistics for historical data
 */
function calculateSummaryStatistics(dataPoints: any[], metrics: string): any {
  if (dataPoints.length === 0) return {};

  const summary: any = {};
  const metricsToCalculate = metrics === 'all' ?
    ['totalProfit', 'successRate', 'executionTime', 'gasCosts', 'slippage'] :
    metrics.split(',');

  metricsToCalculate.forEach(metric => {
    const values = dataPoints.map(dp => dp.metrics[metric]).filter(v => v !== undefined);

    if (values.length > 0) {
      summary[metric] = {
        min: Math.min(...values),
        max: Math.max(...values),
        avg: values.reduce((sum, val) => sum + val, 0) / values.length,
        total: metric === 'totalProfit' ? values.reduce((sum, val) => sum + val, 0) : undefined
      };
    }
  });

  // Calculate trends
  summary.trends = calculateTrends(dataPoints);

  return summary;
}

/**
 * Calculate trends from historical data
 */
function calculateTrends(dataPoints: any[]): any {
  if (dataPoints.length < 2) return {};

  const firstHalf = dataPoints.slice(0, Math.floor(dataPoints.length / 2));
  const secondHalf = dataPoints.slice(Math.floor(dataPoints.length / 2));

  const firstHalfAvgProfit = firstHalf.reduce((sum, dp) => sum + dp.metrics.totalProfit, 0) / firstHalf.length;
  const secondHalfAvgProfit = secondHalf.reduce((sum, dp) => sum + dp.metrics.totalProfit, 0) / secondHalf.length;

  const profitTrend = ((secondHalfAvgProfit - firstHalfAvgProfit) / firstHalfAvgProfit) * 100;

  return {
    profitTrend: {
      direction: profitTrend > 0 ? 'up' : 'down',
      percentage: Math.abs(profitTrend).toFixed(2)
    },
    dataQuality: {
      completeness: 100, // Simulated
      reliability: 95 // Simulated
    }
  };
}

/**
 * Get cross-chain analytics
 */
async function getCrossChainAnalytics(params: {
  timeframe: string;
  networkPair?: string;
  bridgeProtocol?: string;
}): Promise<any> {

  // Simulate cross-chain analytics
  return {
    overview: {
      totalCrossChainOperations: Math.floor(Math.random() * 100) + 50,
      successfulOperations: Math.floor(Math.random() * 80) + 40,
      totalVolume: Math.random() * 100000 + 50000,
      averageBridgeTime: Math.random() * 600 + 300
    },
    networkPairs: [
      { pair: 'ethereum-polygon', operations: 25, avgTime: 420, successRate: 95 },
      { pair: 'ethereum-arbitrum', operations: 20, avgTime: 380, successRate: 97 },
      { pair: 'polygon-bsc', operations: 15, avgTime: 450, successRate: 92 }
    ],
    bridgeProtocols: [
      { protocol: 'stargate', operations: 30, avgFee: 12, successRate: 96 },
      { protocol: 'layerzero', operations: 20, avgFee: 8, successRate: 94 },
      { protocol: 'wormhole', operations: 10, avgFee: 15, successRate: 90 }
    ],
    profitability: {
      totalProfit: Math.random() * 10000 + 5000,
      averageProfit: Math.random() * 100 + 50,
      profitMargin: Math.random() * 10 + 5
    }
  };
}

/**
 * Get performance comparison between periods
 */
async function getPerformanceComparison(params: {
  period1: string;
  period2: string;
  metrics: string[];
}): Promise<any> {

  // Simulate performance comparison
  const period1Data = {
    totalProfit: Math.random() * 5000 + 2000,
    successRate: Math.random() * 10 + 85,
    executionTime: Math.random() * 100 + 120
  };

  const period2Data = {
    totalProfit: Math.random() * 5000 + 2000,
    successRate: Math.random() * 10 + 85,
    executionTime: Math.random() * 100 + 120
  };

  const comparison = {
    period1: {
      label: params.period1,
      data: period1Data
    },
    period2: {
      label: params.period2,
      data: period2Data
    },
    changes: {
      totalProfit: {
        absolute: period2Data.totalProfit - period1Data.totalProfit,
        percentage: ((period2Data.totalProfit - period1Data.totalProfit) / period1Data.totalProfit) * 100
      },
      successRate: {
        absolute: period2Data.successRate - period1Data.successRate,
        percentage: ((period2Data.successRate - period1Data.successRate) / period1Data.successRate) * 100
      },
      executionTime: {
        absolute: period2Data.executionTime - period1Data.executionTime,
        percentage: ((period2Data.executionTime - period1Data.executionTime) / period1Data.executionTime) * 100
      }
    }
  };

  return comparison;
}
