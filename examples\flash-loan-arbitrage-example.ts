/**
 * Flash Loan Arbitrage Bot Example
 * 
 * This example demonstrates comprehensive flash loan and flash swap integration
 * for all arbitrage strategies with automatic provider selection and optimization.
 */

import { ServiceIntegrator } from '../backend/services/ServiceIntegrator.js';
import { FlashLoanService, FlashLoanProvider, FlashLoanStrategy } from '../backend/services/FlashLoanService.js';
import { PreExecutionValidationService } from '../backend/services/PreExecutionValidationService.js';
import { OpportunityDetectionService, ArbitrageType } from '../backend/services/OpportunityDetectionService.js';
import { ExecutionService } from '../backend/services/ExecutionService.js';
import logger from '../backend/utils/logger.js';

async function runFlashLoanArbitrageBot() {
  logger.info('Starting Flash Loan Arbitrage Bot...');

  // Initialize the service integrator with flash loans enabled
  const serviceIntegrator = new ServiceIntegrator({
    enablePreExecutionValidation: true,
    enableMEVProtection: true,
    enableFlashLoans: true, // Enable flash loan integration
    enableMLLearning: true,
    enableRiskManagement: true
  });

  try {
    // Initialize all services
    await serviceIntegrator.initialize();

    // Get service instances
    const flashLoanService = serviceIntegrator.getService<FlashLoanService>('flashLoan');
    const opportunityDetectionService = serviceIntegrator.getService<OpportunityDetectionService>('opportunityDetection');
    const executionService = serviceIntegrator.getService<ExecutionService>('execution');
    const preExecutionValidationService = serviceIntegrator.getService<PreExecutionValidationService>('preExecutionValidation');

    // Set up comprehensive event listeners
    setupFlashLoanEventListeners(
      flashLoanService,
      opportunityDetectionService,
      executionService,
      preExecutionValidationService
    );

    // Display flash loan system status
    await displayFlashLoanSystemStatus(serviceIntegrator);

    // Run flash loan optimization examples
    await runFlashLoanOptimizationExamples(flashLoanService);

    // Simulate flash loan arbitrage opportunities
    await simulateFlashLoanArbitrageOpportunities(
      flashLoanService,
      preExecutionValidationService
    );

    logger.info('Flash Loan Arbitrage Bot is now running...');
    logger.info('Features enabled:');
    logger.info('- ✅ Multi-provider flash loan support (Aave V3, Balancer V2, dYdX, Uniswap V3)');
    logger.info('- ✅ Automatic strategy detection (Flash Loan vs Flash Swap)');
    logger.info('- ✅ Optimal provider selection based on cost and liquidity');
    logger.info('- ✅ Enhanced pre-execution validation with flash loan cost calculation');
    logger.info('- ✅ MEV-optimized transaction submission');
    logger.info('- ✅ Atomic execution guarantees');

    // Keep the bot running
    await new Promise(resolve => {
      process.on('SIGINT', async () => {
        logger.info('Shutting down Flash Loan Arbitrage Bot...');
        await serviceIntegrator.shutdown();
        resolve(void 0);
      });
    });

  } catch (error) {
    logger.error('Failed to start Flash Loan Arbitrage Bot:', error);
    await serviceIntegrator.shutdown();
    process.exit(1);
  }
}

function setupFlashLoanEventListeners(
  flashLoanService: FlashLoanService,
  opportunityDetectionService: OpportunityDetectionService,
  executionService: ExecutionService,
  preExecutionValidationService: PreExecutionValidationService
) {
  // Flash Loan Service Events
  flashLoanService.on('flashLoanQuoteReceived', (event) => {
    logger.debug(`Flash loan quote: ${event.provider} - $${event.totalCost.toFixed(2)} for $${event.amount.toLocaleString()}`);
  });

  // Opportunity Detection with Flash Loan Integration
  opportunityDetectionService.on('opportunity', (opportunity) => {
    const flashLoanInfo = opportunity.validationResult?.flashLoanOptimization;
    
    if (flashLoanInfo) {
      logger.info(`🎯 Flash Loan Opportunity: ${opportunity.type} - ${opportunity.assets.join('/')}`);
      logger.info(`   Original profit: $${opportunity.potentialProfit.toFixed(2)}`);
      logger.info(`   Flash loan provider: ${flashLoanInfo.recommendedProvider}`);
      logger.info(`   Strategy: ${flashLoanInfo.recommendedStrategy}`);
      logger.info(`   Optimal amount: $${flashLoanInfo.optimalAmount.toLocaleString()}`);
      logger.info(`   Flash loan cost: $${flashLoanInfo.totalCost.toFixed(2)}`);
      logger.info(`   Expected profit: $${flashLoanInfo.expectedProfit.toFixed(2)}`);
      logger.info(`   Risk score: ${flashLoanInfo.riskScore}/100`);
      logger.info(`   Reasoning: ${flashLoanInfo.reasoning}`);
    } else {
      logger.info(`🎯 Standard Opportunity: ${opportunity.type} - ${opportunity.assets.join('/')} - $${opportunity.potentialProfit.toFixed(2)}`);
    }
  });

  // Enhanced Validation Events
  preExecutionValidationService.on('validationComplete', (event) => {
    const { opportunity, result } = event;
    
    if (result.isValid && result.flashLoanOptimization) {
      logger.info(`✅ Flash Loan Validation PASSED for ${opportunity.id}`);
      logger.info(`   Enhanced cost breakdown:`);
      logger.info(`     Gas costs: $${result.totalCosts.gasCosts.toFixed(2)}`);
      logger.info(`     DEX fees: $${result.totalCosts.dexFees.toFixed(2)}`);
      logger.info(`     Bridge fees: $${result.totalCosts.bridgeFees.toFixed(2)}`);
      logger.info(`     Slippage costs: $${result.totalCosts.slippageCosts.toFixed(2)}`);
      logger.info(`     MEV protection: $${result.totalCosts.mevProtectionCosts.toFixed(2)}`);
      logger.info(`     Flash loan costs: $${result.totalCosts.flashLoanCosts.toFixed(2)}`);
      logger.info(`     Total costs: $${result.totalCosts.totalCosts.toFixed(2)}`);
      logger.info(`   Net profit: $${result.simulatedProfit.toFixed(2)} (${result.profitMargin.toFixed(2)}% margin)`);
    }
  });

  // Execution Events with Flash Loan Tracking
  executionService.on('tradeUpdate', (trade) => {
    if (trade.flashLoanExecution) {
      const flashLoan = trade.flashLoanExecution;
      
      switch (trade.status) {
        case 'executing':
          logger.info(`⚡ Flash Loan Trade ${trade.id} executing...`);
          logger.info(`   Provider: ${flashLoan.provider}`);
          logger.info(`   Strategy: ${flashLoan.strategy}`);
          logger.info(`   Amount: $${flashLoan.amount.toLocaleString()}`);
          break;
          
        case 'success':
          logger.info(`✅ Flash Loan Trade ${trade.id} completed successfully!`);
          logger.info(`   Provider: ${flashLoan.provider}`);
          logger.info(`   Strategy: ${flashLoan.strategy}`);
          logger.info(`   Flash loan fee: $${flashLoan.fee.toFixed(2)}`);
          logger.info(`   Gas used: ${flashLoan.gasUsed.toLocaleString()}`);
          logger.info(`   Execution time: ${flashLoan.executionTime}ms`);
          logger.info(`   Profit after fees: $${flashLoan.profitAfterFees.toFixed(2)}`);
          logger.info(`   Total trade profit: $${trade.executedProfit.toFixed(2)}`);
          break;
          
        case 'failed':
          logger.error(`❌ Flash Loan Trade ${trade.id} failed: ${trade.errorMessage}`);
          if (flashLoan.error) {
            logger.error(`   Flash loan error: ${flashLoan.error}`);
          }
          break;
      }
    }
  });
}

async function displayFlashLoanSystemStatus(serviceIntegrator: ServiceIntegrator) {
  logger.info('=== FLASH LOAN SYSTEM STATUS ===');
  
  const flashLoanService = serviceIntegrator.getService<FlashLoanService>('flashLoan');
  if (!flashLoanService) {
    logger.error('Flash Loan Service not available');
    return;
  }

  const stats = flashLoanService.getFlashLoanStats();
  
  logger.info(`Flash loan providers: ${stats.activeProviders}/${stats.totalProviders} active`);
  logger.info(`Supported networks: ${stats.supportedNetworks.join(', ')}`);
  logger.info(`Supported assets: ${stats.supportedAssets.join(', ')}`);
  
  logger.info('Provider details:');
  stats.providers.forEach(provider => {
    logger.info(`  📊 ${provider.name}:`);
    logger.info(`     Fee rate: ${provider.feeRate / 100}%`);
    logger.info(`     Max liquidity: $${provider.maxLiquidity.toLocaleString()}`);
    logger.info(`     Networks: ${provider.networks.join(', ')}`);
  });
  
  logger.info('==================================');
}

async function runFlashLoanOptimizationExamples(flashLoanService: FlashLoanService) {
  logger.info('=== FLASH LOAN OPTIMIZATION EXAMPLES ===');

  // Example 1: Intra-chain arbitrage opportunity
  const intraChainOpportunity = {
    id: 'example_intra_chain_1',
    type: ArbitrageType.INTRA_CHAIN,
    assets: ['USDC', 'USDT'],
    exchanges: ['Uniswap V3', 'SushiSwap'],
    potentialProfit: 250,
    profitPercentage: 1.5,
    timestamp: Date.now(),
    network: 'ethereum',
    route: { steps: [], totalGasCost: 0, expectedProfit: 250, priceImpact: 0.3 },
    estimatedGas: 180000,
    slippage: 0.3,
    confidence: 90
  };

  try {
    const optimization1 = await flashLoanService.optimizeFlashLoanStrategy(intraChainOpportunity);
    
    logger.info(`📈 Intra-chain optimization result:`);
    logger.info(`   Recommended: ${optimization1.recommendedProvider} (${optimization1.recommendedStrategy})`);
    logger.info(`   Optimal amount: $${optimization1.optimalAmount.toLocaleString()}`);
    logger.info(`   Total cost: $${optimization1.totalCost.toFixed(2)}`);
    logger.info(`   Expected profit: $${optimization1.expectedProfit.toFixed(2)}`);
    logger.info(`   Risk score: ${optimization1.riskScore}/100`);
    logger.info(`   Reasoning: ${optimization1.reasoning}`);
    
    if (optimization1.alternatives.length > 0) {
      logger.info(`   Alternatives:`);
      optimization1.alternatives.forEach((alt, index) => {
        logger.info(`     ${index + 1}. ${alt.provider}: $${alt.totalCost.toFixed(2)} cost`);
      });
    }
  } catch (error) {
    logger.error('Intra-chain optimization failed:', error);
  }

  // Example 2: Cross-chain arbitrage opportunity
  const crossChainOpportunity = {
    id: 'example_cross_chain_1',
    type: ArbitrageType.CROSS_CHAIN,
    assets: ['WETH', 'ETH'],
    exchanges: ['Ethereum Uniswap', 'Polygon QuickSwap'],
    potentialProfit: 500,
    profitPercentage: 2.8,
    timestamp: Date.now(),
    network: 'ethereum',
    route: { steps: [], totalGasCost: 0, expectedProfit: 500, priceImpact: 0.8 },
    estimatedGas: 350000,
    slippage: 0.8,
    confidence: 75
  };

  try {
    const optimization2 = await flashLoanService.optimizeFlashLoanStrategy(crossChainOpportunity);
    
    logger.info(`🌉 Cross-chain optimization result:`);
    logger.info(`   Recommended: ${optimization2.recommendedProvider} (${optimization2.recommendedStrategy})`);
    logger.info(`   Optimal amount: $${optimization2.optimalAmount.toLocaleString()}`);
    logger.info(`   Total cost: $${optimization2.totalCost.toFixed(2)}`);
    logger.info(`   Expected profit: $${optimization2.expectedProfit.toFixed(2)}`);
    logger.info(`   Risk score: ${optimization2.riskScore}/100`);
    logger.info(`   Reasoning: ${optimization2.reasoning}`);
  } catch (error) {
    logger.error('Cross-chain optimization failed:', error);
  }

  logger.info('==========================================');
}

async function simulateFlashLoanArbitrageOpportunities(
  flashLoanService: FlashLoanService,
  preExecutionValidationService: PreExecutionValidationService
) {
  logger.info('=== SIMULATING FLASH LOAN ARBITRAGE OPPORTUNITIES ===');

  // Simulate various opportunity types
  const opportunities = [
    {
      id: 'sim_triangular_1',
      type: ArbitrageType.TRIANGULAR,
      assets: ['USDC', 'WETH', 'WBTC'],
      exchanges: ['Uniswap V3', 'Balancer V2', 'Curve'],
      potentialProfit: 800,
      profitPercentage: 3.2,
      timestamp: Date.now(),
      network: 'ethereum',
      route: { steps: [], totalGasCost: 0, expectedProfit: 800, priceImpact: 1.2 },
      estimatedGas: 420000,
      slippage: 1.2,
      confidence: 85
    },
    {
      id: 'sim_high_volume_1',
      type: ArbitrageType.INTRA_CHAIN,
      assets: ['USDC', 'DAI'],
      exchanges: ['Uniswap V3', 'Balancer V2'],
      potentialProfit: 150,
      profitPercentage: 0.8,
      timestamp: Date.now(),
      network: 'ethereum',
      route: { steps: [], totalGasCost: 0, expectedProfit: 150, priceImpact: 0.2 },
      estimatedGas: 160000,
      slippage: 0.2,
      confidence: 95
    }
  ];

  for (const opportunity of opportunities) {
    try {
      logger.info(`🔄 Simulating opportunity: ${opportunity.id}`);
      
      // Get flash loan optimization
      const flashLoanOptimization = await flashLoanService.optimizeFlashLoanStrategy(opportunity);
      
      // Add optimization to opportunity for validation
      opportunity.validationResult = {
        isValid: true,
        simulatedProfit: 0,
        totalCosts: {
          gasCosts: 0,
          dexFees: 0,
          bridgeFees: 0,
          slippageCosts: 0,
          mevProtectionCosts: 0,
          networkCongestionPenalty: 0,
          flashLoanCosts: 0,
          totalCosts: 0
        },
        profitMargin: 0,
        executionTime: 0,
        riskScore: 0,
        flashLoanOptimization
      };
      
      // Perform validation with flash loan integration
      const validationResult = await preExecutionValidationService.validateOpportunity(opportunity);
      
      logger.info(`   Validation: ${validationResult.isValid ? 'PASSED' : 'FAILED'}`);
      if (validationResult.isValid) {
        logger.info(`   Final profit: $${validationResult.simulatedProfit.toFixed(2)}`);
        logger.info(`   Profit margin: ${validationResult.profitMargin.toFixed(2)}%`);
        logger.info(`   Flash loan provider: ${flashLoanOptimization.recommendedProvider}`);
        logger.info(`   Flash loan cost: $${flashLoanOptimization.totalCost.toFixed(2)}`);
      } else {
        logger.info(`   Failure reason: ${validationResult.reason}`);
      }
      
    } catch (error) {
      logger.error(`Simulation failed for ${opportunity.id}:`, error);
    }
  }

  logger.info('======================================================');
}

// Run the flash loan arbitrage bot
if (require.main === module) {
  runFlashLoanArbitrageBot().catch(console.error);
}
