import { Router } from 'express';
import logger from '../utils/logger.js';
import { MLLearningService } from '../services/MLLearningService.js';
import { StrategySelectionService } from '../services/StrategySelectionService.js';

const router = Router();

let mlLearningService: MLLearningService | null = null;
let strategySelectionService: StrategySelectionService | null = null;

// Initialize services (will be called from server.ts)
export function initializeMLRoutes(mlService: MLLearningService, strategyService: StrategySelectionService) {
  mlLearningService = mlService;
  strategySelectionService = strategyService;
}

// Get strategy performance summary
router.get('/strategy-performance', async (req, res) => {
  try {
    if (!mlLearningService) {
      return res.status(503).json({
        success: false,
        error: 'ML Learning Service not available'
      });
    }

    const summary = await mlLearningService.getStrategyPerformanceSummary();
    
    res.json({
      success: true,
      data: summary || [],
      timestamp: Date.now()
    });

  } catch (error) {
    logger.error('Error fetching strategy performance:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch strategy performance'
    });
  }
});

// Get strategy weights
router.get('/strategy-weights', async (req, res) => {
  try {
    if (!mlLearningService) {
      return res.status(503).json({
        success: false,
        error: 'ML Learning Service not available'
      });
    }

    const weights = mlLearningService.getStrategyWeights();
    const weightsArray = Array.from(weights.values());
    
    res.json({
      success: true,
      data: weightsArray,
      marketRegime: mlLearningService.getCurrentMarketRegime(),
      timestamp: Date.now()
    });

  } catch (error) {
    logger.error('Error fetching strategy weights:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch strategy weights'
    });
  }
});

// Get learning events
router.get('/learning-events', async (req, res) => {
  try {
    if (!mlLearningService) {
      return res.status(503).json({
        success: false,
        error: 'ML Learning Service not available'
      });
    }

    const limit = parseInt(req.query.limit as string) || 50;
    const events = await mlLearningService.getLearningEvents(limit);
    
    res.json({
      success: true,
      data: events,
      timestamp: Date.now()
    });

  } catch (error) {
    logger.error('Error fetching learning events:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch learning events'
    });
  }
});

// Get current market regime
router.get('/market-regime', async (req, res) => {
  try {
    if (!mlLearningService) {
      return res.status(503).json({
        success: false,
        error: 'ML Learning Service not available'
      });
    }

    const currentRegime = mlLearningService.getCurrentMarketRegime();
    
    res.json({
      success: true,
      data: {
        current_regime: currentRegime,
        timestamp: Date.now()
      }
    });

  } catch (error) {
    logger.error('Error fetching market regime:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch market regime'
    });
  }
});

// Get ML learning statistics
router.get('/learning-stats', async (req, res) => {
  try {
    if (!mlLearningService) {
      return res.status(503).json({
        success: false,
        error: 'ML Learning Service not available'
      });
    }

    const weights = mlLearningService.getStrategyWeights();
    const weightsArray = Array.from(weights.values());
    
    // Calculate statistics
    const totalStrategies = weightsArray.length;
    const avgWeight = weightsArray.length > 0 
      ? weightsArray.reduce((sum, w) => sum + w.weight, 0) / weightsArray.length 
      : 0;
    
    const highPerformingStrategies = weightsArray.filter(w => w.recent_performance_score > 70).length;
    const lowPerformingStrategies = weightsArray.filter(w => w.recent_performance_score < 30).length;
    
    const currentRegime = mlLearningService.getCurrentMarketRegime();
    
    res.json({
      success: true,
      data: {
        total_strategies: totalStrategies,
        average_weight: avgWeight,
        high_performing_strategies: highPerformingStrategies,
        low_performing_strategies: lowPerformingStrategies,
        current_market_regime: currentRegime,
        learning_active: true,
        last_updated: new Date().toISOString()
      },
      timestamp: Date.now()
    });

  } catch (error) {
    logger.error('Error fetching learning stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch learning stats'
    });
  }
});

// Get strategy recommendations for given opportunities
router.post('/strategy-recommendation', async (req, res) => {
  try {
    if (!strategySelectionService) {
      return res.status(503).json({
        success: false,
        error: 'Strategy Selection Service not available'
      });
    }

    const { opportunities, marketConditions, network } = req.body;
    
    if (!opportunities || !Array.isArray(opportunities)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid opportunities data'
      });
    }

    const recommendation = await strategySelectionService.selectStrategy(
      opportunities,
      marketConditions || {},
      network || 'ethereum'
    );
    
    res.json({
      success: true,
      data: recommendation,
      timestamp: Date.now()
    });

  } catch (error) {
    logger.error('Error getting strategy recommendation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get strategy recommendation'
    });
  }
});

// Get learning insights and trends
router.get('/learning-insights', async (req, res) => {
  try {
    if (!mlLearningService) {
      return res.status(503).json({
        success: false,
        error: 'ML Learning Service not available'
      });
    }

    const days = parseInt(req.query.days as string) || 7;
    const events = await mlLearningService.getLearningEvents(100);
    const weights = mlLearningService.getStrategyWeights();
    
    // Analyze learning trends
    const recentEvents = events.filter(event => {
      const eventDate = new Date(event.created_at);
      const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      return eventDate >= cutoff;
    });

    const weightUpdates = recentEvents.filter(e => e.event_type === 'weight_update');
    const regimeChanges = recentEvents.filter(e => e.event_type === 'regime_change');
    
    // Calculate improvement trends
    const improvingStrategies = weightUpdates.filter(e => 
      e.new_weight && e.old_weight && e.new_weight > e.old_weight
    ).length;
    
    const decliningStrategies = weightUpdates.filter(e => 
      e.new_weight && e.old_weight && e.new_weight < e.old_weight
    ).length;

    const insights = {
      learning_period_days: days,
      total_learning_events: recentEvents.length,
      weight_updates: weightUpdates.length,
      regime_changes: regimeChanges.length,
      improving_strategies: improvingStrategies,
      declining_strategies: decliningStrategies,
      adaptation_rate: recentEvents.length / days, // Events per day
      most_active_strategies: this.getMostActiveStrategies(weightUpdates),
      recent_regime_changes: regimeChanges.slice(0, 5)
    };
    
    res.json({
      success: true,
      data: insights,
      timestamp: Date.now()
    });

  } catch (error) {
    logger.error('Error fetching learning insights:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch learning insights'
    });
  }
});

// Helper function to get most active strategies
function getMostActiveStrategies(weightUpdates: any[]) {
  const strategyActivity = new Map<string, number>();
  
  weightUpdates.forEach(update => {
    const key = `${update.strategy_type}_${update.network}`;
    strategyActivity.set(key, (strategyActivity.get(key) || 0) + 1);
  });
  
  return Array.from(strategyActivity.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([strategy, count]) => ({ strategy, updates: count }));
}

export default router;
