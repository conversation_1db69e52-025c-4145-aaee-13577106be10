// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

/**
 * @title Flash Loan Provider Interfaces
 * @dev Interfaces for multiple flash loan providers
 */

// Aave V3 Flash Loan Interface
interface IAaveV3Pool {
    function flashLoan(
        address receiverAddress,
        address[] calldata assets,
        uint256[] calldata amounts,
        uint256[] calldata interestRateModes,
        address onBehalfOf,
        bytes calldata params,
        uint16 referralCode
    ) external;

    function flashLoanSimple(
        address receiverAddress,
        address asset,
        uint256 amount,
        bytes calldata params,
        uint16 referralCode
    ) external;

    function FLASHLOAN_PREMIUM_TOTAL() external view returns (uint128);
}

interface IAaveV3FlashLoanReceiver {
    function executeOperation(
        address[] calldata assets,
        uint256[] calldata amounts,
        uint256[] calldata premiums,
        address initiator,
        bytes calldata params
    ) external returns (bool);
}

// Balancer V2 Flash Loan Interface
interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IBalancerFlashLoanReceiver {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

// dYdX Flash Loan Interface
interface IDyDxSoloMargin {
    struct ActionArgs {
        uint8 actionType;
        uint256 accountId;
        uint256 amount;
        address primaryMarketId;
        uint256 secondaryMarketId;
        address otherAddress;
        uint256 otherAccountId;
        bytes data;
    }

    function operate(
        address[] memory accounts,
        ActionArgs[] memory actions
    ) external;
}

interface IDyDxCallee {
    function callFunction(
        address sender,
        uint256 accountId,
        bytes calldata data
    ) external;
}

// Uniswap V3 Flash Swap Interface
interface IUniswapV3Pool {
    function flash(
        address recipient,
        uint256 amount0,
        uint256 amount1,
        bytes calldata data
    ) external;
}

interface IUniswapV3FlashCallback {
    function uniswapV3FlashCallback(
        uint256 fee0,
        uint256 fee1,
        bytes calldata data
    ) external;
}

// Compound Flash Loan Interface (if supported)
interface ICompoundFlashLoan {
    function flashLoan(
        address receiver,
        address asset,
        uint256 amount,
        bytes calldata params
    ) external;
}

interface ICompoundFlashLoanReceiver {
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external returns (bool);
}

// Generic Flash Loan Provider Interface
interface IFlashLoanProvider {
    function getFlashLoanFeeRate() external view returns (uint256);
    function getMaxFlashLoanAmount(address asset) external view returns (uint256);
    function getAvailableLiquidity(address asset) external view returns (uint256);
    function isFlashLoanSupported(address asset) external view returns (bool);
}

// Flash Swap Interface for DEX protocols
interface IFlashSwapProvider {
    function getFlashSwapFee() external view returns (uint256);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function swap(uint amount0Out, uint amount1Out, address to, bytes calldata data) external;
}

// Universal Flash Loan Callback Interface
interface IUniversalFlashLoanCallback {
    function onFlashLoan(
        address provider,
        address asset,
        uint256 amount,
        uint256 fee,
        bytes calldata data
    ) external returns (bool);
}

// Flash Loan Strategy Interface
interface IFlashLoanStrategy {
    enum StrategyType {
        FLASH_LOAN,
        FLASH_SWAP,
        HYBRID
    }

    function executeStrategy(
        StrategyType strategyType,
        address provider,
        address asset,
        uint256 amount,
        bytes calldata strategyData
    ) external returns (bool success, uint256 profit);

    function validateStrategy(
        StrategyType strategyType,
        address provider,
        address asset,
        uint256 amount,
        bytes calldata strategyData
    ) external view returns (bool isValid, string memory reason);
}

// Flash Loan Router Interface
interface IFlashLoanRouter {
    struct FlashLoanParams {
        address provider;
        address asset;
        uint256 amount;
        uint256 maxFee;
        bytes strategyData;
    }

    function executeOptimalFlashLoan(
        FlashLoanParams calldata params
    ) external returns (bool success, uint256 actualFee, uint256 profit);

    function getOptimalProvider(
        address asset,
        uint256 amount
    ) external view returns (address provider, uint256 fee, uint256 availableLiquidity);

    function estimateFlashLoanCost(
        address provider,
        address asset,
        uint256 amount
    ) external view returns (uint256 fee, uint256 gasEstimate);
}

// Multi-Provider Flash Loan Aggregator
interface IFlashLoanAggregator {
    struct ProviderQuote {
        address provider;
        uint256 fee;
        uint256 gasEstimate;
        uint256 availableLiquidity;
        bool isAvailable;
    }

    function getQuotes(
        address asset,
        uint256 amount
    ) external view returns (ProviderQuote[] memory quotes);

    function executeWithBestProvider(
        address asset,
        uint256 amount,
        bytes calldata strategyData
    ) external returns (bool success, address usedProvider, uint256 totalCost);

    function addProvider(
        address provider,
        uint256 priority
    ) external;

    function removeProvider(address provider) external;

    function updateProviderPriority(
        address provider,
        uint256 newPriority
    ) external;
}

// Flash Loan Security Interface
interface IFlashLoanSecurity {
    function validateFlashLoanSecurity(
        address initiator,
        address asset,
        uint256 amount,
        bytes calldata data
    ) external view returns (bool isSecure, string memory reason);

    function checkReentrancyProtection() external view returns (bool);
    
    function validateRepayment(
        address asset,
        uint256 borrowedAmount,
        uint256 fee,
        uint256 currentBalance
    ) external view returns (bool canRepay, uint256 shortfall);

    function emergencyStop() external;
    function resumeOperations() external;
    function isOperational() external view returns (bool);
}

// Flash Loan Analytics Interface
interface IFlashLoanAnalytics {
    struct FlashLoanMetrics {
        uint256 totalVolume;
        uint256 totalFees;
        uint256 successRate;
        uint256 averageAmount;
        uint256 totalTransactions;
    }

    function getProviderMetrics(address provider) external view returns (FlashLoanMetrics memory);
    function getAssetMetrics(address asset) external view returns (FlashLoanMetrics memory);
    function getOverallMetrics() external view returns (FlashLoanMetrics memory);
    
    function recordFlashLoan(
        address provider,
        address asset,
        uint256 amount,
        uint256 fee,
        bool success
    ) external;
}
