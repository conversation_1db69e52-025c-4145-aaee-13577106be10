// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title Governance
 * @dev Contract for managing bot parameters and emergency controls
 */
contract Governance is Ownable, ReentrancyGuard {

    struct Proposal {
        uint256 id;
        address proposer;
        string description;
        bytes callData;
        address target;
        uint256 value;
        uint256 startTime;
        uint256 endTime;
        uint256 forVotes;
        uint256 againstVotes;
        bool executed;
        bool canceled;
        mapping(address => bool) hasVoted;
        mapping(address => bool) voteChoice; // true = for, false = against
    }

    struct Parameter {
        string name;
        uint256 value;
        uint256 minValue;
        uint256 maxValue;
        uint256 lastUpdated;
        address updatedBy;
    }

    mapping(uint256 => Proposal) public proposals;
    mapping(string => Parameter) public parameters;
    mapping(address => uint256) public votingPower;
    mapping(address => bool) public emergencyStoppers;

    uint256 public proposalCount;
    uint256 public votingPeriod = 3 days;
    uint256 public proposalThreshold = 1000; // Minimum voting power to create proposal
    uint256 public quorumThreshold = 5000; // Minimum votes needed for proposal to pass

    bool public emergencyStop = false;

    string[] public parameterNames;

    event ProposalCreated(uint256 indexed proposalId, address indexed proposer, string description);
    event VoteCast(uint256 indexed proposalId, address indexed voter, bool support, uint256 weight);
    event ProposalExecuted(uint256 indexed proposalId);
    event ProposalCanceled(uint256 indexed proposalId);
    event ParameterUpdated(string indexed name, uint256 oldValue, uint256 newValue);
    event EmergencyStopToggled(bool stopped, address indexed triggeredBy);
    event VotingPowerUpdated(address indexed account, uint256 oldPower, uint256 newPower);

    modifier onlyEmergencyStopper() {
        require(emergencyStoppers[msg.sender] || msg.sender == owner(), "Not authorized for emergency stop");
        _;
    }

    modifier whenNotStopped() {
        require(!emergencyStop, "Emergency stop is active");
        _;
    }

    constructor() Ownable(msg.sender) {
        // Initialize default parameters
        _setParameter("minProfitThreshold", 50 * 1e18, 10 * 1e18, 1000 * 1e18);
        _setParameter("maxSlippage", 500, 100, 2000); // basis points
        _setParameter("maxPositionSize", 10000 * 1e18, 1000 * 1e18, 100000 * 1e18);
        _setParameter("gasPriceMultiplier", 110, 100, 200); // percentage
        _setParameter("maxDailyLoss", 1000 * 1e18, 100 * 1e18, 10000 * 1e18);

        // Set initial voting power for deployer
        votingPower[msg.sender] = 10000;
        emergencyStoppers[msg.sender] = true;
    }

    /**
     * @dev Create a new proposal
     */
    function propose(
        string memory description,
        address target,
        uint256 value,
        bytes memory callData
    ) external whenNotStopped returns (uint256) {
        require(votingPower[msg.sender] >= proposalThreshold, "Insufficient voting power");

        uint256 proposalId = proposalCount++;
        Proposal storage proposal = proposals[proposalId];

        proposal.id = proposalId;
        proposal.proposer = msg.sender;
        proposal.description = description;
        proposal.target = target;
        proposal.value = value;
        proposal.callData = callData;
        proposal.startTime = block.timestamp;
        proposal.endTime = block.timestamp + votingPeriod;

        emit ProposalCreated(proposalId, msg.sender, description);

        return proposalId;
    }

    /**
     * @dev Cast a vote on a proposal
     */
    function castVote(uint256 proposalId, bool support) external whenNotStopped {
        Proposal storage proposal = proposals[proposalId];
        require(block.timestamp >= proposal.startTime, "Voting not started");
        require(block.timestamp <= proposal.endTime, "Voting ended");
        require(!proposal.hasVoted[msg.sender], "Already voted");
        require(votingPower[msg.sender] > 0, "No voting power");

        proposal.hasVoted[msg.sender] = true;
        proposal.voteChoice[msg.sender] = support;

        if (support) {
            proposal.forVotes += votingPower[msg.sender];
        } else {
            proposal.againstVotes += votingPower[msg.sender];
        }

        emit VoteCast(proposalId, msg.sender, support, votingPower[msg.sender]);
    }

    /**
     * @dev Execute a proposal if it has passed
     */
    function executeProposal(uint256 proposalId) external whenNotStopped {
        Proposal storage proposal = proposals[proposalId];
        require(block.timestamp > proposal.endTime, "Voting still active");
        require(!proposal.executed, "Already executed");
        require(!proposal.canceled, "Proposal canceled");
        require(proposal.forVotes > proposal.againstVotes, "Proposal failed");
        require(proposal.forVotes >= quorumThreshold, "Quorum not reached");

        proposal.executed = true;

        if (proposal.target != address(0)) {
            (bool success,) = proposal.target.call{value: proposal.value}(proposal.callData);
            require(success, "Proposal execution failed");
        }

        emit ProposalExecuted(proposalId);
    }

    /**
     * @dev Cancel a proposal (only by proposer or owner)
     */
    function cancelProposal(uint256 proposalId) external {
        Proposal storage proposal = proposals[proposalId];
        require(
            msg.sender == proposal.proposer || msg.sender == owner(),
            "Not authorized to cancel"
        );
        require(!proposal.executed, "Already executed");
        require(!proposal.canceled, "Already canceled");

        proposal.canceled = true;
        emit ProposalCanceled(proposalId);
    }

    /**
     * @dev Update a parameter (only through governance or owner)
     */
    function updateParameter(string memory name, uint256 value) external {
        require(
            msg.sender == owner() || msg.sender == address(this),
            "Only governance can update parameters"
        );

        Parameter storage param = parameters[name];
        require(bytes(param.name).length > 0, "Parameter does not exist");
        require(value >= param.minValue && value <= param.maxValue, "Value out of range");

        uint256 oldValue = param.value;
        param.value = value;
        param.lastUpdated = block.timestamp;
        param.updatedBy = msg.sender;

        emit ParameterUpdated(name, oldValue, value);
    }

    /**
     * @dev Set voting power for an address (only owner)
     */
    function setVotingPower(address account, uint256 power) external onlyOwner {
        uint256 oldPower = votingPower[account];
        votingPower[account] = power;
        emit VotingPowerUpdated(account, oldPower, power);
    }

    /**
     * @dev Add emergency stopper
     */
    function addEmergencyStopper(address stopper) external onlyOwner {
        emergencyStoppers[stopper] = true;
    }

    /**
     * @dev Remove emergency stopper
     */
    function removeEmergencyStopper(address stopper) external onlyOwner {
        emergencyStoppers[stopper] = false;
    }

    /**
     * @dev Toggle emergency stop
     */
    function toggleEmergencyStop() external onlyEmergencyStopper {
        emergencyStop = !emergencyStop;
        emit EmergencyStopToggled(emergencyStop, msg.sender);
    }

    /**
     * @dev Get parameter value
     */
    function getParameter(string memory name) external view returns (uint256) {
        return parameters[name].value;
    }

    /**
     * @dev Get proposal details
     */
    function getProposal(uint256 proposalId) external view returns (
        address proposer,
        string memory description,
        uint256 startTime,
        uint256 endTime,
        uint256 forVotes,
        uint256 againstVotes,
        bool executed,
        bool canceled
    ) {
        Proposal storage proposal = proposals[proposalId];
        return (
            proposal.proposer,
            proposal.description,
            proposal.startTime,
            proposal.endTime,
            proposal.forVotes,
            proposal.againstVotes,
            proposal.executed,
            proposal.canceled
        );
    }

    /**
     * @dev Internal function to set parameter
     */
    function _setParameter(string memory name, uint256 value, uint256 minValue, uint256 maxValue) internal {
        parameters[name] = Parameter({
            name: name,
            value: value,
            minValue: minValue,
            maxValue: maxValue,
            lastUpdated: block.timestamp,
            updatedBy: msg.sender
        });
        parameterNames.push(name);
    }

    /**
     * @dev Get all parameter names
     */
    function getParameterNames() external view returns (string[] memory) {
        return parameterNames;
    }
}
