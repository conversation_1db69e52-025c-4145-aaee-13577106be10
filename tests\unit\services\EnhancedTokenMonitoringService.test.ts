/**
 * Unit Tests for Enhanced Token Monitoring Service
 * 
 * Tests token monitoring functionality including:
 * - Multi-chain token monitoring across 10 networks
 * - Top 50 verified tokens tracking
 * - Price update latency (<5s target)
 * - Market data aggregation and validation
 * - Network health monitoring
 */

import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { EnhancedTokenMonitoringService } from '../../../backend/services/EnhancedTokenMonitoringService.js';

// Mock dependencies
jest.mock('../../../backend/utils/logger.js', () => ({
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

jest.mock('../../../backend/services/EnhancedCacheService.js', () => ({
  enhancedCacheService: {
    get: jest.fn(),
    set: jest.fn(),
    getMetrics: jest.fn(() => ({ hitRatio: 0.85 }))
  }
}));

jest.mock('../../../backend/services/DataRoutingService.js', () => ({
  dataRoutingService: {
    routeData: jest.fn(() => Promise.resolve(true))
  }
}));

// Mock external price APIs
jest.mock('axios', () => ({
  default: {
    get: jest.fn(() => Promise.resolve({
      data: {
        ethereum: { usd: 2000, usd_24h_change: 5.2 },
        bitcoin: { usd: 45000, usd_24h_change: 2.1 }
      }
    }))
  }
}));

describe('EnhancedTokenMonitoringService', () => {
  let service: EnhancedTokenMonitoringService;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new EnhancedTokenMonitoringService();
  });

  afterEach(async () => {
    if (service) {
      await service.shutdown();
    }
  });

  describe('Initialization', () => {
    it('should initialize with supported networks', async () => {
      await service.initialize();
      
      const stats = service.getMonitoringStats();
      expect(stats.supportedNetworks).toContain('ethereum');
      expect(stats.supportedNetworks).toContain('bsc');
      expect(stats.supportedNetworks).toContain('polygon');
      expect(stats.supportedNetworks).toContain('solana');
      expect(stats.supportedNetworks).toContain('avalanche');
      expect(stats.supportedNetworks).toContain('arbitrum');
      expect(stats.supportedNetworks).toContain('optimism');
      expect(stats.supportedNetworks).toContain('base');
      expect(stats.supportedNetworks).toContain('fantom');
      expect(stats.supportedNetworks).toContain('sui');
    });

    it('should load top 50 verified tokens', async () => {
      await service.initialize();
      
      const stats = service.getMonitoringStats();
      expect(stats.totalTokensMonitored).toBeGreaterThan(0);
      expect(stats.totalTokensMonitored).toBeLessThanOrEqual(50);
    });

    it('should start monitoring intervals', async () => {
      await service.initialize();
      
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });
  });

  describe('Token Management', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should add tokens to monitoring list', async () => {
      const tokenData = {
        address: '******************************************',
        symbol: 'TEST',
        name: 'Test Token',
        decimals: 18,
        networks: ['ethereum'],
        marketCap: 1000000,
        volume24h: 500000,
        isVerified: true
      };

      const result = await service.addTokenToMonitoring(tokenData);
      expect(result).toBe(true);

      const stats = service.getMonitoringStats();
      expect(stats.totalTokensMonitored).toBeGreaterThan(0);
    });

    it('should remove tokens from monitoring', async () => {
      const tokenAddress = '******************************************';
      
      // First add a token
      await service.addTokenToMonitoring({
        address: tokenAddress,
        symbol: 'TEST',
        name: 'Test Token',
        decimals: 18,
        networks: ['ethereum'],
        marketCap: 1000000,
        volume24h: 500000,
        isVerified: true
      });

      const result = await service.removeTokenFromMonitoring(tokenAddress);
      expect(result).toBe(true);
    });

    it('should validate token data before adding', async () => {
      const invalidTokenData = {
        address: 'invalid-address',
        symbol: '',
        name: '',
        decimals: -1,
        networks: [],
        marketCap: -1000,
        volume24h: -500,
        isVerified: false
      };

      const result = await service.addTokenToMonitoring(invalidTokenData);
      expect(result).toBe(false);
    });
  });

  describe('Price Monitoring', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should update token prices within latency target', async () => {
      const startTime = Date.now();
      await service.updateTokenPrices();
      const endTime = Date.now();

      const latency = endTime - startTime;
      expect(latency).toBeLessThan(5000); // 5 second target
    });

    it('should handle price update failures gracefully', async () => {
      // Mock axios to throw an error
      const axios = require('axios');
      axios.default.get.mockRejectedValueOnce(new Error('API Error'));

      await service.updateTokenPrices();
      
      // Service should still be healthy despite API error
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should cache price data appropriately', async () => {
      const { enhancedCacheService } = require('../../../backend/services/EnhancedCacheService.js');
      
      await service.updateTokenPrices();
      
      expect(enhancedCacheService.set).toHaveBeenCalled();
    });

    it('should route price data to storage', async () => {
      const { dataRoutingService } = require('../../../backend/services/DataRoutingService.js');
      
      await service.updateTokenPrices();
      
      expect(dataRoutingService.routeData).toHaveBeenCalled();
    });
  });

  describe('Multi-Chain Support', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should monitor tokens across multiple networks', async () => {
      const stats = service.getMonitoringStats();
      
      expect(stats.networkStats.size).toBeGreaterThan(1);
      
      // Check that major networks are being monitored
      expect(stats.networkStats.has('ethereum')).toBe(true);
      expect(stats.networkStats.has('bsc')).toBe(true);
      expect(stats.networkStats.has('polygon')).toBe(true);
    });

    it('should track network-specific metrics', async () => {
      const stats = service.getMonitoringStats();
      
      stats.networkStats.forEach((networkStat, network) => {
        expect(networkStat).toHaveProperty('tokensMonitored');
        expect(networkStat).toHaveProperty('priceUpdates');
        expect(networkStat).toHaveProperty('lastUpdate');
        expect(networkStat).toHaveProperty('averageLatency');
        expect(networkStat).toHaveProperty('isHealthy');
      });
    });

    it('should handle network-specific failures', async () => {
      // Simulate network failure for one network
      const stats = service.getMonitoringStats();
      const networkCount = stats.networkStats.size;

      // Service should continue monitoring other networks
      await service.updateTokenPrices();
      
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });
  });

  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should track price update latency', async () => {
      await service.updateTokenPrices();
      
      const stats = service.getMonitoringStats();
      stats.networkStats.forEach((networkStat) => {
        expect(networkStat.averageLatency).toBeGreaterThanOrEqual(0);
      });
    });

    it('should maintain latency under target', async () => {
      await service.updateTokenPrices();
      
      const stats = service.getMonitoringStats();
      stats.networkStats.forEach((networkStat) => {
        expect(networkStat.averageLatency).toBeLessThan(5000); // 5 second target
      });
    });

    it('should track update frequency', async () => {
      const initialStats = service.getMonitoringStats();
      
      await service.updateTokenPrices();
      
      const updatedStats = service.getMonitoringStats();
      updatedStats.networkStats.forEach((networkStat, network) => {
        const initialStat = initialStats.networkStats.get(network);
        if (initialStat) {
          expect(networkStat.priceUpdates).toBeGreaterThanOrEqual(initialStat.priceUpdates);
        }
      });
    });
  });

  describe('Market Data Validation', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should validate price data before storage', async () => {
      const mockPriceData = {
        symbol: 'ETH',
        price: 2000,
        volume24h: 1000000,
        change24h: 5.2,
        marketCap: 240000000000,
        timestamp: Date.now()
      };

      const isValid = await service.validatePriceData(mockPriceData);
      expect(isValid).toBe(true);
    });

    it('should reject invalid price data', async () => {
      const invalidPriceData = {
        symbol: '',
        price: -1000,
        volume24h: -500000,
        change24h: NaN,
        marketCap: -1000000,
        timestamp: 0
      };

      const isValid = await service.validatePriceData(invalidPriceData);
      expect(isValid).toBe(false);
    });

    it('should handle extreme price changes', async () => {
      const extremePriceData = {
        symbol: 'ETH',
        price: 20000000, // Extremely high price
        volume24h: 1000000,
        change24h: 1000, // 1000% change
        marketCap: 240000000000,
        timestamp: Date.now()
      };

      const isValid = await service.validatePriceData(extremePriceData);
      expect(isValid).toBe(false);
    });
  });

  describe('Health Monitoring', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should report healthy status when all networks are operational', () => {
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should return monitoring statistics', () => {
      const stats = service.getMonitoringStats();
      
      expect(stats).toHaveProperty('totalTokensMonitored');
      expect(stats).toHaveProperty('supportedNetworks');
      expect(stats).toHaveProperty('networkStats');
      expect(stats).toHaveProperty('lastGlobalUpdate');
      expect(stats).toHaveProperty('isHealthy');
    });

    it('should track service uptime', () => {
      const stats = service.getMonitoringStats();
      expect(stats.lastGlobalUpdate).toBeGreaterThan(0);
    });
  });

  describe('Configuration', () => {
    it('should use configurable update intervals', async () => {
      await service.initialize();
      
      // Service should respect configured intervals
      const stats = service.getMonitoringStats();
      expect(stats.supportedNetworks.length).toBe(10); // 10 networks as configured
    });

    it('should use configurable token limits', async () => {
      await service.initialize();
      
      const stats = service.getMonitoringStats();
      expect(stats.totalTokensMonitored).toBeLessThanOrEqual(50); // Top 50 tokens limit
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should handle API rate limiting', async () => {
      // Mock rate limit error
      const axios = require('axios');
      axios.default.get.mockRejectedValueOnce({
        response: { status: 429, statusText: 'Too Many Requests' }
      });

      await service.updateTokenPrices();
      
      // Service should handle rate limiting gracefully
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should handle network timeouts', async () => {
      // Mock timeout error
      const axios = require('axios');
      axios.default.get.mockRejectedValueOnce(new Error('ETIMEDOUT'));

      await service.updateTokenPrices();
      
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should handle malformed API responses', async () => {
      // Mock malformed response
      const axios = require('axios');
      axios.default.get.mockResolvedValueOnce({
        data: 'invalid json response'
      });

      await service.updateTokenPrices();
      
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });
  });
});
