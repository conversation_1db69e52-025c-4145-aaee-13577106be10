'use client';

import { useState, useMemo } from 'react';
import { 
  Zap, 
  DollarSign, 
  Clock, 
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Filter,
  RefreshCw,
  ExternalLink,
  Info
} from 'lucide-react';

import { FlashLoanQuote } from '@/types';
import { useFlashLoanQuotes } from '@/hooks/useApi';
import { 
  formatCurrency, 
  formatPercentage, 
  formatNumber,
  formatTimeAgo,
  formatDuration,
  getProfitColor,
  getStatusColor
} from '@/lib/utils';
import { LoadingSpinner, SkeletonTable } from '@/components/ui/LoadingSpinner';

interface FlashLoanDisplayProps {
  asset?: string;
  amount?: string;
  network?: string;
  detailed?: boolean;
}

interface ProviderCardProps {
  quote: FlashLoanQuote;
  isSelected?: boolean;
  onSelect?: () => void;
}

function ProviderCard({ quote, isSelected = false, onSelect }: ProviderCardProps) {
  const isAvailable = quote.is_available || quote.isAvailable;
  const feePercentage = quote.fee_percentage || quote.feePercentage || 0;
  const availableLiquidity = quote.available_liquidity || quote.availableLiquidity || 0;
  const executionTime = quote.execution_time || quote.executionTime || 0;
  const gasEstimate = quote.gas_estimate || quote.gasEstimate || 0;

  const providerNames = {
    aave_v3: 'Aave V3',
    balancer_v2: 'Balancer V2',
    dydx: 'dYdX',
    uniswap_v3: 'Uniswap V3',
  };

  const providerName = providerNames[quote.provider] || quote.provider;
  const statusColor = isAvailable ? 'text-success-400' : 'text-error-400';
  const StatusIcon = isAvailable ? CheckCircle : XCircle;

  return (
    <div 
      className={`
        glass-effect rounded-lg p-4 cursor-pointer transition-all duration-200
        ${isSelected 
          ? 'ring-2 ring-primary-500 bg-primary-500/10' 
          : 'hover:bg-dark-700/30'
        }
        ${!isAvailable && 'opacity-60'}
      `}
      onClick={onSelect}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Zap className="w-4 h-4 text-primary-400" />
          <span className="font-medium text-dark-100">{providerName}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <StatusIcon className={`w-4 h-4 ${statusColor}`} />
          <span className={`text-xs font-medium ${statusColor}`}>
            {isAvailable ? 'Available' : 'Unavailable'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <div className="text-xs text-dark-500 mb-1">Fee</div>
          <div className="text-sm font-medium text-warning-400">
            {formatPercentage(feePercentage)}
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Fee Amount</div>
          <div className="text-sm font-medium text-dark-200">
            {formatCurrency(quote.fee)}
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Liquidity</div>
          <div className="text-sm font-medium text-dark-200">
            {formatCurrency(availableLiquidity)}
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Gas Est.</div>
          <div className="text-sm font-medium text-dark-200">
            {formatNumber(gasEstimate)}
          </div>
        </div>
      </div>

      <div className="pt-3 border-t border-dark-700">
        <div className="flex items-center justify-between">
          <span className="text-xs text-dark-500">Execution Time</span>
          <span className="text-xs text-dark-300">
            {formatDuration(executionTime * 1000)}
          </span>
        </div>
      </div>
    </div>
  );
}

interface FlashLoanFormProps {
  onQuoteRequest: (params: { asset: string; amount: string; network: string }) => void;
  isLoading?: boolean;
}

function FlashLoanForm({ onQuoteRequest, isLoading = false }: FlashLoanFormProps) {
  const [asset, setAsset] = useState('USDC');
  const [amount, setAmount] = useState('10000');
  const [network, setNetwork] = useState('ethereum');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onQuoteRequest({ asset, amount, network });
  };

  const popularAssets = ['USDC', 'USDT', 'DAI', 'WETH', 'WBTC'];
  const supportedNetworks = [
    { id: 'ethereum', name: 'Ethereum' },
    { id: 'polygon', name: 'Polygon' },
    { id: 'arbitrum', name: 'Arbitrum' },
    { id: 'optimism', name: 'Optimism' },
  ];

  return (
    <div className="glass-effect rounded-lg p-4 mb-6">
      <h4 className="text-lg font-semibold text-dark-100 mb-4">
        Get Flash Loan Quotes
      </h4>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="asset-select" className="block text-sm font-medium text-dark-300 mb-2">
              Asset
            </label>
            <select
              id="asset-select"
              value={asset}
              onChange={(e) => setAsset(e.target.value)}
              className="input-field w-full"
              aria-label="Select asset for flash loan"
            >
              {popularAssets.map(a => (
                <option key={a} value={a}>{a}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="amount-input" className="block text-sm font-medium text-dark-300 mb-2">
              Amount
            </label>
            <input
              id="amount-input"
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="input-field w-full"
              placeholder="Enter amount"
              min="1"
              step="0.01"
              aria-label="Enter flash loan amount"
            />
          </div>

          <div>
            <label htmlFor="network-select" className="block text-sm font-medium text-dark-300 mb-2">
              Network
            </label>
            <select
              id="network-select"
              value={network}
              onChange={(e) => setNetwork(e.target.value)}
              className="input-field w-full"
              aria-label="Select blockchain network"
            >
              {supportedNetworks.map(n => (
                <option key={n.id} value={n.id}>{n.name}</option>
              ))}
            </select>
          </div>
        </div>
        
        <button
          type="submit"
          disabled={isLoading || !asset || !amount || !network}
          className="btn-primary w-full"
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Getting Quotes...
            </>
          ) : (
            <>
              <Zap className="w-4 h-4 mr-2" />
              Get Flash Loan Quotes
            </>
          )}
        </button>
      </form>
    </div>
  );
}

export function FlashLoanDisplay({ 
  asset: initialAsset = 'USDC',
  amount: initialAmount = '10000',
  network: initialNetwork = 'ethereum',
  detailed = false 
}: FlashLoanDisplayProps) {
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [quoteParams, setQuoteParams] = useState({
    asset: initialAsset,
    amount: initialAmount,
    network: initialNetwork,
  });

  const { data: quotes = [], isLoading, error } = useFlashLoanQuotes(quoteParams);

  // Sort quotes by best terms (lowest fee, highest liquidity)
  const sortedQuotes = useMemo(() => {
    return [...quotes].sort((a, b) => {
      // First prioritize availability
      const aAvailable = a.is_available || a.isAvailable;
      const bAvailable = b.is_available || b.isAvailable;
      
      if (aAvailable !== bAvailable) {
        return bAvailable ? 1 : -1;
      }
      
      // Then sort by fee percentage (lower is better)
      const aFee = a.fee_percentage || a.feePercentage || 0;
      const bFee = b.fee_percentage || b.feePercentage || 0;
      
      return aFee - bFee;
    });
  }, [quotes]);

  const bestQuote = sortedQuotes.find(q => q.is_available || q.isAvailable);

  const handleQuoteRequest = (params: { asset: string; amount: string; network: string }) => {
    setQuoteParams(params);
    setSelectedProvider(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Zap className="w-6 h-6 text-primary-400" />
          <div>
            <h3 className="text-lg font-semibold text-dark-100">
              Flash Loan Quotes
            </h3>
            <p className="text-sm text-dark-400">
              Compare flash loan providers and fees
            </p>
          </div>
        </div>
        
        <button
          type="button"
          className="btn-secondary text-sm"
          aria-label="Refresh flash loan quotes"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh Quotes
        </button>
      </div>

      {/* Quote Request Form */}
      <FlashLoanForm onQuoteRequest={handleQuoteRequest} isLoading={isLoading} />

      {/* Best Quote Summary */}
      {bestQuote && (
        <div className="glass-effect rounded-lg p-4 border border-success-500/30">
          <div className="flex items-center space-x-2 mb-3">
            <CheckCircle className="w-5 h-5 text-success-400" />
            <span className="font-medium text-success-400">Best Available Quote</span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <div className="text-xs text-dark-500 mb-1">Provider</div>
              <div className="text-sm font-medium text-dark-100">
                {bestQuote.provider.replace('_', ' ').toUpperCase()}
              </div>
            </div>
            
            <div>
              <div className="text-xs text-dark-500 mb-1">Fee</div>
              <div className="text-sm font-medium text-warning-400">
                {formatPercentage(bestQuote.fee_percentage || bestQuote.feePercentage || 0)}
              </div>
            </div>
            
            <div>
              <div className="text-xs text-dark-500 mb-1">Fee Amount</div>
              <div className="text-sm font-medium text-dark-200">
                {formatCurrency(bestQuote.fee)}
              </div>
            </div>
            
            <div>
              <div className="text-xs text-dark-500 mb-1">Available</div>
              <div className="text-sm font-medium text-success-400">
                {formatCurrency(bestQuote.available_liquidity || bestQuote.availableLiquidity || 0)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="glass-effect rounded-lg p-4 border border-error-500/30">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-error-400" />
            <span className="font-medium text-error-400">Error Loading Quotes</span>
          </div>
          <p className="text-sm text-dark-300">
            Failed to fetch flash loan quotes. Please try again.
          </p>
        </div>
      )}

      {/* Quotes Grid */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="glass-effect rounded-lg p-4">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-dark-700 rounded w-3/4"></div>
                  <div className="h-3 bg-dark-700 rounded w-1/2"></div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="h-3 bg-dark-700 rounded"></div>
                    <div className="h-3 bg-dark-700 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : quotes.length === 0 ? (
          <div className="text-center py-12">
            <Zap className="w-12 h-12 text-dark-600 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-dark-300 mb-2">
              No Quotes Available
            </h4>
            <p className="text-dark-500">
              No flash loan quotes found for the specified parameters.
            </p>
          </div>
        ) : (
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-dark-100">
                Available Providers ({quotes.length})
              </h4>
              <div className="text-sm text-dark-400">
                Sorted by best terms
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sortedQuotes.map((quote) => (
                <ProviderCard
                  key={quote.provider}
                  quote={quote}
                  isSelected={selectedProvider === quote.provider}
                  onSelect={() => setSelectedProvider(quote.provider)}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Provider Comparison Table */}
      {detailed && quotes.length > 0 && (
        <div className="glass-effect rounded-xl p-6">
          <h4 className="text-lg font-semibold text-dark-100 mb-4">
            Provider Comparison
          </h4>

          <div className="overflow-x-auto">
            <table className="data-table">
              <thead>
                <tr>
                  <th>Provider</th>
                  <th>Status</th>
                  <th>Fee %</th>
                  <th>Fee Amount</th>
                  <th>Available Liquidity</th>
                  <th>Gas Estimate</th>
                  <th>Execution Time</th>
                </tr>
              </thead>
              <tbody>
                {sortedQuotes.map((quote) => {
                  const isAvailable = quote.is_available || quote.isAvailable;
                  const statusColor = isAvailable ? 'text-success-400' : 'text-error-400';

                  return (
                    <tr key={quote.provider}>
                      <td>
                        <div className="flex items-center space-x-2">
                          <Zap className="w-4 h-4 text-primary-400" />
                          <span className="font-medium">
                            {quote.provider.replace('_', ' ').toUpperCase()}
                          </span>
                        </div>
                      </td>
                      <td>
                        <span className={`text-sm font-medium ${statusColor}`}>
                          {isAvailable ? 'Available' : 'Unavailable'}
                        </span>
                      </td>
                      <td className="text-warning-400 font-medium">
                        {formatPercentage(quote.fee_percentage || quote.feePercentage || 0)}
                      </td>
                      <td>{formatCurrency(quote.fee)}</td>
                      <td>{formatCurrency(quote.available_liquidity || quote.availableLiquidity || 0)}</td>
                      <td>{formatNumber(quote.gas_estimate || quote.gasEstimate || 0)}</td>
                      <td>{formatDuration((quote.execution_time || quote.executionTime || 0) * 1000)}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Info Panel */}
      <div className="glass-effect rounded-lg p-4 border border-primary-500/30">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-primary-400 mt-0.5" />
          <div>
            <h5 className="font-medium text-primary-400 mb-2">Flash Loan Information</h5>
            <div className="text-sm text-dark-300 space-y-1">
              <p>• Flash loans allow borrowing without collateral, but must be repaid in the same transaction</p>
              <p>• Fees are typically 0.05% - 0.09% of the borrowed amount</p>
              <p>• Gas costs and execution time vary by provider and network congestion</p>
              <p>• Always simulate transactions before execution to ensure profitability</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
