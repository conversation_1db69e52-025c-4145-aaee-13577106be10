import { EventEmitter } from 'events';
import WebSocket from 'ws';
import axios from 'axios';
import { createClient } from 'redis';
import logger from '../utils/logger.js';
import config from '../config/index.js';

export interface PriceData {
  symbol: string;
  price: number;
  volume24h: number;
  change24h: number;
  timestamp: number;
  source: string;
  network: string;
}

export interface DEXPriceData extends PriceData {
  exchange: string;
  pair: string;
  liquidity: number;
  reserves: {
    token0: number;
    token1: number;
  };
}

export class PriceFeedService extends EventEmitter {
  private redisClient: any;
  private dexConnections: Map<string, WebSocket> = new Map();
  private priceCache: Map<string, PriceData> = new Map();
  private updateInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // DEX WebSocket endpoints (these would be real endpoints in production)
  private dexEndpoints = {
    uniswap: 'wss://api.uniswap.org/v1/graphql',
    sushiswap: 'wss://api.sushi.com/graphql',
    balancer: 'wss://api.balancer.fi/graphql',
    curve: 'wss://api.curve.fi/v1/graphql'
  };

  constructor() {
    super();
    this.initializeRedis();
  }

  private async initializeRedis() {
    try {
      this.redisClient = createClient({
        url: config.REDIS_URL
      });

      this.redisClient.on('error', (err: any) => {
        logger.error('Redis Client Error:', err);
      });

      this.redisClient.on('connect', () => {
        logger.info('Connected to Redis');
      });

      await this.redisClient.connect();
    } catch (error) {
      logger.error('Failed to initialize Redis:', error);
    }
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Price Feed Service...');
    this.isRunning = true;

    // Connect to DEX WebSockets
    await this.connectToDEXs();

    // Start price update interval
    this.updateInterval = setInterval(() => {
      this.updatePrices();
    }, 5000); // Update every 5 seconds

    // Initial price fetch
    await this.updatePrices();

    logger.info('Price Feed Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Price Feed Service...');
    this.isRunning = false;

    // Close DEX connections
    this.dexConnections.forEach((ws, exchange) => {
      ws.close();
    });
    this.dexConnections.clear();

    // Clear update interval
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }

    // Close Redis connection
    if (this.redisClient) {
      await this.redisClient.quit();
    }

    logger.info('Price Feed Service stopped');
  }

  private async connectToDEXs() {
    for (const [exchange, endpoint] of Object.entries(this.dexEndpoints)) {
      try {
        await this.connectToDEX(exchange, endpoint);
      } catch (error) {
        logger.error(`Failed to connect to ${exchange}:`, error);
      }
    }
  }

  private async connectToDEX(exchange: string, endpoint: string) {
    return new Promise<void>((resolve, reject) => {
      try {
        // For demo purposes, we'll simulate WebSocket connections
        // In production, these would be real WebSocket connections to DEX APIs
        
        logger.info(`Connecting to ${exchange} at ${endpoint}`);
        
        // Simulate connection success
        setTimeout(() => {
          logger.info(`Connected to ${exchange}`);
          this.startSimulatedPriceUpdates(exchange);
          resolve();
        }, 1000);

      } catch (error) {
        logger.error(`Error connecting to ${exchange}:`, error);
        reject(error);
      }
    });
  }

  private startSimulatedPriceUpdates(exchange: string) {
    // Simulate real-time price updates
    const symbols = ['ETH/USDC', 'WBTC/ETH', 'UNI/ETH', 'AAVE/ETH', 'MATIC/USDC'];
    
    setInterval(() => {
      symbols.forEach(symbol => {
        const basePrice = this.getBasePrice(symbol);
        const price = basePrice * (0.98 + Math.random() * 0.04); // ±2% variation
        
        const priceData: DEXPriceData = {
          symbol,
          price,
          volume24h: Math.random() * 10000000,
          change24h: (Math.random() - 0.5) * 10,
          timestamp: Date.now(),
          source: 'dex',
          network: 'ethereum',
          exchange,
          pair: symbol,
          liquidity: Math.random() * 5000000,
          reserves: {
            token0: Math.random() * 1000000,
            token1: Math.random() * 1000000
          }
        };

        this.updatePriceData(priceData);
      });
    }, 2000 + Math.random() * 3000); // Random interval between 2-5 seconds
  }

  private getBasePrice(symbol: string): number {
    const basePrices: Record<string, number> = {
      'ETH/USDC': 2000,
      'WBTC/ETH': 15,
      'UNI/ETH': 0.003,
      'AAVE/ETH': 0.05,
      'MATIC/USDC': 0.8
    };
    return basePrices[symbol] || 1;
  }

  private async updatePrices() {
    try {
      // Fetch prices from external APIs (CoinGecko, etc.)
      await this.fetchExternalPrices();
      
      // Aggregate and cache prices
      await this.aggregatePrices();
      
    } catch (error) {
      logger.error('Error updating prices:', error);
    }
  }

  private async fetchExternalPrices() {
    try {
      // This would fetch from real APIs like CoinGecko
      // For demo, we'll simulate the data
      
      const symbols = ['ethereum', 'bitcoin', 'uniswap', 'aave-token', 'matic-network'];
      
      for (const symbol of symbols) {
        const priceData: PriceData = {
          symbol: symbol.toUpperCase(),
          price: Math.random() * 1000,
          volume24h: Math.random() * 1000000000,
          change24h: (Math.random() - 0.5) * 20,
          timestamp: Date.now(),
          source: 'coingecko',
          network: 'ethereum'
        };

        this.updatePriceData(priceData);
      }

    } catch (error) {
      logger.error('Error fetching external prices:', error);
    }
  }

  private updatePriceData(priceData: PriceData) {
    const key = `${priceData.symbol}_${priceData.source}`;
    this.priceCache.set(key, priceData);

    // Cache in Redis
    if (this.redisClient) {
      this.redisClient.setEx(
        `price:${key}`,
        300, // 5 minutes TTL
        JSON.stringify(priceData)
      ).catch((error: any) => {
        logger.error('Error caching price data:', error);
      });
    }

    // Emit price update event
    this.emit('priceUpdate', priceData);
  }

  private async aggregatePrices() {
    try {
      // Group prices by symbol
      const priceGroups: Map<string, PriceData[]> = new Map();
      
      this.priceCache.forEach((priceData, key) => {
        const symbol = priceData.symbol;
        if (!priceGroups.has(symbol)) {
          priceGroups.set(symbol, []);
        }
        priceGroups.get(symbol)!.push(priceData);
      });

      // Calculate aggregated prices
      priceGroups.forEach((prices, symbol) => {
        if (prices.length > 1) {
          const aggregatedPrice = this.calculateAggregatedPrice(prices);
          this.emit('aggregatedPrice', aggregatedPrice);
        }
      });

    } catch (error) {
      logger.error('Error aggregating prices:', error);
    }
  }

  private calculateAggregatedPrice(prices: PriceData[]): PriceData {
    // Calculate volume-weighted average price
    let totalValue = 0;
    let totalVolume = 0;
    let totalChange = 0;

    prices.forEach(price => {
      totalValue += price.price * price.volume24h;
      totalVolume += price.volume24h;
      totalChange += price.change24h;
    });

    const weightedPrice = totalVolume > 0 ? totalValue / totalVolume : 0;
    const avgChange = prices.length > 0 ? totalChange / prices.length : 0;

    return {
      symbol: prices[0].symbol,
      price: weightedPrice,
      volume24h: totalVolume,
      change24h: avgChange,
      timestamp: Date.now(),
      source: 'aggregated',
      network: prices[0].network
    };
  }

  public async getPrice(symbol: string, source?: string): Promise<PriceData | null> {
    try {
      if (source) {
        const key = `${symbol}_${source}`;
        return this.priceCache.get(key) || null;
      }

      // Return the most recent price from any source
      const prices = Array.from(this.priceCache.values())
        .filter(p => p.symbol === symbol)
        .sort((a, b) => b.timestamp - a.timestamp);

      return prices[0] || null;

    } catch (error) {
      logger.error(`Error getting price for ${symbol}:`, error);
      return null;
    }
  }

  public async getPriceFromRedis(symbol: string, source?: string): Promise<PriceData | null> {
    try {
      if (!this.redisClient) return null;

      const key = source ? `price:${symbol}_${source}` : `price:${symbol}_*`;
      
      if (source) {
        const data = await this.redisClient.get(key);
        return data ? JSON.parse(data) : null;
      } else {
        // Get all prices for symbol
        const keys = await this.redisClient.keys(`price:${symbol}_*`);
        if (keys.length === 0) return null;

        const prices = await Promise.all(
          keys.map(async (k: string) => {
            const data = await this.redisClient.get(k);
            return data ? JSON.parse(data) : null;
          })
        );

        // Return most recent
        return prices
          .filter(p => p !== null)
          .sort((a, b) => b.timestamp - a.timestamp)[0] || null;
      }

    } catch (error) {
      logger.error(`Error getting price from Redis for ${symbol}:`, error);
      return null;
    }
  }

  public getAllPrices(): PriceData[] {
    return Array.from(this.priceCache.values());
  }

  public getPricesBySource(source: string): PriceData[] {
    return Array.from(this.priceCache.values())
      .filter(p => p.source === source);
  }

  public isHealthy(): boolean {
    return this.isRunning && this.priceCache.size > 0;
  }

  public getStats() {
    const sourceStats: Record<string, number> = {};
    this.priceCache.forEach(price => {
      sourceStats[price.source] = (sourceStats[price.source] || 0) + 1;
    });

    return {
      totalPrices: this.priceCache.size,
      connectedDEXs: this.dexConnections.size,
      sourceStats,
      isRunning: this.isRunning
    };
  }
}
