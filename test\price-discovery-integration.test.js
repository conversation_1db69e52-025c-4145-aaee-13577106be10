import { expect } from "chai";
import hre from "hardhat";
const { ethers } = hre;

describe("Price Discovery and Oracle Integration Tests", function () {
  let owner;
  let user1;
  let mockOracle;
  let mockTokens = {};

  beforeEach(async function () {
    [owner, user1] = await ethers.getSigners();
    
    // Deploy MockPriceOracle
    const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
    mockOracle = await MockPriceOracle.deploy();
    await mockOracle.waitForDeployment();
    
    // Deploy mock tokens
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    
    mockTokens.WETH = await MockERC20.deploy(
      "Wrapped Ether",
      "WETH",
      18,
      ethers.parseEther("1000000"),
      owner.address
    );
    await mockTokens.WETH.waitForDeployment();
    
    mockTokens.USDC = await MockERC20.deploy(
      "USD Coin",
      "USDC",
      6,
      ethers.parseUnits("1000000000", 6),
      owner.address
    );
    await mockTokens.USDC.waitForDeployment();
  });

  describe("Price Feed Accuracy", function () {
    it("Should set and retrieve prices accurately", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const testPrice = ethers.parseUnits("2000", 8); // $2000 with 8 decimals
      
      await mockOracle.setPrice(wethAddress, testPrice);
      const retrievedPrice = await mockOracle.getPrice(wethAddress);
      
      expect(retrievedPrice).to.equal(testPrice);
      
      console.log(`WETH price set and retrieved: $${ethers.formatUnits(retrievedPrice, 8)}`);
    });

    it("Should provide Chainlink-compatible interface", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const testPrice = ethers.parseUnits("2000", 8);
      
      await mockOracle.setPrice(wethAddress, testPrice);
      
      const [roundId, answer, startedAt, updatedAt, answeredInRound] = 
        await mockOracle.latestRoundData(wethAddress);
      
      expect(roundId).to.equal(1);
      expect(answer).to.equal(testPrice);
      expect(updatedAt).to.be.greaterThan(0);
      
      console.log(`Chainlink interface - Round: ${roundId}, Price: $${ethers.formatUnits(answer, 8)}`);
    });

    it("Should handle multiple token prices", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const usdcAddress = await mockTokens.USDC.getAddress();
      
      const ethPrice = ethers.parseUnits("2000", 8); // $2000
      const usdcPrice = ethers.parseUnits("1", 8); // $1
      
      await mockOracle.setPrice(wethAddress, ethPrice);
      await mockOracle.setPrice(usdcAddress, usdcPrice);
      
      const retrievedEthPrice = await mockOracle.getPrice(wethAddress);
      const retrievedUsdcPrice = await mockOracle.getPrice(usdcAddress);
      
      expect(retrievedEthPrice).to.equal(ethPrice);
      expect(retrievedUsdcPrice).to.equal(usdcPrice);
      
      console.log(`ETH: $${ethers.formatUnits(retrievedEthPrice, 8)}, USDC: $${ethers.formatUnits(retrievedUsdcPrice, 8)}`);
    });
  });

  describe("Price History and TWAP", function () {
    it("Should maintain price history", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Set multiple prices to build history
      const prices = [
        ethers.parseUnits("2000", 8),
        ethers.parseUnits("2050", 8),
        ethers.parseUnits("1980", 8),
        ethers.parseUnits("2020", 8)
      ];
      
      for (const price of prices) {
        await mockOracle.setPrice(wethAddress, price);
      }
      
      const history = await mockOracle.getPriceHistory(wethAddress);
      expect(history.length).to.equal(prices.length);
      
      console.log(`Price history length: ${history.length}`);
      console.log(`Latest price: $${ethers.formatUnits(history[history.length - 1], 8)}`);
    });

    it("Should calculate TWAP correctly", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Set prices for TWAP calculation
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2100", 8));
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("1900", 8));
      
      const twap = await mockOracle.getTWAP(wethAddress, 3600); // 1 hour period
      expect(twap).to.be.greaterThan(0);
      
      console.log(`TWAP price: $${ethers.formatUnits(twap, 8)}`);
    });

    it("Should calculate price deviation from TWAP", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Set stable prices first
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      
      // Set a deviated price
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2200", 8)); // 10% higher
      
      const deviation = await mockOracle.getPriceDeviation(wethAddress, 3600);
      expect(deviation).to.be.greaterThan(0);
      
      console.log(`Price deviation: ${deviation / 100}%`);
    });
  });

  describe("Circuit Breaker Functionality", function () {
    it("Should trigger circuit breaker on extreme price movements", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();

      // Set initial price
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));

      // Try to set extreme price (should trigger circuit breaker)
      // MAX_PRICE_DEVIATION is 2000 basis points (20%), so 3x increase should trigger it
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("6000", 8)); // 3x price increase

      // Check if circuit breaker was triggered
      const isTriggered = await mockOracle.circuitBreakerTriggered(wethAddress);
      expect(isTriggered).to.be.true;

      console.log(`Circuit breaker triggered for extreme price movement`);
    });

    it("Should allow normal price updates when circuit breaker is disabled", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Disable circuit breaker
      await mockOracle.setCircuitBreakerEnabled(false);
      
      // Set initial price
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      
      // Set extreme price (should work with circuit breaker disabled)
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("6000", 8)); // 3x increase
      
      const newPrice = await mockOracle.getPriceUnsafe(wethAddress);
      expect(newPrice).to.equal(ethers.parseUnits("6000", 8));
      
      console.log(`Extreme price update allowed with circuit breaker disabled`);
    });

    it("Should reset circuit breaker", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Set initial price and trigger circuit breaker
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      await mockOracle.simulateManipulation(wethAddress, 300);
      
      // Reset circuit breaker
      await mockOracle.resetCircuitBreaker(wethAddress);
      
      const isTriggered = await mockOracle.circuitBreakerTriggered(wethAddress);
      expect(isTriggered).to.be.false;
      
      console.log(`Circuit breaker reset successfully`);
    });
  });

  describe("Price Staleness Detection", function () {
    it("Should detect stale prices", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Set price
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      
      // Set short staleness threshold for testing
      await mockOracle.setStalenessThreshold(wethAddress, 1); // 1 second
      
      // Wait and check staleness
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      await mockOracle.checkStaleness(wethAddress);
      
      const isStale = await mockOracle.isStale(wethAddress);
      expect(isStale).to.be.true;
      
      console.log(`Price staleness detection working correctly`);
    });

    it("Should reject stale price requests", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Set price and make it stale
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      await mockOracle.setStalenessThreshold(wethAddress, 1);
      await new Promise(resolve => setTimeout(resolve, 2000));
      await mockOracle.checkStaleness(wethAddress);
      
      // Should revert when trying to get stale price
      await expect(mockOracle.getPrice(wethAddress)).to.be.revertedWith("Price is stale");
      
      console.log(`Stale price requests properly rejected`);
    });
  });

  describe("Price Validation and Edge Cases", function () {
    it("Should handle price updates within circuit breaker limits", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();

      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));

      // Set price within 20% limit (should work)
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2300", 8)); // 15% increase

      const newPrice = await mockOracle.getPriceUnsafe(wethAddress);
      expect(newPrice).to.equal(ethers.parseUnits("2300", 8));

      console.log(`Price update within limits - New price: $${ethers.formatUnits(newPrice, 8)}`);
    });

    it("Should reject invalid price inputs", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();

      // Should reject zero price
      await expect(mockOracle.setPrice(wethAddress, 0)).to.be.revertedWith("Invalid price");

      console.log(`Invalid price inputs properly rejected`);
    });

    it("Should handle price queries for non-existent tokens", async function () {
      const randomAddress = ethers.Wallet.createRandom().address;

      await expect(mockOracle.getPrice(randomAddress)).to.be.revertedWith("Price not set");

      console.log(`Non-existent token price queries properly handled`);
    });
  });

  describe("Performance and Latency", function () {
    it("Should update prices within target latency (<5 seconds)", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const startTime = Date.now();
      
      // Perform multiple price updates
      for (let i = 0; i < 10; i++) {
        await mockOracle.setPrice(wethAddress, ethers.parseUnits((2000 + i).toString(), 8));
      }
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      expect(totalTime).to.be.lessThan(5000); // <5 seconds
      
      console.log(`10 price updates completed in: ${totalTime}ms`);
    });

    it("Should handle concurrent price queries efficiently", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const usdcAddress = await mockTokens.USDC.getAddress();
      
      // Set prices
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8));
      await mockOracle.setPrice(usdcAddress, ethers.parseUnits("1", 8));
      
      const startTime = Date.now();
      
      // Concurrent price queries
      const promises = [];
      for (let i = 0; i < 20; i++) {
        promises.push(mockOracle.getPrice(wethAddress));
        promises.push(mockOracle.getPrice(usdcAddress));
      }
      
      await Promise.all(promises);
      const endTime = Date.now();
      
      expect(endTime - startTime).to.be.lessThan(2000); // <2 seconds for 40 queries
      
      console.log(`40 concurrent price queries completed in: ${endTime - startTime}ms`);
    });

    it("Should maintain accuracy under load", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const testPrice = ethers.parseUnits("2000", 8);
      
      await mockOracle.setPrice(wethAddress, testPrice);
      
      // Multiple concurrent reads
      const promises = Array(50).fill(0).map(() => mockOracle.getPrice(wethAddress));
      const results = await Promise.all(promises);
      
      // All results should be accurate
      results.forEach(price => {
        expect(price).to.equal(testPrice);
      });
      
      console.log(`Price accuracy maintained under load - 50 concurrent reads`);
    });
  });
});
