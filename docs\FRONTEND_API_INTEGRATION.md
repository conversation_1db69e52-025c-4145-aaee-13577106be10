# Frontend API Integration Guide

## Overview

This document provides comprehensive guidance for integrating the enhanced frontend dashboard with the MEV Arbitrage Bot backend services.

## API Endpoints

### Core Data Endpoints

#### Opportunities
- **GET** `/api/opportunities`
  - **Parameters**: `type`, `minProfit`, `maxSlippage`, `network`, `asset`, `limit`, `sortBy`, `order`
  - **Response**: Array of arbitrage opportunities with profit calculations
  - **Cache TTL**: 60 seconds (critical data)
  - **WebSocket Channel**: `opportunities:active`

#### Trades
- **GET** `/api/trades`
  - **Parameters**: `status`, `type`, `network`, `asset`, `limit`, `sortBy`, `order`
  - **Response**: Array of executed trades with performance metrics
  - **Cache TTL**: 60 seconds (critical data)
  - **WebSocket Channel**: `trades:execution`

#### System Health
- **GET** `/health`
  - **Response**: Overall system health status and service availability
  - **Cache TTL**: 30 seconds
  - **WebSocket Channel**: `system:health`

### Analytics Endpoints

#### Performance Metrics
- **GET** `/api/analytics/performance`
  - **Response**: Overall trading performance metrics
  - **Cache TTL**: 300 seconds
  - **Update Interval**: 30 seconds

#### Historical Data
- **GET** `/api/analytics/historical`
  - **Parameters**: `timeframe`, `metrics`, `networks`, `strategies`
  - **Response**: Time-series data for charts and analysis
  - **Cache TTL**: 600 seconds

### ML Learning Endpoints

#### Strategy Performance
- **GET** `/api/ml/strategy-performance`
  - **Response**: ML strategy performance and weights
  - **Cache TTL**: 300 seconds
  - **WebSocket Channel**: `ml:events`

#### Learning Stats
- **GET** `/api/ml/learning-stats`
  - **Response**: Overall ML learning statistics and market regime
  - **Cache TTL**: 300 seconds

### System Monitoring Endpoints

#### Execution Queue
- **GET** `/api/system/execution-queue`
  - **Response**: Current execution queue status and items
  - **Cache TTL**: 30 seconds
  - **WebSocket Channel**: `queue:status`

#### MEV Protection
- **GET** `/api/system/mev-protection`
  - **Response**: MEV protection status and statistics
  - **Cache TTL**: 60 seconds
  - **WebSocket Channel**: `mev:protection`

#### Flash Loan Quotes
- **GET** `/api/system/flash-loan-quotes`
  - **Response**: Available flash loan providers and rates
  - **Cache TTL**: 120 seconds
  - **WebSocket Channel**: `flash_loan:quotes`

#### Network Status
- **GET** `/api/networks/status`
  - **Response**: Multi-chain network health and latency
  - **Cache TTL**: 300 seconds
  - **WebSocket Channel**: `network:status`

## WebSocket Integration

### Connection Management
- **URL**: `ws://localhost:8080/ws` (development) or `wss://domain.com/ws` (production)
- **Reconnection**: Exponential backoff with max 10 attempts
- **Heartbeat**: 30-second intervals
- **Compression**: Enabled for messages >1KB

### Subscription Channels
```javascript
// Critical real-time data (5-second updates)
'opportunities:active'    // Live arbitrage opportunities
'trades:execution'        // Trade execution updates
'prices:current'          // Current price feeds
'queue:status'           // Execution queue changes

// System monitoring (15-second updates)
'system:health'          // System health status
'performance:metrics'    // Performance metrics
'mev:protection'         // MEV protection status

// Background data (30-second updates)
'analytics:dashboard'    // Dashboard analytics
'network:status'         // Network status updates
'ml:events'             // ML learning events
```

### Message Format
```javascript
{
  "type": "opportunity.updated",
  "data": [...],
  "timestamp": 1640995200000,
  "correlationId": "uuid",
  "priority": "high"
}
```

## Caching Strategy

### Multi-Tier Caching
1. **Browser Cache**: In-memory cache with TTL
2. **Service Worker**: Offline capability (future enhancement)
3. **CDN Cache**: Static assets and API responses

### Cache Configuration
```javascript
const CACHE_CONFIG = {
  criticalDataTTL: 60000,     // 1 minute
  defaultTTL: 300000,         // 5 minutes
  maxCacheSize: 1000,         // Max entries
  cleanupInterval: 600000     // 10 minutes
}
```

### Cache Keys
- Format: `api:{endpoint}:{params_hash}`
- Example: `api:/api/opportunities:{"limit":50,"network":"ethereum"}`

## Error Handling

### Circuit Breaker Pattern
- **Failure Threshold**: 5 consecutive failures
- **Timeout**: 60 seconds
- **States**: Closed → Open → Half-Open → Closed

### Retry Strategy
- **Max Retries**: 3 attempts
- **Backoff**: Exponential (1s, 2s, 4s)
- **Jitter**: ±25% randomization

### Error Types
```javascript
// Network errors
{ type: 'NETWORK_ERROR', message: 'Connection failed', retryable: true }

// API errors
{ type: 'API_ERROR', status: 500, message: 'Internal server error', retryable: true }

// Validation errors
{ type: 'VALIDATION_ERROR', message: 'Invalid parameters', retryable: false }

// Circuit breaker
{ type: 'CIRCUIT_OPEN', message: 'Service unavailable', retryable: false }
```

## Performance Optimization

### Request Optimization
- **Batching**: Combine multiple requests where possible
- **Compression**: Enable gzip/deflate
- **Keep-Alive**: Reuse connections
- **Timeout**: 5x API response target (1000ms default)

### Response Optimization
- **Pagination**: Default 50 items, max 500
- **Field Selection**: Request only needed fields
- **Compression**: Server-side response compression

### Memory Management
- **Cache Cleanup**: Automatic cleanup of expired entries
- **Memory Monitoring**: Track cache size and performance
- **Garbage Collection**: Periodic cleanup of unused objects

## Data Consistency

### Eventual Consistency
- **WebSocket Updates**: Real-time data updates
- **Polling Fallback**: 30-second intervals when WebSocket unavailable
- **Conflict Resolution**: Last-write-wins for real-time data

### Data Validation
- **Schema Validation**: Validate API responses
- **Type Checking**: Ensure data types match expectations
- **Null Handling**: Graceful handling of missing data

## Security Considerations

### API Security
- **HTTPS**: All production API calls over HTTPS
- **CORS**: Proper CORS configuration
- **Rate Limiting**: Client-side rate limiting
- **Authentication**: JWT tokens (when implemented)

### Data Protection
- **Sanitization**: Sanitize all user inputs
- **XSS Prevention**: Escape HTML content
- **CSRF Protection**: CSRF tokens for state-changing operations

## Monitoring and Debugging

### Performance Metrics
```javascript
{
  latency: 150,              // Average API response time (ms)
  throughput: 1000,          // Requests per second
  errorRate: 0.5,            // Error percentage
  cacheHitRatio: 85.2,       // Cache hit percentage
  memoryUsage: 45000000      // Memory usage in bytes
}
```

### Debug Information
- **Request Logging**: All API requests with timing
- **Error Logging**: Detailed error information
- **Performance Logging**: Slow requests and bottlenecks
- **Cache Statistics**: Hit/miss ratios and cleanup events

### Health Checks
- **API Availability**: Regular health check pings
- **WebSocket Status**: Connection state monitoring
- **Performance Thresholds**: Alert on performance degradation
- **Error Rate Monitoring**: Alert on high error rates

## Integration Examples

### Basic API Call
```javascript
// Using the enhanced API client
const opportunities = await apiClient.getOpportunities({
  network: 'ethereum',
  minProfit: 100,
  limit: 20
});
```

### WebSocket Subscription
```javascript
// Subscribe to real-time opportunities
wsManager.subscribe('opportunities:active', {
  network: 'ethereum',
  minProfit: 50
});
```

### Error Handling
```javascript
try {
  const data = await apiClient.getTrades();
} catch (error) {
  if (error.type === 'CIRCUIT_OPEN') {
    // Use cached data or show offline message
    showOfflineMessage();
  } else if (error.retryable) {
    // Retry with backoff
    setTimeout(() => retryRequest(), 1000);
  } else {
    // Show error to user
    showErrorMessage(error.message);
  }
}
```

This integration guide ensures optimal performance, reliability, and user experience for the MEV Arbitrage Bot frontend dashboard.
