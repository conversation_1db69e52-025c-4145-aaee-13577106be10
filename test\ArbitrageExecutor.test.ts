import { expect } from "chai";
import { ethers } from "hardhat";
import { loadFixture } from "@nomicfoundation/hardhat-network-helpers";
import { ArbitrageExecutor, TokenDiscovery, LiquidityChecker } from "../typechain-types";

describe("ArbitrageExecutor", function () {
  async function deployArbitrageExecutorFixture() {
    const [owner, executor, user] = await ethers.getSigners();

    // Deploy dependencies
    const TokenDiscovery = await ethers.getContractFactory("TokenDiscovery");
    const tokenDiscovery = await TokenDiscovery.deploy();

    const LiquidityChecker = await ethers.getContractFactory("LiquidityChecker");
    const liquidityChecker = await LiquidityChecker.deploy();

    // Deploy ArbitrageExecutor
    const ArbitrageExecutor = await ethers.getContractFactory("ArbitrageExecutor");
    const arbitrageExecutor = await ArbitrageExecutor.deploy(
      await tokenDiscovery.getAddress(),
      await liquidityChecker.getAddress()
    );

    // Deploy mock ERC20 tokens for testing
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    const tokenA = await MockERC20.deploy("Token A", "TKNA", ethers.parseEther("1000000"));
    const tokenB = await MockERC20.deploy("Token B", "TKNB", ethers.parseEther("1000000"));

    return {
      arbitrageExecutor,
      tokenDiscovery,
      liquidityChecker,
      tokenA,
      tokenB,
      owner,
      executor,
      user
    };
  }

  describe("Deployment", function () {
    it("Should deploy with correct initial state", async function () {
      const { arbitrageExecutor, owner } = await loadFixture(deployArbitrageExecutorFixture);

      expect(await arbitrageExecutor.owner()).to.equal(owner.address);
      expect(await arbitrageExecutor.paused()).to.be.false;
      expect(await arbitrageExecutor.minProfitThreshold()).to.equal(ethers.parseEther("0.01"));
    });

    it("Should set correct dependencies", async function () {
      const { arbitrageExecutor, tokenDiscovery, liquidityChecker } = await loadFixture(deployArbitrageExecutorFixture);

      expect(await arbitrageExecutor.tokenDiscovery()).to.equal(await tokenDiscovery.getAddress());
      expect(await arbitrageExecutor.liquidityChecker()).to.equal(await liquidityChecker.getAddress());
    });
  });

  describe("Access Control", function () {
    it("Should allow owner to add authorized executors", async function () {
      const { arbitrageExecutor, owner, executor } = await loadFixture(deployArbitrageExecutorFixture);

      await arbitrageExecutor.connect(owner).addAuthorizedExecutor(executor.address);
      expect(await arbitrageExecutor.authorizedExecutors(executor.address)).to.be.true;
    });

    it("Should prevent non-owners from adding executors", async function () {
      const { arbitrageExecutor, executor, user } = await loadFixture(deployArbitrageExecutorFixture);

      await expect(
        arbitrageExecutor.connect(user).addAuthorizedExecutor(executor.address)
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });

    it("Should allow owner to remove authorized executors", async function () {
      const { arbitrageExecutor, owner, executor } = await loadFixture(deployArbitrageExecutorFixture);

      await arbitrageExecutor.connect(owner).addAuthorizedExecutor(executor.address);
      await arbitrageExecutor.connect(owner).removeAuthorizedExecutor(executor.address);
      
      expect(await arbitrageExecutor.authorizedExecutors(executor.address)).to.be.false;
    });
  });

  describe("Parameter Management", function () {
    it("Should allow owner to update minimum profit threshold", async function () {
      const { arbitrageExecutor, owner } = await loadFixture(deployArbitrageExecutorFixture);

      const newThreshold = ethers.parseEther("0.05");
      await arbitrageExecutor.connect(owner).setMinProfitThreshold(newThreshold);
      
      expect(await arbitrageExecutor.minProfitThreshold()).to.equal(newThreshold);
    });

    it("Should emit event when profit threshold is updated", async function () {
      const { arbitrageExecutor, owner } = await loadFixture(deployArbitrageExecutorFixture);

      const newThreshold = ethers.parseEther("0.05");
      
      await expect(arbitrageExecutor.connect(owner).setMinProfitThreshold(newThreshold))
        .to.emit(arbitrageExecutor, "MinProfitThresholdUpdated")
        .withArgs(newThreshold);
    });

    it("Should prevent setting zero profit threshold", async function () {
      const { arbitrageExecutor, owner } = await loadFixture(deployArbitrageExecutorFixture);

      await expect(
        arbitrageExecutor.connect(owner).setMinProfitThreshold(0)
      ).to.be.revertedWith("Invalid threshold");
    });
  });

  describe("Emergency Controls", function () {
    it("Should allow owner to pause the contract", async function () {
      const { arbitrageExecutor, owner } = await loadFixture(deployArbitrageExecutorFixture);

      await arbitrageExecutor.connect(owner).pause();
      expect(await arbitrageExecutor.paused()).to.be.true;
    });

    it("Should allow owner to unpause the contract", async function () {
      const { arbitrageExecutor, owner } = await loadFixture(deployArbitrageExecutorFixture);

      await arbitrageExecutor.connect(owner).pause();
      await arbitrageExecutor.connect(owner).unpause();
      
      expect(await arbitrageExecutor.paused()).to.be.false;
    });

    it("Should prevent execution when paused", async function () {
      const { arbitrageExecutor, owner, executor, tokenA, tokenB } = await loadFixture(deployArbitrageExecutorFixture);

      // Setup
      await arbitrageExecutor.connect(owner).addAuthorizedExecutor(executor.address);
      await arbitrageExecutor.connect(owner).pause();

      const arbitrageParams = {
        tokens: [await tokenA.getAddress(), await tokenB.getAddress()],
        exchanges: [ethers.ZeroAddress, ethers.ZeroAddress],
        amounts: [ethers.parseEther("1"), ethers.parseEther("2000")],
        minProfit: ethers.parseEther("0.01"),
        arbitrageType: 0,
        routeData: "0x"
      };

      await expect(
        arbitrageExecutor.connect(executor).executeArbitrage(
          arbitrageParams,
          ethers.ZeroAddress,
          ethers.parseEther("1")
        )
      ).to.be.revertedWith("Pausable: paused");
    });
  });

  describe("Arbitrage Execution", function () {
    it("Should validate arbitrage parameters", async function () {
      const { arbitrageExecutor, owner, executor, tokenA } = await loadFixture(deployArbitrageExecutorFixture);

      await arbitrageExecutor.connect(owner).addAuthorizedExecutor(executor.address);

      // Test with insufficient tokens
      const invalidParams = {
        tokens: [await tokenA.getAddress()], // Only one token
        exchanges: [ethers.ZeroAddress],
        amounts: [ethers.parseEther("1")],
        minProfit: ethers.parseEther("0.01"),
        arbitrageType: 0,
        routeData: "0x"
      };

      await expect(
        arbitrageExecutor.connect(executor).executeArbitrage(
          invalidParams,
          ethers.ZeroAddress,
          ethers.parseEther("1")
        )
      ).to.be.revertedWith("Invalid token count");
    });

    it("Should validate minimum profit requirement", async function () {
      const { arbitrageExecutor, owner, executor, tokenA, tokenB } = await loadFixture(deployArbitrageExecutorFixture);

      await arbitrageExecutor.connect(owner).addAuthorizedExecutor(executor.address);

      const lowProfitParams = {
        tokens: [await tokenA.getAddress(), await tokenB.getAddress()],
        exchanges: [ethers.ZeroAddress, ethers.ZeroAddress],
        amounts: [ethers.parseEther("1"), ethers.parseEther("2000")],
        minProfit: ethers.parseEther("0.005"), // Below threshold
        arbitrageType: 0,
        routeData: "0x"
      };

      await expect(
        arbitrageExecutor.connect(executor).executeArbitrage(
          lowProfitParams,
          ethers.ZeroAddress,
          ethers.parseEther("1")
        )
      ).to.be.revertedWith("Profit below threshold");
    });

    it("Should prevent unauthorized execution", async function () {
      const { arbitrageExecutor, user, tokenA, tokenB } = await loadFixture(deployArbitrageExecutorFixture);

      const arbitrageParams = {
        tokens: [await tokenA.getAddress(), await tokenB.getAddress()],
        exchanges: [ethers.ZeroAddress, ethers.ZeroAddress],
        amounts: [ethers.parseEther("1"), ethers.parseEther("2000")],
        minProfit: ethers.parseEther("0.01"),
        arbitrageType: 0,
        routeData: "0x"
      };

      await expect(
        arbitrageExecutor.connect(user).executeArbitrage(
          arbitrageParams,
          ethers.ZeroAddress,
          ethers.parseEther("1")
        )
      ).to.be.revertedWith("Not authorized");
    });

    it("Should generate unique trade IDs", async function () {
      const { arbitrageExecutor, owner, executor, tokenA, tokenB } = await loadFixture(deployArbitrageExecutorFixture);

      await arbitrageExecutor.connect(owner).addAuthorizedExecutor(executor.address);

      const arbitrageParams = {
        tokens: [await tokenA.getAddress(), await tokenB.getAddress()],
        exchanges: [ethers.ZeroAddress, ethers.ZeroAddress],
        amounts: [ethers.parseEther("1"), ethers.parseEther("2000")],
        minProfit: ethers.parseEther("0.01"),
        arbitrageType: 0,
        routeData: "0x"
      };

      // Mock successful execution by setting up the contract state
      // In a real test, you would mock the flash loan provider and DEX interactions

      const initialNonce = await arbitrageExecutor.executorNonces(executor.address);
      expect(initialNonce).to.equal(0);
    });
  });

  describe("Flash Loan Integration", function () {
    it("Should validate flash loan callback initiator", async function () {
      const { arbitrageExecutor, user } = await loadFixture(deployArbitrageExecutorFixture);

      const assets = [ethers.ZeroAddress];
      const amounts = [ethers.parseEther("1")];
      const premiums = [ethers.parseEther("0.001")];
      const params = "0x";

      await expect(
        arbitrageExecutor.connect(user).executeOperation(
          assets,
          amounts,
          premiums,
          user.address, // Wrong initiator
          params
        )
      ).to.be.revertedWith("Invalid initiator");
    });
  });

  describe("Events", function () {
    it("Should emit ArbitrageExecuted event on successful execution", async function () {
      const { arbitrageExecutor, owner, executor, tokenA, tokenB } = await loadFixture(deployArbitrageExecutorFixture);

      await arbitrageExecutor.connect(owner).addAuthorizedExecutor(executor.address);

      // This test would require mocking the flash loan and DEX interactions
      // For now, we'll test the event structure
      const arbitrageParams = {
        tokens: [await tokenA.getAddress(), await tokenB.getAddress()],
        exchanges: [ethers.ZeroAddress, ethers.ZeroAddress],
        amounts: [ethers.parseEther("1"), ethers.parseEther("2000")],
        minProfit: ethers.parseEther("0.01"),
        arbitrageType: 0,
        routeData: "0x"
      };

      // In a complete test, this would emit the ArbitrageExecuted event
      // await expect(arbitrageExecutor.connect(executor).executeArbitrage(...))
      //   .to.emit(arbitrageExecutor, "ArbitrageExecuted");
    });
  });

  describe("Gas Optimization", function () {
    it("Should have reasonable gas costs for execution", async function () {
      const { arbitrageExecutor, owner, executor } = await loadFixture(deployArbitrageExecutorFixture);

      // Test gas cost of adding authorized executor
      const tx = await arbitrageExecutor.connect(owner).addAuthorizedExecutor(executor.address);
      const receipt = await tx.wait();
      
      expect(receipt?.gasUsed).to.be.lessThan(100000); // Should be efficient
    });
  });
});
