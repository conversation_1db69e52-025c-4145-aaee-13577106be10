import { createClient, SupabaseClient } from '@supabase/supabase-js';
import logger from '../utils/logger.js';

export interface TradeRecord {
  id: string;
  opportunity_id: string;
  type: string;
  assets: string[];
  exchanges: string[];
  executed_profit: number;
  gas_fees: number;
  status: string;
  timestamp: string;
  network: string;
  tx_hash?: string;
  created_at?: string;
}

export interface OpportunityRecord {
  id: string;
  type: string;
  assets: string[];
  exchanges: string[];
  potential_profit: number;
  profit_percentage: number;
  timestamp: string;
  network: string;
  confidence: number;
  slippage: number;
  created_at?: string;
}

export interface PerformanceMetrics {
  id?: string;
  total_trades: number;
  successful_trades: number;
  failed_trades: number;
  total_profit: number;
  total_loss: number;
  net_profit: number;
  win_rate: number;
  avg_profit: number;
  avg_loss: number;
  profit_factor: number;
  sharpe_ratio: number;
  max_drawdown: number;
  roi: number;
  daily_volume: number;
  date: string;
  created_at?: string;
}

export class SupabaseService {
  private supabase: SupabaseClient | null = null;
  private isConnected = false;

  constructor() {
    this.initialize();
  }

  private initialize() {
    try {
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
        logger.warn('Supabase configuration not found, service will be disabled');
        return;
      }

      this.supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      );

      this.isConnected = true;
      logger.info('Supabase service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Supabase service:', error);
      this.isConnected = false;
    }
  }

  // Trade Management
  async saveTrade(trade: TradeRecord): Promise<boolean> {
    if (!this.isConnected || !this.supabase) {
      logger.warn('Supabase not connected, skipping trade save');
      return false;
    }

    try {
      const { error } = await this.supabase
        .from('trades')
        .insert([trade]);

      if (error) {
        logger.error('Error saving trade to Supabase:', error);
        return false;
      }

      logger.debug('Trade saved to Supabase:', trade.id);
      return true;
    } catch (error) {
      logger.error('Failed to save trade:', error);
      return false;
    }
  }

  async getTrades(limit = 100, offset = 0): Promise<TradeRecord[]> {
    if (!this.isConnected || !this.supabase) {
      return [];
    }

    try {
      const { data, error } = await this.supabase
        .from('trades')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error('Error fetching trades from Supabase:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Failed to fetch trades:', error);
      return [];
    }
  }

  // Opportunity Management
  async saveOpportunity(opportunity: OpportunityRecord): Promise<boolean> {
    if (!this.isConnected || !this.supabase) {
      return false;
    }

    try {
      const { error } = await this.supabase
        .from('opportunities')
        .insert([opportunity]);

      if (error) {
        logger.error('Error saving opportunity to Supabase:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Failed to save opportunity:', error);
      return false;
    }
  }

  async getOpportunities(limit = 100): Promise<OpportunityRecord[]> {
    if (!this.isConnected || !this.supabase) {
      return [];
    }

    try {
      const { data, error } = await this.supabase
        .from('opportunities')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Error fetching opportunities from Supabase:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Failed to fetch opportunities:', error);
      return [];
    }
  }

  // Performance Metrics
  async savePerformanceMetrics(metrics: PerformanceMetrics): Promise<boolean> {
    if (!this.isConnected || !this.supabase) {
      return false;
    }

    try {
      const { error } = await this.supabase
        .from('performance_metrics')
        .upsert([metrics], { onConflict: 'date' });

      if (error) {
        logger.error('Error saving performance metrics to Supabase:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Failed to save performance metrics:', error);
      return false;
    }
  }

  async getPerformanceMetrics(days = 30): Promise<PerformanceMetrics[]> {
    if (!this.isConnected || !this.supabase) {
      return [];
    }

    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await this.supabase
        .from('performance_metrics')
        .select('*')
        .gte('date', startDate.toISOString().split('T')[0])
        .order('date', { ascending: false });

      if (error) {
        logger.error('Error fetching performance metrics from Supabase:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Failed to fetch performance metrics:', error);
      return [];
    }
  }

  // Health Check
  isHealthy(): boolean {
    return this.isConnected;
  }

  async testConnection(): Promise<boolean> {
    if (!this.supabase) {
      return false;
    }

    try {
      const { error } = await this.supabase
        .from('trades')
        .select('count')
        .limit(1);

      return !error;
    } catch (error) {
      logger.error('Supabase connection test failed:', error);
      return false;
    }
  }

  // Cleanup old data
  async cleanupOldData(daysToKeep = 90): Promise<void> {
    if (!this.isConnected || !this.supabase) {
      return;
    }

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      // Clean up old opportunities
      await this.supabase
        .from('opportunities')
        .delete()
        .lt('created_at', cutoffDate.toISOString());

      // Keep trades longer, clean up old performance metrics
      await this.supabase
        .from('performance_metrics')
        .delete()
        .lt('date', cutoffDate.toISOString().split('T')[0]);

      logger.info(`Cleaned up data older than ${daysToKeep} days`);
    } catch (error) {
      logger.error('Failed to cleanup old data:', error);
    }
  }
}
