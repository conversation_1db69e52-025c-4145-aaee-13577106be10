'use client';

import { useState, useMemo } from 'react';
import { 
  Activity, 
  Server,
  Database,
  Wifi,
  WifiOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Cpu,
  HardDrive,
  MemoryStick,
  RefreshCw,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

import { SystemHealth, NetworkStatus } from '@/types';
import { 
  formatPercentage, 
  formatNumber,
  formatTimeAgo,
  formatDuration,
  getStatusColor
} from '@/lib/utils';
import { LoadingSpinner, SkeletonCard } from '@/components/ui/LoadingSpinner';

interface SystemHealthMonitorProps {
  systemHealth?: SystemHealth;
  networkStatus?: NetworkStatus[];
  isLoading?: boolean;
  detailed?: boolean;
}

interface ServiceStatusProps {
  serviceName: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency: number;
  lastCheck: string;
  errorCount: number;
}

interface DatabaseStatusProps {
  dbName: string;
  isHealthy: boolean;
  latency: number;
  connectionCount: number;
  lastQuery: string;
}

function ServiceStatusCard({ serviceName, status, latency, lastCheck, errorCount }: ServiceStatusProps) {
  const statusColors = {
    healthy: 'text-success-400',
    degraded: 'text-warning-400',
    unhealthy: 'text-error-400',
  };

  const statusIcons = {
    healthy: CheckCircle,
    degraded: AlertTriangle,
    unhealthy: XCircle,
  };

  const StatusIcon = statusIcons[status];
  const statusColor = statusColors[status];

  return (
    <div className="glass-effect rounded-lg p-4 hover:bg-dark-700/30 transition-all duration-200">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Server className="w-4 h-4 text-primary-400" />
          <span className="font-medium text-dark-100 capitalize">
            {serviceName.replace(/([A-Z])/g, ' $1').trim()}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <StatusIcon className={`w-4 h-4 ${statusColor}`} />
          <span className={`text-xs font-medium ${statusColor} capitalize`}>
            {status}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <div className="text-xs text-dark-500 mb-1">Latency</div>
          <div className={`text-sm font-medium ${
            latency < 100 ? 'text-success-400' : 
            latency < 500 ? 'text-warning-400' : 'text-error-400'
          }`}>
            {latency}ms
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Errors</div>
          <div className={`text-sm font-medium ${
            errorCount === 0 ? 'text-success-400' : 
            errorCount < 5 ? 'text-warning-400' : 'text-error-400'
          }`}>
            {errorCount}
          </div>
        </div>
      </div>

      <div className="mt-3 pt-3 border-t border-dark-700">
        <div className="flex items-center justify-between">
          <span className="text-xs text-dark-500">Last Check</span>
          <span className="text-xs text-dark-300">
            {formatTimeAgo(lastCheck)}
          </span>
        </div>
      </div>
    </div>
  );
}

function DatabaseStatusCard({ dbName, isHealthy, latency, connectionCount, lastQuery }: DatabaseStatusProps) {
  const statusColor = isHealthy ? 'text-success-400' : 'text-error-400';
  const StatusIcon = isHealthy ? CheckCircle : XCircle;

  return (
    <div className="glass-effect rounded-lg p-4 hover:bg-dark-700/30 transition-all duration-200">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Database className="w-4 h-4 text-primary-400" />
          <span className="font-medium text-dark-100 uppercase">{dbName}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <StatusIcon className={`w-4 h-4 ${statusColor}`} />
          <span className={`text-xs font-medium ${statusColor}`}>
            {isHealthy ? 'Healthy' : 'Unhealthy'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <div className="text-xs text-dark-500 mb-1">Query Time</div>
          <div className={`text-sm font-medium ${
            latency < 50 ? 'text-success-400' : 
            latency < 200 ? 'text-warning-400' : 'text-error-400'
          }`}>
            {latency}ms
          </div>
        </div>
        
        <div>
          <div className="text-xs text-dark-500 mb-1">Connections</div>
          <div className="text-sm font-medium text-dark-200">
            {connectionCount}
          </div>
        </div>
      </div>

      <div className="mt-3 pt-3 border-t border-dark-700">
        <div className="flex items-center justify-between">
          <span className="text-xs text-dark-500">Last Query</span>
          <span className="text-xs text-dark-300">
            {formatTimeAgo(lastQuery)}
          </span>
        </div>
      </div>
    </div>
  );
}

export function SystemHealthMonitor({ 
  systemHealth, 
  networkStatus = [], 
  isLoading = false, 
  detailed = false 
}: SystemHealthMonitorProps) {
  const [refreshing, setRefreshing] = useState(false);

  // Calculate system metrics
  const systemMetrics = useMemo(() => {
    if (!systemHealth) {
      return {
        overallStatus: 'unknown',
        uptime: 0,
        serviceCount: 0,
        healthyServices: 0,
        databaseCount: 0,
        healthyDatabases: 0,
        networkCount: networkStatus.length,
        healthyNetworks: networkStatus.filter(n => n.is_healthy || n.isHealthy).length,
      };
    }

    const services = Object.entries(systemHealth.services || {});
    const databases = Object.entries(systemHealth.databases || {});
    
    return {
      overallStatus: systemHealth.status,
      uptime: systemHealth.uptime || 0,
      serviceCount: services.length,
      healthyServices: services.filter(([_, service]) => service.status === 'healthy').length,
      databaseCount: databases.length,
      healthyDatabases: databases.filter(([_, db]) => db.isHealthy).length,
      networkCount: networkStatus.length,
      healthyNetworks: networkStatus.filter(n => n.is_healthy || n.isHealthy).length,
    };
  }, [systemHealth, networkStatus]);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-dark-700 rounded w-1/3 animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
      </div>
    );
  }

  const overallStatusColor = systemMetrics.overallStatus === 'healthy' ? 'text-success-400' : 
                            systemMetrics.overallStatus === 'degraded' ? 'text-warning-400' : 'text-error-400';
  const OverallStatusIcon = systemMetrics.overallStatus === 'healthy' ? CheckCircle : 
                           systemMetrics.overallStatus === 'degraded' ? AlertTriangle : XCircle;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <OverallStatusIcon className={`w-6 h-6 ${overallStatusColor}`} />
          <div>
            <h3 className="text-lg font-semibold text-dark-100">
              System Health
            </h3>
            <p className="text-sm text-dark-400">
              Real-time system and infrastructure monitoring
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className={`
            px-3 py-1 rounded-full text-sm font-medium capitalize
            ${systemMetrics.overallStatus === 'healthy' ? 'bg-success-500/20 text-success-400' :
              systemMetrics.overallStatus === 'degraded' ? 'bg-warning-500/20 text-warning-400' :
              'bg-error-500/20 text-error-400'}
          `}>
            {systemMetrics.overallStatus}
          </div>
          
          <button 
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn-secondary text-sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* System Overview Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="metric-card">
          <div className="flex items-center space-x-2 mb-2">
            <Activity className="w-4 h-4 text-primary-400" />
            <span className="text-sm font-medium text-dark-300">Uptime</span>
          </div>
          <div className="text-xl font-bold text-dark-100">
            {formatPercentage(systemMetrics.uptime)}
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center space-x-2 mb-2">
            <Server className="w-4 h-4 text-success-400" />
            <span className="text-sm font-medium text-dark-300">Services</span>
          </div>
          <div className="text-xl font-bold text-success-400">
            {systemMetrics.healthyServices}/{systemMetrics.serviceCount}
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center space-x-2 mb-2">
            <Database className="w-4 h-4 text-primary-400" />
            <span className="text-sm font-medium text-dark-300">Databases</span>
          </div>
          <div className="text-xl font-bold text-primary-400">
            {systemMetrics.healthyDatabases}/{systemMetrics.databaseCount}
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center space-x-2 mb-2">
            <Wifi className="w-4 h-4 text-warning-400" />
            <span className="text-sm font-medium text-dark-300">Networks</span>
          </div>
          <div className="text-xl font-bold text-warning-400">
            {systemMetrics.healthyNetworks}/{systemMetrics.networkCount}
          </div>
        </div>
      </div>

      {/* Services Status */}
      {systemHealth?.services && Object.keys(systemHealth.services).length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-dark-100">
            Service Status ({Object.keys(systemHealth.services).length})
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(systemHealth.services).map(([serviceName, service]) => (
              <ServiceStatusCard
                key={serviceName}
                serviceName={serviceName}
                status={service.status}
                latency={service.latency}
                lastCheck={service.last_check || service.lastCheck || ''}
                errorCount={service.error_count || service.errorCount || 0}
              />
            ))}
          </div>
        </div>
      )}

      {/* Database Status */}
      {systemHealth?.databases && Object.keys(systemHealth.databases).length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-dark-100">
            Database Status ({Object.keys(systemHealth.databases).length})
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(systemHealth.databases).map(([dbName, db]) => (
              <DatabaseStatusCard
                key={dbName}
                dbName={dbName}
                isHealthy={db.isHealthy}
                latency={db.latency}
                connectionCount={db.connection_count || db.connectionCount || 0}
                lastQuery={db.last_query || db.lastQuery || ''}
              />
            ))}
          </div>
        </div>
      )}

      {/* System Resources */}
      {detailed && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="glass-effect rounded-xl p-6">
            <h4 className="text-lg font-semibold text-dark-100 mb-4">
              System Resources
            </h4>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Cpu className="w-4 h-4 text-primary-400" />
                  <span className="text-sm text-dark-400">CPU Usage</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-dark-700 rounded-full h-2">
                    <div className="bg-primary-500 h-2 rounded-full w-3/4"></div>
                  </div>
                  <span className="text-sm font-medium text-dark-200">75%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MemoryStick className="w-4 h-4 text-warning-400" />
                  <span className="text-sm text-dark-400">Memory Usage</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-dark-700 rounded-full h-2">
                    <div className="bg-warning-500 h-2 rounded-full w-4/5"></div>
                  </div>
                  <span className="text-sm font-medium text-dark-200">82%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <HardDrive className="w-4 h-4 text-success-400" />
                  <span className="text-sm text-dark-400">Disk Usage</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-dark-700 rounded-full h-2">
                    <div className="bg-success-500 h-2 rounded-full w-1/2"></div>
                  </div>
                  <span className="text-sm font-medium text-dark-200">45%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="glass-effect rounded-xl p-6">
            <h4 className="text-lg font-semibold text-dark-100 mb-4">
              Recent System Events
            </h4>

            <div className="space-y-3">
              {[
                {
                  type: 'Service Restart',
                  service: 'OpportunityDetection',
                  status: 'success',
                  time: '5 minutes ago',
                },
                {
                  type: 'Database Connection',
                  service: 'InfluxDB',
                  status: 'warning',
                  time: '12 minutes ago',
                },
                {
                  type: 'Network Sync',
                  service: 'Ethereum RPC',
                  status: 'success',
                  time: '18 minutes ago',
                },
                {
                  type: 'Cache Clear',
                  service: 'Redis',
                  status: 'success',
                  time: '25 minutes ago',
                },
              ].map((event, index) => {
                const statusColor = event.status === 'success' ? 'text-success-400' :
                                   event.status === 'warning' ? 'text-warning-400' : 'text-error-400';
                const StatusIcon = event.status === 'success' ? CheckCircle :
                                  event.status === 'warning' ? AlertTriangle : XCircle;

                return (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-dark-700/30">
                    <div className="flex items-center space-x-3">
                      <StatusIcon className={`w-4 h-4 ${statusColor}`} />
                      <div>
                        <div className="text-sm font-medium text-dark-100">
                          {event.type}
                        </div>
                        <div className="text-xs text-dark-400">
                          {event.service}
                        </div>
                      </div>
                    </div>

                    <div className="text-xs text-dark-400">
                      {event.time}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* No Data State */}
      {!systemHealth && (
        <div className="text-center py-12">
          <Activity className="w-12 h-12 text-dark-600 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-dark-300 mb-2">
            No System Health Data
          </h4>
          <p className="text-dark-500">
            System health monitoring data is not available at the moment.
          </p>
        </div>
      )}
    </div>
  );
}
