# MEV Arbitrage Bot - Environment Configuration Template
# =====================================================
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=3001
NODE_ENV=development

# WebSocket Configuration
WS_PORT=8080
WS_URL=ws://localhost:8080/ws
NEXT_PUBLIC_WS_URL=ws://localhost:8080/ws

# Database Configuration
REDIS_URL=redis://localhost:6379
DATABASE_URL=postgresql://mev_user:mev_password@localhost:5432/mev_arbitrage_bot
POSTGRES_URL=postgresql://mev_user:mev_password@localhost:5432/mev_arbitrage_bot

# Supabase Configuration (Cloud Database)
# Get these from your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here
SUPABASE_JWT_SECRET=your-supabase-jwt-secret-here
SUPABASE_DATABASE_URL="postgresql://postgres.your-project-id:[YOUR-PASSWORD]@aws-0-us-east-2.pooler.supabase.com:5432/postgres"

# InfluxDB Configuration (Docker)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token-here
INFLUXDB_ORG=your-organization-name
INFLUXDB_BUCKET=mev-arbitrage-metrics
INFLUXDB_USERNAME=your-influxdb-username
INFLUXDB_PASSWORD=your-influxdb-password

# Blockchain RPC Configuration
# For production, use actual RPC endpoints from providers like Alchemy, Infura, etc.
ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/your-api-key
POLYGON_RPC_URL=https://polygon-mainnet.alchemyapi.io/v2/your-api-key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# Smart Contract Addresses (Deploy your own contracts)
TOKEN_DISCOVERY_ADDRESS=******************************************
LIQUIDITY_CHECKER_ADDRESS=******************************************
ARBITRAGE_EXECUTOR_ADDRESS=******************************************
GOVERNANCE_ADDRESS=******************************************

# Private Key for Trading (NEVER commit real private keys!)
# Use a dedicated wallet for the bot with limited funds
PRIVATE_KEY=******************************************000000000000000000000000

# Trading Configuration
MIN_PROFIT_THRESHOLD=50
MAX_POSITION_SIZE=10000
MAX_SLIPPAGE=0.5
GAS_PRICE_MULTIPLIER=1.1

# Risk Management
EMERGENCY_STOP=false
MAX_DAILY_LOSS=1000
POSITION_SIZE_PERCENTAGE=2

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true

# External API Keys (for production use)
COINGECKO_API_KEY=your-coingecko-api-key-here
ETHERSCAN_API_KEY=your-etherscan-api-key-here
POLYGONSCAN_API_KEY=your-polygonscan-api-key-here
BSCSCAN_API_KEY=your-bscscan-api-key-here
ALCHEMY_API_KEY=your-alchemy-api-key-here
INFURA_PROJECT_ID=your-infura-project-id-here
MORALIS_API_KEY=your-moralis-api-key-here

# Flashbots Configuration
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
FLASHBOTS_BUILDER_URL=https://builder.flashbots.net

# Telegram Bot (for alerts)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here
TELEGRAM_CHAT_ID=your-telegram-chat-id-here

# Email Notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password-here
ALERT_EMAIL=<EMAIL>

# Security
JWT_SECRET=your-jwt-secret-for-api-authentication
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=100
CORS_ORIGIN=http://localhost:5173,http://localhost:3000


