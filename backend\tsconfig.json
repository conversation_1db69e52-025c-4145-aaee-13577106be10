{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "../dist/backend", "rootDir": ".", "declaration": true, "sourceMap": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2022"], "types": ["node"]}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}