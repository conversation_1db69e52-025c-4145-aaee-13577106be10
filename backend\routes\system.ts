import { Router } from 'express';
import { z } from 'zod';
import logger from '../utils/logger.js';
import { RiskManagementService } from '../services/RiskManagementService.js';

const riskLimitsSchema = z.object({
  maxDailyLoss: z.number().positive().optional(),
  maxPositionSize: z.number().positive().optional(),
  maxTotalExposure: z.number().positive().optional(),
  maxConsecutiveLosses: z.number().int().positive().optional(),
  minWinRate: z.number().min(0).max(100).optional(),
  maxVolatility: z.number().min(0).max(100).optional()
});

export default function createSystemRoutes(riskService: RiskManagementService) {
  const router = Router();

  // Get system health status
  router.get('/health', async (req, res) => {
    try {
      const isHealthy = riskService.isHealthy();
      const emergencyStop = riskService.isEmergencyStopActive();
      const riskMetrics = riskService.getRiskMetrics();
      const activeAlerts = riskService.getActiveAlerts();

      res.json({
        success: true,
        data: {
          isHealthy,
          emergencyStop,
          riskMetrics,
          activeAlerts: activeAlerts.length,
          criticalAlerts: activeAlerts.filter(alert => alert.type === 'critical').length,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      logger.error('Error getting system health:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve system health'
      });
    }
  });

  // Get risk metrics
  router.get('/risk/metrics', async (req, res) => {
    try {
      const metrics = riskService.getRiskMetrics();
      
      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      logger.error('Error getting risk metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve risk metrics'
      });
    }
  });

  // Get risk limits
  router.get('/risk/limits', async (req, res) => {
    try {
      const limits = riskService.getRiskLimits();
      
      res.json({
        success: true,
        data: limits
      });
    } catch (error) {
      logger.error('Error getting risk limits:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve risk limits'
      });
    }
  });

  // Update risk limits
  router.put('/risk/limits', async (req, res) => {
    try {
      const validatedData = riskLimitsSchema.parse(req.body);
      
      riskService.updateRiskLimits(validatedData);
      
      res.json({
        success: true,
        message: 'Risk limits updated successfully',
        data: riskService.getRiskLimits()
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Invalid risk limits data',
          details: error.errors
        });
      }
      
      logger.error('Error updating risk limits:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update risk limits'
      });
    }
  });

  // Get all alerts
  router.get('/alerts', async (req, res) => {
    try {
      const { type, limit = '50' } = req.query;
      
      let alerts = riskService.getAlerts();
      
      if (type && (type === 'warning' || type === 'critical')) {
        alerts = alerts.filter(alert => alert.type === type);
      }
      
      const limitNum = Math.min(parseInt(limit as string) || 50, 200);
      alerts = alerts.slice(0, limitNum);
      
      res.json({
        success: true,
        data: alerts,
        count: alerts.length,
        filters: { type, limit: limitNum }
      });
    } catch (error) {
      logger.error('Error getting alerts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve alerts'
      });
    }
  });

  // Get active alerts
  router.get('/alerts/active', async (req, res) => {
    try {
      const alerts = riskService.getActiveAlerts();
      
      res.json({
        success: true,
        data: alerts,
        count: alerts.length
      });
    } catch (error) {
      logger.error('Error getting active alerts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve active alerts'
      });
    }
  });

  // Clear specific alert
  router.delete('/alerts/:alertId', async (req, res) => {
    try {
      const { alertId } = req.params;
      
      const success = riskService.clearAlert(alertId);
      
      if (success) {
        res.json({
          success: true,
          message: 'Alert cleared successfully'
        });
      } else {
        res.status(404).json({
          success: false,
          error: 'Alert not found'
        });
      }
    } catch (error) {
      logger.error('Error clearing alert:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to clear alert'
      });
    }
  });

  // Emergency stop controls
  router.get('/emergency-stop', async (req, res) => {
    try {
      const isActive = riskService.isEmergencyStopActive();
      
      res.json({
        success: true,
        data: {
          isActive,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      logger.error('Error getting emergency stop status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve emergency stop status'
      });
    }
  });

  // Toggle emergency stop
  router.post('/emergency-stop/toggle', async (req, res) => {
    try {
      const newState = riskService.toggleEmergencyStop();
      
      res.json({
        success: true,
        message: `Emergency stop ${newState ? 'activated' : 'deactivated'}`,
        data: {
          isActive: newState,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      logger.error('Error toggling emergency stop:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to toggle emergency stop'
      });
    }
  });

  // Record trade for risk management
  router.post('/risk/record-trade', async (req, res) => {
    try {
      const { profit } = req.body;
      
      if (typeof profit !== 'number') {
        return res.status(400).json({
          success: false,
          error: 'Profit must be a number'
        });
      }
      
      riskService.recordTrade(profit);
      
      res.json({
        success: true,
        message: 'Trade recorded for risk management'
      });
    } catch (error) {
      logger.error('Error recording trade:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to record trade'
      });
    }
  });

  // Update exposure
  router.post('/risk/update-exposure', async (req, res) => {
    try {
      const { exposure } = req.body;
      
      if (typeof exposure !== 'number' || exposure < 0) {
        return res.status(400).json({
          success: false,
          error: 'Exposure must be a non-negative number'
        });
      }
      
      riskService.updateExposure(exposure);
      
      res.json({
        success: true,
        message: 'Exposure updated successfully'
      });
    } catch (error) {
      logger.error('Error updating exposure:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update exposure'
      });
    }
  });

  // Get system statistics
  router.get('/stats', async (req, res) => {
    try {
      const stats = riskService.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting system stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve system statistics'
      });
    }
  });

  // Get system configuration
  router.get('/config', async (req, res) => {
    try {
      const config = {
        riskLimits: riskService.getRiskLimits(),
        emergencyStop: riskService.isEmergencyStopActive(),
        isHealthy: riskService.isHealthy()
      };
      
      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      logger.error('Error getting system config:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve system configuration'
      });
    }
  });

  return router;
}
