#!/usr/bin/env node

/**
 * Test Coverage Analysis System for MEV Arbitrage Bot
 * 
 * Comprehensive coverage analysis that:
 * - Validates >85% coverage target across all services
 * - Generates detailed coverage reports by service
 * - Identifies uncovered critical code paths
 * - Provides recommendations for improving coverage
 * - Integrates with CI/CD pipeline for automated validation
 */

import { execSync } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { glob } from 'glob';

interface CoverageData {
  lines: {
    total: number;
    covered: number;
    skipped: number;
    pct: number;
  };
  functions: {
    total: number;
    covered: number;
    skipped: number;
    pct: number;
  };
  statements: {
    total: number;
    covered: number;
    skipped: number;
    pct: number;
  };
  branches: {
    total: number;
    covered: number;
    skipped: number;
    pct: number;
  };
}

interface FileCoverage {
  path: string;
  coverage: CoverageData;
  uncoveredLines: number[];
  criticalUncovered: string[];
}

interface ServiceCoverage {
  serviceName: string;
  files: FileCoverage[];
  overallCoverage: CoverageData;
  meetsCoverageTarget: boolean;
  recommendations: string[];
}

interface CoverageReport {
  timestamp: string;
  overallCoverage: CoverageData;
  targetCoverage: number;
  meetsTarget: boolean;
  services: ServiceCoverage[];
  summary: {
    totalFiles: number;
    servicesMeetingTarget: number;
    criticalUncoveredAreas: string[];
    recommendations: string[];
  };
}

class TestCoverageAnalyzer {
  private readonly targetCoverage = 85; // 85% target
  private readonly criticalPatterns = [
    'validate',
    'execute',
    'process',
    'handle',
    'calculate',
    'check',
    'verify',
    'authenticate',
    'authorize'
  ];

  constructor(
    private readonly sourceDir = './backend',
    private readonly testDir = './tests',
    private readonly outputDir = './coverage-reports'
  ) {}

  async runCoverageAnalysis(): Promise<CoverageReport> {
    console.log('🔍 Starting comprehensive test coverage analysis...');
    console.log(`Target coverage: ${this.targetCoverage}%`);
    console.log('=' .repeat(60));

    // Run Jest with coverage
    await this.runTestsWithCoverage();

    // Parse coverage data
    const coverageData = await this.parseCoverageData();

    // Analyze by service
    const services = await this.analyzeServiceCoverage(coverageData);

    // Generate overall report
    const report = await this.generateCoverageReport(services);

    // Save reports
    await this.saveCoverageReports(report);

    // Print summary
    this.printCoverageSummary(report);

    return report;
  }

  private async runTestsWithCoverage(): Promise<void> {
    console.log('🧪 Running tests with coverage collection...');
    
    try {
      const command = 'npm run test:coverage';
      execSync(command, { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ Test execution completed');
    } catch (error) {
      console.warn('⚠️  Some tests may have failed, but continuing with coverage analysis');
    }
  }

  private async parseCoverageData(): Promise<any> {
    console.log('📊 Parsing coverage data...');
    
    try {
      const coverageJsonPath = './coverage/coverage-final.json';
      const coverageData = await fs.readFile(coverageJsonPath, 'utf-8');
      return JSON.parse(coverageData);
    } catch (error) {
      throw new Error(`Failed to parse coverage data: ${error.message}`);
    }
  }

  private async analyzeServiceCoverage(coverageData: any): Promise<ServiceCoverage[]> {
    console.log('🔬 Analyzing coverage by service...');
    
    const services: ServiceCoverage[] = [];
    const serviceMap = new Map<string, FileCoverage[]>();

    // Group files by service
    for (const [filePath, fileData] of Object.entries(coverageData)) {
      const serviceName = this.extractServiceName(filePath);
      
      if (!serviceMap.has(serviceName)) {
        serviceMap.set(serviceName, []);
      }

      const fileCoverage = this.analyzeFileCoverage(filePath, fileData as any);
      serviceMap.get(serviceName)!.push(fileCoverage);
    }

    // Analyze each service
    for (const [serviceName, files] of serviceMap) {
      const serviceAnalysis = await this.analyzeService(serviceName, files);
      services.push(serviceAnalysis);
    }

    return services.sort((a, b) => a.serviceName.localeCompare(b.serviceName));
  }

  private extractServiceName(filePath: string): string {
    const pathParts = filePath.split('/');
    
    if (pathParts.includes('services')) {
      const serviceIndex = pathParts.indexOf('services') + 1;
      if (serviceIndex < pathParts.length) {
        const fileName = pathParts[serviceIndex];
        return fileName.replace(/Service\.ts$/, '').replace(/\.ts$/, '');
      }
    }
    
    if (pathParts.includes('backend')) {
      return pathParts[pathParts.indexOf('backend') + 1] || 'core';
    }
    
    return 'other';
  }

  private analyzeFileCoverage(filePath: string, fileData: any): FileCoverage {
    const coverage: CoverageData = {
      lines: {
        total: Object.keys(fileData.l || {}).length,
        covered: Object.values(fileData.l || {}).filter((hits: any) => hits > 0).length,
        skipped: 0,
        pct: 0
      },
      functions: {
        total: Object.keys(fileData.f || {}).length,
        covered: Object.values(fileData.f || {}).filter((hits: any) => hits > 0).length,
        skipped: 0,
        pct: 0
      },
      statements: {
        total: Object.keys(fileData.s || {}).length,
        covered: Object.values(fileData.s || {}).filter((hits: any) => hits > 0).length,
        skipped: 0,
        pct: 0
      },
      branches: {
        total: Object.keys(fileData.b || {}).length * 2, // Approximate
        covered: 0,
        skipped: 0,
        pct: 0
      }
    };

    // Calculate percentages
    coverage.lines.pct = coverage.lines.total > 0 ? (coverage.lines.covered / coverage.lines.total) * 100 : 100;
    coverage.functions.pct = coverage.functions.total > 0 ? (coverage.functions.covered / coverage.functions.total) * 100 : 100;
    coverage.statements.pct = coverage.statements.total > 0 ? (coverage.statements.covered / coverage.statements.total) * 100 : 100;

    // Find uncovered lines
    const uncoveredLines = Object.entries(fileData.l || {})
      .filter(([_, hits]) => hits === 0)
      .map(([line, _]) => parseInt(line));

    // Identify critical uncovered areas
    const criticalUncovered = this.identifyCriticalUncovered(filePath, uncoveredLines);

    return {
      path: filePath,
      coverage,
      uncoveredLines,
      criticalUncovered
    };
  }

  private async identifyCriticalUncovered(filePath: string, uncoveredLines: number[]): Promise<string[]> {
    const critical: string[] = [];
    
    try {
      const fileContent = await fs.readFile(filePath, 'utf-8');
      const lines = fileContent.split('\n');

      for (const lineNum of uncoveredLines) {
        const line = lines[lineNum - 1]?.toLowerCase() || '';
        
        for (const pattern of this.criticalPatterns) {
          if (line.includes(pattern) && (line.includes('function') || line.includes('method') || line.includes('=>'))) {
            critical.push(`Line ${lineNum}: ${lines[lineNum - 1]?.trim()}`);
            break;
          }
        }
      }
    } catch (error) {
      // File might not exist or be readable
    }

    return critical;
  }

  private async analyzeService(serviceName: string, files: FileCoverage[]): Promise<ServiceCoverage> {
    // Calculate overall service coverage
    const totalLines = files.reduce((sum, f) => sum + f.coverage.lines.total, 0);
    const coveredLines = files.reduce((sum, f) => sum + f.coverage.lines.covered, 0);
    const totalFunctions = files.reduce((sum, f) => sum + f.coverage.functions.total, 0);
    const coveredFunctions = files.reduce((sum, f) => sum + f.coverage.functions.covered, 0);
    const totalStatements = files.reduce((sum, f) => sum + f.coverage.statements.total, 0);
    const coveredStatements = files.reduce((sum, f) => sum + f.coverage.statements.covered, 0);

    const overallCoverage: CoverageData = {
      lines: {
        total: totalLines,
        covered: coveredLines,
        skipped: 0,
        pct: totalLines > 0 ? (coveredLines / totalLines) * 100 : 100
      },
      functions: {
        total: totalFunctions,
        covered: coveredFunctions,
        skipped: 0,
        pct: totalFunctions > 0 ? (coveredFunctions / totalFunctions) * 100 : 100
      },
      statements: {
        total: totalStatements,
        covered: coveredStatements,
        skipped: 0,
        pct: totalStatements > 0 ? (coveredStatements / totalStatements) * 100 : 100
      },
      branches: {
        total: 0,
        covered: 0,
        skipped: 0,
        pct: 0
      }
    };

    const meetsCoverageTarget = overallCoverage.lines.pct >= this.targetCoverage;
    const recommendations = this.generateServiceRecommendations(serviceName, files, overallCoverage);

    return {
      serviceName,
      files,
      overallCoverage,
      meetsCoverageTarget,
      recommendations
    };
  }

  private generateServiceRecommendations(serviceName: string, files: FileCoverage[], coverage: CoverageData): string[] {
    const recommendations: string[] = [];

    if (coverage.lines.pct < this.targetCoverage) {
      const gap = this.targetCoverage - coverage.lines.pct;
      recommendations.push(`Increase line coverage by ${gap.toFixed(1)}% to meet target`);
    }

    if (coverage.functions.pct < 80) {
      recommendations.push('Focus on testing more functions, especially public methods');
    }

    // Find files with lowest coverage
    const lowCoverageFiles = files
      .filter(f => f.coverage.lines.pct < 70)
      .sort((a, b) => a.coverage.lines.pct - b.coverage.lines.pct);

    if (lowCoverageFiles.length > 0) {
      recommendations.push(`Priority files for testing: ${lowCoverageFiles.slice(0, 3).map(f => path.basename(f.path)).join(', ')}`);
    }

    // Check for critical uncovered areas
    const criticalUncovered = files.flatMap(f => f.criticalUncovered);
    if (criticalUncovered.length > 0) {
      recommendations.push(`Critical uncovered areas found: ${criticalUncovered.length} functions need testing`);
    }

    return recommendations;
  }

  private async generateCoverageReport(services: ServiceCoverage[]): Promise<CoverageReport> {
    const totalFiles = services.reduce((sum, s) => sum + s.files.length, 0);
    const servicesMeetingTarget = services.filter(s => s.meetsCoverageTarget).length;

    // Calculate overall coverage
    const allFiles = services.flatMap(s => s.files);
    const totalLines = allFiles.reduce((sum, f) => sum + f.coverage.lines.total, 0);
    const coveredLines = allFiles.reduce((sum, f) => sum + f.coverage.lines.covered, 0);
    const totalFunctions = allFiles.reduce((sum, f) => sum + f.coverage.functions.total, 0);
    const coveredFunctions = allFiles.reduce((sum, f) => sum + f.coverage.functions.covered, 0);
    const totalStatements = allFiles.reduce((sum, f) => sum + f.coverage.statements.total, 0);
    const coveredStatements = allFiles.reduce((sum, f) => sum + f.coverage.statements.covered, 0);

    const overallCoverage: CoverageData = {
      lines: {
        total: totalLines,
        covered: coveredLines,
        skipped: 0,
        pct: totalLines > 0 ? (coveredLines / totalLines) * 100 : 100
      },
      functions: {
        total: totalFunctions,
        covered: coveredFunctions,
        skipped: 0,
        pct: totalFunctions > 0 ? (coveredFunctions / totalFunctions) * 100 : 100
      },
      statements: {
        total: totalStatements,
        covered: coveredStatements,
        skipped: 0,
        pct: totalStatements > 0 ? (coveredStatements / totalStatements) * 100 : 100
      },
      branches: {
        total: 0,
        covered: 0,
        skipped: 0,
        pct: 0
      }
    };

    const criticalUncoveredAreas = services
      .flatMap(s => s.files.flatMap(f => f.criticalUncovered))
      .slice(0, 10); // Top 10 critical areas

    const recommendations = this.generateOverallRecommendations(services, overallCoverage);

    return {
      timestamp: new Date().toISOString(),
      overallCoverage,
      targetCoverage: this.targetCoverage,
      meetsTarget: overallCoverage.lines.pct >= this.targetCoverage,
      services,
      summary: {
        totalFiles,
        servicesMeetingTarget,
        criticalUncoveredAreas,
        recommendations
      }
    };
  }

  private generateOverallRecommendations(services: ServiceCoverage[], coverage: CoverageData): string[] {
    const recommendations: string[] = [];

    if (!coverage || coverage.lines.pct < this.targetCoverage) {
      recommendations.push(`Overall coverage is ${coverage?.lines.pct.toFixed(1)}%, need ${(this.targetCoverage - (coverage?.lines.pct || 0)).toFixed(1)}% more to meet target`);
    }

    const lowCoverageServices = services
      .filter(s => !s.meetsCoverageTarget)
      .sort((a, b) => a.overallCoverage.lines.pct - b.overallCoverage.lines.pct);

    if (lowCoverageServices.length > 0) {
      recommendations.push(`Priority services for testing: ${lowCoverageServices.slice(0, 3).map(s => s.serviceName).join(', ')}`);
    }

    if (services.length > 0) {
      const avgCoverage = services.reduce((sum, s) => sum + s.overallCoverage.lines.pct, 0) / services.length;
      if (avgCoverage < 80) {
        recommendations.push('Focus on unit testing for individual services before integration tests');
      }
    }

    return recommendations;
  }

  private async saveCoverageReports(report: CoverageReport): Promise<void> {
    await fs.mkdir(this.outputDir, { recursive: true });

    // Save JSON report
    const jsonPath = path.join(this.outputDir, `coverage-analysis-${Date.now()}.json`);
    await fs.writeFile(jsonPath, JSON.stringify(report, null, 2));

    // Save CSV summary
    const csvPath = path.join(this.outputDir, `coverage-summary-${Date.now()}.csv`);
    await this.saveCsvSummary(report, csvPath);

    console.log(`\n📄 Coverage reports saved:`);
    console.log(`   JSON: ${jsonPath}`);
    console.log(`   CSV: ${csvPath}`);
  }

  private async saveCsvSummary(report: CoverageReport, csvPath: string): Promise<void> {
    const headers = [
      'Service',
      'Files',
      'Lines Total',
      'Lines Covered',
      'Line Coverage %',
      'Functions Total',
      'Functions Covered',
      'Function Coverage %',
      'Meets Target',
      'Recommendations'
    ];

    const rows = report.services.map(service => [
      service.serviceName,
      service.files.length,
      service.overallCoverage.lines.total,
      service.overallCoverage.lines.covered,
      service.overallCoverage.lines.pct.toFixed(2),
      service.overallCoverage.functions.total,
      service.overallCoverage.functions.covered,
      service.overallCoverage.functions.pct.toFixed(2),
      service.meetsCoverageTarget ? 'Yes' : 'No',
      service.recommendations.join('; ')
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    await fs.writeFile(csvPath, csvContent);
  }

  private printCoverageSummary(report: CoverageReport): void {
    console.log('\n📊 Coverage Analysis Summary');
    console.log('=' .repeat(60));
    
    const status = report.meetsTarget ? '✅' : '❌';
    console.log(`${status} Overall Coverage: ${report.overallCoverage.lines.pct.toFixed(2)}% (Target: ${report.targetCoverage}%)`);
    console.log(`📁 Total Files: ${report.summary.totalFiles}`);
    console.log(`🎯 Services Meeting Target: ${report.summary.servicesMeetingTarget}/${report.services.length}`);
    
    console.log('\n🔍 Service Breakdown:');
    for (const service of report.services) {
      const status = service.meetsCoverageTarget ? '✅' : '❌';
      console.log(`${status} ${service.serviceName}: ${service.overallCoverage.lines.pct.toFixed(2)}% (${service.files.length} files)`);
    }

    if (report.summary.criticalUncoveredAreas.length > 0) {
      console.log('\n⚠️  Critical Uncovered Areas:');
      report.summary.criticalUncoveredAreas.slice(0, 5).forEach(area => {
        console.log(`   • ${area}`);
      });
    }

    if (report.summary.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.summary.recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    }

    console.log('\n' + '=' .repeat(60));
  }
}

// Main execution
async function main() {
  console.log('🧪 MEV Arbitrage Bot - Test Coverage Analysis');
  console.log('Validating >85% coverage target across all services');
  console.log('=' .repeat(60));

  const analyzer = new TestCoverageAnalyzer();

  try {
    const report = await analyzer.runCoverageAnalysis();
    
    if (report.meetsTarget) {
      console.log('\n🎉 Coverage target achieved! All systems go for production.');
      process.exit(0);
    } else {
      console.log('\n⚠️  Coverage target not met. Review recommendations and add more tests.');
      process.exit(1);
    }

  } catch (error) {
    console.error('\n💥 Coverage analysis failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { TestCoverageAnalyzer };
