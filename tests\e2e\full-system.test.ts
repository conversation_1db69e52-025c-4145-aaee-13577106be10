import { jest, describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { spawn, ChildProcess } from 'child_process';
import WebSocket from 'ws';
import axios from 'axios';

describe('End-to-End System Tests', () => {
  let backendProcess: ChildProcess;
  let frontendProcess: ChildProcess;
  const BACKEND_PORT = 3003;
  const FRONTEND_PORT = 5174;
  const BACKEND_URL = `http://localhost:${BACKEND_PORT}`;
  const WS_URL = `ws://localhost:${BACKEND_PORT}`;

  beforeAll(async () => {
    // Start backend server
    backendProcess = spawn('npm', ['run', 'dev:backend'], {
      env: { ...process.env, PORT: BACKEND_PORT.toString() },
      stdio: 'pipe'
    });

    // Start frontend server
    frontendProcess = spawn('npm', ['run', 'dev:frontend'], {
      env: { ...process.env, PORT: FRONTEND_PORT.toString() },
      stdio: 'pipe'
    });

    // Wait for servers to start
    await waitForServer(BACKEND_URL, 30000);
    await waitForServer(`http://localhost:${FRONTEND_PORT}`, 30000);
  }, 60000);

  afterAll(async () => {
    // Cleanup processes
    if (backendProcess) {
      backendProcess.kill('SIGTERM');
    }
    if (frontendProcess) {
      frontendProcess.kill('SIGTERM');
    }

    // Wait for graceful shutdown
    await new Promise(resolve => setTimeout(resolve, 2000));
  });

  async function waitForServer(url: string, timeout: number): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        await axios.get(`${url}/api/health`);
        return;
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    throw new Error(`Server at ${url} did not start within ${timeout}ms`);
  }

  describe('System Health', () => {
    it('should have backend server running', async () => {
      const response = await axios.get(`${BACKEND_URL}/api/health`);
      expect(response.status).toBe(200);
      expect(response.data.status).toBe('ok');
    });

    it('should have all services initialized', async () => {
      const response = await axios.get(`${BACKEND_URL}/api/system/status`);
      expect(response.status).toBe(200);
      
      const services = response.data.services;
      expect(services.priceFeed.status).toBe('running');
      expect(services.tokenDiscovery.status).toBe('running');
      expect(services.opportunityDetection.status).toBe('running');
      expect(services.execution.status).toBe('running');
      expect(services.riskManagement.status).toBe('running');
      expect(services.analytics.status).toBe('running');
    });
  });

  describe('Real-time Data Flow', () => {
    it('should stream price updates via WebSocket', (done) => {
      const ws = new WebSocket(WS_URL);
      let messageReceived = false;

      ws.on('open', () => {
        // Subscribe to price updates
        ws.send(JSON.stringify({
          type: 'subscribe',
          channel: 'priceUpdates'
        }));
      });

      ws.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'priceUpdate' && !messageReceived) {
          messageReceived = true;
          expect(message.data).toBeDefined();
          expect(message.data.symbol).toBeDefined();
          expect(message.data.price).toBeDefined();
          expect(message.data.timestamp).toBeDefined();
          
          ws.close();
          done();
        }
      });

      ws.on('error', done);

      // Timeout after 10 seconds
      setTimeout(() => {
        if (!messageReceived) {
          ws.close();
          done(new Error('No price update received within timeout'));
        }
      }, 10000);
    });

    it('should stream opportunity updates via WebSocket', (done) => {
      const ws = new WebSocket(WS_URL);
      let opportunityReceived = false;

      ws.on('open', () => {
        ws.send(JSON.stringify({
          type: 'subscribe',
          channel: 'opportunities'
        }));
      });

      ws.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'opportunity' && !opportunityReceived) {
          opportunityReceived = true;
          expect(message.data).toBeDefined();
          expect(message.data.id).toBeDefined();
          expect(message.data.type).toBeDefined();
          expect(message.data.potentialProfit).toBeDefined();
          
          ws.close();
          done();
        }
      });

      ws.on('error', done);

      setTimeout(() => {
        if (!opportunityReceived) {
          ws.close();
          done(new Error('No opportunity received within timeout'));
        }
      }, 15000);
    });
  });

  describe('API Functionality', () => {
    it('should return current opportunities', async () => {
      const response = await axios.get(`${BACKEND_URL}/api/opportunities`);
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      
      if (response.data.length > 0) {
        const opportunity = response.data[0];
        expect(opportunity.id).toBeDefined();
        expect(opportunity.type).toBeDefined();
        expect(opportunity.potentialProfit).toBeDefined();
        expect(opportunity.assets).toBeDefined();
        expect(opportunity.exchanges).toBeDefined();
      }
    });

    it('should return price data', async () => {
      const response = await axios.get(`${BACKEND_URL}/api/prices`);
      expect(response.status).toBe(200);
      expect(typeof response.data).toBe('object');
    });

    it('should return token information', async () => {
      const response = await axios.get(`${BACKEND_URL}/api/tokens`);
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });

    it('should return analytics data', async () => {
      const response = await axios.get(`${BACKEND_URL}/api/analytics`);
      expect(response.status).toBe(200);
      expect(typeof response.data).toBe('object');
      
      const analytics = response.data;
      expect(analytics.totalOpportunities).toBeDefined();
      expect(analytics.successRate).toBeDefined();
      expect(analytics.totalProfit).toBeDefined();
    });

    it('should return trade history', async () => {
      const response = await axios.get(`${BACKEND_URL}/api/trades`);
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });
  });

  describe('Performance Under Load', () => {
    it('should handle concurrent API requests', async () => {
      const concurrentRequests = 20;
      const requests = Array.from({ length: concurrentRequests }, () =>
        axios.get(`${BACKEND_URL}/api/opportunities`)
      );

      const startTime = Date.now();
      const responses = await Promise.all(requests);
      const endTime = Date.now();

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // Should handle all requests within reasonable time
      const totalTime = endTime - startTime;
      expect(totalTime).toBeLessThan(5000); // 5 seconds
    });

    it('should maintain WebSocket connections under load', async () => {
      const connectionCount = 10;
      const connections: WebSocket[] = [];
      const messagePromises: Promise<void>[] = [];

      // Create multiple WebSocket connections
      for (let i = 0; i < connectionCount; i++) {
        const ws = new WebSocket(WS_URL);
        connections.push(ws);

        const messagePromise = new Promise<void>((resolve, reject) => {
          ws.on('open', () => {
            ws.send(JSON.stringify({
              type: 'subscribe',
              channel: 'priceUpdates'
            }));
          });

          ws.on('message', (data) => {
            const message = JSON.parse(data.toString());
            if (message.type === 'priceUpdate') {
              resolve();
            }
          });

          ws.on('error', reject);

          setTimeout(() => reject(new Error('Timeout')), 10000);
        });

        messagePromises.push(messagePromise);
      }

      // Wait for all connections to receive messages
      await Promise.all(messagePromises);

      // Cleanup connections
      connections.forEach(ws => ws.close());
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid API endpoints gracefully', async () => {
      try {
        await axios.get(`${BACKEND_URL}/api/nonexistent`);
      } catch (error: any) {
        expect(error.response.status).toBe(404);
      }
    });

    it('should handle malformed requests', async () => {
      try {
        await axios.post(`${BACKEND_URL}/api/opportunities`, {
          invalidData: 'test'
        });
      } catch (error: any) {
        expect(error.response.status).toBeGreaterThanOrEqual(400);
        expect(error.response.status).toBeLessThan(500);
      }
    });

    it('should recover from service errors', async () => {
      // Trigger an error condition
      try {
        await axios.post(`${BACKEND_URL}/api/system/error`, {
          service: 'priceFeed',
          error: 'test error'
        });
      } catch (error) {
        // Expected to fail
      }

      // System should still be responsive
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const response = await axios.get(`${BACKEND_URL}/api/health`);
      expect(response.status).toBe(200);
    });
  });

  describe('Data Consistency', () => {
    it('should maintain data consistency across services', async () => {
      // Get initial state
      const initialOpportunities = await axios.get(`${BACKEND_URL}/api/opportunities`);
      const initialPrices = await axios.get(`${BACKEND_URL}/api/prices`);

      // Wait for some processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Get updated state
      const updatedOpportunities = await axios.get(`${BACKEND_URL}/api/opportunities`);
      const updatedPrices = await axios.get(`${BACKEND_URL}/api/prices`);

      // Data should be consistent and potentially updated
      expect(updatedOpportunities.status).toBe(200);
      expect(updatedPrices.status).toBe(200);
      
      // Opportunities should reference valid price data
      if (updatedOpportunities.data.length > 0) {
        const opportunity = updatedOpportunities.data[0];
        const priceData = updatedPrices.data;
        
        opportunity.assets.forEach((asset: string) => {
          // Each asset in opportunity should have price data or be a known stable asset
          const hasPrice = priceData[asset] || ['USDC', 'USDT', 'DAI'].includes(asset);
          expect(hasPrice).toBeTruthy();
        });
      }
    });
  });
});
