import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';
import { ValidationResult } from './PreExecutionValidationService.js';

export interface QueuedOpportunity {
  opportunity: ArbitrageOpportunity;
  priority: number;
  queueTime: number;
  estimatedExecutionTime: number;
  decayRate: number;
  riskAdjustedProfit: number;
  confidenceScore: number;
}

export interface QueueMetrics {
  totalOpportunities: number;
  averageWaitTime: number;
  averagePriority: number;
  queueThroughput: number;
  rejectedOpportunities: number;
  expiredOpportunities: number;
  reorderedCount: number;
  lastReorderTime: number;
}

export interface ExecutionCapacity {
  maxConcurrentTrades: number;
  currentActiveTrades: number;
  availableCapacity: number;
  flashLoanCapacity: number;
  riskCapacityUsed: number;
  maxRiskCapacity: number;
}

export interface PriorityWeights {
  netProfitWeight: number;
  profitMarginWeight: number;
  confidenceWeight: number;
  timeSensitivityWeight: number;
  riskAdjustmentWeight: number;
}

export class ProfitPrioritizedExecutionQueue extends EventEmitter {
  private queue: QueuedOpportunity[] = [];
  private executionHistory: Map<string, QueuedOpportunity> = new Map();
  private isRunning = false;
  private reorderInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  
  // Queue configuration
  private readonly maxQueueSize = parseInt(config.MAX_QUEUE_SIZE || '100');
  private readonly maxOpportunityAge = parseInt(config.MAX_OPPORTUNITY_AGE || '30000'); // 30 seconds
  private readonly reorderFrequency = parseInt(config.QUEUE_REORDER_FREQUENCY || '5000'); // 5 seconds
  private readonly cleanupFrequency = parseInt(config.QUEUE_CLEANUP_FREQUENCY || '10000'); // 10 seconds
  
  // Priority weights configuration
  private priorityWeights: PriorityWeights = {
    netProfitWeight: parseFloat(config.NET_PROFIT_WEIGHT || '0.4'),
    profitMarginWeight: parseFloat(config.PROFIT_MARGIN_WEIGHT || '0.25'),
    confidenceWeight: parseFloat(config.CONFIDENCE_WEIGHT || '0.2'),
    timeSensitivityWeight: parseFloat(config.TIME_SENSITIVITY_WEIGHT || '0.1'),
    riskAdjustmentWeight: parseFloat(config.RISK_ADJUSTMENT_WEIGHT || '0.05')
  };
  
  // Execution capacity tracking
  private executionCapacity: ExecutionCapacity = {
    maxConcurrentTrades: parseInt(config.MAX_CONCURRENT_TRADES || '3'),
    currentActiveTrades: 0,
    availableCapacity: 0,
    flashLoanCapacity: parseFloat(config.FLASH_LOAN_MAX_AMOUNT || '1000000'),
    riskCapacityUsed: 0,
    maxRiskCapacity: parseFloat(config.MAX_RISK_CAPACITY || '100000')
  };
  
  // Queue metrics
  private queueMetrics: QueueMetrics = {
    totalOpportunities: 0,
    averageWaitTime: 0,
    averagePriority: 0,
    queueThroughput: 0,
    rejectedOpportunities: 0,
    expiredOpportunities: 0,
    reorderedCount: 0,
    lastReorderTime: 0
  };

  constructor() {
    super();
    this.updateExecutionCapacity();
  }

  public async start() {
    if (this.isRunning) return;
    
    logger.info('Starting Profit Prioritized Execution Queue...');
    this.isRunning = true;

    // Start queue reordering
    this.reorderInterval = setInterval(() => {
      this.reorderQueue();
    }, this.reorderFrequency);

    // Start queue cleanup
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredOpportunities();
    }, this.cleanupFrequency);

    logger.info('Profit Prioritized Execution Queue started');
  }

  public async stop() {
    if (!this.isRunning) return;
    
    logger.info('Stopping Profit Prioritized Execution Queue...');
    this.isRunning = false;

    if (this.reorderInterval) {
      clearInterval(this.reorderInterval);
      this.reorderInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    logger.info('Profit Prioritized Execution Queue stopped');
  }

  /**
   * Add opportunity to the queue with priority calculation
   */
  public addOpportunity(
    opportunity: ArbitrageOpportunity,
    validationResult?: ValidationResult
  ): boolean {
    
    try {
      // Check queue capacity
      if (this.queue.length >= this.maxQueueSize) {
        logger.warn(`Queue at capacity (${this.maxQueueSize}), rejecting opportunity ${opportunity.id}`);
        this.queueMetrics.rejectedOpportunities++;
        return false;
      }

      // Check if opportunity already exists
      if (this.queue.some(q => q.opportunity.id === opportunity.id)) {
        logger.debug(`Opportunity ${opportunity.id} already in queue`);
        return false;
      }

      // Calculate priority and queue metrics
      const queuedOpportunity = this.createQueuedOpportunity(opportunity, validationResult);
      
      // Check execution capacity constraints
      if (!this.checkExecutionCapacity(queuedOpportunity)) {
        logger.debug(`Insufficient capacity for opportunity ${opportunity.id}`);
        this.queueMetrics.rejectedOpportunities++;
        return false;
      }

      // Add to queue
      this.queue.push(queuedOpportunity);
      this.queueMetrics.totalOpportunities++;

      // Sort queue by priority (highest first)
      this.sortQueueByPriority();

      logger.info(`Added opportunity ${opportunity.id} to queue with priority ${queuedOpportunity.priority.toFixed(2)}`);
      
      // Emit queue update event
      this.emit('opportunityQueued', {
        opportunity: queuedOpportunity,
        queueSize: this.queue.length,
        position: this.queue.findIndex(q => q.opportunity.id === opportunity.id) + 1
      });

      return true;

    } catch (error) {
      logger.error(`Failed to add opportunity ${opportunity.id} to queue:`, error);
      this.queueMetrics.rejectedOpportunities++;
      return false;
    }
  }

  /**
   * Get next opportunity for execution
   */
  public getNextOpportunity(): QueuedOpportunity | null {
    if (this.queue.length === 0) return null;
    
    // Update execution capacity
    this.updateExecutionCapacity();
    
    // Check if we have capacity for execution
    if (this.executionCapacity.availableCapacity <= 0) {
      logger.debug('No execution capacity available');
      return null;
    }

    // Get highest priority opportunity
    const queuedOpportunity = this.queue.shift();
    if (!queuedOpportunity) return null;

    // Update metrics
    const waitTime = Date.now() - queuedOpportunity.queueTime;
    this.updateQueueMetrics(waitTime);

    // Track execution
    this.executionHistory.set(queuedOpportunity.opportunity.id, queuedOpportunity);
    this.executionCapacity.currentActiveTrades++;

    logger.info(`Dequeued opportunity ${queuedOpportunity.opportunity.id} for execution (waited ${waitTime}ms)`);
    
    // Emit dequeue event
    this.emit('opportunityDequeued', {
      opportunity: queuedOpportunity,
      waitTime,
      queueSize: this.queue.length
    });

    return queuedOpportunity;
  }

  /**
   * Create queued opportunity with priority calculation
   */
  private createQueuedOpportunity(
    opportunity: ArbitrageOpportunity,
    validationResult?: ValidationResult
  ): QueuedOpportunity {
    
    const now = Date.now();
    
    // Calculate net profit (after all costs)
    const netProfit = validationResult?.simulatedProfit || opportunity.potentialProfit;
    
    // Calculate profit margin
    const profitMargin = validationResult?.profitMargin || 
      (netProfit / opportunity.potentialProfit) * 100;
    
    // Calculate confidence score
    const confidenceScore = this.calculateConfidenceScore(opportunity, validationResult);
    
    // Calculate decay rate based on opportunity type and market conditions
    const decayRate = this.calculateDecayRate(opportunity);
    
    // Calculate risk-adjusted profit
    const riskScore = validationResult?.riskScore || 50;
    const riskAdjustedProfit = netProfit * (1 - riskScore / 200); // Reduce profit by risk
    
    // Calculate priority score
    const priority = this.calculatePriority(
      netProfit,
      profitMargin,
      confidenceScore,
      decayRate,
      riskAdjustedProfit
    );
    
    // Estimate execution time
    const estimatedExecutionTime = this.estimateExecutionTime(opportunity);

    return {
      opportunity,
      priority,
      queueTime: now,
      estimatedExecutionTime,
      decayRate,
      riskAdjustedProfit,
      confidenceScore
    };
  }

  /**
   * Calculate priority score using weighted factors
   */
  private calculatePriority(
    netProfit: number,
    profitMargin: number,
    confidenceScore: number,
    decayRate: number,
    riskAdjustedProfit: number
  ): number {
    
    // Normalize factors to 0-100 scale
    const normalizedNetProfit = Math.min(netProfit / 1000 * 100, 100); // $1000 = 100 points
    const normalizedProfitMargin = Math.min(profitMargin, 100);
    const normalizedConfidence = confidenceScore;
    const normalizedTimeSensitivity = Math.max(0, 100 - decayRate * 10); // Higher decay = lower score
    const normalizedRiskAdjustment = Math.min(riskAdjustedProfit / 500 * 100, 100); // $500 = 100 points
    
    // Calculate weighted priority
    const priority = (
      normalizedNetProfit * this.priorityWeights.netProfitWeight +
      normalizedProfitMargin * this.priorityWeights.profitMarginWeight +
      normalizedConfidence * this.priorityWeights.confidenceWeight +
      normalizedTimeSensitivity * this.priorityWeights.timeSensitivityWeight +
      normalizedRiskAdjustment * this.priorityWeights.riskAdjustmentWeight
    );

    return Math.max(0, Math.min(100, priority)); // Clamp to 0-100
  }

  /**
   * Calculate confidence score based on opportunity characteristics
   */
  private calculateConfidenceScore(
    opportunity: ArbitrageOpportunity,
    validationResult?: ValidationResult
  ): number {
    
    let confidence = opportunity.confidence || 50;
    
    // Adjust based on validation result
    if (validationResult) {
      // Higher confidence if validation passed with good margin
      if (validationResult.isValid && validationResult.profitMargin > 20) {
        confidence += 20;
      }
      
      // Lower confidence for high risk scores
      if (validationResult.riskScore > 70) {
        confidence -= 15;
      }
      
      // Adjust based on flash loan optimization
      if (validationResult.flashLoanOptimization) {
        confidence += 10; // Flash loans increase execution confidence
      }
    }
    
    // Adjust based on opportunity type
    switch (opportunity.type) {
      case ArbitrageType.INTRA_CHAIN:
        confidence += 10; // Simpler execution
        break;
      case ArbitrageType.CROSS_CHAIN:
        confidence -= 15; // More complex, higher risk
        break;
      case ArbitrageType.TRIANGULAR:
        confidence -= 5; // Moderate complexity
        break;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * Calculate decay rate (how quickly opportunity value decreases)
   */
  private calculateDecayRate(opportunity: ArbitrageOpportunity): number {
    // Base decay rate depends on opportunity type
    let decayRate = 1; // per second
    
    switch (opportunity.type) {
      case ArbitrageType.INTRA_CHAIN:
        decayRate = 2; // Fast decay - price differences close quickly
        break;
      case ArbitrageType.CROSS_CHAIN:
        decayRate = 0.5; // Slower decay - cross-chain takes time
        break;
      case ArbitrageType.TRIANGULAR:
        decayRate = 1.5; // Moderate decay
        break;
    }
    
    // Adjust based on market volatility (simplified)
    const volatilityMultiplier = 1 + (Math.random() * 0.5); // 1-1.5x
    
    return decayRate * volatilityMultiplier;
  }

  /**
   * Estimate execution time for opportunity
   */
  private estimateExecutionTime(opportunity: ArbitrageOpportunity): number {
    // Base execution time in milliseconds
    let executionTime = 5000; // 5 seconds base
    
    switch (opportunity.type) {
      case ArbitrageType.INTRA_CHAIN:
        executionTime = 3000; // 3 seconds
        break;
      case ArbitrageType.CROSS_CHAIN:
        executionTime = 15000; // 15 seconds
        break;
      case ArbitrageType.TRIANGULAR:
        executionTime = 8000; // 8 seconds
        break;
    }
    
    // Add network congestion delay
    const congestionDelay = Math.random() * 5000; // 0-5 seconds
    
    return executionTime + congestionDelay;
  }

  /**
   * Check if we have capacity to execute opportunity
   */
  private checkExecutionCapacity(queuedOpportunity: QueuedOpportunity): boolean {
    // Check concurrent trades limit
    if (this.executionCapacity.currentActiveTrades >= this.executionCapacity.maxConcurrentTrades) {
      return false;
    }
    
    // Check risk capacity
    const opportunityRisk = queuedOpportunity.riskAdjustedProfit;
    if (this.executionCapacity.riskCapacityUsed + opportunityRisk > this.executionCapacity.maxRiskCapacity) {
      return false;
    }
    
    // Check flash loan capacity if needed
    const flashLoanOptimization = queuedOpportunity.opportunity.validationResult?.flashLoanOptimization;
    if (flashLoanOptimization && flashLoanOptimization.optimalAmount > this.executionCapacity.flashLoanCapacity) {
      return false;
    }
    
    return true;
  }

  /**
   * Update execution capacity metrics
   */
  private updateExecutionCapacity() {
    this.executionCapacity.availableCapacity = 
      this.executionCapacity.maxConcurrentTrades - this.executionCapacity.currentActiveTrades;
  }

  /**
   * Sort queue by priority (highest first)
   */
  private sortQueueByPriority() {
    this.queue.sort((a, b) => {
      // Apply time decay to priority
      const now = Date.now();
      const aDecayedPriority = a.priority * Math.exp(-a.decayRate * (now - a.queueTime) / 1000);
      const bDecayedPriority = b.priority * Math.exp(-b.decayRate * (now - b.queueTime) / 1000);
      
      return bDecayedPriority - aDecayedPriority;
    });
  }

  /**
   * Reorder queue based on updated priorities and market conditions
   */
  private reorderQueue() {
    if (this.queue.length <= 1) return;
    
    const beforeOrder = this.queue.map(q => q.opportunity.id);
    this.sortQueueByPriority();
    const afterOrder = this.queue.map(q => q.opportunity.id);
    
    // Check if order changed
    const orderChanged = !beforeOrder.every((id, index) => id === afterOrder[index]);
    
    if (orderChanged) {
      this.queueMetrics.reorderedCount++;
      this.queueMetrics.lastReorderTime = Date.now();
      
      logger.debug(`Queue reordered - ${this.queue.length} opportunities`);
      
      this.emit('queueReordered', {
        queueSize: this.queue.length,
        topOpportunities: this.queue.slice(0, 5).map(q => ({
          id: q.opportunity.id,
          priority: q.priority,
          netProfit: q.riskAdjustedProfit
        }))
      });
    }
  }

  /**
   * Clean up expired opportunities
   */
  private cleanupExpiredOpportunities() {
    const now = Date.now();
    const beforeSize = this.queue.length;
    
    this.queue = this.queue.filter(queuedOpp => {
      const age = now - queuedOpp.opportunity.timestamp;
      const isExpired = age > this.maxOpportunityAge;
      
      if (isExpired) {
        logger.debug(`Removing expired opportunity ${queuedOpp.opportunity.id} (age: ${age}ms)`);
        this.queueMetrics.expiredOpportunities++;
      }
      
      return !isExpired;
    });
    
    const removedCount = beforeSize - this.queue.length;
    if (removedCount > 0) {
      logger.info(`Cleaned up ${removedCount} expired opportunities`);
      this.emit('opportunitiesExpired', { count: removedCount, queueSize: this.queue.length });
    }
  }

  /**
   * Update queue metrics
   */
  private updateQueueMetrics(waitTime: number) {
    // Update average wait time (rolling average)
    this.queueMetrics.averageWaitTime = 
      (this.queueMetrics.averageWaitTime * 0.9) + (waitTime * 0.1);
    
    // Update average priority
    if (this.queue.length > 0) {
      this.queueMetrics.averagePriority = 
        this.queue.reduce((sum, q) => sum + q.priority, 0) / this.queue.length;
    }
    
    // Calculate throughput (opportunities per minute)
    this.queueMetrics.queueThroughput = this.queueMetrics.totalOpportunities / 
      Math.max(1, (Date.now() - (Date.now() - 60000)) / 60000);
  }

  /**
   * Mark trade as completed to free up capacity
   */
  public markTradeCompleted(opportunityId: string, success: boolean) {
    const queuedOpportunity = this.executionHistory.get(opportunityId);
    if (queuedOpportunity) {
      this.executionCapacity.currentActiveTrades = Math.max(0, this.executionCapacity.currentActiveTrades - 1);
      this.executionCapacity.riskCapacityUsed = Math.max(0, 
        this.executionCapacity.riskCapacityUsed - queuedOpportunity.riskAdjustedProfit);
      
      this.updateExecutionCapacity();
      
      logger.debug(`Trade ${opportunityId} completed (${success ? 'success' : 'failed'}), capacity freed`);
    }
  }

  /**
   * Get queue statistics
   */
  public getQueueStats() {
    return {
      isRunning: this.isRunning,
      queueSize: this.queue.length,
      executionCapacity: { ...this.executionCapacity },
      queueMetrics: { ...this.queueMetrics },
      priorityWeights: { ...this.priorityWeights },
      topOpportunities: this.queue.slice(0, 10).map(q => ({
        id: q.opportunity.id,
        priority: q.priority,
        netProfit: q.riskAdjustedProfit,
        waitTime: Date.now() - q.queueTime,
        type: q.opportunity.type
      }))
    };
  }

  /**
   * Get current queue contents
   */
  public getQueueContents(): QueuedOpportunity[] {
    return [...this.queue];
  }

  /**
   * Clear the queue (emergency function)
   */
  public clearQueue(): number {
    const clearedCount = this.queue.length;
    this.queue = [];
    logger.warn(`Queue cleared - removed ${clearedCount} opportunities`);
    this.emit('queueCleared', { clearedCount });
    return clearedCount;
  }
}
