import WebSocket from 'ws';
import { Server } from 'http';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { databaseManager } from '../services/DatabaseConnectionManager.js';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
  id: string;
}

export interface ClientConnection {
  id: string;
  ws: WebSocket;
  lastHeartbeat: number;
  subscriptions: Set<string>;
  isAlive: boolean;
}

export interface SubscriptionFilter {
  type: string;
  networks?: string[];
  symbols?: string[];
  minProfit?: number;
  maxLatency?: number;
}

export class EnhancedWebSocketService {
  private wss: WebSocket.Server | null = null;
  private clients: Map<string, ClientConnection> = new Map();
  private subscriptions: Map<string, Set<string>> = new Map();
  private messageQueue: Map<string, WebSocketMessage[]> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private dataUpdateInterval: NodeJS.Timeout | null = null;
  private metrics = {
    totalConnections: 0,
    activeConnections: 0,
    messagesSent: 0,
    messagesReceived: 0,
    errors: 0,
    averageLatency: 0
  };

  constructor() {
    this.initializeSubscriptionTypes();
  }

  private initializeSubscriptionTypes(): void {
    const subscriptionTypes = [
      'opportunity.detected',
      'opportunity.updated',
      'opportunity.expired',
      'trade.executed',
      'trade.failed',
      'validation.completed',
      'queue.updated',
      'queue.reordered',
      'system.health',
      'price.updated',
      'profit.validated',
      'mev.protected',
      'flash_loan.quoted',
      'service.status'
    ];

    subscriptionTypes.forEach(type => {
      this.subscriptions.set(type, new Set());
    });
  }

  initialize(server: Server): void {
    this.wss = new WebSocket.Server({
      server,
      path: '/ws',
      maxPayload: 1024 * 1024, // 1MB max payload
      perMessageDeflate: {
        zlibDeflateOptions: {
          level: 3,
          chunkSize: 1024
        },
        threshold: 1024,
        concurrencyLimit: 10
      }
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startHeartbeat();
    this.startDataUpdates();

    logger.info('Enhanced WebSocket service initialized');
  }

  private handleConnection(ws: WebSocket, request: any): void {
    const clientId = this.generateClientId();
    const client: ClientConnection = {
      id: clientId,
      ws,
      lastHeartbeat: Date.now(),
      subscriptions: new Set(),
      isAlive: true
    };

    this.clients.set(clientId, client);
    this.metrics.totalConnections++;
    this.metrics.activeConnections++;

    logger.info(`WebSocket client connected: ${clientId}`);

    // Send welcome message
    this.sendToClient(clientId, {
      type: 'connection.established',
      data: {
        clientId,
        serverTime: Date.now(),
        availableSubscriptions: Array.from(this.subscriptions.keys())
      },
      timestamp: Date.now(),
      id: this.generateMessageId()
    });

    ws.on('message', (data) => this.handleMessage(clientId, data));
    ws.on('close', () => this.handleDisconnection(clientId));
    ws.on('error', (error) => this.handleError(clientId, error));
    ws.on('pong', () => this.handlePong(clientId));

    // Limit connections
    if (this.metrics.activeConnections > parseInt(config.WEBSOCKET_MAX_CONNECTIONS)) {
      this.disconnectOldestClient();
    }
  }

  private handleMessage(clientId: string, data: WebSocket.Data): void {
    try {
      const message = JSON.parse(data.toString());
      this.metrics.messagesReceived++;

      switch (message.type) {
        case 'subscribe':
          this.handleSubscription(clientId, message.data);
          break;
        case 'unsubscribe':
          this.handleUnsubscription(clientId, message.data);
          break;
        case 'heartbeat':
          this.handleHeartbeat(clientId);
          break;
        case 'get_data':
          this.handleDataRequest(clientId, message.data);
          break;
        default:
          logger.warn(`Unknown message type from ${clientId}: ${message.type}`);
      }
    } catch (error) {
      logger.error(`Error handling message from ${clientId}:`, error);
      this.metrics.errors++;
    }
  }

  private handleSubscription(clientId: string, subscriptionData: SubscriptionFilter): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { type } = subscriptionData;
    
    if (this.subscriptions.has(type)) {
      this.subscriptions.get(type)!.add(clientId);
      client.subscriptions.add(type);
      
      this.sendToClient(clientId, {
        type: 'subscription.confirmed',
        data: { subscriptionType: type, filter: subscriptionData },
        timestamp: Date.now(),
        id: this.generateMessageId()
      });

      logger.debug(`Client ${clientId} subscribed to ${type}`);
    } else {
      this.sendToClient(clientId, {
        type: 'subscription.error',
        data: { error: `Unknown subscription type: ${type}` },
        timestamp: Date.now(),
        id: this.generateMessageId()
      });
    }
  }

  private handleUnsubscription(clientId: string, data: { type: string }): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { type } = data;
    
    if (this.subscriptions.has(type)) {
      this.subscriptions.get(type)!.delete(clientId);
      client.subscriptions.delete(type);
      
      this.sendToClient(clientId, {
        type: 'unsubscription.confirmed',
        data: { subscriptionType: type },
        timestamp: Date.now(),
        id: this.generateMessageId()
      });
    }
  }

  private handleHeartbeat(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.lastHeartbeat = Date.now();
      client.isAlive = true;
    }
  }

  private handleDataRequest(clientId: string, request: any): void {
    // Handle specific data requests (pagination, historical data, etc.)
    const { dataType, limit = 20, offset = 0, filters } = request;
    
    // This will be implemented to fetch data based on request type
    // For now, send acknowledgment
    this.sendToClient(clientId, {
      type: 'data.response',
      data: { 
        dataType, 
        message: 'Data request received',
        limit,
        offset 
      },
      timestamp: Date.now(),
      id: this.generateMessageId()
    });
  }

  private handleDisconnection(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      // Remove from all subscriptions
      client.subscriptions.forEach(type => {
        this.subscriptions.get(type)?.delete(clientId);
      });
      
      this.clients.delete(clientId);
      this.messageQueue.delete(clientId);
      this.metrics.activeConnections--;
      
      logger.info(`WebSocket client disconnected: ${clientId}`);
    }
  }

  private handleError(clientId: string, error: Error): void {
    logger.error(`WebSocket error for client ${clientId}:`, error);
    this.metrics.errors++;
  }

  private handlePong(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.isAlive = true;
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.clients.forEach((client, clientId) => {
        if (!client.isAlive) {
          logger.info(`Terminating inactive client: ${clientId}`);
          client.ws.terminate();
          this.handleDisconnection(clientId);
          return;
        }

        client.isAlive = false;
        client.ws.ping();
      });
    }, parseInt(config.WEBSOCKET_HEARTBEAT_INTERVAL));
  }

  private startDataUpdates(): void {
    // Critical data updates every 5 seconds
    this.dataUpdateInterval = setInterval(async () => {
      await this.broadcastCriticalUpdates();
    }, 5000);

    // Performance metrics every 30 seconds
    setInterval(async () => {
      await this.broadcastPerformanceMetrics();
    }, 30000);

    // System health every 15 seconds
    setInterval(async () => {
      await this.broadcastSystemHealth();
    }, 15000);
  }

  private async broadcastCriticalUpdates(): Promise<void> {
    try {
      // Broadcast opportunities, active trades, queue status
      const redis = databaseManager.getRedis();
      if (redis) {
        const [opportunities, trades, queueStatus] = await Promise.all([
          redis.get('opportunities:live'),
          redis.get('trades:active'),
          redis.get('queue:status')
        ]);

        if (opportunities) {
          this.broadcast('opportunity.updated', JSON.parse(opportunities));
        }

        if (trades) {
          this.broadcast('trade.updated', JSON.parse(trades));
        }

        if (queueStatus) {
          this.broadcast('queue.updated', JSON.parse(queueStatus));
        }
      }
    } catch (error) {
      logger.error('Error broadcasting critical updates:', error);
    }
  }

  private async broadcastPerformanceMetrics(): Promise<void> {
    try {
      const redis = databaseManager.getRedis();
      if (redis) {
        const metrics = await redis.get('metrics:performance');
        if (metrics) {
          this.broadcast('metrics.updated', JSON.parse(metrics));
        }
      }
    } catch (error) {
      logger.error('Error broadcasting performance metrics:', error);
    }
  }

  private async broadcastSystemHealth(): Promise<void> {
    try {
      const healthStatus = databaseManager.getHealthStatus();
      const systemHealth = {
        databases: Object.fromEntries(healthStatus),
        websocket: {
          activeConnections: this.metrics.activeConnections,
          totalConnections: this.metrics.totalConnections,
          messagesSent: this.metrics.messagesSent,
          messagesReceived: this.metrics.messagesReceived,
          errors: this.metrics.errors
        },
        timestamp: Date.now()
      };

      this.broadcast('system.health', systemHealth);
    } catch (error) {
      logger.error('Error broadcasting system health:', error);
    }
  }

  public broadcast(type: string, data: any): void {
    const message: WebSocketMessage = {
      type,
      data,
      timestamp: Date.now(),
      id: this.generateMessageId()
    };

    const subscribers = this.subscriptions.get(type);
    if (subscribers) {
      subscribers.forEach(clientId => {
        this.sendToClient(clientId, message);
      });
    }
  }

  private sendToClient(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(message));
        this.metrics.messagesSent++;
      } catch (error) {
        logger.error(`Error sending message to client ${clientId}:`, error);
        this.metrics.errors++;
      }
    }
  }

  private disconnectOldestClient(): void {
    let oldestClient: ClientConnection | null = null;
    let oldestTime = Date.now();

    this.clients.forEach(client => {
      if (client.lastHeartbeat < oldestTime) {
        oldestTime = client.lastHeartbeat;
        oldestClient = client;
      }
    });

    if (oldestClient) {
      logger.info(`Disconnecting oldest client to maintain connection limit: ${oldestClient.id}`);
      oldestClient.ws.terminate();
      this.handleDisconnection(oldestClient.id);
    }
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getMetrics() {
    return { ...this.metrics };
  }

  shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    if (this.dataUpdateInterval) {
      clearInterval(this.dataUpdateInterval);
    }

    this.clients.forEach(client => {
      client.ws.close();
    });

    if (this.wss) {
      this.wss.close();
    }

    logger.info('Enhanced WebSocket service shut down');
  }
}

export const webSocketService = new EnhancedWebSocketService();
