-- PostgreSQL Backup Schema for MEV Arbitrage Bot
-- This schema mirrors the Supabase schema with additional sync tracking
-- for bi-directional synchronization and local backup capabilities

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- ================================
-- SYNC TRACKING TABLES
-- ================================

-- Sync Status Table
CREATE TABLE IF NOT EXISTS sync_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    last_sync_timestamp TIMESTAMPTZ NOT NULL,
    sync_direction VARCHAR(20) NOT NULL CHECK (sync_direction IN ('to_supabase', 'from_supabase', 'bidirectional')),
    sync_status VARCHAR(20) NOT NULL CHECK (sync_status IN ('success', 'failed', 'in_progress')),
    records_synced INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(table_name, sync_direction)
);

-- Sync Conflicts Table
CREATE TABLE IF NOT EXISTS sync_conflicts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    conflict_type VARCHAR(50) NOT NULL CHECK (conflict_type IN ('update_conflict', 'delete_conflict', 'insert_conflict')),
    local_data JSONB,
    remote_data JSONB,
    resolution_strategy VARCHAR(50) CHECK (resolution_strategy IN ('local_wins', 'remote_wins', 'manual_review', 'merge')),
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    resolved_by VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ================================
-- MAIN TABLES (Mirror of Supabase)
-- ================================

-- Trades Table with Sync Tracking
CREATE TABLE IF NOT EXISTS trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    assets TEXT[] NOT NULL,
    exchanges TEXT[] NOT NULL,
    executed_profit DECIMAL(18, 8) NOT NULL,
    gas_fees DECIMAL(18, 8) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failed', 'pending')),
    timestamp TIMESTAMPTZ NOT NULL,
    network VARCHAR(50) NOT NULL,
    tx_hash VARCHAR(66),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE
);

-- Opportunities Table with Sync Tracking
CREATE TABLE IF NOT EXISTS opportunities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL,
    assets TEXT[] NOT NULL,
    exchanges TEXT[] NOT NULL,
    potential_profit DECIMAL(18, 8) NOT NULL,
    profit_percentage DECIMAL(8, 4) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    network VARCHAR(50) NOT NULL,
    confidence DECIMAL(5, 2) NOT NULL CHECK (confidence >= 0 AND confidence <= 100),
    slippage DECIMAL(5, 4) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE
);

-- Validations Table with Sync Tracking
CREATE TABLE IF NOT EXISTS validations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) NOT NULL,
    validation_type VARCHAR(50) NOT NULL CHECK (validation_type IN ('pre_execution', 'profit_check', 'risk_assessment', 'liquidity_check', 'gas_estimation')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'passed', 'failed', 'timeout')),
    result JSONB,
    error_message TEXT,
    validation_time_ms INTEGER,
    confidence_score DECIMAL(5, 2) CHECK (confidence_score >= 0 AND confidence_score <= 100),
    risk_score DECIMAL(5, 2) CHECK (risk_score >= 0 AND risk_score <= 100),
    network VARCHAR(50) NOT NULL,
    validator_service VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE
);

-- Flash Loan Quotes Table with Sync Tracking
CREATE TABLE IF NOT EXISTS flash_loan_quotes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    opportunity_id VARCHAR(255) NOT NULL,
    provider VARCHAR(50) NOT NULL CHECK (provider IN ('aave_v3', 'balancer_v2', 'dydx', 'uniswap_v3')),
    asset_address VARCHAR(255) NOT NULL,
    asset_symbol VARCHAR(20) NOT NULL,
    amount DECIMAL(36, 18) NOT NULL,
    fee_amount DECIMAL(36, 18) NOT NULL,
    fee_percentage DECIMAL(8, 6) NOT NULL,
    max_amount DECIMAL(36, 18) NOT NULL,
    available_liquidity DECIMAL(36, 18) NOT NULL,
    execution_gas_estimate INTEGER NOT NULL,
    network VARCHAR(50) NOT NULL,
    quote_valid_until TIMESTAMPTZ NOT NULL,
    priority_score INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'used', 'expired', 'invalid')),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE
);

-- Performance Metrics Table with Sync Tracking
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE UNIQUE NOT NULL,
    total_trades INTEGER NOT NULL DEFAULT 0,
    successful_trades INTEGER NOT NULL DEFAULT 0,
    failed_trades INTEGER NOT NULL DEFAULT 0,
    total_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    total_loss DECIMAL(18, 8) NOT NULL DEFAULT 0,
    net_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    win_rate DECIMAL(5, 2) NOT NULL DEFAULT 0,
    avg_profit DECIMAL(18, 8) NOT NULL DEFAULT 0,
    avg_loss DECIMAL(18, 8) NOT NULL DEFAULT 0,
    profit_factor DECIMAL(10, 4) NOT NULL DEFAULT 0,
    sharpe_ratio DECIMAL(10, 4) NOT NULL DEFAULT 0,
    max_drawdown DECIMAL(10, 4) NOT NULL DEFAULT 0,
    roi DECIMAL(10, 4) NOT NULL DEFAULT 0,
    daily_volume DECIMAL(18, 8) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE
);

-- Tokens Table with Sync Tracking
CREATE TABLE IF NOT EXISTS tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    address VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    decimals INTEGER NOT NULL DEFAULT 18,
    network VARCHAR(50) NOT NULL,
    chain_id INTEGER,
    is_whitelisted BOOLEAN NOT NULL DEFAULT false,
    is_blacklisted BOOLEAN NOT NULL DEFAULT false,
    safety_score INTEGER NOT NULL DEFAULT 0 CHECK (safety_score >= 0 AND safety_score <= 100),
    liquidity DECIMAL(18, 8) NOT NULL DEFAULT 0,
    total_supply VARCHAR(78),
    market_cap DECIMAL(18, 8) DEFAULT 0,
    volume_24h DECIMAL(18, 8) DEFAULT 0,
    price_usd DECIMAL(18, 8) DEFAULT 0,
    price_change_24h DECIMAL(8, 4) DEFAULT 0,
    market_cap_rank INTEGER DEFAULT 999,
    liquidity_score INTEGER DEFAULT 0,
    coingecko_id VARCHAR(100),
    contract_verified BOOLEAN DEFAULT false,
    dexes TEXT[],
    bridge_protocols TEXT[],
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE,
    UNIQUE(address, network)
);

-- Networks Table with Sync Tracking
CREATE TABLE IF NOT EXISTS networks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    network_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    chain_id INTEGER,
    native_currency_name VARCHAR(50) NOT NULL,
    native_currency_symbol VARCHAR(10) NOT NULL,
    native_currency_decimals INTEGER NOT NULL DEFAULT 18,
    rpc_urls TEXT[] NOT NULL,
    block_explorer_urls TEXT[] NOT NULL,
    dexes TEXT[] NOT NULL,
    bridge_protocols TEXT[] NOT NULL,
    avg_gas_price DECIMAL(18, 8) NOT NULL,
    avg_block_time DECIMAL(8, 4) NOT NULL,
    bridge_fee_percentage DECIMAL(8, 6) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    risk_score INTEGER DEFAULT 0 CHECK (risk_score >= 0 AND risk_score <= 100),
    tvl DECIMAL(18, 8) DEFAULT 0,
    daily_volume DECIMAL(18, 8) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE
);

-- System Alerts Table with Sync Tracking
CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    is_resolved BOOLEAN NOT NULL DEFAULT false,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE
);

-- Configuration Table with Sync Tracking
CREATE TABLE IF NOT EXISTS configuration (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL DEFAULT 'string' CHECK (type IN ('string', 'number', 'boolean', 'json')),
    is_sensitive BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    -- Sync tracking fields
    last_synced_at TIMESTAMPTZ,
    sync_version INTEGER DEFAULT 1,
    sync_source VARCHAR(20) DEFAULT 'local' CHECK (sync_source IN ('local', 'supabase')),
    needs_sync BOOLEAN DEFAULT TRUE
);

-- ================================
-- INDEXES FOR PERFORMANCE
-- ================================

-- Sync Status Indexes
CREATE INDEX IF NOT EXISTS idx_sync_status_table_name ON sync_status(table_name);
CREATE INDEX IF NOT EXISTS idx_sync_status_last_sync ON sync_status(last_sync_timestamp);
CREATE INDEX IF NOT EXISTS idx_sync_status_status ON sync_status(sync_status);

-- Sync Conflicts Indexes
CREATE INDEX IF NOT EXISTS idx_sync_conflicts_table_name ON sync_conflicts(table_name);
CREATE INDEX IF NOT EXISTS idx_sync_conflicts_record_id ON sync_conflicts(record_id);
CREATE INDEX IF NOT EXISTS idx_sync_conflicts_resolved ON sync_conflicts(resolved);

-- Trades Indexes
CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status);
CREATE INDEX IF NOT EXISTS idx_trades_network ON trades(network);
CREATE INDEX IF NOT EXISTS idx_trades_opportunity_id ON trades(opportunity_id);
CREATE INDEX IF NOT EXISTS idx_trades_needs_sync ON trades(needs_sync) WHERE needs_sync = TRUE;
CREATE INDEX IF NOT EXISTS idx_trades_sync_version ON trades(sync_version);

-- Opportunities Indexes
CREATE INDEX IF NOT EXISTS idx_opportunities_timestamp ON opportunities(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_opportunities_network ON opportunities(network);
CREATE INDEX IF NOT EXISTS idx_opportunities_type ON opportunities(type);
CREATE INDEX IF NOT EXISTS idx_opportunities_profit ON opportunities(potential_profit DESC);
CREATE INDEX IF NOT EXISTS idx_opportunities_needs_sync ON opportunities(needs_sync) WHERE needs_sync = TRUE;

-- Validations Indexes
CREATE INDEX IF NOT EXISTS idx_validations_opportunity_id ON validations(opportunity_id);
CREATE INDEX IF NOT EXISTS idx_validations_type ON validations(validation_type);
CREATE INDEX IF NOT EXISTS idx_validations_status ON validations(status);
CREATE INDEX IF NOT EXISTS idx_validations_network ON validations(network);
CREATE INDEX IF NOT EXISTS idx_validations_needs_sync ON validations(needs_sync) WHERE needs_sync = TRUE;

-- Flash Loan Quotes Indexes
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_opportunity_id ON flash_loan_quotes(opportunity_id);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_provider ON flash_loan_quotes(provider);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_status ON flash_loan_quotes(status);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_valid_until ON flash_loan_quotes(quote_valid_until);
CREATE INDEX IF NOT EXISTS idx_flash_loan_quotes_needs_sync ON flash_loan_quotes(needs_sync) WHERE needs_sync = TRUE;

-- Performance Metrics Indexes
CREATE INDEX IF NOT EXISTS idx_performance_metrics_date ON performance_metrics(date DESC);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_needs_sync ON performance_metrics(needs_sync) WHERE needs_sync = TRUE;

-- Tokens Indexes
CREATE INDEX IF NOT EXISTS idx_tokens_address_network ON tokens(address, network);
CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON tokens(symbol);
CREATE INDEX IF NOT EXISTS idx_tokens_network ON tokens(network);
CREATE INDEX IF NOT EXISTS idx_tokens_needs_sync ON tokens(needs_sync) WHERE needs_sync = TRUE;

-- Networks Indexes
CREATE INDEX IF NOT EXISTS idx_networks_network_id ON networks(network_id);
CREATE INDEX IF NOT EXISTS idx_networks_is_active ON networks(is_active);
CREATE INDEX IF NOT EXISTS idx_networks_needs_sync ON networks(needs_sync) WHERE needs_sync = TRUE;

-- System Alerts Indexes
CREATE INDEX IF NOT EXISTS idx_system_alerts_type ON system_alerts(type);
CREATE INDEX IF NOT EXISTS idx_system_alerts_severity ON system_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_system_alerts_resolved ON system_alerts(is_resolved);
CREATE INDEX IF NOT EXISTS idx_system_alerts_needs_sync ON system_alerts(needs_sync) WHERE needs_sync = TRUE;

-- Configuration Indexes
CREATE INDEX IF NOT EXISTS idx_configuration_key ON configuration(key);
CREATE INDEX IF NOT EXISTS idx_configuration_needs_sync ON configuration(needs_sync) WHERE needs_sync = TRUE;
