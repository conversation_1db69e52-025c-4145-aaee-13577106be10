const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Simple deployment to local network...");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("Account balance:", ethers.formatEther(balance), "ETH");

  // Deploy a simple mock contract first to test
  console.log("\nDeploying MockERC20 for testing...");
  const MockERC20 = await ethers.getContractFactory("MockERC20");
  const mockToken = await MockERC20.deploy("Test Token", "TEST", 18);
  await mockToken.waitForDeployment();
  const mockTokenAddress = await mockToken.getAddress();
  console.log("✅ MockERC20 deployed to:", mockTokenAddress);

  console.log("\n✅ Simple deployment completed successfully");
  return { MockERC20: mockTokenAddress };
}

main()
  .then((addresses) => {
    console.log("Deployment addresses:", addresses);
    process.exit(0);
  })
  .catch((error) => {
    console.error("Deployment failed:", error);
    process.exit(1);
  });
