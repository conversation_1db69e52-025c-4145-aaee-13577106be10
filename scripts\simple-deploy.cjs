const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 MEV Arbitrage Bot - Contract Deployment to Local Network");
  console.log("=" .repeat(70));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Deploying with account:", deployer.address);

  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(balance), "ETH");

  const contracts = {};

  // Deploy TokenDiscovery contract (no constructor params)
  console.log("\n🔍 Deploying TokenDiscovery contract...");
  const TokenDiscovery = await ethers.getContractFactory("TokenDiscovery");
  const tokenDiscovery = await TokenDiscovery.deploy();
  await tokenDiscovery.waitForDeployment();
  contracts.TokenDiscovery = await tokenDiscovery.getAddress();
  console.log("✅ TokenDiscovery deployed to:", contracts.TokenDiscovery);

  // Deploy LiquidityChecker contract (no constructor params)
  console.log("\n💧 Deploying LiquidityChecker contract...");
  const LiquidityChecker = await ethers.getContractFactory("LiquidityChecker");
  const liquidityChecker = await LiquidityChecker.deploy();
  await liquidityChecker.waitForDeployment();
  contracts.LiquidityChecker = await liquidityChecker.getAddress();
  console.log("✅ LiquidityChecker deployed to:", contracts.LiquidityChecker);

  // Deploy ArbitrageExecutor contract (requires TokenDiscovery and LiquidityChecker addresses)
  console.log("\n⚡ Deploying ArbitrageExecutor contract...");
  const ArbitrageExecutor = await ethers.getContractFactory("ArbitrageExecutor");
  const arbitrageExecutor = await ArbitrageExecutor.deploy(
    contracts.TokenDiscovery,
    contracts.LiquidityChecker
  );
  await arbitrageExecutor.waitForDeployment();
  contracts.ArbitrageExecutor = await arbitrageExecutor.getAddress();
  console.log("✅ ArbitrageExecutor deployed to:", contracts.ArbitrageExecutor);

  // Deploy Governance contract (no constructor params)
  console.log("\n🏛️ Deploying Governance contract...");
  const Governance = await ethers.getContractFactory("Governance");
  const governance = await Governance.deploy();
  await governance.waitForDeployment();
  contracts.Governance = await governance.getAddress();
  console.log("✅ Governance deployed to:", contracts.Governance);

  // Deploy test tokens for local testing
  console.log("\n🪙 Deploying test tokens for local testing...");
  const MockERC20 = await ethers.getContractFactory("MockERC20");

  // Deploy WETH mock
  const wethSupply = ethers.parseEther("1000000");
  const wethMock = await MockERC20.deploy("Wrapped Ether", "WETH", 18, wethSupply, deployer.address);
  await wethMock.waitForDeployment();
  contracts.WETH = await wethMock.getAddress();
  console.log("✅ WETH Mock deployed to:", contracts.WETH);

  // Deploy USDC mock
  const usdcSupply = ethers.parseUnits("1000000", 6);
  const usdcMock = await MockERC20.deploy("USD Coin", "USDC", 6, usdcSupply, deployer.address);
  await usdcMock.waitForDeployment();
  contracts.USDC = await usdcMock.getAddress();
  console.log("✅ USDC Mock deployed to:", contracts.USDC);

  return contracts;
}

main()
  .then((contracts) => {
    console.log("\n" + "=" .repeat(70));
    console.log("🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!");
    console.log("=" .repeat(70));
    console.log("📍 Network: localhost (Chain ID: 1337)");
    console.log("\n📋 Contract Addresses:");
    console.log("  🔍 TokenDiscovery:     ", contracts.TokenDiscovery);
    console.log("  💧 LiquidityChecker:   ", contracts.LiquidityChecker);
    console.log("  ⚡ ArbitrageExecutor:  ", contracts.ArbitrageExecutor);
    console.log("  🏛️ Governance:         ", contracts.Governance);
    console.log("  🪙 WETH Mock:          ", contracts.WETH);
    console.log("  🪙 USDC Mock:          ", contracts.USDC);

    console.log("\n💡 Environment Variables for Backend:");
    console.log(`export ARBITRAGE_EXECUTOR_ADDRESS=${contracts.ArbitrageExecutor}`);
    console.log(`export TOKEN_DISCOVERY_ADDRESS=${contracts.TokenDiscovery}`);
    console.log(`export LIQUIDITY_CHECKER_ADDRESS=${contracts.LiquidityChecker}`);
    console.log(`export GOVERNANCE_ADDRESS=${contracts.Governance}`);
    console.log(`export HARDHAT_NETWORK=localhost`);
    console.log(`export HARDHAT_RPC_URL=http://localhost:8545`);

    console.log("\n🔧 Next Steps:");
    console.log("  1. Start the backend services with these contract addresses");
    console.log("  2. Configure the frontend to connect to localhost:8545");
    console.log("  3. Run integration tests to verify functionality");
    console.log("  4. Begin MEV arbitrage testing on the local network");

    process.exit(0);
  })
  .catch((error) => {
    console.error("\n❌ Deployment failed:", error);
    process.exit(1);
  });
