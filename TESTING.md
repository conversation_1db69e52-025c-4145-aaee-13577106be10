# MEV Arbitrage Bot - Testing Guide

This document provides comprehensive information about testing the MEV Arbitrage Bot system.

## 🧪 Test Suite Overview

Our testing strategy covers multiple layers to ensure system reliability, performance, and correctness:

- **Unit Tests**: Individual component testing
- **Integration Tests**: Service interaction testing  
- **Performance Tests**: Load and benchmark testing
- **Smart Contract Tests**: Solidity contract testing
- **End-to-End Tests**: Full system workflow testing

## 🚀 Quick Start

### Prerequisites

1. **Node.js 18+** installed
2. **Redis** running (for integration tests)
3. **Dependencies** installed: `npm install`

### Run All Tests

```bash
# Run complete test suite
npm run test:all

# Or use the test runner
node scripts/test-runner.js
```

### Run Specific Test Types

```bash
# Unit tests only
npm test

# Integration tests
npm run test:integration

# Performance tests
npm run test:performance

# Smart contract tests
npm run test:contracts

# End-to-end tests
npm run test:e2e

# Generate coverage report
npm run test:coverage
```

## 📋 Test Categories

### 1. Unit Tests (`tests/unit/`)

Tests individual services and components in isolation.

**Coverage:**
- PriceFeedService: Price data management and real-time updates
- OpportunityDetectionService: Arbitrage opportunity identification
- TokenDiscoveryService: Token validation and whitelisting
- ExecutionService: Trade execution logic
- RiskManagementService: Risk assessment and controls
- AnalyticsService: Performance metrics and reporting

**Key Test Areas:**
- Service lifecycle (start/stop)
- Data validation and processing
- Event emission and handling
- Error handling and recovery
- Performance metrics

### 2. Integration Tests (`tests/integration/`)

Tests service interactions and system-wide functionality.

**Coverage:**
- API endpoint functionality
- WebSocket real-time communication
- Service-to-service data flow
- Database operations
- External service mocking

**Key Test Areas:**
- End-to-end data flow
- Real-time event propagation
- API response consistency
- Error handling across services
- System health monitoring

### 3. Performance Tests (`tests/performance/`)

Evaluates system performance under various load conditions.

**Benchmarks:**
- **Price Updates**: >1,000 updates/second
- **Opportunity Detection**: >500 detections/second
- **API Response Time**: <100ms average
- **WebSocket Latency**: <50ms
- **Memory Usage**: <100MB increase under load

**Test Scenarios:**
- High-frequency price updates
- Concurrent opportunity processing
- Memory leak detection
- Stress testing
- Latency measurement

### 4. Smart Contract Tests (`test/`)

Tests Solidity smart contracts using Hardhat framework.

**Coverage:**
- ArbitrageExecutor: Flash loan execution and arbitrage logic
- TokenDiscovery: Token validation and safety scoring
- LiquidityChecker: Pool liquidity verification
- Governance: Parameter management and emergency controls

**Key Test Areas:**
- Contract deployment and initialization
- Access control and permissions
- Parameter validation
- Gas optimization
- Security vulnerabilities

### 5. End-to-End Tests (`tests/e2e/`)

Tests complete system workflows from user perspective.

**Coverage:**
- Full system startup and health
- Real-time data streaming
- API functionality under load
- Error recovery and resilience
- Data consistency across services

## 🎯 Performance Targets

### Latency Requirements
- **Price Update Processing**: <10ms
- **Opportunity Detection**: <50ms
- **API Response Time**: <100ms
- **WebSocket Message Delivery**: <50ms

### Throughput Requirements
- **Price Updates**: 1,000+ updates/second
- **Opportunity Processing**: 500+ opportunities/second
- **Concurrent API Requests**: 100+ requests/second
- **WebSocket Connections**: 50+ concurrent connections

### Resource Limits
- **Memory Usage**: <500MB total
- **CPU Usage**: <80% under normal load
- **Memory Leaks**: <10MB/hour growth
- **Database Connections**: <20 concurrent

## 🔧 Test Configuration

### Environment Variables

```bash
# Test environment
NODE_ENV=test
PORT=3001
LOG_LEVEL=error

# Redis (test database)
REDIS_URL=redis://localhost:6379/1

# Test timeouts
TEST_TIMEOUT=30000
PERFORMANCE_TEST_DURATION=5000
```

### Jest Configuration

Key settings in `jest.config.js`:
- TypeScript support with ts-jest
- ESM module support
- 30-second test timeout
- Coverage collection from backend services
- Custom test matchers and utilities

## 📊 Coverage Reports

Coverage reports are generated in multiple formats:
- **Terminal**: Text summary during test runs
- **HTML**: Detailed interactive report in `coverage/`
- **LCOV**: Machine-readable format for CI/CD

**Coverage Targets:**
- **Statements**: >90%
- **Branches**: >85%
- **Functions**: >90%
- **Lines**: >90%

## 🐛 Debugging Tests

### Common Issues

1. **Redis Connection Errors**
   ```bash
   # Start Redis
   redis-server
   
   # Or use Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

2. **Port Conflicts**
   ```bash
   # Check for processes using test ports
   lsof -i :3001
   lsof -i :3002
   ```

3. **Memory Issues**
   ```bash
   # Run with increased memory
   node --max-old-space-size=4096 scripts/test-runner.js
   ```

### Debug Mode

```bash
# Run tests with debug output
DEBUG=* npm test

# Run specific test with verbose output
npm test -- --verbose tests/unit/services/PriceFeedService.test.ts
```

## 🚀 Continuous Integration

### GitHub Actions Workflow

```yaml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:all
      - uses: codecov/codecov-action@v3
```

## 📈 Performance Monitoring

### Benchmark Results

The performance benchmark (`npm run benchmark`) provides detailed metrics:

- **Price Update Throughput**: Updates per second
- **Opportunity Detection Speed**: Detections per second  
- **Memory Usage Patterns**: Heap allocation and cleanup
- **Latency Distribution**: P50, P95, P99 percentiles
- **Concurrent Processing**: Multi-threaded performance

### Monitoring in Production

Key metrics to monitor:
- Response time percentiles
- Error rates by endpoint
- Memory and CPU usage
- Database connection pool health
- WebSocket connection stability

## 🔒 Security Testing

### Automated Security Checks

```bash
# Dependency vulnerability scan
npm audit

# Smart contract security analysis
npm run test:contracts -- --security
```

### Manual Security Testing

- Input validation testing
- Authentication bypass attempts
- Rate limiting verification
- SQL injection prevention
- Cross-site scripting (XSS) protection

## 📝 Writing New Tests

### Test Structure

```typescript
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';

describe('ServiceName', () => {
  let service: ServiceName;

  beforeEach(() => {
    service = new ServiceName();
  });

  afterEach(async () => {
    await service.stop();
  });

  describe('Feature Group', () => {
    it('should behave correctly', async () => {
      // Arrange
      const input = 'test data';
      
      // Act
      const result = await service.process(input);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.status).toBe('success');
    });
  });
});
```

### Best Practices

1. **Use descriptive test names**
2. **Follow AAA pattern** (Arrange, Act, Assert)
3. **Mock external dependencies**
4. **Test error conditions**
5. **Verify cleanup in afterEach**
6. **Use custom matchers** for domain-specific assertions

## 🎯 Test Metrics Dashboard

Monitor test health with these key metrics:

- **Test Success Rate**: >95%
- **Test Execution Time**: <5 minutes total
- **Flaky Test Rate**: <2%
- **Coverage Trend**: Increasing over time
- **Performance Regression**: <10% degradation

---

For questions or issues with testing, please check the [troubleshooting guide](./docs/TROUBLESHOOTING.md) or open an issue.
