/**
 * Testnet Environment Validation Script
 * 
 * Validates all testnet configurations, RPC connections, and token contracts
 * across 10 supported blockchain networks before deployment testing.
 */

import { ethers } from "ethers";
import dotenv from "dotenv";
import chalk from "chalk";
import { performance } from "perf_hooks";

// Load testnet environment
dotenv.config({ path: '.env.testnet' });

interface TestnetConfig {
  name: string;
  chainId: number;
  rpcUrl: string;
  explorerUrl: string;
  nativeCurrency: string;
  tokens: {
    [symbol: string]: string;
  };
  dexes: {
    [name: string]: string;
  };
  bridges?: {
    [name: string]: string;
  };
}

const TESTNET_CONFIGS: { [network: string]: TestnetConfig } = {
  ethereum: {
    name: "Ethereum Sepolia",
    chainId: 11155111,
    rpcUrl: process.env.SEPOLIA_RPC_URL || "",
    explorerUrl: "https://sepolia.etherscan.io",
    nativeCurrency: "ETH",
    tokens: {
      WETH: "******************************************",
      USDC: "******************************************",
      USDT: "******************************************",
      WBTC: "******************************************",
    },
    dexes: {
      uniswapV2Router: "******************************************",
      uniswapV3Router: "******************************************",
      sushiswapRouter: "******************************************",
    },
  },
  bsc: {
    name: "BSC Testnet",
    chainId: 97,
    rpcUrl: process.env.BSC_TESTNET_RPC_URL || "https://data-seed-prebsc-1-s1.binance.org:8545/",
    explorerUrl: "https://testnet.bscscan.com",
    nativeCurrency: "BNB",
    tokens: {
      WBNB: "******************************************",
      USDC: "******************************************",
      USDT: "******************************************",
      BTCB: "******************************************",
    },
    dexes: {
      pancakeswapRouter: "******************************************",
      biswapRouter: "******************************************",
    },
  },
  polygon: {
    name: "Polygon Mumbai",
    chainId: 80001,
    rpcUrl: process.env.MUMBAI_RPC_URL || "https://rpc-mumbai.maticvigil.com/",
    explorerUrl: "https://mumbai.polygonscan.com",
    nativeCurrency: "MATIC",
    tokens: {
      WMATIC: "******************************************",
      USDC: "******************************************",
      USDT: "******************************************",
      WETH: "******************************************",
    },
    dexes: {
      quickswapRouter: "******************************************",
      sushiswapRouter: "******************************************",
    },
  },
  avalanche: {
    name: "Avalanche Fuji",
    chainId: 43113,
    rpcUrl: process.env.AVALANCHE_FUJI_RPC_URL || "https://api.avax-test.network/ext/bc/C/rpc",
    explorerUrl: "https://testnet.snowtrace.io",
    nativeCurrency: "AVAX",
    tokens: {
      WAVAX: "******************************************",
      USDC: "******************************************",
      USDT: "******************************************",
      WETH: "******************************************",
    },
    dexes: {
      traderJoeRouter: "******************************************",
      pangolinRouter: "******************************************",
    },
  },
  arbitrum: {
    name: "Arbitrum Goerli",
    chainId: 421613,
    rpcUrl: process.env.ARBITRUM_GOERLI_RPC_URL || "https://goerli-rollup.arbitrum.io/rpc",
    explorerUrl: "https://goerli.arbiscan.io",
    nativeCurrency: "ETH",
    tokens: {
      WETH: "******************************************",
      USDC: "******************************************",
      USDT: "******************************************",
      ARB: "******************************************",
    },
    dexes: {
      uniswapV3Router: "******************************************",
      sushiswapRouter: "******************************************",
    },
  },
  optimism: {
    name: "Optimism Goerli",
    chainId: 420,
    rpcUrl: process.env.OPTIMISM_GOERLI_RPC_URL || "https://goerli.optimism.io",
    explorerUrl: "https://goerli-optimism.etherscan.io",
    nativeCurrency: "ETH",
    tokens: {
      WETH: "******************************************",
      USDC: "******************************************",
      USDT: "******************************************",
      OP: "******************************************",
    },
    dexes: {
      uniswapV3Router: "******************************************",
      velodrome: "******************************************",
    },
  },
  base: {
    name: "Base Goerli",
    chainId: 84531,
    rpcUrl: process.env.BASE_GOERLI_RPC_URL || "https://goerli.base.org",
    explorerUrl: "https://goerli.basescan.org",
    nativeCurrency: "ETH",
    tokens: {
      WETH: "******************************************",
      USDC: "******************************************",
      USDbC: "******************************************",
    },
    dexes: {
      uniswapV3Router: "******************************************",
      baseswap: "******************************************",
    },
  },
  fantom: {
    name: "Fantom Testnet",
    chainId: 4002,
    rpcUrl: process.env.FANTOM_TESTNET_RPC_URL || "https://rpc.testnet.fantom.network/",
    explorerUrl: "https://testnet.ftmscan.com",
    nativeCurrency: "FTM",
    tokens: {
      WFTM: "******************************************",
      USDC: "******************************************",
      fUSDT: "******************************************",
    },
    dexes: {
      spookyswapRouter: "******************************************",
      spiritswapRouter: "******************************************",
    },
  },
};

class TestnetValidator {
  private results: Map<string, any> = new Map();
  private errors: string[] = [];

  async validateAllTestnets(): Promise<boolean> {
    console.log(chalk.blue("🌐 Starting Multi-Chain Testnet Validation...\n"));

    let allValid = true;

    for (const [networkName, config] of Object.entries(TESTNET_CONFIGS)) {
      console.log(chalk.yellow(`📡 Validating ${config.name}...`));
      
      const isValid = await this.validateNetwork(networkName, config);
      if (!isValid) {
        allValid = false;
      }
      
      console.log(); // Add spacing
    }

    this.generateReport();
    return allValid;
  }

  private async validateNetwork(networkName: string, config: TestnetConfig): Promise<boolean> {
    const startTime = performance.now();
    let isValid = true;

    try {
      // 1. Validate RPC Connection
      const rpcValid = await this.validateRPCConnection(config);
      if (!rpcValid) isValid = false;

      // 2. Validate Chain ID
      const chainIdValid = await this.validateChainId(config);
      if (!chainIdValid) isValid = false;

      // 3. Validate Token Contracts
      const tokensValid = await this.validateTokenContracts(config);
      if (!tokensValid) isValid = false;

      // 4. Validate DEX Contracts
      const dexesValid = await this.validateDEXContracts(config);
      if (!dexesValid) isValid = false;

      const duration = performance.now() - startTime;
      
      this.results.set(networkName, {
        name: config.name,
        valid: isValid,
        duration: Math.round(duration),
        rpcValid,
        chainIdValid,
        tokensValid,
        dexesValid,
      });

      if (isValid) {
        console.log(chalk.green(`✅ ${config.name} - All validations passed (${Math.round(duration)}ms)`));
      } else {
        console.log(chalk.red(`❌ ${config.name} - Validation failed (${Math.round(duration)}ms)`));
      }

    } catch (error) {
      isValid = false;
      this.errors.push(`${config.name}: ${error.message}`);
      console.log(chalk.red(`❌ ${config.name} - Validation error: ${error.message}`));
    }

    return isValid;
  }

  private async validateRPCConnection(config: TestnetConfig): Promise<boolean> {
    try {
      if (!config.rpcUrl) {
        console.log(chalk.red(`  ❌ RPC URL not configured`));
        return false;
      }

      const provider = new ethers.JsonRpcProvider(config.rpcUrl);
      const blockNumber = await provider.getBlockNumber();

      if (blockNumber > 0) {
        console.log(chalk.green(`  ✅ RPC Connection - Block #${blockNumber}`));
        return true;
      } else {
        console.log(chalk.red(`  ❌ RPC Connection - Invalid block number`));
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`  ❌ RPC Connection - ${error.message}`));
      return false;
    }
  }

  private async validateChainId(config: TestnetConfig): Promise<boolean> {
    try {
      const provider = new ethers.JsonRpcProvider(config.rpcUrl);
      const network = await provider.getNetwork();
      const actualChainId = Number(network.chainId);

      if (actualChainId === config.chainId) {
        console.log(chalk.green(`  ✅ Chain ID - ${actualChainId}`));
        return true;
      } else {
        console.log(chalk.red(`  ❌ Chain ID - Expected ${config.chainId}, got ${actualChainId}`));
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`  ❌ Chain ID - ${error.message}`));
      return false;
    }
  }

  private async validateTokenContracts(config: TestnetConfig): Promise<boolean> {
    try {
      const provider = new ethers.JsonRpcProvider(config.rpcUrl);
      let validTokens = 0;
      let totalTokens = Object.keys(config.tokens).length;

      for (const [symbol, address] of Object.entries(config.tokens)) {
        try {
          const code = await provider.getCode(address);
          if (code !== "0x") {
            validTokens++;
            console.log(chalk.green(`    ✅ ${symbol} - ${address}`));
          } else {
            console.log(chalk.red(`    ❌ ${symbol} - No contract at ${address}`));
          }
        } catch (error) {
          console.log(chalk.red(`    ❌ ${symbol} - Error: ${error.message}`));
        }
      }

      const isValid = validTokens === totalTokens;
      console.log(chalk[isValid ? 'green' : 'yellow'](`  📊 Tokens - ${validTokens}/${totalTokens} valid`));
      return isValid;
    } catch (error) {
      console.log(chalk.red(`  ❌ Token Validation - ${error.message}`));
      return false;
    }
  }

  private async validateDEXContracts(config: TestnetConfig): Promise<boolean> {
    try {
      const provider = new ethers.JsonRpcProvider(config.rpcUrl);
      let validDEXes = 0;
      let totalDEXes = Object.keys(config.dexes).length;

      for (const [name, address] of Object.entries(config.dexes)) {
        try {
          const code = await provider.getCode(address);
          if (code !== "0x") {
            validDEXes++;
            console.log(chalk.green(`    ✅ ${name} - ${address}`));
          } else {
            console.log(chalk.red(`    ❌ ${name} - No contract at ${address}`));
          }
        } catch (error) {
          console.log(chalk.red(`    ❌ ${name} - Error: ${error.message}`));
        }
      }

      const isValid = validDEXes === totalDEXes;
      console.log(chalk[isValid ? 'green' : 'yellow'](`  📊 DEXes - ${validDEXes}/${totalDEXes} valid`));
      return isValid;
    } catch (error) {
      console.log(chalk.red(`  ❌ DEX Validation - ${error.message}`));
      return false;
    }
  }

  private generateReport(): void {
    console.log(chalk.blue("\n📋 Testnet Validation Report"));
    console.log(chalk.blue("=" * 50));

    let totalNetworks = 0;
    let validNetworks = 0;
    let totalDuration = 0;

    for (const [networkName, result] of this.results) {
      totalNetworks++;
      totalDuration += result.duration;

      if (result.valid) {
        validNetworks++;
        console.log(chalk.green(`✅ ${result.name} - All checks passed (${result.duration}ms)`));
      } else {
        console.log(chalk.red(`❌ ${result.name} - Some checks failed (${result.duration}ms)`));
        console.log(chalk.gray(`   RPC: ${result.rpcValid ? '✅' : '❌'} | Chain ID: ${result.chainIdValid ? '✅' : '❌'} | Tokens: ${result.tokensValid ? '✅' : '❌'} | DEXes: ${result.dexesValid ? '✅' : '❌'}`));
      }
    }

    console.log(chalk.blue("\n📊 Summary:"));
    console.log(chalk.white(`Networks Validated: ${validNetworks}/${totalNetworks}`));
    console.log(chalk.white(`Total Duration: ${totalDuration}ms`));
    console.log(chalk.white(`Average Duration: ${Math.round(totalDuration / totalNetworks)}ms`));

    if (this.errors.length > 0) {
      console.log(chalk.red("\n❌ Errors:"));
      this.errors.forEach(error => console.log(chalk.red(`  • ${error}`)));
    }

    if (validNetworks === totalNetworks) {
      console.log(chalk.green("\n🎉 All testnets validated successfully!"));
    } else {
      console.log(chalk.yellow(`\n⚠️  ${totalNetworks - validNetworks} testnet(s) need attention`));
    }
  }
}

// Main execution
async function main() {
  const validator = new TestnetValidator();

  try {
    const allValid = await validator.validateAllTestnets();
    process.exit(allValid ? 0 : 1);
  } catch (error) {
    console.error(chalk.red("Validation failed:"), error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { TestnetValidator, TESTNET_CONFIGS };
