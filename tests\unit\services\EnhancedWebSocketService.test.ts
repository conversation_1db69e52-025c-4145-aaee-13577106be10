/**
 * Unit Tests for Enhanced WebSocket Service
 * 
 * Tests all WebSocket functionality including:
 * - Connection management and authentication
 * - Message batching and compression
 * - Subscription handling and rate limiting
 * - Performance optimization features
 * - Latency monitoring and metrics
 */

import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import { EnhancedWebSocketService } from '../../../backend/services/EnhancedWebSocketService.js';

// Mock dependencies
jest.mock('../../../backend/utils/logger.js', () => ({
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

jest.mock('../../../backend/services/EnhancedCacheService.js', () => ({
  enhancedCacheService: {
    get: jest.fn(),
    set: jest.fn(),
    getMetrics: jest.fn(() => ({ hitRatio: 0.85 }))
  }
}));

jest.mock('../../../backend/services/EnhancedDatabaseConnectionManager.js', () => ({
  enhancedDatabaseManager: {
    getRedisClient: jest.fn(),
    getAllConnectionStatus: jest.fn(() => ({
      health: new Map([['redis', true], ['supabase', true]]),
      circuitBreakers: new Map([['redis', false], ['supabase', false]])
    })),
    isHealthy: jest.fn(() => true)
  }
}));

jest.mock('../../../backend/services/EnhancedInfluxDBService.js', () => ({
  enhancedInfluxDBService: {
    queryOpportunityStats: jest.fn(() => Promise.resolve({})),
    queryExecutionPerformance: jest.fn(() => Promise.resolve({}))
  }
}));

// Mock WebSocket
const mockWebSocket = {
  readyState: WebSocket.OPEN,
  send: jest.fn(),
  close: jest.fn(),
  terminate: jest.fn(),
  on: jest.fn(),
  removeAllListeners: jest.fn()
};

const mockWebSocketServer = {
  on: jest.fn(),
  close: jest.fn(),
  clients: new Set()
};

jest.mock('ws', () => ({
  WebSocketServer: jest.fn(() => mockWebSocketServer),
  WebSocket: {
    OPEN: 1,
    CLOSED: 3
  }
}));

describe('EnhancedWebSocketService', () => {
  let service: EnhancedWebSocketService;
  let mockServer: Server;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new EnhancedWebSocketService();
    mockServer = {} as Server;
  });

  afterEach(async () => {
    if (service) {
      await service.shutdown();
    }
  });

  describe('Initialization', () => {
    it('should initialize WebSocket server with performance optimizations', () => {
      service.initialize(mockServer);

      expect(WebSocketServer).toHaveBeenCalledWith({
        server: mockServer,
        path: '/ws',
        perMessageDeflate: {
          threshold: 1024,
          concurrencyLimit: 10,
          zlibDeflateOptions: {
            level: 3,
            chunkSize: 1024
          }
        },
        maxPayload: 1024 * 1024,
        clientTracking: true,
        maxCompressedSize: 64 * 1024,
        maxUncompressedSize: 256 * 1024
      });

      expect(mockWebSocketServer.on).toHaveBeenCalledWith('connection', expect.any(Function));
    });

    it('should initialize subscription configurations', () => {
      service.initialize(mockServer);
      
      const metrics = service.getMetrics();
      expect(metrics).toBeDefined();
      expect(metrics.totalConnections).toBe(0);
      expect(metrics.activeConnections).toBe(0);
    });
  });

  describe('Connection Management', () => {
    beforeEach(() => {
      service.initialize(mockServer);
    });

    it('should handle new client connections', () => {
      const connectionHandler = mockWebSocketServer.on.mock.calls
        .find(call => call[0] === 'connection')?.[1];
      
      expect(connectionHandler).toBeDefined();
      
      if (connectionHandler) {
        connectionHandler(mockWebSocket, {});
        
        const metrics = service.getMetrics();
        expect(metrics.totalConnections).toBe(1);
        expect(metrics.activeConnections).toBe(1);
      }
    });

    it('should track client metrics correctly', () => {
      const connectionHandler = mockWebSocketServer.on.mock.calls
        .find(call => call[0] === 'connection')?.[1];
      
      if (connectionHandler) {
        connectionHandler(mockWebSocket, {});
        
        expect(mockWebSocket.on).toHaveBeenCalledWith('message', expect.any(Function));
        expect(mockWebSocket.on).toHaveBeenCalledWith('close', expect.any(Function));
        expect(mockWebSocket.on).toHaveBeenCalledWith('error', expect.any(Function));
        expect(mockWebSocket.on).toHaveBeenCalledWith('pong', expect.any(Function));
      }
    });

    it('should send initial connection message', () => {
      const connectionHandler = mockWebSocketServer.on.mock.calls
        .find(call => call[0] === 'connection')?.[1];
      
      if (connectionHandler) {
        connectionHandler(mockWebSocket, {});
        
        expect(mockWebSocket.send).toHaveBeenCalledWith(
          expect.stringContaining('"type":"connection:established"')
        );
      }
    });
  });

  describe('Message Handling', () => {
    let messageHandler: Function;

    beforeEach(() => {
      service.initialize(mockServer);
      
      const connectionHandler = mockWebSocketServer.on.mock.calls
        .find(call => call[0] === 'connection')?.[1];
      
      if (connectionHandler) {
        connectionHandler(mockWebSocket, {});
        messageHandler = mockWebSocket.on.mock.calls
          .find(call => call[0] === 'message')?.[1];
      }
    });

    it('should handle subscription messages', () => {
      const subscribeMessage = JSON.stringify({
        type: 'subscribe',
        data: { channel: 'opportunities:active' }
      });

      if (messageHandler) {
        messageHandler(Buffer.from(subscribeMessage));
        
        expect(mockWebSocket.send).toHaveBeenCalledWith(
          expect.stringContaining('"type":"subscription:confirmed"')
        );
      }
    });

    it('should handle ping messages', () => {
      const pingMessage = JSON.stringify({
        type: 'ping'
      });

      if (messageHandler) {
        messageHandler(Buffer.from(pingMessage));
        
        expect(mockWebSocket.send).toHaveBeenCalledWith(
          expect.stringContaining('"type":"pong"')
        );
      }
    });

    it('should handle authentication messages', () => {
      const authMessage = JSON.stringify({
        type: 'authenticate',
        data: { token: 'test-token' }
      });

      if (messageHandler) {
        messageHandler(Buffer.from(authMessage));
        
        expect(mockWebSocket.send).toHaveBeenCalledWith(
          expect.stringContaining('"type":"authentication:result"')
        );
      }
    });

    it('should handle invalid JSON messages gracefully', () => {
      const invalidMessage = 'invalid json';

      if (messageHandler) {
        messageHandler(Buffer.from(invalidMessage));
        
        const metrics = service.getMetrics();
        expect(metrics.errorRate).toBeGreaterThan(0);
      }
    });
  });

  describe('Performance Optimization', () => {
    beforeEach(() => {
      service.initialize(mockServer);
    });

    it('should track latency metrics', () => {
      const metrics = service.getMetrics();
      
      expect(metrics.batchProcessingStats).toBeDefined();
      expect(metrics.batchProcessingStats.batchesSent).toBe(0);
      expect(metrics.batchProcessingStats.averageBatchSize).toBe(0);
      expect(metrics.batchProcessingStats.batchLatency).toBe(0);
    });

    it('should track compression metrics', () => {
      const metrics = service.getMetrics();
      
      expect(metrics.compressionRatio).toBeDefined();
      expect(metrics.messageQueueSize).toBeDefined();
    });

    it('should track connection pool stats', () => {
      const metrics = service.getMetrics();
      
      expect(metrics.connectionPoolStats).toBeDefined();
      expect(metrics.connectionPoolStats.poolSize).toBeDefined();
      expect(metrics.connectionPoolStats.activeConnections).toBeDefined();
      expect(metrics.connectionPoolStats.idleConnections).toBeDefined();
    });
  });

  describe('Broadcasting', () => {
    beforeEach(() => {
      service.initialize(mockServer);
    });

    it('should broadcast opportunity updates', async () => {
      const opportunityData = {
        id: 'test-opp-1',
        type: 'intra-chain',
        potentialProfit: 100
      };

      await service.broadcastOpportunity(opportunityData);
      
      // Since no clients are subscribed, no messages should be sent
      // This tests the broadcasting mechanism without actual clients
      expect(true).toBe(true); // Placeholder assertion
    });

    it('should broadcast price updates', async () => {
      const priceData = {
        symbol: 'ETH',
        price: 2000,
        timestamp: Date.now()
      };

      await service.broadcastPriceUpdate(priceData);
      
      expect(true).toBe(true); // Placeholder assertion
    });

    it('should broadcast system health updates', async () => {
      const healthData = {
        status: 'healthy',
        uptime: 3600000
      };

      await service.broadcastSystemHealth(healthData);
      
      expect(true).toBe(true); // Placeholder assertion
    });
  });

  describe('Health Monitoring', () => {
    beforeEach(() => {
      service.initialize(mockServer);
    });

    it('should report healthy status when error rate is low', () => {
      const isHealthy = service.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should return current metrics', () => {
      const metrics = service.getMetrics();
      
      expect(metrics).toHaveProperty('totalConnections');
      expect(metrics).toHaveProperty('activeConnections');
      expect(metrics).toHaveProperty('messagesSent');
      expect(metrics).toHaveProperty('messagesReceived');
      expect(metrics).toHaveProperty('averageLatency');
      expect(metrics).toHaveProperty('errorRate');
      expect(metrics).toHaveProperty('batchProcessingStats');
      expect(metrics).toHaveProperty('connectionPoolStats');
    });

    it('should return connected clients count', () => {
      const clientCount = service.getConnectedClients();
      expect(typeof clientCount).toBe('number');
      expect(clientCount).toBeGreaterThanOrEqual(0);
    });

    it('should return subscription statistics', () => {
      const subscriptionStats = service.getSubscriptionStats();
      expect(subscriptionStats).toBeInstanceOf(Map);
    });
  });

  describe('Shutdown', () => {
    beforeEach(() => {
      service.initialize(mockServer);
    });

    it('should shutdown gracefully', async () => {
      await service.shutdown();
      
      expect(mockWebSocketServer.close).toHaveBeenCalled();
    });

    it('should clear all data structures on shutdown', async () => {
      await service.shutdown();
      
      const metrics = service.getMetrics();
      expect(metrics.activeConnections).toBe(0);
    });
  });
});
