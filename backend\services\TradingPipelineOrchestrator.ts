import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity } from './OpportunityDetectionService.js';
import { Trade } from './ExecutionService.js';
import { enhancedWebSocketService } from './EnhancedWebSocketService.js';
import { dataRoutingService } from './DataRoutingService.js';
import { enhancedCacheService } from './EnhancedCacheService.js';

export interface TradingPair {
  id: string;
  baseToken: string;
  quoteToken: string;
  network: string;
  dex: string;
  liquidity: number;
  volume24h: number;
  isActive: boolean;
  lastUpdate: number;
  priority: 'high' | 'medium' | 'low';
}

export interface CostCalculation {
  gasCost: number;
  bridgeFee: number;
  slippage: number;
  protocolFee: number;
  totalCost: number;
  estimatedProfit: number;
  netProfit: number;
  profitMargin: number;
  riskScore: number;
}

export interface PipelineMetrics {
  totalOpportunitiesDetected: number;
  validatedOpportunities: number;
  executedTrades: number;
  successfulTrades: number;
  totalProfit: number;
  averageExecutionTime: number;
  pipelineLatency: number;
  errorRate: number;
  lastReset: number;
}

export interface PipelineStage {
  name: string;
  status: 'idle' | 'processing' | 'completed' | 'error';
  startTime: number;
  endTime?: number;
  duration?: number;
  throughput: number;
  errorCount: number;
}

export class TradingPipelineOrchestrator extends EventEmitter {
  private tradingPairs: Map<string, TradingPair> = new Map();
  private activePipelines: Map<string, PipelineStage[]> = new Map();
  private metrics: PipelineMetrics;
  private isRunning = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  // Service references
  private opportunityDetectionService: any;
  private profitValidationService: any;
  private preExecutionValidationService: any;
  private executionQueueService: any;
  private flashLoanService: any;
  private mevProtectionService: any;
  private executionService: any;

  constructor() {
    super();
    this.initializeMetrics();
    this.initializeTradingPairs();
    this.setupEventHandlers();
  }

  /**
   * Initialize pipeline metrics
   */
  private initializeMetrics(): void {
    this.metrics = {
      totalOpportunitiesDetected: 0,
      validatedOpportunities: 0,
      executedTrades: 0,
      successfulTrades: 0,
      totalProfit: 0,
      averageExecutionTime: 0,
      pipelineLatency: 0,
      errorRate: 0,
      lastReset: Date.now()
    };
  }

  /**
   * Initialize 450 trading pairs across 10 networks
   */
  private async initializeTradingPairs(): Promise<void> {
    logger.info('Initializing 450 trading pairs across 10 blockchain networks...');

    // Top 50 verified tokens
    const topTokens = [
      'BTC', 'ETH', 'USDT', 'USDC', 'BNB', 'XRP', 'ADA', 'DOGE', 'SOL', 'TRX',
      'MATIC', 'DOT', 'LTC', 'SHIB', 'AVAX', 'UNI', 'ATOM', 'LINK', 'XMR', 'ETC',
      'BCH', 'XLM', 'NEAR', 'ALGO', 'VET', 'ICP', 'FIL', 'MANA', 'SAND', 'CRO',
      'APE', 'LDO', 'QNT', 'AAVE', 'GRT', 'MKR', 'SNX', 'COMP', 'SUSHI', 'YFI',
      'BAT', 'ZRX', 'ENJ', 'STORJ', 'REN', 'KNC', 'BAND', 'CRV', 'BAL', 'RUNE'
    ];

    // 10 blockchain networks
    const networks = [
      'ethereum', 'bsc', 'polygon', 'solana', 'avalanche',
      'arbitrum', 'optimism', 'base', 'fantom', 'sui'
    ];

    // Major DEXs per network
    const dexes = {
      ethereum: ['uniswap-v3', 'sushiswap', 'curve', 'balancer'],
      bsc: ['pancakeswap', 'biswap', 'apeswap'],
      polygon: ['quickswap', 'sushiswap', 'curve'],
      solana: ['raydium', 'orca', 'serum'],
      avalanche: ['traderjoe', 'pangolin', 'sushiswap'],
      arbitrum: ['uniswap-v3', 'sushiswap', 'curve'],
      optimism: ['uniswap-v3', 'velodrome', 'curve'],
      base: ['uniswap-v3', 'aerodrome'],
      fantom: ['spookyswap', 'spiritswap'],
      sui: ['cetus', 'turbos']
    };

    let pairCount = 0;
    const targetPairs = 450;

    // Generate trading pairs
    for (const network of networks) {
      const networkDexes = dexes[network] || ['generic-dex'];
      
      for (const dex of networkDexes) {
        for (let i = 0; i < topTokens.length && pairCount < targetPairs; i++) {
          for (let j = i + 1; j < topTokens.length && pairCount < targetPairs; j++) {
            const baseToken = topTokens[i];
            const quoteToken = topTokens[j];
            
            const pairId = `${baseToken}-${quoteToken}-${network}-${dex}`;
            
            const tradingPair: TradingPair = {
              id: pairId,
              baseToken,
              quoteToken,
              network,
              dex,
              liquidity: Math.random() * 10000000 + 100000, // $100K - $10M
              volume24h: Math.random() * 50000000 + 500000, // $500K - $50M
              isActive: true,
              lastUpdate: Date.now(),
              priority: this.calculatePairPriority(baseToken, quoteToken, network)
            };

            this.tradingPairs.set(pairId, tradingPair);
            pairCount++;

            if (pairCount >= targetPairs) break;
          }
          if (pairCount >= targetPairs) break;
        }
        if (pairCount >= targetPairs) break;
      }
      if (pairCount >= targetPairs) break;
    }

    logger.info(`Initialized ${pairCount} trading pairs for monitoring`);
  }

  /**
   * Calculate priority for trading pair based on tokens and network
   */
  private calculatePairPriority(baseToken: string, quoteToken: string, network: string): 'high' | 'medium' | 'low' {
    const highPriorityTokens = ['BTC', 'ETH', 'USDT', 'USDC', 'BNB'];
    const highPriorityNetworks = ['ethereum', 'bsc', 'polygon'];

    const hasHighPriorityToken = highPriorityTokens.includes(baseToken) || highPriorityTokens.includes(quoteToken);
    const isHighPriorityNetwork = highPriorityNetworks.includes(network);

    if (hasHighPriorityToken && isHighPriorityNetwork) return 'high';
    if (hasHighPriorityToken || isHighPriorityNetwork) return 'medium';
    return 'low';
  }

  /**
   * Setup event handlers for pipeline orchestration
   */
  private setupEventHandlers(): void {
    // Opportunity detection events
    this.on('opportunity:detected', this.handleOpportunityDetected.bind(this));
    this.on('opportunity:validated', this.handleOpportunityValidated.bind(this));
    this.on('opportunity:rejected', this.handleOpportunityRejected.bind(this));

    // Execution events
    this.on('trade:queued', this.handleTradeQueued.bind(this));
    this.on('trade:executing', this.handleTradeExecuting.bind(this));
    this.on('trade:completed', this.handleTradeCompleted.bind(this));
    this.on('trade:failed', this.handleTradeFailed.bind(this));

    // Pipeline events
    this.on('pipeline:stage:start', this.handlePipelineStageStart.bind(this));
    this.on('pipeline:stage:complete', this.handlePipelineStageComplete.bind(this));
    this.on('pipeline:stage:error', this.handlePipelineStageError.bind(this));
  }

  /**
   * Start the trading pipeline orchestrator
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Trading pipeline already running');
      return;
    }

    try {
      logger.info('Starting Trading Pipeline Orchestrator...');
      this.isRunning = true;

      // Start monitoring all trading pairs
      this.startPairMonitoring();

      // Start pipeline metrics collection
      this.startMetricsCollection();

      logger.info('✅ Trading Pipeline Orchestrator started successfully');
      this.emit('pipeline:started');

    } catch (error) {
      logger.error('Failed to start trading pipeline:', error);
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * Start monitoring all trading pairs for opportunities
   */
  private startPairMonitoring(): void {
    this.monitoringInterval = setInterval(async () => {
      await this.scanTradingPairs();
    }, 5000); // Scan every 5 seconds for <5s latency requirement

    logger.info('Started monitoring 450 trading pairs');
  }

  /**
   * Scan all trading pairs for arbitrage opportunities
   */
  private async scanTradingPairs(): Promise<void> {
    try {
      const activePairs = Array.from(this.tradingPairs.values())
        .filter(pair => pair.isActive)
        .sort((a, b) => {
          // Prioritize high priority pairs
          if (a.priority !== b.priority) {
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
          }
          // Then by volume
          return b.volume24h - a.volume24h;
        });

      // Process pairs in batches to maintain performance
      const batchSize = 50;
      for (let i = 0; i < activePairs.length; i += batchSize) {
        const batch = activePairs.slice(i, i + batchSize);
        await Promise.all(batch.map(pair => this.scanPairForOpportunities(pair)));
      }

    } catch (error) {
      logger.error('Error scanning trading pairs:', error);
    }
  }

  /**
   * Scan a specific trading pair for arbitrage opportunities
   */
  private async scanPairForOpportunities(pair: TradingPair): Promise<void> {
    try {
      // Create pipeline ID for tracking
      const pipelineId = `${pair.id}-${Date.now()}`;
      
      // Initialize pipeline stages
      const stages: PipelineStage[] = [
        { name: 'detection', status: 'idle', startTime: 0, throughput: 0, errorCount: 0 },
        { name: 'cost_calculation', status: 'idle', startTime: 0, throughput: 0, errorCount: 0 },
        { name: 'profit_validation', status: 'idle', startTime: 0, throughput: 0, errorCount: 0 },
        { name: 'pre_execution_validation', status: 'idle', startTime: 0, throughput: 0, errorCount: 0 },
        { name: 'queue_entry', status: 'idle', startTime: 0, throughput: 0, errorCount: 0 },
        { name: 'execution', status: 'idle', startTime: 0, throughput: 0, errorCount: 0 }
      ];

      this.activePipelines.set(pipelineId, stages);

      // Stage 1: Opportunity Detection
      await this.executeStage(pipelineId, 'detection', async () => {
        const opportunity = await this.detectOpportunityForPair(pair);
        if (opportunity) {
          this.metrics.totalOpportunitiesDetected++;
          this.emit('opportunity:detected', { pipelineId, opportunity, pair });
          return opportunity;
        }
        return null;
      });

    } catch (error) {
      logger.error(`Error scanning pair ${pair.id}:`, error);
    }
  }

  /**
   * Execute a pipeline stage with monitoring
   */
  private async executeStage<T>(
    pipelineId: string, 
    stageName: string, 
    stageFunction: () => Promise<T>
  ): Promise<T | null> {
    const stages = this.activePipelines.get(pipelineId);
    if (!stages) return null;

    const stage = stages.find(s => s.name === stageName);
    if (!stage) return null;

    try {
      stage.status = 'processing';
      stage.startTime = Date.now();
      this.emit('pipeline:stage:start', { pipelineId, stageName });

      const result = await stageFunction();

      stage.status = 'completed';
      stage.endTime = Date.now();
      stage.duration = stage.endTime - stage.startTime;
      stage.throughput++;

      this.emit('pipeline:stage:complete', { pipelineId, stageName, duration: stage.duration });
      return result;

    } catch (error) {
      stage.status = 'error';
      stage.errorCount++;
      this.emit('pipeline:stage:error', { pipelineId, stageName, error: error.message });
      throw error;
    }
  }

  /**
   * Detect arbitrage opportunity for a specific pair
   */
  private async detectOpportunityForPair(pair: TradingPair): Promise<ArbitrageOpportunity | null> {
    // This would integrate with the actual opportunity detection service
    // For now, simulate opportunity detection
    const hasOpportunity = Math.random() < 0.05; // 5% chance of opportunity
    
    if (!hasOpportunity) return null;

    const opportunity: ArbitrageOpportunity = {
      id: `opp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'intra-chain',
      assets: [pair.baseToken, pair.quoteToken],
      network: pair.network,
      dexes: [pair.dex, 'alternative-dex'],
      potentialProfit: Math.random() * 1000 + 100,
      gasEstimate: Math.random() * 50 + 10,
      confidence: Math.random() * 0.3 + 0.7,
      timestamp: Date.now(),
      expiresAt: Date.now() + 60000, // 1 minute expiry
      isValidated: false,
      riskScore: Math.random() * 0.5 + 0.1,
      estimatedExecutionTime: Math.random() * 30 + 10,
      requiredCapital: Math.random() * 10000 + 1000,
      priceImpact: Math.random() * 0.02 + 0.001,
      liquidityDepth: pair.liquidity,
      marketConditions: {
        volatility: Math.random() * 0.1 + 0.02,
        volume: pair.volume24h,
        spread: Math.random() * 0.005 + 0.001
      }
    };

    return opportunity;
  }

  /**
   * Calculate comprehensive costs for an opportunity
   */
  public async calculateComprehensiveCosts(opportunity: ArbitrageOpportunity): Promise<CostCalculation> {
    try {
      // Gas cost calculation
      const gasCost = await this.calculateGasCost(opportunity);
      
      // Bridge fee calculation (for cross-chain)
      const bridgeFee = opportunity.type === 'cross-chain' ? 
        await this.calculateBridgeFee(opportunity) : 0;
      
      // Slippage calculation
      const slippage = await this.calculateSlippage(opportunity);
      
      // Protocol fees
      const protocolFee = await this.calculateProtocolFees(opportunity);
      
      // Total costs
      const totalCost = gasCost + bridgeFee + slippage + protocolFee;
      
      // Profit calculations
      const estimatedProfit = opportunity.potentialProfit;
      const netProfit = estimatedProfit - totalCost;
      const profitMargin = netProfit / estimatedProfit;
      
      // Risk assessment
      const riskScore = this.calculateRiskScore(opportunity, totalCost, netProfit);

      const calculation: CostCalculation = {
        gasCost,
        bridgeFee,
        slippage,
        protocolFee,
        totalCost,
        estimatedProfit,
        netProfit,
        profitMargin,
        riskScore
      };

      // Cache the calculation
      await enhancedCacheService.set(
        `cost_calc:${opportunity.id}`, 
        calculation, 
        300 // 5 minutes
      );

      return calculation;

    } catch (error) {
      logger.error('Error calculating comprehensive costs:', error);
      throw error;
    }
  }

  /**
   * Calculate gas cost for opportunity execution
   */
  private async calculateGasCost(opportunity: ArbitrageOpportunity): Promise<number> {
    // Base gas estimates for different operation types
    const gasEstimates = {
      'intra-chain': 150000,
      'cross-chain': 300000,
      'triangular': 200000,
      'flash-loan': 400000
    };

    const baseGas = gasEstimates[opportunity.type] || 150000;
    
    // Get current gas price for the network
    const gasPrice = await this.getCurrentGasPrice(opportunity.network);
    
    // Calculate total gas cost in USD
    const gasCostWei = baseGas * gasPrice;
    const gasCostUSD = await this.convertWeiToUSD(gasCostWei, opportunity.network);
    
    return gasCostUSD;
  }

  /**
   * Calculate bridge fees for cross-chain opportunities
   */
  private async calculateBridgeFee(opportunity: ArbitrageOpportunity): Promise<number> {
    if (opportunity.type !== 'cross-chain') return 0;
    
    // Bridge fee is typically 0.1% - 0.5% of transaction value
    const bridgeFeePercentage = 0.003; // 0.3%
    return opportunity.requiredCapital * bridgeFeePercentage;
  }

  /**
   * Calculate slippage impact
   */
  private async calculateSlippage(opportunity: ArbitrageOpportunity): Promise<number> {
    // Slippage depends on trade size vs liquidity depth
    const tradeToLiquidityRatio = opportunity.requiredCapital / opportunity.liquidityDepth;
    const slippagePercentage = Math.min(tradeToLiquidityRatio * 0.1, 0.05); // Max 5%
    
    return opportunity.requiredCapital * slippagePercentage;
  }

  /**
   * Calculate protocol fees
   */
  private async calculateProtocolFees(opportunity: ArbitrageOpportunity): Promise<number> {
    // Most DEXs charge 0.3% trading fee
    const tradingFeePercentage = 0.003;
    const numberOfTrades = opportunity.dexes.length;
    
    return opportunity.requiredCapital * tradingFeePercentage * numberOfTrades;
  }

  /**
   * Calculate risk score based on various factors
   */
  private calculateRiskScore(
    opportunity: ArbitrageOpportunity, 
    totalCost: number, 
    netProfit: number
  ): number {
    let riskScore = 0;

    // Profit margin risk
    const profitMargin = netProfit / opportunity.potentialProfit;
    if (profitMargin < 0.1) riskScore += 0.3; // High risk if profit margin < 10%
    else if (profitMargin < 0.2) riskScore += 0.1;

    // Market volatility risk
    riskScore += opportunity.marketConditions.volatility * 2;

    // Liquidity risk
    const liquidityRatio = opportunity.requiredCapital / opportunity.liquidityDepth;
    if (liquidityRatio > 0.1) riskScore += 0.2; // High risk if trade > 10% of liquidity

    // Time sensitivity risk
    const timeToExpiry = opportunity.expiresAt - Date.now();
    if (timeToExpiry < 30000) riskScore += 0.2; // High risk if < 30 seconds

    // Network congestion risk (simplified)
    if (opportunity.network === 'ethereum') riskScore += 0.1;

    return Math.min(riskScore, 1.0); // Cap at 1.0
  }

  // Helper methods for cost calculations
  private async getCurrentGasPrice(network: string): Promise<number> {
    // This would integrate with actual gas price APIs
    const gasPrices = {
      ethereum: 30e9, // 30 gwei
      bsc: 5e9,       // 5 gwei
      polygon: 30e9,  // 30 gwei
      arbitrum: 0.1e9, // 0.1 gwei
      optimism: 0.001e9, // 0.001 gwei
      avalanche: 25e9, // 25 gwei
      fantom: 20e9,   // 20 gwei
      base: 0.1e9,    // 0.1 gwei
      solana: 5000,   // 5000 lamports
      sui: 1000       // 1000 MIST
    };

    return gasPrices[network] || 20e9;
  }

  private async convertWeiToUSD(weiAmount: number, network: string): Promise<number> {
    // This would integrate with actual price feeds
    const ethPriceUSD = 2000; // Simplified
    const weiPerEth = 1e18;
    
    return (weiAmount / weiPerEth) * ethPriceUSD;
  }

  /**
   * Start metrics collection
   */
  private startMetricsCollection(): void {
    setInterval(() => {
      this.updateMetrics();
    }, 10000); // Update every 10 seconds
  }

  /**
   * Update pipeline metrics
   */
  private updateMetrics(): void {
    // Calculate average execution time
    const completedPipelines = Array.from(this.activePipelines.values())
      .filter(stages => stages.every(stage => stage.status === 'completed'));

    if (completedPipelines.length > 0) {
      const totalExecutionTime = completedPipelines.reduce((sum, stages) => {
        const totalTime = stages.reduce((stageSum, stage) => stageSum + (stage.duration || 0), 0);
        return sum + totalTime;
      }, 0);

      this.metrics.averageExecutionTime = totalExecutionTime / completedPipelines.length;
    }

    // Calculate error rate
    const totalStages = Array.from(this.activePipelines.values())
      .reduce((sum, stages) => sum + stages.length, 0);
    
    const errorStages = Array.from(this.activePipelines.values())
      .reduce((sum, stages) => sum + stages.filter(s => s.status === 'error').length, 0);

    this.metrics.errorRate = totalStages > 0 ? errorStages / totalStages : 0;

    // Broadcast metrics update
    enhancedWebSocketService.broadcastPerformanceMetrics(this.metrics);
  }

  // Event handlers
  private async handleOpportunityDetected(event: any): Promise<void> {
    const { pipelineId, opportunity, pair } = event;
    
    // Stage 2: Cost Calculation
    await this.executeStage(pipelineId, 'cost_calculation', async () => {
      const costCalculation = await this.calculateComprehensiveCosts(opportunity);
      
      // Only proceed if net profit is positive (100% positive profit guarantee)
      if (costCalculation.netProfit > 0) {
        this.emit('opportunity:cost_calculated', { pipelineId, opportunity, costCalculation });
        return costCalculation;
      } else {
        this.emit('opportunity:rejected', { 
          pipelineId, 
          opportunity, 
          reason: 'Negative net profit',
          costCalculation 
        });
        return null;
      }
    });
  }

  private async handleOpportunityValidated(event: any): Promise<void> {
    this.metrics.validatedOpportunities++;
    // Continue with execution pipeline
  }

  private async handleOpportunityRejected(event: any): Promise<void> {
    // Clean up pipeline
    this.activePipelines.delete(event.pipelineId);
  }

  private async handleTradeQueued(event: any): Promise<void> {
    // Track queued trades
  }

  private async handleTradeExecuting(event: any): Promise<void> {
    // Track executing trades
  }

  private async handleTradeCompleted(event: any): Promise<void> {
    this.metrics.executedTrades++;
    this.metrics.successfulTrades++;
    this.metrics.totalProfit += event.profit || 0;
  }

  private async handleTradeFailed(event: any): Promise<void> {
    this.metrics.executedTrades++;
    // Don't increment successful trades
  }

  private async handlePipelineStageStart(event: any): Promise<void> {
    logger.debug(`Pipeline ${event.pipelineId} stage ${event.stageName} started`);
  }

  private async handlePipelineStageComplete(event: any): Promise<void> {
    logger.debug(`Pipeline ${event.pipelineId} stage ${event.stageName} completed in ${event.duration}ms`);
  }

  private async handlePipelineStageError(event: any): Promise<void> {
    logger.error(`Pipeline ${event.pipelineId} stage ${event.stageName} error: ${event.error}`);
  }

  /**
   * Get current pipeline metrics
   */
  public getMetrics(): PipelineMetrics {
    return { ...this.metrics };
  }

  /**
   * Get active trading pairs
   */
  public getTradingPairs(): TradingPair[] {
    return Array.from(this.tradingPairs.values());
  }

  /**
   * Get active pipelines
   */
  public getActivePipelines(): Map<string, PipelineStage[]> {
    return new Map(this.activePipelines);
  }

  /**
   * Stop the trading pipeline orchestrator
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) return;

    logger.info('Stopping Trading Pipeline Orchestrator...');
    this.isRunning = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    // Wait for active pipelines to complete
    await this.waitForPipelinesToComplete();

    logger.info('✅ Trading Pipeline Orchestrator stopped');
    this.emit('pipeline:stopped');
  }

  /**
   * Wait for all active pipelines to complete
   */
  private async waitForPipelinesToComplete(): Promise<void> {
    const maxWaitTime = 30000; // 30 seconds
    const startTime = Date.now();

    while (this.activePipelines.size > 0 && (Date.now() - startTime) < maxWaitTime) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (this.activePipelines.size > 0) {
      logger.warn(`${this.activePipelines.size} pipelines still active after timeout`);
    }
  }
}

// Export singleton instance
export const tradingPipelineOrchestrator = new TradingPipelineOrchestrator();
