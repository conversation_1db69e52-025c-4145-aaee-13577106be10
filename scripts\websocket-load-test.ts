#!/usr/bin/env node

/**
 * WebSocket Load Testing Script for MEV Arbitrage Bot
 * 
 * Comprehensive load testing framework that:
 * - Simulates concurrent WebSocket connections
 * - Measures latency and throughput under load
 * - Validates performance against <5s latency target
 * - Tests connection stability and error handling
 * - Generates detailed performance reports
 */

import { WebSocketLoadTester, LoadTestConfig, LoadTestMetrics } from '../tests/performance/websocket-load-test.js';
import { performance } from 'perf_hooks';
import fs from 'fs/promises';
import path from 'path';

interface TestScenario {
  name: string;
  description: string;
  config: LoadTestConfig;
  expectedResults: {
    maxLatency: number;
    minThroughput: number;
    maxErrorRate: number;
    minSuccessRate: number;
  };
}

interface TestSuite {
  name: string;
  scenarios: TestScenario[];
  setupDelay: number;
  teardownDelay: number;
}

class WebSocketLoadTestRunner {
  private results: Map<string, LoadTestMetrics> = new Map();
  private startTime: number = 0;
  private endTime: number = 0;

  constructor(private outputDir: string = './test-results') {}

  async runTestSuite(suite: TestSuite): Promise<Map<string, LoadTestMetrics>> {
    console.log(`\n🚀 Starting WebSocket Load Test Suite: ${suite.name}`);
    console.log('=' .repeat(60));
    
    this.startTime = performance.now();

    for (const scenario of suite.scenarios) {
      console.log(`\n📊 Running scenario: ${scenario.name}`);
      console.log(`   Description: ${scenario.description}`);
      console.log(`   Clients: ${scenario.config.concurrentClients}`);
      console.log(`   Duration: ${scenario.config.testDurationMs / 1000}s`);
      console.log(`   Message Interval: ${scenario.config.messageIntervalMs}ms`);

      try {
        const tester = new WebSocketLoadTester(scenario.config);
        const metrics = await tester.runLoadTest();
        
        this.results.set(scenario.name, metrics);
        this.printScenarioResults(scenario, metrics);
        
        // Validate against expected results
        this.validateResults(scenario, metrics);

        // Wait between scenarios
        if (suite.setupDelay > 0) {
          console.log(`   ⏳ Waiting ${suite.setupDelay / 1000}s before next scenario...`);
          await this.delay(suite.setupDelay);
        }

      } catch (error) {
        console.error(`   ❌ Scenario failed: ${error.message}`);
        
        // Store failed result
        this.results.set(scenario.name, {
          totalClients: scenario.config.concurrentClients,
          successfulConnections: 0,
          failedConnections: scenario.config.concurrentClients,
          totalMessagesSent: 0,
          totalMessagesReceived: 0,
          averageLatency: 0,
          maxLatency: 0,
          minLatency: 0,
          latencyP95: 0,
          latencyP99: 0,
          connectionErrors: scenario.config.concurrentClients,
          messageErrors: 0,
          throughputMsgsPerSec: 0,
          memoryUsageMB: 0,
          testDurationMs: 0
        });
      }
    }

    this.endTime = performance.now();
    
    // Generate comprehensive report
    await this.generateReport(suite);
    
    console.log(`\n✅ Test suite completed in ${((this.endTime - this.startTime) / 1000).toFixed(2)}s`);
    
    return this.results;
  }

  private printScenarioResults(scenario: TestScenario, metrics: LoadTestMetrics): void {
    console.log(`\n   📈 Results for ${scenario.name}:`);
    console.log(`   ├─ Connections: ${metrics.successfulConnections}/${metrics.totalClients} successful`);
    console.log(`   ├─ Messages: ${metrics.totalMessagesReceived} received, ${metrics.totalMessagesSent} sent`);
    console.log(`   ├─ Latency: avg=${metrics.averageLatency.toFixed(2)}ms, max=${metrics.maxLatency.toFixed(2)}ms`);
    console.log(`   ├─ Latency P95: ${metrics.latencyP95.toFixed(2)}ms, P99: ${metrics.latencyP99.toFixed(2)}ms`);
    console.log(`   ├─ Throughput: ${metrics.throughputMsgsPerSec.toFixed(2)} msgs/sec`);
    console.log(`   ├─ Errors: ${metrics.connectionErrors} connection, ${metrics.messageErrors} message`);
    console.log(`   └─ Memory: ${metrics.memoryUsageMB.toFixed(2)}MB`);
  }

  private validateResults(scenario: TestScenario, metrics: LoadTestMetrics): void {
    const validations = [
      {
        name: 'Latency Target',
        actual: metrics.averageLatency,
        expected: scenario.expectedResults.maxLatency,
        operator: 'lt',
        unit: 'ms'
      },
      {
        name: 'Throughput Target',
        actual: metrics.throughputMsgsPerSec,
        expected: scenario.expectedResults.minThroughput,
        operator: 'gt',
        unit: 'msgs/sec'
      },
      {
        name: 'Success Rate',
        actual: (metrics.successfulConnections / metrics.totalClients) * 100,
        expected: scenario.expectedResults.minSuccessRate,
        operator: 'gt',
        unit: '%'
      },
      {
        name: 'Error Rate',
        actual: (metrics.connectionErrors / metrics.totalClients) * 100,
        expected: scenario.expectedResults.maxErrorRate,
        operator: 'lt',
        unit: '%'
      }
    ];

    console.log(`\n   🎯 Validation Results:`);
    
    for (const validation of validations) {
      const passed = validation.operator === 'lt' 
        ? validation.actual < validation.expected
        : validation.actual > validation.expected;
      
      const status = passed ? '✅' : '❌';
      const comparison = validation.operator === 'lt' ? '<' : '>';
      
      console.log(`   ${status} ${validation.name}: ${validation.actual.toFixed(2)}${validation.unit} ${comparison} ${validation.expected}${validation.unit}`);
    }
  }

  private async generateReport(suite: TestSuite): Promise<void> {
    const report = {
      testSuite: suite.name,
      timestamp: new Date().toISOString(),
      duration: this.endTime - this.startTime,
      scenarios: Array.from(this.results.entries()).map(([name, metrics]) => ({
        name,
        metrics,
        scenario: suite.scenarios.find(s => s.name === name)
      })),
      summary: this.generateSummary()
    };

    // Ensure output directory exists
    await fs.mkdir(this.outputDir, { recursive: true });

    // Write JSON report
    const jsonPath = path.join(this.outputDir, `websocket-load-test-${Date.now()}.json`);
    await fs.writeFile(jsonPath, JSON.stringify(report, null, 2));

    // Write CSV report
    const csvPath = path.join(this.outputDir, `websocket-load-test-${Date.now()}.csv`);
    await this.writeCsvReport(csvPath);

    console.log(`\n📄 Reports generated:`);
    console.log(`   JSON: ${jsonPath}`);
    console.log(`   CSV: ${csvPath}`);
  }

  private generateSummary() {
    const allMetrics = Array.from(this.results.values());
    
    if (allMetrics.length === 0) {
      return {
        totalScenarios: 0,
        averageLatency: 0,
        maxLatency: 0,
        totalThroughput: 0,
        overallSuccessRate: 0,
        totalErrors: 0
      };
    }

    const totalConnections = allMetrics.reduce((sum, m) => sum + m.totalClients, 0);
    const successfulConnections = allMetrics.reduce((sum, m) => sum + m.successfulConnections, 0);
    const totalErrors = allMetrics.reduce((sum, m) => sum + m.connectionErrors + m.messageErrors, 0);

    return {
      totalScenarios: allMetrics.length,
      averageLatency: allMetrics.reduce((sum, m) => sum + m.averageLatency, 0) / allMetrics.length,
      maxLatency: Math.max(...allMetrics.map(m => m.maxLatency)),
      totalThroughput: allMetrics.reduce((sum, m) => sum + m.throughputMsgsPerSec, 0),
      overallSuccessRate: (successfulConnections / totalConnections) * 100,
      totalErrors
    };
  }

  private async writeCsvReport(csvPath: string): Promise<void> {
    const headers = [
      'Scenario',
      'Total Clients',
      'Successful Connections',
      'Failed Connections',
      'Messages Sent',
      'Messages Received',
      'Average Latency (ms)',
      'Max Latency (ms)',
      'P95 Latency (ms)',
      'P99 Latency (ms)',
      'Throughput (msgs/sec)',
      'Connection Errors',
      'Message Errors',
      'Memory Usage (MB)',
      'Test Duration (ms)'
    ];

    const rows = Array.from(this.results.entries()).map(([name, metrics]) => [
      name,
      metrics.totalClients,
      metrics.successfulConnections,
      metrics.failedConnections,
      metrics.totalMessagesSent,
      metrics.totalMessagesReceived,
      metrics.averageLatency.toFixed(2),
      metrics.maxLatency.toFixed(2),
      metrics.latencyP95.toFixed(2),
      metrics.latencyP99.toFixed(2),
      metrics.throughputMsgsPerSec.toFixed(2),
      metrics.connectionErrors,
      metrics.messageErrors,
      metrics.memoryUsageMB.toFixed(2),
      metrics.testDurationMs
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');

    await fs.writeFile(csvPath, csvContent);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Define test scenarios
const testSuites: TestSuite[] = [
  {
    name: 'WebSocket Performance Validation',
    setupDelay: 2000,
    teardownDelay: 1000,
    scenarios: [
      {
        name: 'Baseline Performance',
        description: 'Test basic WebSocket performance with minimal load',
        config: {
          concurrentClients: 5,
          testDurationMs: 30000,
          messageIntervalMs: 2000,
          targetLatencyMs: 5000,
          wsUrl: 'ws://localhost:8080/ws',
          subscriptionChannels: ['opportunities:active', 'prices:current']
        },
        expectedResults: {
          maxLatency: 5000,
          minThroughput: 1,
          maxErrorRate: 5,
          minSuccessRate: 95
        }
      },
      {
        name: 'Medium Load Test',
        description: 'Test WebSocket performance under medium concurrent load',
        config: {
          concurrentClients: 25,
          testDurationMs: 60000,
          messageIntervalMs: 1000,
          targetLatencyMs: 5000,
          wsUrl: 'ws://localhost:8080/ws',
          subscriptionChannels: ['opportunities:active', 'prices:current', 'system:health']
        },
        expectedResults: {
          maxLatency: 5000,
          minThroughput: 10,
          maxErrorRate: 10,
          minSuccessRate: 90
        }
      },
      {
        name: 'High Load Stress Test',
        description: 'Test WebSocket performance under high concurrent load',
        config: {
          concurrentClients: 50,
          testDurationMs: 120000,
          messageIntervalMs: 500,
          targetLatencyMs: 5000,
          wsUrl: 'ws://localhost:8080/ws',
          subscriptionChannels: ['opportunities:active', 'prices:current', 'system:health', 'performance:metrics']
        },
        expectedResults: {
          maxLatency: 7000, // Allow slightly higher latency under stress
          minThroughput: 20,
          maxErrorRate: 15,
          minSuccessRate: 85
        }
      },
      {
        name: 'Peak Load Test',
        description: 'Test WebSocket performance at maximum expected load',
        config: {
          concurrentClients: 100,
          testDurationMs: 180000,
          messageIntervalMs: 250,
          targetLatencyMs: 5000,
          wsUrl: 'ws://localhost:8080/ws',
          subscriptionChannels: ['opportunities:active', 'prices:current', 'system:health', 'performance:metrics', 'analytics:dashboard']
        },
        expectedResults: {
          maxLatency: 10000, // Higher tolerance for peak load
          minThroughput: 30,
          maxErrorRate: 20,
          minSuccessRate: 80
        }
      }
    ]
  }
];

// Main execution
async function main() {
  console.log('🔥 MEV Arbitrage Bot - WebSocket Load Testing Framework');
  console.log('Testing WebSocket performance against <5s latency target');
  console.log('=' .repeat(60));

  const runner = new WebSocketLoadTestRunner('./test-results/websocket-load');

  try {
    for (const suite of testSuites) {
      await runner.runTestSuite(suite);
      
      // Wait between test suites
      console.log('\n⏳ Waiting 10 seconds before next test suite...');
      await new Promise(resolve => setTimeout(resolve, 10000));
    }

    console.log('\n🎉 All WebSocket load tests completed successfully!');
    console.log('Check the test-results directory for detailed reports.');

  } catch (error) {
    console.error('\n💥 Load testing failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { WebSocketLoadTestRunner, testSuites };
