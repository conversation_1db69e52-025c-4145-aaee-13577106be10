import axios from 'axios';
import logger from '../utils/logger.js';

export interface MarketToken {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
  platforms?: {
    [key: string]: string;
  };
}

export interface TokenWithAddresses {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  total_volume: number;
  market_cap_rank: number;
  price_change_percentage_24h: number;
  addresses: {
    ethereum?: string;
    polygon?: string;
    bsc?: string;
    arbitrum?: string;
    optimism?: string;
    avalanche?: string;
  };
  safety_score: number;
  liquidity_score: number;
  last_updated: string;
}

export class MarketDataService {
  private readonly COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';
  private readonly RATE_LIMIT_DELAY = 1000; // 1 second between requests
  private lastRequestTime = 0;
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  constructor() {
    logger.info('MarketDataService initialized');
  }

  /**
   * Rate limiting helper
   */
  private async rateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.RATE_LIMIT_DELAY) {
      const delay = this.RATE_LIMIT_DELAY - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * Cache helper
   */
  private getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  /**
   * Fetch top tokens by market cap with 24h volume filter
   */
  async getTopTokensByMarketCap(limit: number = 50, minVolume24h: number = 1000000): Promise<MarketToken[]> {
    const cacheKey = `top_tokens_${limit}_${minVolume24h}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      logger.info(`Returning cached top tokens data`);
      return cached;
    }

    try {
      await this.rateLimit();
      
      logger.info(`Fetching top ${limit} tokens by market cap with min 24h volume: $${minVolume24h.toLocaleString()}`);
      
      const response = await axios.get(`${this.COINGECKO_API_BASE}/coins/markets`, {
        params: {
          vs_currency: 'usd',
          order: 'market_cap_desc',
          per_page: limit * 2, // Get more to filter by volume
          page: 1,
          sparkline: false,
          price_change_percentage: '24h',
          locale: 'en'
        },
        timeout: 10000
      });

      if (!response.data || !Array.isArray(response.data)) {
        throw new Error('Invalid response format from CoinGecko API');
      }

      // Filter by minimum 24h volume and take top tokens
      const filteredTokens = response.data
        .filter((token: MarketToken) => 
          token.total_volume >= minVolume24h && 
          token.market_cap > 0 &&
          token.current_price > 0
        )
        .slice(0, limit);

      logger.info(`Successfully fetched ${filteredTokens.length} top tokens`);
      this.setCachedData(cacheKey, filteredTokens);
      
      return filteredTokens;
    } catch (error) {
      logger.error('Error fetching top tokens:', error);
      throw new Error(`Failed to fetch top tokens: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get token contract addresses across multiple networks
   */
  async getTokenAddresses(coinId: string): Promise<{ [network: string]: string }> {
    const cacheKey = `addresses_${coinId}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      await this.rateLimit();
      
      const response = await axios.get(`${this.COINGECKO_API_BASE}/coins/${coinId}`, {
        params: {
          localization: false,
          tickers: false,
          market_data: false,
          community_data: false,
          developer_data: false,
          sparkline: false
        },
        timeout: 10000
      });

      const platforms = response.data?.platforms || {};
      const addresses: { [network: string]: string } = {};

      // Map platform names to our network names
      const networkMapping: { [key: string]: string } = {
        'ethereum': 'ethereum',
        'polygon-pos': 'polygon',
        'binance-smart-chain': 'bsc',
        'arbitrum-one': 'arbitrum',
        'optimistic-ethereum': 'optimism',
        'avalanche': 'avalanche'
      };

      Object.entries(platforms).forEach(([platform, address]) => {
        const networkName = networkMapping[platform];
        if (networkName && address && typeof address === 'string' && address !== '') {
          addresses[networkName] = address;
        }
      });

      this.setCachedData(cacheKey, addresses);
      return addresses;
    } catch (error) {
      logger.error(`Error fetching addresses for ${coinId}:`, error);
      return {};
    }
  }

  /**
   * Calculate safety score based on market metrics
   */
  private calculateSafetyScore(token: MarketToken): number {
    let score = 0;

    // Market cap rank (40 points max)
    if (token.market_cap_rank <= 10) score += 40;
    else if (token.market_cap_rank <= 50) score += 30;
    else if (token.market_cap_rank <= 100) score += 20;
    else if (token.market_cap_rank <= 500) score += 10;

    // Volume/Market cap ratio (20 points max)
    const volumeRatio = token.total_volume / token.market_cap;
    if (volumeRatio >= 0.1) score += 20;
    else if (volumeRatio >= 0.05) score += 15;
    else if (volumeRatio >= 0.01) score += 10;
    else if (volumeRatio >= 0.005) score += 5;

    // Price stability (20 points max)
    const priceChange = Math.abs(token.price_change_percentage_24h || 0);
    if (priceChange <= 5) score += 20;
    else if (priceChange <= 10) score += 15;
    else if (priceChange <= 20) score += 10;
    else if (priceChange <= 50) score += 5;

    // Market cap size (20 points max)
    if (token.market_cap >= 10000000000) score += 20; // $10B+
    else if (token.market_cap >= 1000000000) score += 15; // $1B+
    else if (token.market_cap >= 100000000) score += 10; // $100M+
    else if (token.market_cap >= 10000000) score += 5; // $10M+

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Calculate liquidity score based on volume metrics
   */
  private calculateLiquidityScore(token: MarketToken): number {
    let score = 0;

    // 24h volume (50 points max)
    if (token.total_volume >= 100000000) score += 50; // $100M+
    else if (token.total_volume >= 50000000) score += 40; // $50M+
    else if (token.total_volume >= 10000000) score += 30; // $10M+
    else if (token.total_volume >= 5000000) score += 20; // $5M+
    else if (token.total_volume >= 1000000) score += 10; // $1M+

    // Volume consistency (volume/market_cap ratio) (30 points max)
    const volumeRatio = token.total_volume / token.market_cap;
    if (volumeRatio >= 0.2) score += 30;
    else if (volumeRatio >= 0.1) score += 25;
    else if (volumeRatio >= 0.05) score += 20;
    else if (volumeRatio >= 0.01) score += 15;
    else if (volumeRatio >= 0.005) score += 10;

    // Market cap rank bonus (20 points max)
    if (token.market_cap_rank <= 20) score += 20;
    else if (token.market_cap_rank <= 50) score += 15;
    else if (token.market_cap_rank <= 100) score += 10;
    else if (token.market_cap_rank <= 200) score += 5;

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Get enriched token data with addresses and scores
   */
  async getEnrichedTopTokens(limit: number = 50): Promise<TokenWithAddresses[]> {
    try {
      logger.info(`Fetching enriched data for top ${limit} tokens`);
      
      const topTokens = await this.getTopTokensByMarketCap(limit, 1000000); // Min $1M volume
      const enrichedTokens: TokenWithAddresses[] = [];

      for (const token of topTokens) {
        try {
          const addresses = await this.getTokenAddresses(token.id);
          
          const enrichedToken: TokenWithAddresses = {
            id: token.id,
            symbol: token.symbol.toUpperCase(),
            name: token.name,
            current_price: token.current_price,
            market_cap: token.market_cap,
            total_volume: token.total_volume,
            market_cap_rank: token.market_cap_rank,
            price_change_percentage_24h: token.price_change_percentage_24h || 0,
            addresses,
            safety_score: this.calculateSafetyScore(token),
            liquidity_score: this.calculateLiquidityScore(token),
            last_updated: new Date().toISOString()
          };

          enrichedTokens.push(enrichedToken);
          
          // Small delay between address fetches
          await new Promise(resolve => setTimeout(resolve, 200));
        } catch (error) {
          logger.warn(`Failed to enrich token ${token.symbol}:`, error);
          // Continue with next token
        }
      }

      logger.info(`Successfully enriched ${enrichedTokens.length} tokens`);
      return enrichedTokens;
    } catch (error) {
      logger.error('Error getting enriched tokens:', error);
      throw error;
    }
  }

  /**
   * Get real-time price data for specific tokens
   */
  async getTokenPrices(tokenIds: string[]): Promise<{ [id: string]: { usd: number; usd_24h_change: number } }> {
    if (tokenIds.length === 0) return {};

    const cacheKey = `prices_${tokenIds.sort().join(',')}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      await this.rateLimit();
      
      const response = await axios.get(`${this.COINGECKO_API_BASE}/simple/price`, {
        params: {
          ids: tokenIds.join(','),
          vs_currencies: 'usd',
          include_24hr_change: true
        },
        timeout: 10000
      });

      this.setCachedData(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Error fetching token prices:', error);
      return {};
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    logger.info('Market data cache cleared');
  }
}
