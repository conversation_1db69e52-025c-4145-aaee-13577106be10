import { jest, describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import request from 'supertest';
import { createServer } from 'http';
import express from 'express';
import { WebSocketServer } from 'ws';
import WebSocket from 'ws';

// Import services
import { PriceFeedService } from '../../backend/services/PriceFeedService.js';
import { TokenDiscoveryService } from '../../backend/services/TokenDiscoveryService.js';
import { OpportunityDetectionService } from '../../backend/services/OpportunityDetectionService.js';
import { ExecutionService } from '../../backend/services/ExecutionService.js';
import { RiskManagementService } from '../../backend/services/RiskManagementService.js';
import { AnalyticsService } from '../../backend/services/AnalyticsService.js';

describe('System Integration Tests', () => {
  let app: express.Application;
  let server: any;
  let wss: WebSocketServer;
  let services: {
    priceFeed: PriceFeedService;
    tokenDiscovery: TokenDiscoveryService;
    opportunityDetection: OpportunityDetectionService;
    execution: ExecutionService;
    riskManagement: RiskManagementService;
    analytics: AnalyticsService;
  };

  beforeAll(async () => {
    // Setup test server
    app = express();
    app.use(express.json());
    server = createServer(app);
    wss = new WebSocketServer({ server });

    // Initialize services
    services = {
      priceFeed: new PriceFeedService(),
      tokenDiscovery: new TokenDiscoveryService(),
      opportunityDetection: new OpportunityDetectionService(
        new PriceFeedService(),
        new TokenDiscoveryService()
      ),
      execution: new ExecutionService(),
      riskManagement: new RiskManagementService(),
      analytics: new AnalyticsService()
    };

    // Setup API routes
    app.get('/api/health', (req, res) => {
      res.json({ status: 'ok', timestamp: Date.now() });
    });

    app.get('/api/opportunities', (req, res) => {
      const opportunities = services.opportunityDetection.getActiveOpportunities();
      res.json(opportunities);
    });

    app.get('/api/prices', (req, res) => {
      const prices = services.priceFeed.getAllPrices();
      res.json(prices);
    });

    app.get('/api/tokens', (req, res) => {
      const tokens = services.tokenDiscovery.getWhitelistedTokens();
      res.json(tokens);
    });

    app.get('/api/analytics', (req, res) => {
      const analytics = services.analytics.getPerformanceMetrics();
      res.json(analytics);
    });

    // Start services
    await Promise.all([
      services.priceFeed.start(),
      services.tokenDiscovery.start(),
      services.opportunityDetection.start(),
      services.execution.start(),
      services.riskManagement.start(),
      services.analytics.start()
    ]);

    // Start server
    await new Promise<void>((resolve) => {
      server.listen(3002, resolve);
    });
  });

  afterAll(async () => {
    // Stop services
    await Promise.all([
      services.priceFeed.stop(),
      services.tokenDiscovery.stop(),
      services.opportunityDetection.stop(),
      services.execution.stop(),
      services.riskManagement.stop(),
      services.analytics.stop()
    ]);

    // Close server
    wss.close();
    server.close();
  });

  describe('API Endpoints', () => {
    it('should respond to health check', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.status).toBe('ok');
      expect(response.body.timestamp).toBeDefined();
    });

    it('should return opportunities', async () => {
      const response = await request(app)
        .get('/api/opportunities')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should return price data', async () => {
      // Add some test price data
      services.priceFeed.updatePriceData(global.testUtils.mockPriceData);

      const response = await request(app)
        .get('/api/prices')
        .expect(200);

      expect(typeof response.body).toBe('object');
    });

    it('should return token data', async () => {
      const response = await request(app)
        .get('/api/tokens')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should return analytics data', async () => {
      const response = await request(app)
        .get('/api/analytics')
        .expect(200);

      expect(typeof response.body).toBe('object');
    });
  });

  describe('Service Integration', () => {
    it('should propagate price updates through the system', async () => {
      const priceData = global.testUtils.mockPriceData;
      
      // Update price in PriceFeedService
      services.priceFeed.updatePriceData(priceData);
      
      // Wait for propagation
      await global.testUtils.delay(100);
      
      // Check if OpportunityDetectionService received the update
      const retrievedPrice = services.priceFeed.getPrice(priceData.symbol);
      expect(retrievedPrice).toEqual(priceData);
    });

    it('should detect and process arbitrage opportunities end-to-end', async () => {
      // Setup price difference
      const ethUniswap = {
        ...global.testUtils.mockPriceData,
        symbol: 'ETH',
        price: 2000,
        source: 'uniswap'
      };
      
      const ethSushiswap = {
        ...global.testUtils.mockPriceData,
        symbol: 'ETH',
        price: 2100, // 5% difference
        source: 'sushiswap'
      };

      // Add prices
      services.priceFeed.updatePriceData(ethUniswap);
      services.priceFeed.updatePriceData(ethSushiswap);

      // Wait for opportunity detection
      await global.testUtils.delay(200);

      // Check if opportunity was detected
      const opportunities = services.opportunityDetection.getActiveOpportunities();
      expect(opportunities.length).toBeGreaterThan(0);

      const ethOpportunity = opportunities.find(opp => 
        opp.assets.includes('ETH')
      );
      expect(ethOpportunity).toBeDefined();
      expect(ethOpportunity?.profitPercentage).toBeGreaterThan(4);
    });

    it('should handle risk management integration', async () => {
      const opportunity = global.testUtils.mockOpportunity;
      
      // Test risk assessment
      const riskAssessment = services.riskManagement.assessOpportunity(opportunity);
      expect(riskAssessment).toBeDefined();
      expect(riskAssessment.approved).toBeDefined();
      expect(riskAssessment.riskScore).toBeWithinRange(0, 100);
    });

    it('should track analytics across services', async () => {
      // Generate some activity
      services.priceFeed.updatePriceData(global.testUtils.mockPriceData);
      services.opportunityDetection.addOpportunity(global.testUtils.mockOpportunity);
      
      await global.testUtils.delay(100);
      
      // Check analytics
      const metrics = services.analytics.getPerformanceMetrics();
      expect(metrics).toBeDefined();
      expect(metrics.totalOpportunities).toBeGreaterThanOrEqual(0);
    });
  });

  describe('WebSocket Integration', () => {
    it('should broadcast price updates via WebSocket', (done) => {
      const ws = new WebSocket('ws://localhost:3002');
      
      ws.on('open', () => {
        // Listen for price updates
        ws.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'priceUpdate') {
            expect(message.data).toBeDefined();
            ws.close();
            done();
          }
        });

        // Trigger price update
        services.priceFeed.updatePriceData(global.testUtils.mockPriceData);
      });

      ws.on('error', done);
    });

    it('should broadcast opportunity updates via WebSocket', (done) => {
      const ws = new WebSocket('ws://localhost:3002');
      
      ws.on('open', () => {
        ws.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'opportunity') {
            expect(message.data).toBeDefined();
            expect(message.data.id).toBeDefined();
            ws.close();
            done();
          }
        });

        // Trigger opportunity
        services.opportunityDetection.addOpportunity(global.testUtils.mockOpportunity);
      });

      ws.on('error', done);
    });
  });

  describe('Error Handling', () => {
    it('should handle service failures gracefully', async () => {
      // Simulate service failure
      const originalConsoleError = console.error;
      console.error = jest.fn();

      try {
        // Force an error in one service
        services.priceFeed.emit('error', new Error('Test error'));
        
        // System should still respond
        const response = await request(app)
          .get('/api/health')
          .expect(200);

        expect(response.body.status).toBe('ok');
      } finally {
        console.error = originalConsoleError;
      }
    });

    it('should handle invalid API requests', async () => {
      await request(app)
        .get('/api/nonexistent')
        .expect(404);
    });
  });
});
