#!/usr/bin/env node

/**
 * Comprehensive System Validator for MEV Arbitrage Bot
 * ===================================================
 * 
 * Complete system validation including:
 * 1. Database connectivity and performance validation
 * 2. Service health and dependency validation
 * 3. Complete arbitrage workflow testing
 * 4. Performance target validation
 * 5. Real-time monitoring setup validation
 * 6. Error handling and recovery testing
 * 7. Production readiness assessment
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Validation configuration
const VALIDATION_CONFIG = {
  performance: {
    maxDatabaseQueryTime: 100, // ms
    maxServiceResponseTime: 1000, // ms
    maxWebSocketLatency: 5000, // ms
    maxMemoryUsage: 500 * 1024 * 1024, // 500MB
    minThroughput: 10, // ops/sec
    targetUptime: 99 // %
  },
  
  endpoints: {
    health: '/api/health',
    opportunities: '/api/opportunities',
    analytics: '/api/analytics',
    system: '/api/system/status',
    tokens: '/api/tokens/top',
    execution: '/api/execution/queue'
  },
  
  databases: {
    redis: { port: 6379, testKey: 'validation_test' },
    postgres: { port: 5432, testQuery: 'SELECT 1 as test' },
    influxdb: { port: 8086, testBucket: 'mev-arbitrage-metrics' },
    supabase: { healthEndpoint: '/rest/v1/' }
  },
  
  services: {
    backend: { port: 3001, healthEndpoint: '/api/health' },
    frontend: { port: 3000, healthEndpoint: '/' },
    websocket: { port: 3001, path: '/ws' }
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class ComprehensiveSystemValidator {
  constructor() {
    this.validationResults = {
      databases: {},
      services: {},
      endpoints: {},
      performance: {},
      workflow: {},
      overall: { success: false, score: 0, errors: [], warnings: [] }
    };
    
    this.startTime = Date.now();
  }
  
  log(message, level = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const levelColors = {
      debug: colors.cyan,
      info: colors.blue,
      warn: colors.yellow,
      error: colors.red,
      success: colors.green
    };
    
    const color = levelColors[level] || colors.reset;
    console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
  }
  
  logPhase(phase) {
    this.log(`\n🔍 ${phase}`, 'info');
    this.log('=' .repeat(60), 'info');
  }
  
  async runComprehensiveValidation() {
    try {
      this.log(`${colors.bright}🧪 MEV ARBITRAGE BOT - COMPREHENSIVE SYSTEM VALIDATION${colors.reset}`);
      this.log('=' .repeat(70));
      this.log('Validating complete system readiness for production deployment');
      this.log('=' .repeat(70));
      
      // Phase 1: Database Validation
      await this.validateDatabases();
      
      // Phase 2: Service Health Validation
      await this.validateServices();
      
      // Phase 3: API Endpoint Validation
      await this.validateEndpoints();
      
      // Phase 4: Performance Validation
      await this.validatePerformance();
      
      // Phase 5: Complete Workflow Validation
      await this.validateArbitrageWorkflow();
      
      // Phase 6: Error Handling Validation
      await this.validateErrorHandling();
      
      // Phase 7: Production Readiness Assessment
      await this.assessProductionReadiness();
      
      // Generate final report
      this.generateValidationReport();
      
      return this.validationResults;
      
    } catch (error) {
      this.log(`💥 Validation failed: ${error.message}`, 'error');
      this.validationResults.overall.errors.push(error.message);
      return this.validationResults;
    }
  }
  
  async validateDatabases() {
    this.logPhase('Database Validation');
    
    // Redis validation
    await this.validateRedis();
    
    // PostgreSQL validation
    await this.validatePostgreSQL();
    
    // InfluxDB validation
    await this.validateInfluxDB();
    
    // Supabase validation
    await this.validateSupabase();
    
    const dbResults = Object.values(this.validationResults.databases);
    const successfulDbs = dbResults.filter(db => db.success).length;
    
    this.log(`Database validation: ${successfulDbs}/${dbResults.length} successful`, 
             successfulDbs === dbResults.length ? 'success' : 'warn');
  }
  
  async validateRedis() {
    const result = { success: false, latency: null, error: null };
    
    try {
      const startTime = Date.now();
      await execAsync(`redis-cli -h localhost -p ${VALIDATION_CONFIG.databases.redis.port} ping`);
      result.latency = Date.now() - startTime;
      
      // Test write/read operations
      const testKey = VALIDATION_CONFIG.databases.redis.testKey;
      await execAsync(`redis-cli -h localhost -p ${VALIDATION_CONFIG.databases.redis.port} set ${testKey} "validation_test"`);
      const { stdout } = await execAsync(`redis-cli -h localhost -p ${VALIDATION_CONFIG.databases.redis.port} get ${testKey}`);
      
      if (stdout.trim() === '"validation_test"') {
        result.success = true;
        this.log(`✅ Redis: Connected (${result.latency}ms)`, 'success');
      } else {
        throw new Error('Read/write test failed');
      }
      
    } catch (error) {
      result.error = error.message;
      this.log(`❌ Redis: ${error.message}`, 'error');
    }
    
    this.validationResults.databases.redis = result;
  }
  
  async validatePostgreSQL() {
    const result = { success: false, latency: null, error: null };
    
    try {
      const startTime = Date.now();
      const { stdout } = await execAsync(`psql -h localhost -p ${VALIDATION_CONFIG.databases.postgres.port} -U mev_user -d mev_arbitrage_bot -c "${VALIDATION_CONFIG.databases.postgres.testQuery}"`);
      result.latency = Date.now() - startTime;
      
      if (stdout.includes('test')) {
        result.success = true;
        this.log(`✅ PostgreSQL: Connected (${result.latency}ms)`, 'success');
      } else {
        throw new Error('Test query failed');
      }
      
    } catch (error) {
      result.error = error.message;
      this.log(`❌ PostgreSQL: ${error.message}`, 'error');
    }
    
    this.validationResults.databases.postgres = result;
  }
  
  async validateInfluxDB() {
    const result = { success: false, latency: null, error: null };
    
    try {
      const startTime = Date.now();
      await execAsync(`influx ping --host http://localhost:${VALIDATION_CONFIG.databases.influxdb.port}`);
      result.latency = Date.now() - startTime;
      
      // Test bucket access
      await execAsync(`influx bucket list --host http://localhost:${VALIDATION_CONFIG.databases.influxdb.port} --token 0yplGOzAMMkssXmfL_kD40Ey78LZ8_De2RbvK5Z-s-bIMZPbU_F2k-ZB3_jo-wpUbPZhsf3VDcgGwu3jwy5Ctw==`);
      
      result.success = true;
      this.log(`✅ InfluxDB: Connected (${result.latency}ms)`, 'success');
      
    } catch (error) {
      result.error = error.message;
      this.log(`❌ InfluxDB: ${error.message}`, 'error');
    }
    
    this.validationResults.databases.influxdb = result;
  }
  
  async validateSupabase() {
    const result = { success: false, latency: null, error: null };
    
    try {
      if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
        throw new Error('Supabase configuration missing');
      }
      
      const startTime = Date.now();
      const response = await fetch(`${process.env.SUPABASE_URL}${VALIDATION_CONFIG.databases.supabase.healthEndpoint}`, {
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY
        }
      });
      
      result.latency = Date.now() - startTime;
      
      if (response.ok) {
        result.success = true;
        this.log(`✅ Supabase: Connected (${result.latency}ms)`, 'success');
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
    } catch (error) {
      result.error = error.message;
      this.log(`❌ Supabase: ${error.message}`, 'error');
    }
    
    this.validationResults.databases.supabase = result;
  }
  
  async validateServices() {
    this.logPhase('Service Health Validation');
    
    for (const [serviceName, config] of Object.entries(VALIDATION_CONFIG.services)) {
      const result = { success: false, latency: null, error: null };
      
      try {
        const startTime = Date.now();
        const response = await fetch(`http://localhost:${config.port}${config.healthEndpoint}`);
        result.latency = Date.now() - startTime;
        
        if (response.ok) {
          result.success = true;
          this.log(`✅ ${serviceName}: Healthy (${result.latency}ms)`, 'success');
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
      } catch (error) {
        result.error = error.message;
        this.log(`❌ ${serviceName}: ${error.message}`, 'error');
      }
      
      this.validationResults.services[serviceName] = result;
    }
  }
  
  async validateEndpoints() {
    this.logPhase('API Endpoint Validation');
    
    for (const [endpointName, path] of Object.entries(VALIDATION_CONFIG.endpoints)) {
      const result = { success: false, latency: null, error: null, data: null };
      
      try {
        const startTime = Date.now();
        const response = await fetch(`http://localhost:3001${path}`);
        result.latency = Date.now() - startTime;
        
        if (response.ok) {
          result.data = await response.json();
          result.success = true;
          this.log(`✅ ${endpointName}: Operational (${result.latency}ms)`, 'success');
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
      } catch (error) {
        result.error = error.message;
        this.log(`❌ ${endpointName}: ${error.message}`, 'error');
      }
      
      this.validationResults.endpoints[endpointName] = result;
    }
  }
  
  async validatePerformance() {
    this.logPhase('Performance Validation');
    
    // Memory usage validation
    const memoryUsage = process.memoryUsage();
    const heapUsed = memoryUsage.heapUsed;
    
    this.validationResults.performance.memory = {
      heapUsed,
      withinTarget: heapUsed <= VALIDATION_CONFIG.performance.maxMemoryUsage,
      targetMB: Math.round(VALIDATION_CONFIG.performance.maxMemoryUsage / 1024 / 1024),
      actualMB: Math.round(heapUsed / 1024 / 1024)
    };
    
    if (this.validationResults.performance.memory.withinTarget) {
      this.log(`✅ Memory usage: ${this.validationResults.performance.memory.actualMB}MB (target: ${this.validationResults.performance.memory.targetMB}MB)`, 'success');
    } else {
      this.log(`⚠️  Memory usage: ${this.validationResults.performance.memory.actualMB}MB exceeds target ${this.validationResults.performance.memory.targetMB}MB`, 'warn');
    }
    
    // Database query performance
    await this.validateDatabasePerformance();
    
    // API response time validation
    await this.validateAPIPerformance();
  }
  
  async validateDatabasePerformance() {
    const dbPerformance = {};
    
    // Test Redis performance
    try {
      const iterations = 100;
      const startTime = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        await execAsync(`redis-cli -h localhost -p 6379 set perf_test_${i} value_${i}`);
      }
      
      const totalTime = Date.now() - startTime;
      const avgLatency = totalTime / iterations;
      
      dbPerformance.redis = {
        avgLatency,
        withinTarget: avgLatency <= VALIDATION_CONFIG.performance.maxDatabaseQueryTime,
        iterations
      };
      
      this.log(`Redis performance: ${avgLatency.toFixed(2)}ms avg (${iterations} ops)`, 
               dbPerformance.redis.withinTarget ? 'success' : 'warn');
      
    } catch (error) {
      dbPerformance.redis = { error: error.message };
      this.log(`❌ Redis performance test failed: ${error.message}`, 'error');
    }
    
    this.validationResults.performance.databases = dbPerformance;
  }
  
  async validateAPIPerformance() {
    const apiPerformance = {};
    
    for (const [endpointName, path] of Object.entries(VALIDATION_CONFIG.endpoints)) {
      try {
        const iterations = 10;
        const latencies = [];
        
        for (let i = 0; i < iterations; i++) {
          const startTime = Date.now();
          const response = await fetch(`http://localhost:3001${path}`);
          const latency = Date.now() - startTime;
          
          if (response.ok) {
            latencies.push(latency);
          }
        }
        
        if (latencies.length > 0) {
          const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
          const maxLatency = Math.max(...latencies);
          
          apiPerformance[endpointName] = {
            avgLatency,
            maxLatency,
            withinTarget: avgLatency <= VALIDATION_CONFIG.performance.maxServiceResponseTime,
            iterations: latencies.length
          };
          
          this.log(`${endpointName} API: ${avgLatency.toFixed(2)}ms avg, ${maxLatency}ms max`, 
                   apiPerformance[endpointName].withinTarget ? 'success' : 'warn');
        }
        
      } catch (error) {
        apiPerformance[endpointName] = { error: error.message };
        this.log(`❌ ${endpointName} API performance test failed: ${error.message}`, 'error');
      }
    }
    
    this.validationResults.performance.apis = apiPerformance;
  }
  
  async validateArbitrageWorkflow() {
    this.logPhase('Complete Arbitrage Workflow Validation');
    
    const workflow = {
      tokenDiscovery: false,
      priceFeeds: false,
      opportunityDetection: false,
      validation: false,
      execution: false,
      monitoring: false
    };
    
    try {
      // Test token discovery
      const tokensResponse = await fetch('http://localhost:3001/api/tokens/top');
      if (tokensResponse.ok) {
        const tokensData = await tokensResponse.json();
        workflow.tokenDiscovery = tokensData.success && tokensData.data && tokensData.data.length > 0;
        this.log(`✅ Token discovery: ${tokensData.data?.length || 0} tokens available`, 'success');
      }
      
      // Test opportunity detection
      const opportunitiesResponse = await fetch('http://localhost:3001/api/opportunities');
      if (opportunitiesResponse.ok) {
        const opportunitiesData = await opportunitiesResponse.json();
        workflow.opportunityDetection = opportunitiesData.success;
        this.log(`✅ Opportunity detection: ${opportunitiesData.data?.length || 0} opportunities`, 'success');
      }
      
      // Test system monitoring
      const healthResponse = await fetch('http://localhost:3001/api/health');
      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        workflow.monitoring = healthData.status === 'healthy';
        this.log(`✅ System monitoring: ${healthData.status}`, 'success');
      }
      
    } catch (error) {
      this.log(`❌ Workflow validation error: ${error.message}`, 'error');
    }
    
    this.validationResults.workflow = workflow;
    
    const workflowSteps = Object.values(workflow);
    const successfulSteps = workflowSteps.filter(step => step).length;
    
    this.log(`Arbitrage workflow: ${successfulSteps}/${workflowSteps.length} steps operational`, 
             successfulSteps === workflowSteps.length ? 'success' : 'warn');
  }
  
  async validateErrorHandling() {
    this.logPhase('Error Handling Validation');
    
    // Test invalid endpoint
    try {
      const response = await fetch('http://localhost:3001/api/invalid-endpoint');
      if (response.status === 404) {
        this.log('✅ 404 error handling: Proper error response', 'success');
      } else {
        this.log(`⚠️  Unexpected response for invalid endpoint: ${response.status}`, 'warn');
      }
    } catch (error) {
      this.log(`❌ Error handling test failed: ${error.message}`, 'error');
    }
  }
  
  async assessProductionReadiness() {
    this.logPhase('Production Readiness Assessment');
    
    let score = 0;
    let maxScore = 0;
    const errors = [];
    const warnings = [];
    
    // Database readiness (25 points)
    maxScore += 25;
    const dbResults = Object.values(this.validationResults.databases);
    const successfulDbs = dbResults.filter(db => db.success).length;
    score += (successfulDbs / dbResults.length) * 25;
    
    if (successfulDbs < dbResults.length) {
      errors.push(`${dbResults.length - successfulDbs} database(s) failed validation`);
    }
    
    // Service readiness (25 points)
    maxScore += 25;
    const serviceResults = Object.values(this.validationResults.services);
    const healthyServices = serviceResults.filter(service => service.success).length;
    score += (healthyServices / serviceResults.length) * 25;
    
    if (healthyServices < serviceResults.length) {
      errors.push(`${serviceResults.length - healthyServices} service(s) unhealthy`);
    }
    
    // Performance readiness (25 points)
    maxScore += 25;
    let performanceScore = 0;
    
    if (this.validationResults.performance.memory?.withinTarget) {
      performanceScore += 10;
    } else {
      warnings.push('Memory usage exceeds target');
    }
    
    const apiPerf = this.validationResults.performance.apis || {};
    const performantAPIs = Object.values(apiPerf).filter(api => api.withinTarget).length;
    const totalAPIs = Object.keys(apiPerf).length;
    
    if (totalAPIs > 0) {
      performanceScore += (performantAPIs / totalAPIs) * 15;
    }
    
    score += performanceScore;
    
    // Workflow readiness (25 points)
    maxScore += 25;
    const workflowSteps = Object.values(this.validationResults.workflow);
    const operationalSteps = workflowSteps.filter(step => step).length;
    score += (operationalSteps / workflowSteps.length) * 25;
    
    if (operationalSteps < workflowSteps.length) {
      warnings.push(`${workflowSteps.length - operationalSteps} workflow step(s) not operational`);
    }
    
    const finalScore = Math.round((score / maxScore) * 100);
    
    this.validationResults.overall = {
      success: finalScore >= 80,
      score: finalScore,
      errors,
      warnings
    };
    
    if (finalScore >= 90) {
      this.log(`🎉 Production readiness: ${finalScore}% - EXCELLENT`, 'success');
    } else if (finalScore >= 80) {
      this.log(`✅ Production readiness: ${finalScore}% - GOOD`, 'success');
    } else if (finalScore >= 60) {
      this.log(`⚠️  Production readiness: ${finalScore}% - NEEDS IMPROVEMENT`, 'warn');
    } else {
      this.log(`❌ Production readiness: ${finalScore}% - NOT READY`, 'error');
    }
  }
  
  generateValidationReport() {
    const totalTime = Date.now() - this.startTime;
    
    this.log('\n📊 COMPREHENSIVE VALIDATION REPORT', 'info');
    this.log('=' .repeat(50), 'info');
    this.log(`Total Validation Time: ${totalTime}ms`, 'info');
    this.log(`Overall Score: ${this.validationResults.overall.score}%`, 'info');
    this.log(`Production Ready: ${this.validationResults.overall.success ? 'YES' : 'NO'}`, 
             this.validationResults.overall.success ? 'success' : 'error');
    
    if (this.validationResults.overall.errors.length > 0) {
      this.log('\n❌ CRITICAL ISSUES:', 'error');
      this.validationResults.overall.errors.forEach(error => {
        this.log(`  - ${error}`, 'error');
      });
    }
    
    if (this.validationResults.overall.warnings.length > 0) {
      this.log('\n⚠️  WARNINGS:', 'warn');
      this.validationResults.overall.warnings.forEach(warning => {
        this.log(`  - ${warning}`, 'warn');
      });
    }
    
    if (this.validationResults.overall.success) {
      this.log('\n🚀 SYSTEM IS READY FOR PRODUCTION DEPLOYMENT!', 'success');
    } else {
      this.log('\n🔧 SYSTEM REQUIRES FIXES BEFORE PRODUCTION DEPLOYMENT', 'warn');
    }
  }
}

// Main execution
async function main() {
  const validator = new ComprehensiveSystemValidator();
  const results = await validator.runComprehensiveValidation();
  
  // Exit with appropriate code
  process.exit(results.overall.success ? 0 : 1);
}

// Handle direct execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('💥 Validation failed:', error);
    process.exit(1);
  });
}

export default ComprehensiveSystemValidator;
