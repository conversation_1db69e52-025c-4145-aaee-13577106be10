import { MarketDataService, TokenWithAddresses } from './MarketDataService.js';
import logger from '../utils/logger.js';

export interface DatabaseToken {
  id: string;
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  network: string;
  is_whitelisted: boolean;
  is_blacklisted: boolean;
  safety_score: number;
  liquidity: number;
  total_supply: string;
  market_cap: number;
  volume_24h: number;
  price_usd: number;
  price_change_24h: number;
  market_cap_rank: number;
  liquidity_score: number;
  coingecko_id: string;
  last_updated: string;
  created_at: string;
}

export interface TokenOpportunityMetrics {
  symbol: string;
  networks: string[];
  totalLiquidity: number;
  avgSafetyScore: number;
  maxVolume24h: number;
  priceVolatility: number;
  arbitrageScore: number;
}

export class TopTokensService {
  private marketDataService: MarketDataService;
  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_FREQUENCY = 15 * 60 * 1000; // 15 minutes
  private topTokens: Map<string, TokenWithAddresses> = new Map();
  private tokensByNetwork: Map<string, DatabaseToken[]> = new Map();

  constructor() {
    this.marketDataService = new MarketDataService();
    logger.info('TopTokensService initialized');
  }

  /**
   * Start automatic token updates
   */
  startAutoUpdate(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    // Initial update
    this.updateTopTokens().catch(error => {
      logger.error('Initial token update failed:', error);
    });

    // Schedule regular updates
    this.updateInterval = setInterval(async () => {
      try {
        await this.updateTopTokens();
      } catch (error) {
        logger.error('Scheduled token update failed:', error);
      }
    }, this.UPDATE_FREQUENCY);

    logger.info(`Auto-update started with ${this.UPDATE_FREQUENCY / 1000}s interval`);
  }

  /**
   * Stop automatic updates
   */
  stopAutoUpdate(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      logger.info('Auto-update stopped');
    }
  }

  /**
   * Update top tokens from market data
   */
  async updateTopTokens(): Promise<void> {
    try {
      logger.info('Starting top tokens update...');
      
      const enrichedTokens = await this.marketDataService.getEnrichedTopTokens(50);
      
      // Update internal cache
      this.topTokens.clear();
      this.tokensByNetwork.clear();

      const databaseTokens: DatabaseToken[] = [];

      for (const token of enrichedTokens) {
        this.topTokens.set(token.id, token);

        // Create database tokens for each network the token exists on
        Object.entries(token.addresses).forEach(([network, address]) => {
          if (address && address !== '') {
            const dbToken: DatabaseToken = {
              id: `${network}_${address.toLowerCase()}`,
              address: address.toLowerCase(),
              name: token.name,
              symbol: token.symbol,
              decimals: this.getDefaultDecimals(token.symbol),
              network,
              is_whitelisted: true,
              is_blacklisted: false,
              safety_score: token.safety_score,
              liquidity: token.total_volume,
              total_supply: '0', // Would need additional API call
              market_cap: token.market_cap,
              volume_24h: token.total_volume,
              price_usd: token.current_price,
              price_change_24h: token.price_change_percentage_24h,
              market_cap_rank: token.market_cap_rank,
              liquidity_score: token.liquidity_score,
              coingecko_id: token.id,
              last_updated: token.last_updated,
              created_at: new Date().toISOString()
            };

            databaseTokens.push(dbToken);

            // Group by network
            if (!this.tokensByNetwork.has(network)) {
              this.tokensByNetwork.set(network, []);
            }
            this.tokensByNetwork.get(network)!.push(dbToken);
          }
        });
      }

      logger.info(`Updated ${enrichedTokens.length} top tokens across ${databaseTokens.length} network instances`);
      
      // Emit update event
      this.emit('tokensUpdated', {
        totalTokens: enrichedTokens.length,
        networkInstances: databaseTokens.length,
        networks: Array.from(this.tokensByNetwork.keys()),
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error updating top tokens:', error);
      throw error;
    }
  }

  /**
   * Get default decimals for known tokens
   */
  private getDefaultDecimals(symbol: string): number {
    const knownDecimals: { [symbol: string]: number } = {
      'ETH': 18,
      'BTC': 8,
      'USDC': 6,
      'USDT': 6,
      'DAI': 18,
      'WETH': 18,
      'WBTC': 8,
      'LINK': 18,
      'UNI': 18,
      'AAVE': 18,
      'COMP': 18,
      'MKR': 18,
      'SNX': 18,
      'CRV': 18,
      'SUSHI': 18,
      'YFI': 18,
      'MATIC': 18,
      'AVAX': 18,
      'FTM': 18,
      'BNB': 18
    };

    return knownDecimals[symbol.toUpperCase()] || 18;
  }

  /**
   * Get all top tokens
   */
  getTopTokens(): TokenWithAddresses[] {
    return Array.from(this.topTokens.values());
  }

  /**
   * Get tokens by network
   */
  getTokensByNetwork(network: string): DatabaseToken[] {
    return this.tokensByNetwork.get(network) || [];
  }

  /**
   * Get tokens suitable for arbitrage (high volume, good liquidity)
   */
  getArbitrageTokens(minVolume24h: number = 5000000, minSafetyScore: number = 60): DatabaseToken[] {
    const arbitrageTokens: DatabaseToken[] = [];

    this.tokensByNetwork.forEach((tokens) => {
      tokens.forEach(token => {
        if (token.volume_24h >= minVolume24h && 
            token.safety_score >= minSafetyScore &&
            token.is_whitelisted && 
            !token.is_blacklisted) {
          arbitrageTokens.push(token);
        }
      });
    });

    // Sort by arbitrage potential (volume * safety score)
    return arbitrageTokens.sort((a, b) => {
      const scoreA = a.volume_24h * (a.safety_score / 100);
      const scoreB = b.volume_24h * (b.safety_score / 100);
      return scoreB - scoreA;
    });
  }

  /**
   * Get cross-network arbitrage opportunities
   */
  getCrossNetworkTokens(): TokenOpportunityMetrics[] {
    const tokenMetrics = new Map<string, TokenOpportunityMetrics>();

    // Group tokens by symbol across networks
    this.tokensByNetwork.forEach((tokens, network) => {
      tokens.forEach(token => {
        if (!tokenMetrics.has(token.symbol)) {
          tokenMetrics.set(token.symbol, {
            symbol: token.symbol,
            networks: [],
            totalLiquidity: 0,
            avgSafetyScore: 0,
            maxVolume24h: 0,
            priceVolatility: 0,
            arbitrageScore: 0
          });
        }

        const metrics = tokenMetrics.get(token.symbol)!;
        metrics.networks.push(network);
        metrics.totalLiquidity += token.liquidity;
        metrics.maxVolume24h = Math.max(metrics.maxVolume24h, token.volume_24h);
        metrics.priceVolatility = Math.max(metrics.priceVolatility, Math.abs(token.price_change_24h));
      });
    });

    // Calculate averages and arbitrage scores
    tokenMetrics.forEach((metrics, symbol) => {
      const tokenInstances = Array.from(this.tokensByNetwork.values())
        .flat()
        .filter(t => t.symbol === symbol);

      if (tokenInstances.length > 0) {
        metrics.avgSafetyScore = tokenInstances.reduce((sum, t) => sum + t.safety_score, 0) / tokenInstances.length;
        
        // Arbitrage score: networks * volume * safety * (1 + volatility/100)
        metrics.arbitrageScore = metrics.networks.length * 
                                 (metrics.maxVolume24h / 1000000) * 
                                 (metrics.avgSafetyScore / 100) * 
                                 (1 + metrics.priceVolatility / 100);
      }
    });

    // Return tokens available on multiple networks, sorted by arbitrage score
    return Array.from(tokenMetrics.values())
      .filter(metrics => metrics.networks.length >= 2)
      .sort((a, b) => b.arbitrageScore - a.arbitrageScore);
  }

  /**
   * Get token by address and network
   */
  getToken(address: string, network: string): DatabaseToken | null {
    const tokens = this.getTokensByNetwork(network);
    return tokens.find(t => t.address.toLowerCase() === address.toLowerCase()) || null;
  }

  /**
   * Get all supported networks
   */
  getSupportedNetworks(): string[] {
    return Array.from(this.tokensByNetwork.keys());
  }

  /**
   * Get statistics
   */
  getStatistics() {
    const totalTokens = this.topTokens.size;
    const totalNetworkInstances = Array.from(this.tokensByNetwork.values())
      .reduce((sum, tokens) => sum + tokens.length, 0);
    
    const networks = Array.from(this.tokensByNetwork.keys());
    const crossNetworkTokens = this.getCrossNetworkTokens().length;
    
    const totalVolume24h = Array.from(this.topTokens.values())
      .reduce((sum, token) => sum + token.total_volume, 0);
    
    const avgSafetyScore = Array.from(this.topTokens.values())
      .reduce((sum, token) => sum + token.safety_score, 0) / totalTokens;

    return {
      totalTokens,
      totalNetworkInstances,
      networks,
      crossNetworkTokens,
      totalVolume24h,
      avgSafetyScore,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Event emitter functionality
   */
  private listeners: { [event: string]: Function[] } = {};

  on(event: string, listener: Function): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }

  private emit(event: string, data: any): void {
    if (this.listeners[event]) {
      this.listeners[event].forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          logger.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }
}
