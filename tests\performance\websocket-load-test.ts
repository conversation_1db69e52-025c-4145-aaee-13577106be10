/**
 * WebSocket Load Testing Framework for MEV Arbitrage Bot
 * 
 * Tests WebSocket performance under various load conditions:
 * - Concurrent client simulation
 * - Latency measurement and validation (<5s target)
 * - Message throughput testing
 * - Connection stability testing
 * - Memory usage monitoring
 */

import WebSocket from 'ws';
import { performance } from 'perf_hooks';
import logger from '../../backend/utils/logger.js';

export interface LoadTestConfig {
  concurrentClients: number;
  testDurationMs: number;
  messageIntervalMs: number;
  targetLatencyMs: number;
  wsUrl: string;
  subscriptionChannels: string[];
}

export interface LoadTestMetrics {
  totalClients: number;
  successfulConnections: number;
  failedConnections: number;
  totalMessagesSent: number;
  totalMessagesReceived: number;
  averageLatency: number;
  maxLatency: number;
  minLatency: number;
  latencyP95: number;
  latencyP99: number;
  connectionErrors: number;
  messageErrors: number;
  throughputMsgsPerSec: number;
  memoryUsageMB: number;
  testDurationMs: number;
}

export interface ClientMetrics {
  clientId: string;
  connected: boolean;
  messagesSent: number;
  messagesReceived: number;
  latencies: number[];
  errors: number;
  connectionTime: number;
  lastActivity: number;
}

export class WebSocketLoadTester {
  private clients: Map<string, WebSocket> = new Map();
  private clientMetrics: Map<string, ClientMetrics> = new Map();
  private testStartTime: number = 0;
  private testEndTime: number = 0;
  private isRunning: boolean = false;

  constructor(private config: LoadTestConfig) {}

  async runLoadTest(): Promise<LoadTestMetrics> {
    logger.info(`Starting WebSocket load test with ${this.config.concurrentClients} clients`);
    
    this.testStartTime = performance.now();
    this.isRunning = true;

    // Create concurrent clients
    const clientPromises = [];
    for (let i = 0; i < this.config.concurrentClients; i++) {
      clientPromises.push(this.createClient(`client_${i}`));
    }

    // Wait for all clients to attempt connection
    await Promise.allSettled(clientPromises);

    // Run test for specified duration
    await this.runTestDuration();

    // Cleanup
    await this.cleanup();

    this.testEndTime = performance.now();
    this.isRunning = false;

    return this.calculateMetrics();
  }

  private async createClient(clientId: string): Promise<void> {
    return new Promise((resolve) => {
      const startTime = performance.now();
      const ws = new WebSocket(this.config.wsUrl);

      const metrics: ClientMetrics = {
        clientId,
        connected: false,
        messagesSent: 0,
        messagesReceived: 0,
        latencies: [],
        errors: 0,
        connectionTime: 0,
        lastActivity: startTime
      };

      this.clientMetrics.set(clientId, metrics);

      ws.on('open', () => {
        metrics.connected = true;
        metrics.connectionTime = performance.now() - startTime;
        this.clients.set(clientId, ws);

        // Subscribe to test channels
        this.config.subscriptionChannels.forEach(channel => {
          this.sendMessage(clientId, {
            type: 'subscribe',
            data: { channel }
          });
        });

        logger.debug(`Client ${clientId} connected in ${metrics.connectionTime.toFixed(2)}ms`);
        resolve();
      });

      ws.on('message', (data) => {
        const receiveTime = performance.now();
        metrics.messagesReceived++;
        metrics.lastActivity = receiveTime;

        try {
          const message = JSON.parse(data.toString());
          
          // Calculate latency if message has timestamp
          if (message.timestamp) {
            const latency = receiveTime - message.timestamp;
            metrics.latencies.push(latency);
            
            // Alert on high latency
            if (latency > this.config.targetLatencyMs) {
              logger.warn(`High latency detected for ${clientId}: ${latency.toFixed(2)}ms`);
            }
          }
        } catch (error) {
          metrics.errors++;
          logger.error(`Message parsing error for ${clientId}:`, error);
        }
      });

      ws.on('error', (error) => {
        metrics.errors++;
        logger.error(`WebSocket error for ${clientId}:`, error);
        resolve();
      });

      ws.on('close', () => {
        metrics.connected = false;
        this.clients.delete(clientId);
        logger.debug(`Client ${clientId} disconnected`);
        resolve();
      });

      // Timeout for connection
      setTimeout(() => {
        if (!metrics.connected) {
          metrics.errors++;
          ws.terminate();
          resolve();
        }
      }, 10000); // 10 second timeout
    });
  }

  private async runTestDuration(): Promise<void> {
    const testInterval = setInterval(() => {
      if (!this.isRunning) {
        clearInterval(testInterval);
        return;
      }

      // Send periodic messages from each client
      this.clients.forEach((ws, clientId) => {
        if (ws.readyState === WebSocket.OPEN) {
          this.sendMessage(clientId, {
            type: 'ping',
            timestamp: performance.now()
          });
        }
      });
    }, this.config.messageIntervalMs);

    // Wait for test duration
    await new Promise(resolve => setTimeout(resolve, this.config.testDurationMs));
    
    clearInterval(testInterval);
  }

  private sendMessage(clientId: string, message: any): void {
    const ws = this.clients.get(clientId);
    const metrics = this.clientMetrics.get(clientId);
    
    if (ws && metrics && ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
        metrics.messagesSent++;
      } catch (error) {
        metrics.errors++;
        logger.error(`Failed to send message from ${clientId}:`, error);
      }
    }
  }

  private async cleanup(): Promise<void> {
    const closePromises: Promise<void>[] = [];

    this.clients.forEach((ws, clientId) => {
      closePromises.push(new Promise((resolve) => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close(1000, 'Test completed');
        }
        resolve();
      }));
    });

    await Promise.allSettled(closePromises);
    
    // Wait a bit for cleanup
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private calculateMetrics(): LoadTestMetrics {
    const allLatencies: number[] = [];
    let totalMessagesSent = 0;
    let totalMessagesReceived = 0;
    let successfulConnections = 0;
    let failedConnections = 0;
    let connectionErrors = 0;
    let messageErrors = 0;

    this.clientMetrics.forEach(metrics => {
      if (metrics.connected || metrics.messagesReceived > 0) {
        successfulConnections++;
      } else {
        failedConnections++;
      }

      totalMessagesSent += metrics.messagesSent;
      totalMessagesReceived += metrics.messagesReceived;
      connectionErrors += metrics.errors;
      messageErrors += metrics.errors;
      allLatencies.push(...metrics.latencies);
    });

    // Calculate latency statistics
    allLatencies.sort((a, b) => a - b);
    const averageLatency = allLatencies.length > 0 
      ? allLatencies.reduce((sum, lat) => sum + lat, 0) / allLatencies.length 
      : 0;
    const maxLatency = allLatencies.length > 0 ? Math.max(...allLatencies) : 0;
    const minLatency = allLatencies.length > 0 ? Math.min(...allLatencies) : 0;
    const latencyP95 = allLatencies.length > 0 
      ? allLatencies[Math.floor(allLatencies.length * 0.95)] 
      : 0;
    const latencyP99 = allLatencies.length > 0 
      ? allLatencies[Math.floor(allLatencies.length * 0.99)] 
      : 0;

    const testDurationMs = this.testEndTime - this.testStartTime;
    const throughputMsgsPerSec = (totalMessagesReceived / testDurationMs) * 1000;

    // Get memory usage
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;

    return {
      totalClients: this.config.concurrentClients,
      successfulConnections,
      failedConnections,
      totalMessagesSent,
      totalMessagesReceived,
      averageLatency,
      maxLatency,
      minLatency,
      latencyP95,
      latencyP99,
      connectionErrors,
      messageErrors,
      throughputMsgsPerSec,
      memoryUsageMB,
      testDurationMs
    };
  }

  // Utility method for running predefined test scenarios
  static async runStandardTests(): Promise<Map<string, LoadTestMetrics>> {
    const results = new Map<string, LoadTestMetrics>();
    
    const testScenarios = [
      {
        name: 'Light Load',
        config: {
          concurrentClients: 10,
          testDurationMs: 30000,
          messageIntervalMs: 1000,
          targetLatencyMs: 5000,
          wsUrl: 'ws://localhost:8080/ws',
          subscriptionChannels: ['opportunities:active', 'prices:current']
        }
      },
      {
        name: 'Medium Load',
        config: {
          concurrentClients: 50,
          testDurationMs: 60000,
          messageIntervalMs: 500,
          targetLatencyMs: 5000,
          wsUrl: 'ws://localhost:8080/ws',
          subscriptionChannels: ['opportunities:active', 'prices:current', 'system:health']
        }
      },
      {
        name: 'Heavy Load',
        config: {
          concurrentClients: 100,
          testDurationMs: 120000,
          messageIntervalMs: 250,
          targetLatencyMs: 5000,
          wsUrl: 'ws://localhost:8080/ws',
          subscriptionChannels: ['opportunities:active', 'prices:current', 'system:health', 'performance:metrics']
        }
      }
    ];

    for (const scenario of testScenarios) {
      logger.info(`Running ${scenario.name} test scenario...`);
      const tester = new WebSocketLoadTester(scenario.config);
      const metrics = await tester.runLoadTest();
      results.set(scenario.name, metrics);
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    return results;
  }
}
