import { Router } from 'express';
import { z } from 'zod';
import logger from '../utils/logger.js';
import { TokenDiscoveryService } from '../services/TokenDiscoveryService.js';

const tokenSchema = z.object({
  name: z.string().min(1),
  symbol: z.string().min(1),
  address: z.string().regex(/^0x[a-fA-F0-9]{40}$/),
  network: z.string().min(1),
  decimals: z.number().int().min(0).max(18).optional()
});

const blacklistSchema = z.object({
  address: z.string().regex(/^0x[a-fA-F0-9]{40}$/),
  network: z.string().min(1),
  reason: z.string().min(1)
});

export default function createTokenRoutes(tokenService: TokenDiscoveryService) {
  const router = Router();

  // Get all whitelisted tokens
  router.get('/', async (req, res) => {
    try {
      const tokens = tokenService.getWhitelistedTokens();
      res.json({
        success: true,
        data: tokens,
        count: tokens.length
      });
    } catch (error) {
      logger.error('Error getting tokens:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve tokens'
      });
    }
  });

  // Get specific token
  router.get('/:network/:address', async (req, res) => {
    try {
      const { network, address } = req.params;
      
      if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token address format'
        });
      }

      const token = tokenService.getToken(address, network);
      
      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      res.json({
        success: true,
        data: token
      });
    } catch (error) {
      logger.error('Error getting token:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve token'
      });
    }
  });

  // Validate token
  router.post('/validate', async (req, res) => {
    try {
      const { address, network } = req.body;
      
      if (!address || !network) {
        return res.status(400).json({
          success: false,
          error: 'Address and network are required'
        });
      }

      if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token address format'
        });
      }

      const validation = await tokenService.validateToken(address, network);
      
      res.json({
        success: true,
        data: validation
      });
    } catch (error) {
      logger.error('Error validating token:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to validate token'
      });
    }
  });

  // Add token to whitelist
  router.post('/whitelist', async (req, res) => {
    try {
      const validatedData = tokenSchema.parse(req.body);
      
      const success = await tokenService.addToWhitelist(validatedData);
      
      if (success) {
        res.status(201).json({
          success: true,
          message: 'Token added to whitelist successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'Failed to add token to whitelist'
        });
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token data',
          details: error.errors
        });
      }
      
      logger.error('Error adding token to whitelist:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to add token to whitelist'
      });
    }
  });

  // Add token to blacklist
  router.post('/blacklist', async (req, res) => {
    try {
      const validatedData = blacklistSchema.parse(req.body);
      
      tokenService.addToBlacklist(
        validatedData.address,
        validatedData.network,
        validatedData.reason
      );
      
      res.status(201).json({
        success: true,
        message: 'Token added to blacklist successfully'
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Invalid blacklist data',
          details: error.errors
        });
      }
      
      logger.error('Error adding token to blacklist:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to add token to blacklist'
      });
    }
  });

  // Check if token is whitelisted
  router.get('/:network/:address/whitelisted', async (req, res) => {
    try {
      const { network, address } = req.params;
      
      if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token address format'
        });
      }

      const isWhitelisted = tokenService.isTokenWhitelisted(address, network);
      
      res.json({
        success: true,
        data: {
          address,
          network,
          isWhitelisted
        }
      });
    } catch (error) {
      logger.error('Error checking whitelist status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check whitelist status'
      });
    }
  });

  // Check if token is blacklisted
  router.get('/:network/:address/blacklisted', async (req, res) => {
    try {
      const { network, address } = req.params;
      
      if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token address format'
        });
      }

      const isBlacklisted = tokenService.isTokenBlacklisted(address, network);
      
      res.json({
        success: true,
        data: {
          address,
          network,
          isBlacklisted
        }
      });
    } catch (error) {
      logger.error('Error checking blacklist status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check blacklist status'
      });
    }
  });

  // Get token statistics
  router.get('/stats', async (req, res) => {
    try {
      const stats = tokenService.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting token stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve token statistics'
      });
    }
  });

  // Search tokens
  router.get('/search', async (req, res) => {
    try {
      const { q, network, minLiquidity, minSafetyScore } = req.query;
      
      let tokens = tokenService.getWhitelistedTokens();
      
      // Apply filters
      if (q) {
        const query = (q as string).toLowerCase();
        tokens = tokens.filter(token => 
          token.name.toLowerCase().includes(query) ||
          token.symbol.toLowerCase().includes(query) ||
          token.address.toLowerCase().includes(query)
        );
      }
      
      if (network) {
        tokens = tokens.filter(token => token.network === network);
      }
      
      if (minLiquidity) {
        const minLiq = parseFloat(minLiquidity as string);
        tokens = tokens.filter(token => token.liquidity >= minLiq);
      }
      
      if (minSafetyScore) {
        const minScore = parseFloat(minSafetyScore as string);
        tokens = tokens.filter(token => token.safetyScore >= minScore);
      }
      
      res.json({
        success: true,
        data: tokens,
        count: tokens.length,
        filters: { q, network, minLiquidity, minSafetyScore }
      });
    } catch (error) {
      logger.error('Error searching tokens:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to search tokens'
      });
    }
  });

  return router;
}
