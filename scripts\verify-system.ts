#!/usr/bin/env node

/**
 * System Verification Script
 * 
 * This script verifies that the MEV Arbitrage Bot system is properly configured
 * and ready for operation. It performs comprehensive checks without starting
 * the actual trading system.
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { config } from 'dotenv';
import { ConfigValidator } from '../backend/startup/ConfigValidator.js';
import { DependencyChecker } from '../backend/startup/DependencyChecker.js';
import { SystemTester } from '../backend/startup/SystemTester.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

interface VerificationResult {
  overall: 'PASS' | 'FAIL' | 'WARNING';
  config: any;
  dependencies: any;
  tests: any;
  recommendations: string[];
  criticalIssues: string[];
}

class SystemVerifier {
  private configValidator: ConfigValidator;
  private dependencyChecker: DependencyChecker;
  private systemTester: SystemTester;

  constructor() {
    this.configValidator = new ConfigValidator();
    this.dependencyChecker = new DependencyChecker();
    this.systemTester = new SystemTester();
  }

  public async verify(): Promise<VerificationResult> {
    console.log('🔍 MEV Arbitrage Bot - System Verification');
    console.log('==========================================\n');

    const result: VerificationResult = {
      overall: 'PASS',
      config: null,
      dependencies: null,
      tests: null,
      recommendations: [],
      criticalIssues: []
    };

    try {
      // Phase 1: Configuration Validation
      console.log('📋 Phase 1: Configuration Validation');
      console.log('------------------------------------');
      result.config = await this.configValidator.validate();
      this.printConfigResults(result.config);

      if (result.config.criticalErrors.length > 0) {
        result.overall = 'FAIL';
        result.criticalIssues.push(...result.config.criticalErrors);
      }

      // Phase 2: Dependency Checking
      console.log('\n🔍 Phase 2: Dependency Verification');
      console.log('-----------------------------------');
      result.dependencies = await this.dependencyChecker.checkAll();
      this.printDependencyResults(result.dependencies);

      if (result.dependencies.criticalFailures.length > 0) {
        result.overall = 'FAIL';
        result.criticalIssues.push(...result.dependencies.criticalFailures);
      }

      // Phase 3: System Tests (without starting services)
      console.log('\n🧪 Phase 3: System Readiness Tests');
      console.log('----------------------------------');
      result.tests = await this.runReadinessTests();
      this.printTestResults(result.tests);

      if (result.tests.criticalFailures.length > 0) {
        result.overall = 'FAIL';
        result.criticalIssues.push(...result.tests.criticalFailures);
      }

      // Compile recommendations
      result.recommendations = [
        ...result.config.recommendations,
        ...result.dependencies.warnings,
        ...result.tests.warnings
      ];

      // Determine overall status
      if (result.overall !== 'FAIL' && result.recommendations.length > 0) {
        result.overall = 'WARNING';
      }

      // Print final summary
      this.printFinalSummary(result);

      return result;

    } catch (error) {
      console.error('❌ Verification failed with error:', error);
      result.overall = 'FAIL';
      result.criticalIssues.push(`Verification error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  private printConfigResults(config: any): void {
    if (config.isValid) {
      console.log('✅ Configuration validation passed');
    } else {
      console.log('❌ Configuration validation failed');
    }

    if (config.criticalErrors.length > 0) {
      console.log('\n🚨 Critical Configuration Errors:');
      config.criticalErrors.forEach((error: string) => {
        console.log(`   ❌ ${error}`);
      });
    }

    if (config.errors.length > 0) {
      console.log('\n⚠️ Configuration Errors:');
      config.errors.forEach((error: string) => {
        console.log(`   ⚠️ ${error}`);
      });
    }

    if (config.warnings.length > 0) {
      console.log('\n⚠️ Configuration Warnings:');
      config.warnings.forEach((warning: string) => {
        console.log(`   ⚠️ ${warning}`);
      });
    }

    if (config.missingOptional.length > 0) {
      console.log(`\nℹ️ Missing optional configurations: ${config.missingOptional.join(', ')}`);
    }
  }

  private printDependencyResults(dependencies: any): void {
    console.log(`Dependencies: ${dependencies.healthyCount}/${dependencies.totalCount} healthy`);

    dependencies.dependencies.forEach((dep: any) => {
      const status = dep.status === 'healthy' ? '✅' : '❌';
      const critical = dep.critical ? '[CRITICAL]' : '[OPTIONAL]';
      const responseTime = dep.responseTime ? ` (${dep.responseTime}ms)` : '';
      
      console.log(`${status} ${dep.name} ${critical}${responseTime}`);
      
      if (dep.error) {
        console.log(`     Error: ${dep.error}`);
      }
    });

    if (dependencies.criticalFailures.length > 0) {
      console.log('\n🚨 Critical Dependency Failures:');
      dependencies.criticalFailures.forEach((failure: string) => {
        console.log(`   ❌ ${failure}`);
      });
    }
  }

  private async runReadinessTests(): Promise<any> {
    // Simplified readiness tests that don't require full service startup
    const tests = [
      {
        name: 'Environment File Check',
        test: () => this.checkEnvironmentFile(),
        critical: true
      },
      {
        name: 'Directory Structure',
        test: () => this.checkDirectoryStructure(),
        critical: true
      },
      {
        name: 'Database Schema',
        test: () => this.checkDatabaseSchema(),
        critical: false
      },
      {
        name: 'Network Connectivity',
        test: () => this.checkNetworkConnectivity(),
        critical: true
      }
    ];

    const results = [];
    const criticalFailures = [];
    const warnings = [];

    for (const testDef of tests) {
      try {
        await testDef.test();
        results.push({ name: testDef.name, passed: true, critical: testDef.critical });
        console.log(`✅ ${testDef.name}`);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.push({ 
          name: testDef.name, 
          passed: false, 
          critical: testDef.critical, 
          error: errorMessage 
        });
        
        console.log(`❌ ${testDef.name}: ${errorMessage}`);
        
        if (testDef.critical) {
          criticalFailures.push(`${testDef.name}: ${errorMessage}`);
        } else {
          warnings.push(`${testDef.name}: ${errorMessage}`);
        }
      }
    }

    return {
      tests: results,
      criticalFailures,
      warnings,
      passedCount: results.filter(t => t.passed).length,
      totalCount: results.length
    };
  }

  private async checkEnvironmentFile(): Promise<void> {
    const fs = await import('fs');
    const path = await import('path');
    
    const envPath = path.join(process.cwd(), '.env');
    
    if (!fs.existsSync(envPath)) {
      throw new Error('.env file not found');
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    if (envContent.trim().length === 0) {
      throw new Error('.env file is empty');
    }
  }

  private async checkDirectoryStructure(): Promise<void> {
    const fs = await import('fs');
    
    const requiredDirs = [
      'backend/services',
      'backend/startup',
      'backend/utils',
      'backend/config',
      'database'
    ];

    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        throw new Error(`Missing required directory: ${dir}`);
      }
    }

    // Create logs directory if it doesn't exist
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs', { recursive: true });
    }
  }

  private async checkDatabaseSchema(): Promise<void> {
    // This would check if the ML tables exist in Supabase
    // For now, just check if the schema file exists
    const fs = await import('fs');
    
    if (!fs.existsSync('database/supabase-schema.sql')) {
      throw new Error('Database schema file not found');
    }
  }

  private async checkNetworkConnectivity(): Promise<void> {
    // Test basic internet connectivity
    const https = await import('https');
    
    return new Promise((resolve, reject) => {
      const req = https.request('https://api.github.com', { method: 'HEAD', timeout: 5000 }, (res) => {
        if (res.statusCode === 200) {
          resolve();
        } else {
          reject(new Error(`Network test failed: HTTP ${res.statusCode}`));
        }
      });

      req.on('error', (error) => {
        reject(new Error(`Network connectivity failed: ${error.message}`));
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Network connectivity test timeout'));
      });

      req.end();
    });
  }

  private printTestResults(tests: any): void {
    console.log(`Tests: ${tests.passedCount}/${tests.totalCount} passed`);

    if (tests.criticalFailures.length > 0) {
      console.log('\n🚨 Critical Test Failures:');
      tests.criticalFailures.forEach((failure: string) => {
        console.log(`   ❌ ${failure}`);
      });
    }

    if (tests.warnings.length > 0) {
      console.log('\n⚠️ Test Warnings:');
      tests.warnings.forEach((warning: string) => {
        console.log(`   ⚠️ ${warning}`);
      });
    }
  }

  private printFinalSummary(result: VerificationResult): void {
    console.log('\n' + '='.repeat(50));
    console.log('📊 VERIFICATION SUMMARY');
    console.log('='.repeat(50));

    // Overall status
    const statusIcon = result.overall === 'PASS' ? '✅' : 
                      result.overall === 'WARNING' ? '⚠️' : '❌';
    console.log(`\nOverall Status: ${statusIcon} ${result.overall}`);

    // Critical issues
    if (result.criticalIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES THAT MUST BE RESOLVED:');
      result.criticalIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }

    // Recommendations
    if (result.recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      result.recommendations.slice(0, 5).forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
      
      if (result.recommendations.length > 5) {
        console.log(`   ... and ${result.recommendations.length - 5} more`);
      }
    }

    // Next steps
    console.log('\n🚀 NEXT STEPS:');
    
    if (result.overall === 'PASS') {
      console.log('✅ System is ready for startup!');
      console.log('   Run: npm start');
    } else if (result.overall === 'WARNING') {
      console.log('⚠️ System can start but has warnings');
      console.log('   Run: npm start (address warnings when possible)');
    } else {
      console.log('❌ System is NOT ready for startup');
      console.log('   Fix critical issues before starting');
    }

    console.log('\n📚 For detailed setup instructions, see: docs/STARTUP_GUIDE.md');
    console.log('🔧 For ML system details, see: docs/ML_LEARNING_SYSTEM.md');
  }
}

// Run verification
async function main() {
  const verifier = new SystemVerifier();
  const result = await verifier.verify();
  
  // Exit with appropriate code
  process.exit(result.overall === 'FAIL' ? 1 : 0);
}

main().catch((error) => {
  console.error('Verification script failed:', error);
  process.exit(1);
});
