import { WebSocketServer, WebSocket } from 'ws';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';

interface WebSocketClient {
  ws: WebSocket;
  id: string;
  subscriptions: Set<string>;
  lastPing: number;
}

export class WebSocketManager extends EventEmitter {
  private wss: WebSocketServer;
  private clients: Map<string, WebSocketClient> = new Map();
  private channels: Map<string, Set<string>> = new Map();
  private pingInterval!: NodeJS.Timeout;

  constructor(wss: WebSocketServer) {
    super();
    this.wss = wss;
    this.setupWebSocketServer();
    this.startPingInterval();
  }

  private setupWebSocketServer() {
    this.wss.on('connection', (ws, request) => {
      const clientId = this.generateClientId();
      const client: WebSocketClient = {
        ws,
        id: clientId,
        subscriptions: new Set(),
        lastPing: Date.now()
      };

      this.clients.set(clientId, client);
      logger.info(`WebSocket client connected: ${clientId}`);

      ws.on('message', (message) => {
        this.emit('message', ws, message);
        client.lastPing = Date.now();
      });

      ws.on('close', () => {
        this.handleClientDisconnect(clientId);
      });

      ws.on('error', (error) => {
        logger.error(`WebSocket error for client ${clientId}:`, error);
        this.handleClientDisconnect(clientId);
      });

      ws.on('pong', () => {
        client.lastPing = Date.now();
      });

      this.emit('connection', ws, request);
    });
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private handleClientDisconnect(clientId: string) {
    const client = this.clients.get(clientId);
    if (client) {
      // Remove client from all channels
      client.subscriptions.forEach(channel => {
        this.unsubscribeFromChannel(clientId, channel);
      });

      this.clients.delete(clientId);
      logger.info(`WebSocket client disconnected: ${clientId}`);
    }
  }

  private startPingInterval() {
    this.pingInterval = setInterval(() => {
      const now = Date.now();
      const timeout = 30000; // 30 seconds

      this.clients.forEach((client, clientId) => {
        if (now - client.lastPing > timeout) {
          logger.warn(`Client ${clientId} timed out, closing connection`);
          client.ws.terminate();
          this.handleClientDisconnect(clientId);
        } else if (client.ws.readyState === WebSocket.OPEN) {
          client.ws.ping();
        }
      });
    }, 15000); // Check every 15 seconds
  }

  public subscribe(ws: WebSocket, channel: string) {
    const client = this.findClientByWebSocket(ws);
    if (!client) {
      logger.warn('Attempted to subscribe unknown client to channel:', channel);
      return;
    }

    client.subscriptions.add(channel);

    if (!this.channels.has(channel)) {
      this.channels.set(channel, new Set());
    }

    this.channels.get(channel)!.add(client.id);

    logger.info(`Client ${client.id} subscribed to channel: ${channel}`);

    // Send confirmation
    this.sendToClient(client.id, {
      type: 'subscribed',
      channel,
      timestamp: new Date().toISOString()
    });
  }

  public unsubscribe(ws: WebSocket, channel: string) {
    const client = this.findClientByWebSocket(ws);
    if (!client) {
      logger.warn('Attempted to unsubscribe unknown client from channel:', channel);
      return;
    }

    this.unsubscribeFromChannel(client.id, channel);

    // Send confirmation
    this.sendToClient(client.id, {
      type: 'unsubscribed',
      channel,
      timestamp: new Date().toISOString()
    });
  }

  private unsubscribeFromChannel(clientId: string, channel: string) {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.delete(channel);
    }

    const channelClients = this.channels.get(channel);
    if (channelClients) {
      channelClients.delete(clientId);
      if (channelClients.size === 0) {
        this.channels.delete(channel);
      }
    }

    logger.info(`Client ${clientId} unsubscribed from channel: ${channel}`);
  }

  private findClientByWebSocket(ws: WebSocket): WebSocketClient | undefined {
    for (const client of this.clients.values()) {
      if (client.ws === ws) {
        return client;
      }
    }
    return undefined;
  }

  public broadcast(channel: string, data: any) {
    const channelClients = this.channels.get(channel);
    if (!channelClients || channelClients.size === 0) {
      return;
    }

    const message = JSON.stringify({
      type: 'broadcast',
      channel,
      data,
      timestamp: new Date().toISOString()
    });

    let sentCount = 0;
    channelClients.forEach(clientId => {
      const client = this.clients.get(clientId);
      if (client && client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(message);
          sentCount++;
        } catch (error) {
          logger.error(`Failed to send message to client ${clientId}:`, error);
          this.handleClientDisconnect(clientId);
        }
      }
    });

    logger.debug(`Broadcasted to channel ${channel}: ${sentCount} clients`);
  }

  public sendToClient(clientId: string, data: any) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      logger.warn(`Cannot send message to client ${clientId}: not connected`);
      return false;
    }

    try {
      const message = JSON.stringify({
        type: 'message',
        data,
        timestamp: new Date().toISOString()
      });

      client.ws.send(message);
      return true;
    } catch (error) {
      logger.error(`Failed to send message to client ${clientId}:`, error);
      this.handleClientDisconnect(clientId);
      return false;
    }
  }

  public getStats() {
    const channelStats: Record<string, number> = {};
    this.channels.forEach((clients, channel) => {
      channelStats[channel] = clients.size;
    });

    return {
      totalClients: this.clients.size,
      totalChannels: this.channels.size,
      channelStats
    };
  }

  public getClientSubscriptions(clientId: string): string[] {
    const client = this.clients.get(clientId);
    return client ? Array.from(client.subscriptions) : [];
  }

  public close() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }

    this.clients.forEach((client, clientId) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.close(1000, 'Server shutting down');
      }
    });

    this.clients.clear();
    this.channels.clear();

    logger.info('WebSocket manager closed');
  }
}
