import { expect } from "chai";
import hre from "hardhat";
const { ethers } = hre;

describe("Opportunity Detection and Profit Calculation Tests", function () {
  let owner;
  let user1;
  let mockOracle;
  let mockTokens = {};
  let liquidityChecker;

  beforeEach(async function () {
    [owner, user1] = await ethers.getSigners();
    
    // Deploy contracts
    const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
    mockOracle = await MockPriceOracle.deploy();
    await mockOracle.waitForDeployment();
    
    const LiquidityChecker = await ethers.getContractFactory("LiquidityChecker");
    liquidityChecker = await LiquidityChecker.deploy();
    await liquidityChecker.waitForDeployment();
    
    // Deploy mock tokens
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    
    mockTokens.WETH = await MockERC20.deploy(
      "Wrapped Ether",
      "WETH",
      18,
      ethers.parseEther("1000000"),
      owner.address
    );
    await mockTokens.WETH.waitForDeployment();
    
    mockTokens.USDC = await MockERC20.deploy(
      "USD Coin",
      "USDC",
      6,
      ethers.parseUnits("1000000000", 6),
      owner.address
    );
    await mockTokens.USDC.waitForDeployment();
    
    mockTokens.WBTC = await MockERC20.deploy(
      "Wrapped Bitcoin",
      "WBTC",
      8,
      ethers.parseUnits("100000", 8),
      owner.address
    );
    await mockTokens.WBTC.waitForDeployment();
  });

  describe("Price Difference Detection", function () {
    it("Should detect arbitrage opportunities between price sources", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const usdcAddress = await mockTokens.USDC.getAddress();
      
      // Set different prices to simulate arbitrage opportunity
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8)); // $2000
      await mockOracle.setPrice(usdcAddress, ethers.parseUnits("1", 8)); // $1
      
      const ethPrice = await mockOracle.getPrice(wethAddress);
      const usdcPrice = await mockOracle.getPrice(usdcAddress);
      
      // Calculate implied exchange rate
      const impliedRate = Number(ethPrice) / Number(usdcPrice);
      const expectedRate = 2000; // $2000 ETH / $1 USDC
      
      expect(Math.abs(impliedRate - expectedRate)).to.be.lessThan(1); // Within $1 tolerance
      
      console.log(`ETH/USDC implied rate: ${impliedRate.toFixed(2)}`);
      console.log(`Expected rate: ${expectedRate}`);
    });

    it("Should calculate profit potential accurately", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Simulate price difference between exchanges
      const exchange1Price = ethers.parseUnits("2000", 8); // $2000
      const exchange2Price = ethers.parseUnits("2050", 8); // $2050
      
      await mockOracle.setPrice(wethAddress, exchange1Price);
      
      // Calculate profit for 1 ETH arbitrage
      const tradeAmount = ethers.parseEther("1");
      const priceDiff = Number(exchange2Price - exchange1Price);
      const grossProfit = priceDiff / 1e8; // Convert from 8 decimals to dollars
      
      expect(grossProfit).to.equal(50); // $50 profit
      
      // Account for fees (assume 0.3% per trade, 2 trades = 0.6%)
      const feeRate = 0.006;
      const tradeCost = 2000 * feeRate; // $12 in fees
      const netProfit = grossProfit - tradeCost;
      
      expect(netProfit).to.equal(38); // $38 net profit
      
      console.log(`Gross profit: $${grossProfit}`);
      console.log(`Trading fees: $${tradeCost}`);
      console.log(`Net profit: $${netProfit}`);
    });

    it("Should validate minimum profit thresholds", async function () {
      const minProfitThreshold = 50; // $50 minimum
      
      // Test profitable opportunity
      const profitableOpportunity = {
        grossProfit: 100,
        fees: 20,
        gasEstimate: 150000,
        gasPriceGwei: 30
      };
      
      const gasCost = (profitableOpportunity.gasEstimate * profitableOpportunity.gasPriceGwei * 1e-9) * 2000; // Assume ETH = $2000
      const netProfit = profitableOpportunity.grossProfit - profitableOpportunity.fees - gasCost;
      
      expect(netProfit).to.be.greaterThan(minProfitThreshold);
      
      // Test unprofitable opportunity
      const unprofitableOpportunity = {
        grossProfit: 20,
        fees: 15,
        gasEstimate: 200000,
        gasPriceGwei: 50
      };
      
      const highGasCost = (unprofitableOpportunity.gasEstimate * unprofitableOpportunity.gasPriceGwei * 1e-9) * 2000;
      const lowNetProfit = unprofitableOpportunity.grossProfit - unprofitableOpportunity.fees - highGasCost;
      
      expect(lowNetProfit).to.be.lessThan(minProfitThreshold);
      
      console.log(`Profitable opportunity net profit: $${netProfit.toFixed(2)}`);
      console.log(`Unprofitable opportunity net profit: $${lowNetProfit.toFixed(2)}`);
    });
  });

  describe("Cross-Chain Opportunity Detection", function () {
    it("Should identify cross-chain price differences", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      
      // Simulate prices on different chains
      const ethereumPrice = ethers.parseUnits("2000", 8);
      const polygonPrice = ethers.parseUnits("2030", 8); // 1.5% higher
      
      await mockOracle.setPrice(wethAddress, ethereumPrice);
      
      const priceDifference = Number(polygonPrice - ethereumPrice) / 1e8;
      const profitMargin = (priceDifference / (Number(ethereumPrice) / 1e8)) * 100;
      
      expect(profitMargin).to.be.approximately(1.5, 0.1); // ~1.5% margin
      
      // Account for bridge costs
      const bridgeFee = 0.01 * 2000; // $20 bridge fee
      const bridgeTime = 600; // 10 minutes
      
      const netProfitPerETH = priceDifference - bridgeFee;
      expect(netProfitPerETH).to.be.greaterThan(0); // Should still be profitable
      
      console.log(`Cross-chain profit margin: ${profitMargin.toFixed(2)}%`);
      console.log(`Net profit per ETH: $${netProfitPerETH.toFixed(2)}`);
      console.log(`Bridge time: ${bridgeTime} seconds`);
    });

    it("Should calculate bridge costs accurately", async function () {
      const bridgeConfigs = {
        ethereum_to_polygon: {
          baseFee: ethers.parseEther("0.01"), // 0.01 ETH
          percentageFee: 0.001, // 0.1%
          timeMinutes: 10
        },
        ethereum_to_arbitrum: {
          baseFee: ethers.parseEther("0.005"), // 0.005 ETH
          percentageFee: 0.0005, // 0.05%
          timeMinutes: 15
        }
      };
      
      const tradeAmount = ethers.parseEther("5"); // 5 ETH
      const ethPrice = 2000; // $2000
      
      for (const [bridge, config] of Object.entries(bridgeConfigs)) {
        const baseFeeUSD = Number(config.baseFee) * ethPrice;
        const percentageFeeUSD = Number(tradeAmount) * config.percentageFee * ethPrice;
        const totalFeeUSD = baseFeeUSD + percentageFeeUSD;
        
        expect(totalFeeUSD).to.be.greaterThan(0);
        
        console.log(`${bridge} - Base fee: $${baseFeeUSD}, Percentage fee: $${percentageFeeUSD.toFixed(2)}, Total: $${totalFeeUSD.toFixed(2)}, Time: ${config.timeMinutes}min`);
      }
    });
  });

  describe("Triangular Arbitrage Detection", function () {
    it("Should detect triangular arbitrage opportunities", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const usdcAddress = await mockTokens.USDC.getAddress();
      const wbtcAddress = await mockTokens.WBTC.getAddress();
      
      // Set prices for triangular arbitrage: ETH -> USDC -> BTC -> ETH
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8)); // 1 ETH = $2000
      await mockOracle.setPrice(usdcAddress, ethers.parseUnits("1", 8)); // 1 USDC = $1
      await mockOracle.setPrice(wbtcAddress, ethers.parseUnits("40000", 8)); // 1 BTC = $40000
      
      // Calculate triangular arbitrage
      const startAmount = 1; // 1 ETH
      const ethToUsd = startAmount * 2000; // 2000 USDC
      const usdToBtc = ethToUsd / 40000; // 0.05 BTC
      const btcToEth = usdToBtc * (40000 / 2000); // Back to ETH
      
      const profit = btcToEth - startAmount;
      const profitPercentage = (profit / startAmount) * 100;
      
      // In this case, should be no profit (balanced prices)
      expect(Math.abs(profit)).to.be.lessThan(0.001); // Very small due to rounding
      
      console.log(`Triangular arbitrage - Start: ${startAmount} ETH, End: ${btcToEth.toFixed(6)} ETH`);
      console.log(`Profit: ${profit.toFixed(6)} ETH (${profitPercentage.toFixed(4)}%)`);
    });

    it("Should detect profitable triangular opportunities with price imbalances", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const usdcAddress = await mockTokens.USDC.getAddress();
      const wbtcAddress = await mockTokens.WBTC.getAddress();
      
      // Set imbalanced prices
      await mockOracle.setPrice(wethAddress, ethers.parseUnits("2000", 8)); // 1 ETH = $2000
      await mockOracle.setPrice(usdcAddress, ethers.parseUnits("1", 8)); // 1 USDC = $1
      await mockOracle.setPrice(wbtcAddress, ethers.parseUnits("39500", 8)); // 1 BTC = $39500 (undervalued)
      
      // Calculate triangular arbitrage with imbalance
      const startAmount = 1; // 1 ETH
      const ethToUsd = startAmount * 2000; // 2000 USDC
      const usdToBtc = ethToUsd / 39500; // ~0.0506 BTC (more BTC due to lower price)
      const btcToEth = usdToBtc * (40000 / 2000); // Use market rate for BTC->ETH: ~1.0127 ETH
      
      const profit = btcToEth - startAmount;
      const profitPercentage = (profit / startAmount) * 100;
      
      expect(profit).to.be.greaterThan(0.01); // Should be profitable
      
      console.log(`Profitable triangular arbitrage - Profit: ${profit.toFixed(6)} ETH (${profitPercentage.toFixed(4)}%)`);
    });
  });

  describe("Liquidity and Slippage Analysis", function () {
    it("Should check liquidity availability", async function () {
      const wethAddress = await mockTokens.WETH.getAddress();
      const usdcAddress = await mockTokens.USDC.getAddress();
      
      // Test liquidity checking functionality
      const tradeAmount = ethers.parseEther("10"); // 10 ETH
      
      // Mock liquidity check (in real implementation, this would query DEX pools)
      const mockLiquidity = {
        available: ethers.parseEther("1000"), // 1000 ETH available
        slippage: 0.002 // 0.2% slippage for 10 ETH trade
      };
      
      expect(tradeAmount).to.be.lessThan(mockLiquidity.available);
      expect(mockLiquidity.slippage).to.be.lessThan(0.01); // <1% slippage acceptable
      
      console.log(`Trade amount: ${ethers.formatEther(tradeAmount)} ETH`);
      console.log(`Available liquidity: ${ethers.formatEther(mockLiquidity.available)} ETH`);
      console.log(`Expected slippage: ${(mockLiquidity.slippage * 100).toFixed(2)}%`);
    });

    it("Should calculate slippage impact on profits", async function () {
      const baseProfit = 100; // $100 base profit
      const slippageRates = [0.001, 0.005, 0.01, 0.02]; // 0.1%, 0.5%, 1%, 2%
      
      for (const slippage of slippageRates) {
        const slippageCost = 2000 * slippage; // Assume $2000 trade value
        const adjustedProfit = baseProfit - slippageCost;
        const profitReduction = (slippageCost / baseProfit) * 100;
        
        console.log(`Slippage ${(slippage * 100).toFixed(1)}%: Cost $${slippageCost.toFixed(2)}, Adjusted profit $${adjustedProfit.toFixed(2)} (${profitReduction.toFixed(1)}% reduction)`);
        
        expect(adjustedProfit).to.be.lessThan(baseProfit);
      }
    });
  });

  describe("Performance and Accuracy", function () {
    it("Should detect opportunities within target timeframes", async function () {
      const startTime = Date.now();
      
      // Simulate opportunity detection process
      const opportunities = [];
      
      for (let i = 0; i < 10; i++) {
        const wethAddress = await mockTokens.WETH.getAddress();
        const usdcAddress = await mockTokens.USDC.getAddress();
        
        // Set prices
        await mockOracle.setPrice(wethAddress, ethers.parseUnits((2000 + i * 10).toString(), 8));
        await mockOracle.setPrice(usdcAddress, ethers.parseUnits("1", 8));
        
        // Get prices and calculate opportunity
        const ethPrice = await mockOracle.getPrice(wethAddress);
        const usdcPrice = await mockOracle.getPrice(usdcAddress);
        
        const opportunity = {
          pair: "ETH/USDC",
          ethPrice: Number(ethPrice) / 1e8,
          usdcPrice: Number(usdcPrice) / 1e8,
          timestamp: Date.now()
        };
        
        opportunities.push(opportunity);
      }
      
      const endTime = Date.now();
      const detectionTime = endTime - startTime;
      
      expect(detectionTime).to.be.lessThan(5000); // <5 seconds
      expect(opportunities.length).to.equal(10);
      
      console.log(`Detected ${opportunities.length} opportunities in ${detectionTime}ms`);
    });

    it("Should maintain >90% accuracy in profit calculations", async function () {
      const testCases = [
        { grossProfit: 100, fees: 10, gasEstimate: 150000, gasPriceGwei: 30 },
        { grossProfit: 200, fees: 25, gasEstimate: 200000, gasPriceGwei: 40 },
        { grossProfit: 50, fees: 8, gasEstimate: 120000, gasPriceGwei: 25 },
        { grossProfit: 300, fees: 35, gasEstimate: 250000, gasPriceGwei: 50 }
      ];
      
      let accurateCalculations = 0;
      
      for (const testCase of testCases) {
        const gasCost = (testCase.gasEstimate * testCase.gasPriceGwei * 1e-9) * 2000; // ETH = $2000
        const calculatedProfit = testCase.grossProfit - testCase.fees - gasCost;
        
        // Simulate "actual" profit with small variance
        const actualProfit = calculatedProfit * (0.95 + Math.random() * 0.1); // ±5% variance
        const accuracy = 1 - Math.abs(calculatedProfit - actualProfit) / actualProfit;
        
        if (accuracy > 0.9) {
          accurateCalculations++;
        }
        
        console.log(`Test case - Calculated: $${calculatedProfit.toFixed(2)}, Actual: $${actualProfit.toFixed(2)}, Accuracy: ${(accuracy * 100).toFixed(1)}%`);
      }
      
      const accuracyRate = accurateCalculations / testCases.length;
      expect(accuracyRate).to.be.greaterThan(0.9); // >90% accuracy
      
      console.log(`Overall accuracy rate: ${(accuracyRate * 100).toFixed(1)}%`);
    });
  });
});
