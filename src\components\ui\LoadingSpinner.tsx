import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
  fullScreen?: boolean;
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
};

export function LoadingSpinner({ 
  size = 'md', 
  className, 
  text,
  fullScreen = false 
}: LoadingSpinnerProps) {
  const spinner = (
    <div className={cn('flex items-center justify-center', fullScreen && 'min-h-screen')}>
      <div className="flex flex-col items-center space-y-4">
        <div
          className={cn(
            'animate-spin rounded-full border-2 border-dark-600 border-t-primary-500',
            sizeClasses[size],
            className
          )}
        />
        {text && (
          <p className="text-sm text-dark-400 animate-pulse">{text}</p>
        )}
      </div>
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-dark-900/80 backdrop-blur-sm z-50 flex items-center justify-center">
        {spinner}
      </div>
    );
  }

  return spinner;
}

// Skeleton loading components
export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn('glass-effect rounded-xl p-6 animate-pulse', className)}>
      <div className="space-y-4">
        <div className="h-4 bg-dark-700 rounded w-3/4"></div>
        <div className="h-8 bg-dark-700 rounded w-1/2"></div>
        <div className="space-y-2">
          <div className="h-3 bg-dark-700 rounded"></div>
          <div className="h-3 bg-dark-700 rounded w-5/6"></div>
        </div>
      </div>
    </div>
  );
}

export function SkeletonTable({ rows = 5 }: { rows?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4 animate-pulse">
          <div className="h-4 bg-dark-700 rounded w-1/4"></div>
          <div className="h-4 bg-dark-700 rounded w-1/3"></div>
          <div className="h-4 bg-dark-700 rounded w-1/6"></div>
          <div className="h-4 bg-dark-700 rounded w-1/4"></div>
        </div>
      ))}
    </div>
  );
}

export function SkeletonChart({ className }: { className?: string }) {
  return (
    <div className={cn('chart-container animate-pulse', className)}>
      <div className="h-64 bg-dark-700 rounded"></div>
    </div>
  );
}
